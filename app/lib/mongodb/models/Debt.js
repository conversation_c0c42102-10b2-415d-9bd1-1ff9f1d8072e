import mongoose from "mongoose";

const debtHistorySchema = new mongoose.Schema({
   date: {
      type: Date,
      default: Date.now,
   },
   oldBalance: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   newBalance: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   oldMinimumPayment: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   newMinimumPayment: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   oldAPR: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   newAPR: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   oldDueDate: {
      type: String,
      required: true,
   },
   newDueDate: {
      type: String,
      required: true,
   },
   note: {
      type: String,
      default: "Balance update",
   },
});

const debtSchema = new mongoose.Schema(
   {
      userId: {
         type: mongoose.Schema.Types.ObjectId,
         ref: "User",
         required: true,
         index: true,
      },
      debtType: {
         type: String,
         enum: [
            "credit card",
            "personal loan",
            "student loan",
            "mortgage",
            "auto loan",
            "medical debt",
            "other",
         ],
         required: true,
      },
      lender: {
         type: String,
         required: true,
         trim: true,
      },
      balance: {
         type: Number,
         required: true,
         set: (v) => Number(v.toFixed(2)),
      },
      apr: {
         type: Number,
         required: true,
         set: (v) => Number(v.toFixed(2)),
      },
      minimumPayment: {
         type: Number,
         required: true,
         set: (v) => Number(v.toFixed(2)),
      },
      dueDate: {
         type: String, // Day of month (1-31)
         required: true,
         default: "1",
      },
      active: {
         type: Boolean,
         default: true,
      },
      history: [debtHistorySchema],
   },
   {
      timestamps: true,
   }
);

// Pre-save middleware to ensure all monetary values are rounded to 2 decimal places
debtSchema.pre("save", function (next) {
   if (this.balance) this.balance = Number(this.balance.toFixed(2));
   if (this.apr) this.apr = Number(this.apr.toFixed(2));
   if (this.minimumPayment)
      this.minimumPayment = Number(this.minimumPayment.toFixed(2));

   next();
});

const Debt = mongoose.models.Debt || mongoose.model("Debt", debtSchema);

export default Debt;

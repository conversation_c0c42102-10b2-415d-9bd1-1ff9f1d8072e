import mongoose from "mongoose";

const recurringIncomeSchema = new mongoose.Schema({
   description: {
      type: String,
      required: true,
   },
   payAmount: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   payPeriod: {
      type: String,
      enum: ["weekly", "biweekly", "semimonthly", "monthly"],
      required: true,
   },
   payDay: {
      type: String, // For monthly: day of month (1-31)
      required: function () {
         return ["monthly"].includes(this.payPeriod);
      },
   },
   payDayOfWeek: {
      type: String, // For weekly/biweekly: day of week (1-7)
      required: function () {
         return ["weekly", "biweekly"].includes(this.payPeriod);
      },
   },
   payWeekDay: {
      type: String, // For weekly/biweekly: day name
      enum: [
         "Sunday",
         "Monday",
         "Tuesday",
         "Wednesday",
         "Thursday",
         "Friday",
         "Saturday",
      ],
      required: function () {
         return ["weekly", "biweekly"].includes(this.payPeriod);
      },
   },
   lastPaymentDate: {
      type: Date,
      required: function () {
         return this.payPeriod === "biweekly";
      },
      validate: {
         validator: function (v) {
            return this.payPeriod !== "biweekly" || v != null;
         },
         message: "Last payment date is required for bi-weekly incomes",
      },
   },
   enabled: {
      type: Boolean,
      default: true,
   },
});

const accountSchema = new mongoose.Schema({
   _id: {
      type: Number,
      required: true,
   },
   name: {
      type: String,
      required: true,
      trim: true,
   },
   bank: {
      type: String,
      required: true,
      trim: true,
   },
   accountType: {
      type: String,
      enum: ["cash"],
      required: true,
      default: "cash",
   },
   balance: {
      type: Number,
      required: true,
      default: 0,
      set: (v) => Number(v.toFixed(2)),
   },
   active: {
      type: Boolean,
      default: true,
   },
   plaidItemId: {
      type: String,
      required: false,
      default: null,
   },
});

const userSchema = new mongoose.Schema(
   {
      name: {
         type: String,
         required: true,
         trim: true,
      },
      email: {
         type: String,
         required: true,
         unique: true,
         trim: true,
         lowercase: true,
      },
      password: {
         type: String,
         required: true,
         minlength: 6,
      },

      isAdmin: {
         type: Boolean,
         default: false,
      },
      onboardingComplete: {
         type: Boolean,
         default: false,
      },
      mainIncomeId: {
         type: mongoose.Schema.Types.ObjectId,
         ref: "User.recurringIncomes",
         required: false,
         default: null,
      },
      recurringExpenses: [
         {
            id: {
               type: Number,
               required: true,
            },
            name: {
               type: String,
               required: true,
            },
            amount: {
               type: Number,
               required: true,
            },
            frequency: {
               type: String,
               enum: ["weekly", "biweekly", "monthly", "annually", "oneoff"],
               required: true,
            },
            dueDay: {
               type: String,
               required: true,
            },
            dueMonth: {
               type: String,
               required: false,
               validate: {
                  validator: function (v) {
                     // Only require dueMonth if frequency is annually
                     return this.frequency !== "annually" || v != null;
                  },
                  message: "dueMonth is required for annual expenses",
               },
            },
            enabled: {
               type: Boolean,
               default: true,
            },
            weeklyChargeType: {
               type: String,
               enum: ["one-time", "spread"],
               default: "one-time",
               validate: {
                  validator: function (v) {
                     // Only require weeklyChargeType if frequency is weekly
                     return this.frequency !== "weekly" || v != null;
                  },
                  message: "Weekly charge type is required for weekly expenses",
               },
            },
         },
      ],
      recurringIncomes: [recurringIncomeSchema],
      createdAt: {
         type: Date,
         default: Date.now,
      },
      // Balance tracking
      totalAssigned: {
         type: Number,
         default: 0,
         set: (v) => Number(v.toFixed(2)),
      },
      totalIncome: {
         type: Number,
         default: 0,
         set: (v) => Number(v.toFixed(2)),
      },
      readyToAssign: {
         type: Number,
         default: 0,
         set: (v) => Number(v.toFixed(2)),
      },
      accounts: [accountSchema],
      preferences: {
         type: Object,
         default: {
            showFutureExpenses: true,
            cashFlowWarnings: {
               enabled: true,
               periodsAhead: 4,
            },
            hideInactiveDebts: true,
         },
      },
      plaidItems: [
         {
            itemId: {
               type: String,
               required: true,
            },
            accessToken: {
               type: String,
               required: true,
            },
            institutionId: {
               type: String,
               required: true,
            },
            institutionName: {
               type: String,
               required: true,
            },
            lastSync: Date,
            status: {
               type: String,
               enum: [
                  "good",
                  "error",
                  "login_required",
                  "pending_expiration",
                  "pending_disconnect",
                  "suspended", // New status for suspended connections
               ],
               default: "good",
            },
            error: {
               errorCode: String,
               errorMessage: String,
               errorType: String,
               lastErrorDate: Date,
            },
            suspendedAt: {
               type: Date,
               required: false,
            },
            linkedAccountId: {
               type: Number,
               required: false,
               ref: "User.accounts",
               get: (v) => (v ? Number(v) : null),
               set: (v) => (v ? Number(v) : null),
            },
            accountType: String,
            accountSubtype: String,
            plaidAccountId: {
               type: String,
               required: true,
            },
            name: String,
            cursor: String,
            startDate: {
               type: Date,
               required: true,
            },
         },
      ],

      // Stripe subscription data
      stripeCustomerId: {
         type: String,
         required: false,
      },
      subscription: {
         id: {
            type: String,
            required: false,
         },
         status: {
            type: String,
            enum: [
               "active",
               "canceled",
               "incomplete",
               "incomplete_expired",
               "past_due",
               "trialing",
               "unpaid",
            ],
            required: false,
         },
         planId: {
            type: String,
            enum: ["free", "basic", "pro"],
            default: "free",
         },
         currentPeriodStart: {
            type: Date,
            required: false,
         },
         currentPeriodEnd: {
            type: Date,
            required: false,
         },
         cancelAtPeriodEnd: {
            type: Boolean,
            default: false,
         },
         nextPlanId: {
            type: String,
            enum: ["free", "basic", "pro"],
            required: false,
         },
      },
   },
   {
      timestamps: true,
   }
);

// Pre-save middleware to ensure all monetary values are rounded to 2 decimal places
userSchema.pre("save", function (next) {
   if (this.totalAssigned)
      this.totalAssigned = Number(this.totalAssigned.toFixed(2));
   if (this.totalIncome) this.totalIncome = Number(this.totalIncome.toFixed(2));
   if (this.readyToAssign)
      this.readyToAssign = Number(this.readyToAssign.toFixed(2));

   // Round account balances to 2 decimal places
   if (this.accounts && this.accounts.length > 0) {
      this.accounts = this.accounts.map((account) => ({
         ...account,
         balance: Number(account.balance.toFixed(2)),
      }));
   }

   // Handle recurring expenses
   if (this.recurringExpenses && this.recurringExpenses.length > 0) {
      this.recurringExpenses = this.recurringExpenses.map((expense) => ({
         ...expense,
         amount: Number(expense.amount.toFixed(2)),
      }));
   }

   // Handle recurring incomes
   if (this.recurringIncomes && this.recurringIncomes.length > 0) {
      this.recurringIncomes = this.recurringIncomes.map((income) => ({
         ...income,
         payAmount: Number(income.payAmount.toFixed(2)),
      }));
   }

   next();
});

const User = mongoose.models.User || mongoose.model("User", userSchema);

export default User;

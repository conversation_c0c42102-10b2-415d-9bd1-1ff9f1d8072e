import mongoose from "mongoose";

const LogSchema = new mongoose.Schema(
   {
      timestamp: {
         type: Date,
         default: Date.now,
         required: true,
      },
      level: {
         type: String,
         enum: ["info", "warning", "error", "debug"],
         default: "info",
      },
      source: {
         type: String,
         required: true,
      },
      event: {
         type: String,
         required: true,
      },
      userId: {
         type: mongoose.Schema.Types.ObjectId,
         ref: "User",
         required: false,
      },
      message: {
         type: String,
      },
      data: {
         type: mongoose.Schema.Types.Mixed,
      },
   },
   {
      timestamps: false, // We're using our own timestamp field
   }
);

// Create indexes for faster querying
LogSchema.index({ timestamp: -1 }); // Descending index for newest logs first
LogSchema.index({ source: 1, timestamp: -1 }); // Combined index for source + timestamp
LogSchema.index({ level: 1, timestamp: -1 }); // Combined index for level + timestamp
LogSchema.index({ userId: 1, timestamp: -1 }); // Combined index for userId + timestamp

// Check if model already exists to prevent overwriting during hot reloads
const Log = mongoose.models.Log || mongoose.model("Log", LogSchema);

export default Log;

import mongoose from "mongoose";

const IncomeSchema = new mongoose.Schema(
   {
      userId: {
         type: String,
         required: true,
      },
      date: {
         type: String,
         required: true,
         validate: {
            validator: function (v) {
               // Validate YYYY-MM-DD format
               return /^\d{4}-\d{2}-\d{2}$/.test(v);
            },
            message: "Date must be in YYYY-MM-DD format",
         },
      },
      description: {
         type: String,
         required: true,
      },
      expectedAmount: {
         type: Number,
         required: true,
         set: (v) => (v != null ? Number(Number(v).toFixed(2)) : 0),
      },
      receivedAmount: {
         type: Number,
         required: false,
         set: (v) => (v != null ? Number(Number(v).toFixed(2)) : 0),
      },
      category: {
         type: String,
         default: "Salary",
      },
      status: {
         type: String,
         enum: ["projected", "scheduled", "received"],
         default: "scheduled",
      },
      notes: {
         type: String,
      },
   },
   {
      timestamps: true,
   }
);

// Pre-save middleware to ensure all monetary values are rounded to 2 decimal places
IncomeSchema.pre("save", function (next) {
   if (this.expectedAmount)
      this.expectedAmount = Number(this.expectedAmount.toFixed(2));
   if (this.receivedAmount)
      this.receivedAmount = Number(this.receivedAmount.toFixed(2));
   next();
});

// Create compound index for userId and date
IncomeSchema.index({ userId: 1, date: 1 });

export default mongoose.models.Income || mongoose.model("Income", IncomeSchema);

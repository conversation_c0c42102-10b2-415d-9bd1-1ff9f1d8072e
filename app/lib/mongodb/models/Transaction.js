import mongoose from "mongoose";

const TransactionSchema = new mongoose.Schema({
   userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
   },
   type: {
      type: String,
      enum: ["Income", "Expense", "Transfer", "Adjustment"],
      required: true,
   },
   amount: {
      type: Number,
      required: true,
      set: (v) => Number(v.toFixed(2)),
   },
   payee: {
      type: String,
      required: true,
   },
   assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      default: null,
   },
   assignedToType: {
      type: String,
      enum: ["Income", "Expense", null],
      default: null,
   },
   accountId: {
      type: Number,
      required: true, // Changed to required since all transactions need an account
   },
   status: {
      type: String,
      enum: ["pending", "cleared"],
      default: "pending",
      required: true,
   },
   date: {
      type: Date,
      default: Date.now,
   },
   notes: {
      type: String,
   },
   isPayment: {
      type: Boolean,
      default: false,
   },
   isAdjustment: {
      type: Boolean,
      default: false,
   },
   // Add Plaid-specific fields
   plaidData: {
      type: mongoose.Schema.Types.Mixed, // Store the complete Plaid transaction object
      required: false,
   },
   plaidTransactionId: {
      type: String,
      required: false,
   },
   plaidAccountId: {
      type: String,
      required: false,
   },
   isPlaidTransaction: {
      type: Boolean,
      default: false,
   },
   pendingPlaidTransactionId: {
      type: String,
      required: false,
      default: null,
   },
});

// Pre-save middleware to ensure all monetary values are rounded to 2 decimal places
TransactionSchema.pre("save", function (next) {
   if (this.amount) this.amount = Number(this.amount.toFixed(2));
   if (!this.assignedTo) {
      this.assignedToType = null;
   }
   next();
});

// Add indexes for better query performance
TransactionSchema.index({ userId: 1, date: -1 });
TransactionSchema.index({ userId: 1, type: 1 });
TransactionSchema.index({ plaidTransactionId: 1 });
TransactionSchema.index({
   userId: 1,
   accountId: 1,
   date: 1,
   amount: 1,
   type: 1,
});

export default mongoose.models.Transaction ||
   mongoose.model("Transaction", TransactionSchema);

import mongoose from "mongoose";

const ExpenseSchema = new mongoose.Schema(
   {
      userId: {
         type: String,
         required: true,
      },
      recurringExpenseId: {
         type: Number,
         required: false,
         default: null,
         description:
            "Reference to the recurring expense this expense was created from",
      },
      date: {
         type: String,
         required: true,
         validate: {
            validator: function (v) {
               // Validate YYYY-MM-DD format
               return /^\d{4}-\d{2}-\d{2}$/.test(v);
            },
            message: "Date must be in YYYY-MM-DD format",
         },
      },
      startDate: {
         type: String,
         validate: {
            validator: function (v) {
               // Only require startDate if frequency is weekly AND weeklyChargeType is spread
               const isRequired =
                  this.frequency === "weekly" &&
                  this.weeklyChargeType === "spread";
               if (isRequired && !v) return false;
               // If provided, must be in YYYY-MM-DD format
               if (v && !/^\d{4}-\d{2}-\d{2}$/.test(v)) return false;
               return true;
            },
            message:
               "startDate is required for weekly spread expenses and must be in YYYY-MM-DD format",
         },
      },
      endDate: {
         type: String,
         validate: {
            validator: function (v) {
               // Only require endDate if frequency is weekly AND weeklyChargeType is spread
               const isRequired =
                  this.frequency === "weekly" &&
                  this.weeklyChargeType === "spread";
               if (isRequired && !v) return false;
               // If provided, must be in YYYY-MM-DD format
               if (v && !/^\d{4}-\d{2}-\d{2}$/.test(v)) return false;
               return true;
            },
            message:
               "endDate is required for weekly spread expenses and must be in YYYY-MM-DD format",
         },
      },
      description: {
         type: String,
         required: true,
      },
      frequency: {
         type: String,
         enum: [
            "weekly",
            "biweekly",
            "monthly",
            "quarterly",
            "annually",
            "oneoff",
         ],
         default: "oneoff",
      },
      weeklyChargeType: {
         type: String,
         enum: ["one-time", "spread"],
         validate: {
            validator: function (v) {
               // Only allow weeklyChargeType if frequency is weekly
               return this.frequency !== "weekly" || v != null;
            },
            message: "weeklyChargeType is required for weekly expenses",
         },
      },
      amountDue: {
         type: Number,
         required: true,
         set: (v) => (v != null ? Number(Number(v).toFixed(2)) : 0),
      },
      amountAssigned: {
         type: Number,
         required: true,
         default: 0,
         set: (v) => (v != null ? Number(Number(v).toFixed(2)) : 0),
      },
      amountSpent: {
         type: Number,
         required: true,
         default: 0,
         set: (v) => (v != null ? Number(Number(v).toFixed(2)) : 0),
      },
      status: {
         type: String,
         enum: [
            "projected",
            "scheduled",
            "funded",
            "paid",
            "late",
            "overpaid",
            "underpaid",
            "future",
         ],
         default: "scheduled",
      },
      notes: {
         type: String,
      },
      isDebtPayment: {
         type: Boolean,
         default: false,
      },
      debtId: {
         type: String,
         required: false,
         description: "Reference to the debt this expense was created from",
      },
   },
   {
      timestamps: true,
      toJSON: { virtuals: true },
      toObject: { virtuals: true },
   }
);

// Add virtual for amountAvailable
ExpenseSchema.virtual("amountAvailable").get(function () {
   return Number((this.amountAssigned + this.amountSpent).toFixed(2));
});

// Pre-save middleware to ensure proper calculations
ExpenseSchema.pre("save", function (next) {
   // Round monetary values
   if (this.amountDue) this.amountDue = Number(this.amountDue.toFixed(2));
   if (this.amountAssigned)
      this.amountAssigned = Number(this.amountAssigned.toFixed(2));
   if (this.amountSpent) this.amountSpent = Number(this.amountSpent.toFixed(2));

   next();
});

// Create compound index for userId and date
ExpenseSchema.index({ userId: 1, date: 1 });

// Clear existing model to prevent OverwriteModelError
mongoose.models = {};

export default mongoose.model("Expense", ExpenseSchema);

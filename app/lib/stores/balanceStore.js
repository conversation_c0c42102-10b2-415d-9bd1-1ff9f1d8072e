import { create } from "zustand";
import { devtools } from "zustand/middleware";

const useBalanceStore = create(
   devtools(
      (set, get) => ({
         // Balance state
         balances: {
            currentBalance: 0,
            totalIncome: 0,
            totalAssigned: 0,
            readyToAssign: 0,
            accounts: [],
         },
         isLoadingBalances: false,

         // Actions
         setBalances: (newBalances) =>
            set(
               (state) => ({
                  balances: {
                     ...state.balances,
                     ...newBalances,
                  },
               }),
               false,
               "setBalances"
            ),

         updateReadyToAssign: (amount) =>
            set(
               (state) => {
                  const newReadyToAssign = Number(
                     (state.balances.readyToAssign - amount).toFixed(2)
                  );
                  const newTotalAssigned = Number(
                     (state.balances.totalAssigned + amount).toFixed(2)
                  );

                  return {
                     balances: {
                        ...state.balances,
                        readyToAssign: newReadyToAssign,
                        totalAssigned: newTotalAssigned,
                     },
                  };
               },
               false,
               "updateReadyToAssign"
            ),

         // Update assigned amount with difference
         updateAssignedAmount: (difference) =>
            set(
               (state) => {
                  const newTotalAssigned = Number(
                     (state.balances.totalAssigned + difference).toFixed(2)
                  );
                  const newReadyToAssign = Number(
                     (state.balances.totalIncome - newTotalAssigned).toFixed(2)
                  );

                  return {
                     balances: {
                        ...state.balances,
                        totalAssigned: newTotalAssigned,
                        readyToAssign: newReadyToAssign,
                     },
                  };
               },
               false,
               "updateAssignedAmount"
            ),

         setLoadingBalances: (loading) =>
            set({ isLoadingBalances: loading }, false, "setLoadingBalances"),

         // Refresh balances from server
         refreshBalances: async () => {
            set({ isLoadingBalances: true }, false, "refreshBalances:start");

            try {
               // Fetch both balances and accounts in parallel
               const [balanceResponse, accountsResponse] = await Promise.all([
                  fetch("/api/user/balance", { method: "POST" }),
                  fetch("/api/user/accounts"),
               ]);

               if (!balanceResponse.ok) {
                  throw new Error("Failed to fetch balances");
               }

               const balanceData = await balanceResponse.json();
               let accountsData = [];

               // Try to get accounts data, but don't fail if it's not available
               if (accountsResponse.ok) {
                  const accountsResult = await accountsResponse.json();
                  accountsData = accountsResult.accounts || [];
               } else {
                  // If accounts fetch fails, preserve existing accounts data
                  const currentState = get();
                  accountsData = currentState.balances.accounts || [];
               }

               const freshBalances = {
                  currentBalance: Number(balanceData.currentBalance || 0),
                  totalIncome: Number(balanceData.totalIncome || 0),
                  totalAssigned: Number(balanceData.totalAssigned || 0),
                  readyToAssign: Number(balanceData.readyToAssign || 0),
                  accounts: accountsData,
               };

               set(
                  {
                     balances: freshBalances,
                     isLoadingBalances: false,
                  },
                  false,
                  "refreshBalances:success"
               );

               return freshBalances;
            } catch (error) {
               console.error("Error refreshing balances:", error);
               set(
                  { isLoadingBalances: false },
                  false,
                  "refreshBalances:error"
               );
               throw error;
            }
         },

         // Fetch accounts data separately
         fetchAccounts: async () => {
            try {
               const response = await fetch("/api/user/accounts");
               if (!response.ok) {
                  throw new Error("Failed to fetch accounts");
               }
               const data = await response.json();
               const accounts = data.accounts || [];

               // Update only the accounts in the store
               set(
                  (state) => ({
                     balances: {
                        ...state.balances,
                        accounts: accounts,
                     },
                  }),
                  false,
                  "fetchAccounts"
               );

               return accounts;
            } catch (error) {
               console.error("Error fetching accounts:", error);
               throw error;
            }
         },

         // Initialize balances (usually called on app startup)
         initializeBalances: async () => {
            const { refreshBalances } = get();
            return await refreshBalances();
         },

         // Reset store (useful for logout)
         resetStore: () =>
            set(
               {
                  balances: {
                     currentBalance: 0,
                     totalIncome: 0,
                     totalAssigned: 0,
                     readyToAssign: 0,
                     accounts: [],
                  },
                  isLoadingBalances: false,
               },
               false,
               "resetStore"
            ),
      }),
      {
         name: "balance-store",
      }
   )
);

export default useBalanceStore;

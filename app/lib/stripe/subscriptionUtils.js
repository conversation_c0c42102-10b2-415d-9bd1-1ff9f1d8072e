import stripe, { SUBSCRIPTION_PLANS } from "./config.js";
import dbConnect from "../mongodb/dbConnect.js";
import User from "../mongodb/models/User.js";

/**
 * PLAID CURSOR STRATEGY DOCUMENTATION
 *
 * According to Plaid's documentation for /transactions/sync, there are two cursor strategies
 * for existing Items (connections that have been used before):
 *
 * 1. FULL HISTORICAL SYNC (cursor: "")
 *    - Returns ALL historical transactions as "adds"
 *    - Useful for rebuilding complete transaction history
 *    - Can be slow and generate high system load for large datasets
 *    - Best for: Users who need complete historical data after reactivation
 *
 * 2. FORWARD-ONLY SYNC (cursor: "now")
 *    - Returns no transactions initially, only a cursor for future updates
 *    - Only syncs NEW transactions going forward from the API call time
 *    - Much faster and lighter on system resources
 *    - Best for: Users who want to resume from where they left off
 *
 * OUR IMPLEMENTATION:
 * - When connections are suspended: We preserve the existing cursor
 * - When connections are reactivated:
 *   - Automatic reactivation (via webhooks): Uses "now" cursor for performance
 *   - Manual reactivation (via API): Allows user to choose strategy
 *
 * This approach follows Plaid's recommendation to use "now" cursor for existing Items
 * unless you specifically need to rebuild the complete transaction history.
 */

/**
 * Create a Stripe customer for a user
 * @param {Object} user - User object with email and name
 * @returns {Promise<string>} - Stripe customer ID
 */
export async function createStripeCustomer(user) {
   try {
      const customer = await stripe.customers.create({
         email: user.email,
         name: user.name,
         metadata: {
            userId: user._id.toString(),
         },
      });

      return customer.id;
   } catch (error) {
      console.error("Error creating Stripe customer:", error);
      throw error;
   }
}

/**
 * Get user's current subscription plan
 * @param {string} userId - MongoDB user ID
 * @returns {Promise<Object>} - Subscription plan object
 */
export async function getUserSubscriptionPlan(userId) {
   try {
      await dbConnect();
      const user = await User.findById(userId);

      if (!user || !user.stripeCustomerId) {
         return SUBSCRIPTION_PLANS.free;
      }

      // If user has active subscription, check if it's still valid
      if (user.subscription && user.subscription.status === "active") {
         // If subscription is set to cancel at period end, check if we're still within the period
         if (
            user.subscription.cancelAtPeriodEnd &&
            user.subscription.currentPeriodEnd
         ) {
            const now = new Date();
            const periodEnd = new Date(user.subscription.currentPeriodEnd);

            // If we're past the period end, they should be on free tier
            if (now > periodEnd) {
               return SUBSCRIPTION_PLANS.free;
            }
         }

         return (
            SUBSCRIPTION_PLANS[user.subscription.planId] ||
            SUBSCRIPTION_PLANS.free
         );
      }

      return SUBSCRIPTION_PLANS.free;
   } catch (error) {
      console.error("Error getting user subscription plan:", error);
      return SUBSCRIPTION_PLANS.free;
   }
}

/**
 * Check if user has access to a specific feature
 * @param {string} userId - MongoDB user ID
 * @param {string} feature - Feature name to check
 * @returns {Promise<boolean>} - Whether user has access
 */
export async function checkUserFeatureAccess(userId, feature) {
   try {
      const plan = await getUserSubscriptionPlan(userId);
      return plan.limits[feature] === true;
   } catch (error) {
      console.error("Error checking user feature access:", error);
      return false;
   }
}

/**
 * Check if user has suspended Plaid connections that can be reactivated
 * @param {string} userId - MongoDB user ID
 * @returns {Promise<Object>} - Information about suspended connections
 */
export async function checkSuspendedConnections(userId) {
   try {
      await dbConnect();
      const user = await User.findById(userId);

      if (!user || !user.plaidItems || user.plaidItems.length === 0) {
         return {
            hasSuspendedConnections: false,
            suspendedCount: 0,
            totalConnections: 0,
         };
      }

      const suspendedItems = user.plaidItems.filter(
         (item) => item.status === "suspended"
      );

      return {
         hasSuspendedConnections: suspendedItems.length > 0,
         suspendedCount: suspendedItems.length,
         totalConnections: user.plaidItems.length,
         suspendedConnections: suspendedItems.map((item) => ({
            itemId: item.itemId,
            institutionName: item.institutionName,
            suspendedAt: item.suspendedAt,
            linkedAccountId: item.linkedAccountId,
         })),
      };
   } catch (error) {
      console.error("Error checking suspended connections:", error);
      return {
         hasSuspendedConnections: false,
         suspendedCount: 0,
         totalConnections: 0,
         error: error.message,
      };
   }
}

/**
 * Get user's account limit
 * @param {string} userId - MongoDB user ID
 * @returns {Promise<number>} - Account limit (-1 for unlimited)
 */
export async function getUserAccountLimit(userId) {
   try {
      const plan = await getUserSubscriptionPlan(userId);
      return plan.limits.accounts;
   } catch (error) {
      console.error("Error getting user account limit:", error);
      return 3; // Default to free plan limit
   }
}

/**
 * Update user's subscription in database
 * @param {string} userId - MongoDB user ID
 * @param {Object} subscriptionData - Subscription data from Stripe
 */
export async function updateUserSubscription(userId, subscriptionData) {
   try {
      console.log("updateUserSubscription called with userId:", userId);
      console.log("subscriptionData:", subscriptionData);

      await dbConnect();

      const updateData = {
         "subscription.id": subscriptionData.id,
         "subscription.status": subscriptionData.status,
         "subscription.planId": subscriptionData.planId,
         "subscription.currentPeriodStart": new Date(
            subscriptionData.current_period_start * 1000
         ),
         "subscription.currentPeriodEnd": new Date(
            subscriptionData.current_period_end * 1000
         ),
         "subscription.cancelAtPeriodEnd":
            subscriptionData.cancel_at_period_end,
         updatedAt: new Date(),
      };

      console.log("Update data being applied:", updateData);

      const result = await User.findByIdAndUpdate(userId, updateData, {
         new: true,
      });

      console.log("User update result:", result ? "Success" : "User not found");

      if (result) {
         console.log("Updated user subscription:", result.subscription);
      }
   } catch (error) {
      console.error("Error updating user subscription:", error);
      throw error;
   }
}

/**
 * Disconnect Plaid connections when subscription is cancelled
 * This calls /item/remove to stop billing and removes all connection data
 * @param {string} userId - MongoDB user ID
 * @returns {Promise<Object>} - Disconnection result
 */
export async function disconnectPlaidConnections(userId) {
   try {
      await dbConnect();
      const user = await User.findById(userId);

      if (!user || !user.plaidItems || user.plaidItems.length === 0) {
         return {
            success: true,
            message: "No Plaid connections to disconnect",
         };
      }

      console.log(
         `Disconnecting ${user.plaidItems.length} Plaid connections for user ${user.email}`
      );

      // Import plaidClient here to avoid circular dependencies
      const { plaidClient } = await import("../plaid/config.js");

      const disconnectionResults = [];

      // Process each plaid item
      for (const plaidItem of user.plaidItems) {
         try {
            // Call /item/remove to stop billing and invalidate access token
            await plaidClient.itemRemove({
               access_token: plaidItem.accessToken,
            });

            console.log(
               `Successfully disconnected Plaid item: ${plaidItem.itemId}`
            );
            disconnectionResults.push({
               itemId: plaidItem.itemId,
               institutionName: plaidItem.institutionName,
               success: true,
            });
         } catch (plaidError) {
            console.error(
               `Error disconnecting Plaid item ${plaidItem.itemId}:`,
               plaidError
            );
            disconnectionResults.push({
               itemId: plaidItem.itemId,
               institutionName: plaidItem.institutionName,
               success: false,
               error: plaidError.message,
            });
            // Continue with other items even if one fails
         }
      }

      // Remove all plaid items from user document and clear plaidItemId from accounts
      await User.findByIdAndUpdate(user._id, {
         $unset: {
            plaidItems: 1, // Remove all plaid items
         },
         $set: {
            "accounts.$[].plaidItemId": null, // Clear plaidItemId from all accounts
         },
      });

      console.log(
         `Disconnected ${disconnectionResults.length} Plaid connections for user ${user.email}:`,
         {
            successful: disconnectionResults.filter((r) => r.success).length,
            failed: disconnectionResults.filter((r) => !r.success).length,
         }
      );

      return {
         success: true,
         message: `Disconnected ${disconnectionResults.length} Plaid connections`,
         disconnectedCount: disconnectionResults.length,
         results: disconnectionResults,
      };
   } catch (error) {
      console.error("Error disconnecting Plaid connections:", error);
      throw error;
   }
}

/**
 * Downgrade user to free tier
 * @param {string} userId - MongoDB user ID
 */
export async function downgradeToFree(userId) {
   try {
      await dbConnect();
      const user = await User.findById(userId);

      if (!user) {
         throw new Error("User not found");
      }

      console.log(`Downgrading user ${user.email} to free tier`);
      console.log(`User subscription data:`, {
         id: user.subscription?.id,
         status: user.subscription?.status,
         planId: user.subscription?.planId,
         currentPeriodEnd: user.subscription?.currentPeriodEnd,
         hasSubscription: !!user.subscription,
         hasSubscriptionId: !!user.subscription?.id,
      });

      let periodEndDate = null;

      // If user has a subscription, cancel it at period end in Stripe
      if (user.subscription && user.subscription.id) {
         console.log(
            `Attempting to cancel subscription at period end for user ${user.email}: ${user.subscription.id}`
         );

         try {
            // Cancel the subscription at period end (not immediately)
            const updatedSubscription = await stripe.subscriptions.update(
               user.subscription.id,
               {
                  cancel_at_period_end: true,
               }
            );

            periodEndDate = new Date(
               updatedSubscription.current_period_end * 1000
            );

            console.log(
               `Subscription set to cancel at period end for user ${user.email}:`,
               {
                  id: updatedSubscription.id,
                  status: updatedSubscription.status,
                  cancel_at_period_end:
                     updatedSubscription.cancel_at_period_end,
                  current_period_end: periodEndDate,
               }
            );
         } catch (stripeError) {
            console.error(
               `Error setting subscription to cancel at period end for user ${user.email}:`,
               stripeError
            );
            // Don't throw here - continue with database update even if Stripe fails
            // Use existing period end date if available
            periodEndDate = user.subscription.currentPeriodEnd;
         }
      } else {
         console.log(`No active subscription to cancel for user ${user.email}`);
      }

      // Update user subscription in database - keep it active until period end
      const updateData = {
         "subscription.cancelAtPeriodEnd": true,
         "subscription.nextPlanId": "free", // Track what plan they'll downgrade to
         updatedAt: new Date(),
      };

      // If we have a period end date, update it
      if (periodEndDate) {
         updateData["subscription.currentPeriodEnd"] = periodEndDate;
      }

      // If no subscription exists, immediately set to free tier and suspend connections
      if (!user.subscription || !user.subscription.id) {
         updateData["subscription.id"] = null;
         updateData["subscription.status"] = "active";
         updateData["subscription.planId"] = "free";
         updateData["subscription.currentPeriodStart"] = new Date();
         updateData["subscription.currentPeriodEnd"] = null;
         updateData["subscription.cancelAtPeriodEnd"] = false;
         delete updateData["subscription.nextPlanId"];

         // Suspend Plaid connections immediately for users without active subscriptions
         await disconnectPlaidConnections(userId);
      }

      await User.findByIdAndUpdate(userId, updateData);

      const message =
         user.subscription && user.subscription.id
            ? `Successfully scheduled downgrade to free tier. You'll maintain access to paid features until ${
                 periodEndDate
                    ? periodEndDate.toDateString()
                    : "the end of your billing period"
              }. Your bank connections will be disconnected when the subscription expires.`
            : "Successfully downgraded to free tier. Your bank connections have been disconnected.";

      console.log(`User ${user.email} downgrade processed: ${message}`);

      return { success: true, message };
   } catch (error) {
      console.error("Error downgrading user to free tier:", error);
      throw error;
   }
}

/**
 * Reactivate a canceled subscription before period end
 * @param {string} userId - MongoDB user ID
 * @returns {Promise<Object>} - Reactivation result
 */
export async function reactivateSubscription(userId) {
   try {
      await dbConnect();
      const user = await User.findById(userId);

      if (!user) {
         throw new Error("User not found");
      }

      console.log(`Reactivating subscription for user ${user.email}`);
      console.log(`User subscription data:`, {
         id: user.subscription?.id,
         status: user.subscription?.status,
         planId: user.subscription?.planId,
         cancelAtPeriodEnd: user.subscription?.cancelAtPeriodEnd,
         currentPeriodEnd: user.subscription?.currentPeriodEnd,
      });

      // Check if user has a subscription that can be reactivated
      if (!user.subscription || !user.subscription.id) {
         throw new Error("No subscription found to reactivate");
      }

      if (!user.subscription.cancelAtPeriodEnd) {
         throw new Error("Subscription is not scheduled for cancellation");
      }

      // Check if we're still within the billing period
      if (user.subscription.currentPeriodEnd) {
         const now = new Date();
         const periodEnd = new Date(user.subscription.currentPeriodEnd);

         if (now > periodEnd) {
            throw new Error(
               "Cannot reactivate subscription - billing period has already ended"
            );
         }
      }

      // Reactivate the subscription in Stripe
      const updatedSubscription = await stripe.subscriptions.update(
         user.subscription.id,
         {
            cancel_at_period_end: false,
         }
      );

      console.log(
         `Subscription reactivated in Stripe for user ${user.email}:`,
         {
            id: updatedSubscription.id,
            status: updatedSubscription.status,
            cancel_at_period_end: updatedSubscription.cancel_at_period_end,
            current_period_end: new Date(
               updatedSubscription.current_period_end * 1000
            ),
         }
      );

      // Update user subscription in database
      await User.findByIdAndUpdate(userId, {
         "subscription.cancelAtPeriodEnd": false,
         "subscription.nextPlanId": null,
         updatedAt: new Date(),
      });

      // Reactivate any suspended Plaid connections
      await reactivatePlaidConnections(userId);

      const message = `Successfully reactivated your subscription. You will continue to be billed at the end of your current period (${new Date(
         updatedSubscription.current_period_end * 1000
      ).toDateString()}). Your bank connections have been restored.`;

      console.log(`User ${user.email} subscription reactivated: ${message}`);

      return { success: true, message };
   } catch (error) {
      console.error("Error reactivating subscription:", error);
      throw error;
   }
}

/**
 * Process expired subscriptions and move users to free tier
 * This should be called by a webhook or scheduled task
 * @param {string} userId - MongoDB user ID (optional, processes all if not provided)
 * @returns {Promise<Object>} - Processing results
 */
export async function processExpiredSubscriptions(userId = null) {
   try {
      await dbConnect();

      const query = {
         "subscription.cancelAtPeriodEnd": true,
         "subscription.currentPeriodEnd": { $lt: new Date() },
         "subscription.status": "active",
      };

      if (userId) {
         query._id = userId;
      }

      const usersToProcess = await User.find(query);

      console.log(`Processing ${usersToProcess.length} expired subscriptions`);

      const results = [];

      for (const user of usersToProcess) {
         try {
            // Disconnect Plaid connections before updating subscription
            await disconnectPlaidConnections(user._id);

            // Update user to free tier
            await User.findByIdAndUpdate(user._id, {
               "subscription.id": null,
               "subscription.status": "active",
               "subscription.planId": "free",
               "subscription.currentPeriodStart": new Date(),
               "subscription.currentPeriodEnd": null,
               "subscription.cancelAtPeriodEnd": false,
               "subscription.nextPlanId": null,
               updatedAt: new Date(),
            });

            console.log(
               `Successfully moved user ${user.email} to free tier and disconnected Plaid connections`
            );
            results.push({
               userId: user._id,
               email: user.email,
               success: true,
               message:
                  "Successfully moved to free tier and disconnected Plaid connections",
            });
         } catch (error) {
            console.error(`Error processing user ${user.email}:`, error);
            results.push({
               userId: user._id,
               email: user.email,
               success: false,
               error: error.message,
            });
         }
      }

      return {
         success: true,
         processed: results.length,
         results,
      };
   } catch (error) {
      console.error("Error processing expired subscriptions:", error);
      throw error;
   }
}

/**
 * Set up free tier for user (database-only, no Stripe subscription)
 * @param {Object} user - User object with _id
 * @returns {Promise<Object>} - Success response
 */
export async function setupFreeTier(user) {
   try {
      console.log(`Setting up database-only free tier for user ${user.email}`);

      // Update user's subscription in database only - no Stripe subscription created
      await User.findByIdAndUpdate(user._id, {
         "subscription.id": null, // No Stripe subscription ID
         "subscription.status": "active", // Active free tier
         "subscription.planId": "free", // Free plan
         "subscription.currentPeriodStart": new Date(),
         "subscription.currentPeriodEnd": null, // Free tier doesn't expire
         "subscription.cancelAtPeriodEnd": false,
         updatedAt: new Date(),
      });

      console.log(`Database-only free tier set up for user ${user.email}`);

      return {
         success: true,
         type: "database_only",
         planId: "free",
         message: "Free tier set up successfully (no invoices created)",
      };
   } catch (error) {
      console.error("Error setting up free tier:", error);
      throw error;
   }
}

/**
 * Cancel user's subscription
 * @param {string} userId - MongoDB user ID
 */
export async function cancelUserSubscription(userId) {
   try {
      await dbConnect();
      const user = await User.findById(userId);

      if (user && user.subscription && user.subscription.id) {
         // Cancel subscription in Stripe
         await stripe.subscriptions.update(user.subscription.id, {
            cancel_at_period_end: true,
         });

         // Update in database
         await User.findByIdAndUpdate(userId, {
            "subscription.cancelAtPeriodEnd": true,
            updatedAt: new Date(),
         });
      }
   } catch (error) {
      console.error("Error canceling user subscription:", error);
      throw error;
   }
}

import Stripe from "stripe";

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
   apiVersion: "2024-06-20",
});

// Subscription plans configuration
export const SUBSCRIPTION_PLANS = {
   free: {
      id: "free",
      name: "Free",
      price: 0,
      priceId: process.env.STRIPE_FREE_PRICE_ID || null, // Add environment variable for free price ID
      features: ["Basic budget tracking", "Up to 3 accounts", "Basic reports"],
      limits: {
         accounts: 3,
         bank_connections: false,
         data_export: false,
         advanced_reporting: false,
         financial_forecasting: false,
         priority_support: false,
      },
   },
   basic: {
      id: "basic",
      name: "Basic",
      price: 4.99,
      priceId: process.env.STRIPE_BASIC_PRICE_ID,
      features: [
         "Basic budget tracking",
         "Up to 10 accounts",
         "Bank connections",
         "Advanced reporting",
         "Data export",
      ],
      limits: {
         accounts: 10,
         bank_connections: true,
         data_export: true,
         advanced_reporting: true,
         financial_forecasting: false,
         priority_support: false,
      },
   },
   pro: {
      id: "pro",
      name: "Pro",
      price: 9.99,
      priceId: process.env.STRIPE_PRO_PRICE_ID,
      features: [
         "Basic budget tracking",
         "Unlimited accounts",
         "Bank connections",
         "Advanced reporting",
         "Data export",
         "Financial forecasting",
         "Priority support",
      ],
      limits: {
         accounts: -1, // unlimited
         bank_connections: true,
         data_export: true,
         advanced_reporting: true,
         financial_forecasting: true,
         priority_support: true,
      },
   },
};

export default stripe;

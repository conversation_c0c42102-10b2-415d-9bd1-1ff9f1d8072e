import { Configuration, PlaidApi, PlaidEnvironments } from "plaid";

const configuration = new Configuration({
   basePath:
      process.env.PLAID_ENV === "sandbox"
         ? PlaidEnvironments.sandbox
         : process.env.PLAID_ENV === "production"
         ? PlaidEnvironments.production
         : PlaidEnvironments.development,
   baseOptions: {
      headers: {
         "PLAID-CLIENT-ID": process.env.PLAID_CLIENT_ID,
         "PLAID-SECRET": process.env.PLAID_SECRET,
      },
   },
});

// Create a singleton Plaid client instance
const plaidClient = new PlaidApi(configuration);

export { plaidClient, Configuration, PlaidApi, PlaidEnvironments };

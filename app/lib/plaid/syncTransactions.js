import mongoose from "mongoose";
import User from "../mongodb/models/User";
import Transaction from "../mongodb/models/Transaction";
import Income from "../mongodb/models/Income";
import Expense from "../mongodb/models/Expense";
import Debt from "../mongodb/models/Debt";
import { plaidClient } from "./config";
import { logInfo, logError, logDebug } from "../utils/logger";

// Helper function to round numbers to 2 decimal places
function roundToTwo(num) {
   return Number(Math.round(num + "e+2") + "e-2");
}

// Helper function to get the start of the current month
function getCurrentMonthStart() {
   const now = new Date();
   return new Date(now.getFullYear(), now.getMonth(), 1);
}

// Helper function to filter transactions by date (using plaidItem's startDate)
function filterTransactionsByDate(transactions, cutoffDate = null) {
   if (!cutoffDate) {
      cutoffDate = getCurrentMonthStart();
   }

   console.log("Filtering transactions by date:", {
      cutoffDate: cutoffDate.toISOString(),
      totalTransactions: transactions.length,
      dateFilteringEnabled: true,
   });

   return transactions.filter((tx) => {
      const txDate = new Date(tx.date);
      return txDate >= cutoffDate;
   });
}

/**
 * Synchronizes transactions for a Plaid item
 * @param {string} itemId - The Plaid item ID
 * @param {string} userId - The user ID
 * @param {boolean} isWebhook - Whether this is being called from a webhook
 * @returns {Promise<Object>} - The sync result
 *
 * Note: This function uses the plaidItem.startDate to filter transactions.
 * Only transactions from the connection date/time forward will be synchronized.
 * This prevents historical transactions from interfering with user-set starting balances.
 */
export async function syncTransactionsForItem(
   itemId,
   userId,
   isWebhook = false
) {
   // Start a MongoDB session for the transaction
   const mongoSession = await mongoose.startSession();
   mongoSession.startTransaction();

   try {
      // Log sync start
      await logInfo(
         "plaid-sync",
         "sync_started",
         `Starting Plaid transaction sync for item: ${itemId}`,
         {
            itemId,
            userId,
            isWebhook,
            timestamp: new Date().toISOString(),
         },
         userId
      );

      await mongoose.connect(process.env.MONGODB_URI);

      // Find the user and get the access token for the item
      const user = await User.findById(userId);

      if (!user) {
         await mongoSession.abortTransaction();
         await logError(
            "plaid-sync",
            "user_not_found",
            `User not found for ID: ${userId}`,
            { userId, itemId, isWebhook }
         );
         throw new Error("User not found");
      }

      const plaidItem = user.plaidItems.find((item) => item.itemId === itemId);

      if (!plaidItem) {
         await mongoSession.abortTransaction();
         await logError(
            "plaid-sync",
            "item_not_found",
            `Plaid item ${itemId} not found for user ${userId}`,
            { userId, itemId, isWebhook }
         );
         throw new Error("Plaid item not found");
      }

      let newTransactions = 0;
      let updatedTransactions = 0;
      let removedTransactions = 0;

      // Get the cursor (or empty string if it's the first sync)
      const cursor = plaidItem.cursor || "";

      // Log cursor info for debugging
      console.log(`Syncing with cursor for item ${itemId}:`, {
         cursor: cursor || "(empty - first sync)",
         itemId,
         userId,
         isWebhook,
         timestamp: new Date().toISOString(),
      });

      // Call Plaid's transactionsSync endpoint
      const syncRequest = {
         access_token: plaidItem.accessToken,
         cursor: cursor,
         count: 500,
      };

      // Initialize collections to hold all transaction data across pagination
      let allAdded = [];
      let allModified = [];
      let allRemoved = [];
      let hasMore = true;
      let currentCursor = cursor;
      let finalCursor = null;

      // Process all available pages when this is triggered by a webhook
      while (hasMore) {
         // Log the API call to Plaid with the current cursor
         await logInfo(
            "plaid-sync",
            "plaid_api_call",
            `Calling Plaid transactionsSync API for item: ${itemId} (page)`,
            {
               itemId,
               userId,
               isWebhook,
               hasCursor: !!currentCursor,
               cursor: currentCursor || "(empty)",
               timestamp: new Date().toISOString(),
            },
            userId
         );

         try {
            // Set a timeout for the Plaid API call
            const timeoutPromise = new Promise((_, reject) => {
               setTimeout(() => {
                  reject(new Error("Plaid API timeout after 20 seconds"));
               }, 20000); // 20 second timeout
            });

            // Make the actual Plaid API call with the current cursor
            const apiPromise = plaidClient.transactionsSync({
               access_token: plaidItem.accessToken,
               cursor: currentCursor,
               count: 500,
            });

            // Race the API call against the timeout
            const pageResponse = await Promise.race([
               apiPromise,
               timeoutPromise,
            ]);

            // Extract data for this page
            const { added, modified, removed, next_cursor, has_more } =
               pageResponse.data;

            // Add the results to our collections
            allAdded = allAdded.concat(added);
            allModified = allModified.concat(modified);
            allRemoved = allRemoved.concat(removed);

            // Update pagination variables
            hasMore = has_more;
            currentCursor = next_cursor;
            finalCursor = next_cursor; // Save the final cursor for database update

            // Log successful page retrieval
            await logInfo(
               "plaid-sync",
               "plaid_api_success",
               `Received Plaid transactionsSync page for item: ${itemId}`,
               {
                  itemId,
                  userId,
                  isWebhook,
                  pageAddedCount: added.length,
                  pageModifiedCount: modified.length,
                  pageRemovedCount: removed.length,
                  totalAddedSoFar: allAdded.length,
                  totalModifiedSoFar: allModified.length,
                  totalRemovedSoFar: allRemoved.length,
                  hasMore: has_more,
                  timestamp: new Date().toISOString(),
               },
               userId
            );
         } catch (plaidError) {
            // Abort transaction and log detailed Plaid API error
            await mongoSession.abortTransaction();
            await logError(
               "plaid-sync",
               "plaid_api_error",
               `Plaid API error for item: ${itemId}`,
               {
                  itemId,
                  userId,
                  isWebhook,
                  cursor: currentCursor || "(empty)",
                  error: plaidError.message,
                  stack: plaidError.stack,
                  plaidErrorCode: plaidError.response?.data?.error_code,
                  plaidErrorType: plaidError.response?.data?.error_type,
                  plaidErrorMessage: plaidError.response?.data?.error_message,
                  timestamp: new Date().toISOString(),
               },
               userId
            );
            throw plaidError;
         }
      }

      // After pagination is complete, use the accumulated data
      let added = allAdded;
      let modified = allModified;
      let removed = allRemoved;
      const next_cursor = finalCursor;

      // Filter transactions by date if this is the first sync (no cursor) or if called from webhook
      // This ensures we only sync transactions from the plaidItem's startDate forward
      if (!cursor || isWebhook) {
         const cutoffDate = plaidItem.startDate || getCurrentMonthStart();
         const originalAddedCount = added.length;
         const originalModifiedCount = modified.length;

         console.log("Applying date filtering for sync:", {
            itemId,
            userId,
            isWebhook,
            hasCursor: !!cursor,
            cutoffDate: cutoffDate.toISOString(),
            plaidItemStartDate: plaidItem.startDate
               ? plaidItem.startDate.toISOString()
               : null,
            usingPlaidItemStartDate: !!plaidItem.startDate,
            fallbackToCurrentMonth: !plaidItem.startDate,
            originalAddedCount,
            originalModifiedCount,
         });

         added = filterTransactionsByDate(added, cutoffDate);
         modified = filterTransactionsByDate(modified, cutoffDate);

         // Log the filtering results
         await logInfo(
            "plaid-sync",
            "date_filtering_applied",
            `Applied date filtering for transactions from ${
               cutoffDate.toISOString().split("T")[0]
            } forward`,
            {
               itemId,
               userId,
               isWebhook,
               cutoffDate: cutoffDate.toISOString(),
               originalAddedCount,
               filteredAddedCount: added.length,
               originalModifiedCount,
               filteredModifiedCount: modified.length,
               usingPlaidItemStartDate: !!plaidItem.startDate,
               plaidItemStartDate: plaidItem.startDate
                  ? plaidItem.startDate.toISOString()
                  : null,
               transactionsFilteredOut:
                  originalAddedCount -
                  added.length +
                  (originalModifiedCount - modified.length),
               timestamp: new Date().toISOString(),
            },
            userId
         );
      }

      // Log the final transaction counts
      await logInfo(
         "plaid-sync",
         "sync_processing",
         `Processing Plaid transactions for item: ${itemId}`,
         {
            itemId,
            userId,
            isWebhook,
            addedCount: added.length,
            modifiedCount: modified.length,
            removedCount: removed.length,
            timestamp: new Date().toISOString(),
         },
         userId
      );

      // Handle pending-to-posted transitions BEFORE processing arrays separately
      let pendingToPostedCount = 0;

      // Find posted transactions in 'added' that have a pending_transaction_id
      const postedTransactionsWithPendingId = added.filter(
         (tx) => tx.pending_transaction_id
      );

      for (const postedTx of postedTransactionsWithPendingId) {
         // Check if the corresponding pending transaction is in the 'removed' array
         const pendingTxInRemoved = removed.find(
            (removedTx) =>
               removedTx.transaction_id === postedTx.pending_transaction_id
         );

         if (pendingTxInRemoved) {
            // Find the existing pending transaction in our database
            const existingPendingTx = await Transaction.findOne(
               { plaidTransactionId: postedTx.pending_transaction_id },
               null,
               { session: mongoSession }
            );

            if (existingPendingTx) {
               // Log this pending-to-posted transition
               await logInfo(
                  "plaid-sync",
                  "pending_to_posted_transition",
                  `Converting pending transaction ${postedTx.pending_transaction_id} to posted ${postedTx.transaction_id}`,
                  {
                     itemId,
                     userId,
                     pendingId: postedTx.pending_transaction_id,
                     postedId: postedTx.transaction_id,
                     preservedAssignedTo: existingPendingTx.assignedTo,
                     preservedAssignedToType: existingPendingTx.assignedToType,
                     oldAmount: existingPendingTx.amount,
                     newAmount:
                        postedTx.amount > 0
                           ? -Math.abs(postedTx.amount)
                           : Math.abs(postedTx.amount),
                     timestamp: new Date().toISOString(),
                  },
                  userId
               );

               // Calculate the new amount using the same logic as other parts of the code
               const newAmount =
                  postedTx.amount > 0
                     ? -Math.abs(postedTx.amount)
                     : Math.abs(postedTx.amount);

               const amountDifference = newAmount - existingPendingTx.amount;

               // Update associated income/expense records if amount changed and transaction is categorized
               if (
                  Math.abs(amountDifference) > 0.01 &&
                  existingPendingTx.assignedTo &&
                  existingPendingTx.assignedToType
               ) {
                  if (existingPendingTx.assignedToType === "Income") {
                     // Update income amounts with the difference
                     const income = await Income.findById(
                        existingPendingTx.assignedTo
                     ).session(mongoSession);
                     if (income) {
                        const newReceivedAmount = roundToTwo(
                           (income.receivedAmount || 0) + amountDifference
                        );

                        await Income.findByIdAndUpdate(
                           existingPendingTx.assignedTo,
                           { receivedAmount: newReceivedAmount },
                           { session: mongoSession }
                        );

                        // Update status if needed
                        const newStatus =
                           roundToTwo(newReceivedAmount) >=
                           roundToTwo(income.expectedAmount)
                              ? "received"
                              : "scheduled";

                        if (income.status !== newStatus) {
                           await Income.findByIdAndUpdate(
                              existingPendingTx.assignedTo,
                              { status: newStatus },
                              { session: mongoSession }
                           );
                        }
                     }
                  } else if (existingPendingTx.assignedToType === "Expense") {
                     // Update expense amounts with the difference
                     const expense = await Expense.findById(
                        existingPendingTx.assignedTo
                     ).session(mongoSession);
                     if (expense) {
                        const newAmountSpent = roundToTwo(
                           (expense.amountSpent || 0) + amountDifference
                        );

                        await Expense.findByIdAndUpdate(
                           existingPendingTx.assignedTo,
                           { amountSpent: newAmountSpent },
                           { session: mongoSession }
                        );

                        // Update status if needed
                        const newStatus =
                           newAmountSpent >= expense.amountDue
                              ? "paid"
                              : expense.amountAssigned >= expense.amountDue
                              ? "funded"
                              : "scheduled";

                        if (expense.status !== newStatus) {
                           await Expense.findByIdAndUpdate(
                              existingPendingTx.assignedTo,
                              { status: newStatus },
                              { session: mongoSession }
                           );
                        }

                        // Handle debt payment updates if needed
                        if (
                           expense.isDebtPayment &&
                           expense.debtId &&
                           Math.abs(amountDifference) > 0.01
                        ) {
                           const debt = await Debt.findById(
                              expense.debtId
                           ).session(mongoSession);
                           if (debt) {
                              const oldBalance = debt.balance;
                              const newBalance = roundToTwo(
                                 oldBalance - amountDifference
                              );

                              const historyEntry = {
                                 date: new Date(),
                                 oldBalance: oldBalance,
                                 newBalance: newBalance,
                                 oldMinimumPayment: debt.minimumPayment,
                                 newMinimumPayment: debt.minimumPayment,
                                 oldAPR: debt.apr,
                                 newAPR: debt.apr,
                                 oldDueDate: debt.dueDate,
                                 newDueDate: debt.dueDate,
                                 note: `Payment amount updated during pending-to-posted transition by $${Math.abs(
                                    amountDifference
                                 ).toFixed(2)}`,
                              };

                              await Debt.findByIdAndUpdate(
                                 expense.debtId,
                                 {
                                    $set: { balance: newBalance },
                                    $push: { history: historyEntry },
                                 },
                                 { session: mongoSession }
                              );
                           }
                        }
                     }
                  }
               }

               // Update the existing transaction with new posted data while preserving assignedTo info
               await Transaction.findByIdAndUpdate(
                  existingPendingTx._id,
                  {
                     $set: {
                        plaidTransactionId: postedTx.transaction_id, // Update to new posted transaction ID
                        amount: newAmount,
                        type: postedTx.amount > 0 ? "Expense" : "Income",
                        payee: postedTx.name || postedTx.merchant_name,
                        date: new Date(postedTx.date),
                        status: postedTx.pending ? "pending" : "cleared", // Should be "cleared" for posted
                        plaidData: postedTx,
                        pendingPlaidTransactionId:
                           postedTx.pending_transaction_id,
                        // PRESERVE existing assignedTo information - this is the key fix
                        assignedTo: existingPendingTx.assignedTo,
                        assignedToType: existingPendingTx.assignedToType,
                     },
                  },
                  { session: mongoSession }
               );

               // Update account balance if amount changed
               if (
                  Math.abs(amountDifference) > 0.01 &&
                  existingPendingTx.accountId
               ) {
                  await User.updateOne(
                     {
                        _id: userId,
                        "accounts._id": existingPendingTx.accountId,
                     },
                     {
                        $inc: { "accounts.$.balance": amountDifference },
                     },
                     { session: mongoSession }
                  );
               }

               // Remove this posted transaction from the 'added' array since we handled it
               added = added.filter(
                  (tx) => tx.transaction_id !== postedTx.transaction_id
               );

               // Remove the pending transaction from the 'removed' array since we updated it instead
               removed = removed.filter(
                  (tx) => tx.transaction_id !== postedTx.pending_transaction_id
               );

               pendingToPostedCount++;
            }
         }
      }

      // Log if we handled any pending-to-posted transitions
      if (pendingToPostedCount > 0) {
         await logInfo(
            "plaid-sync",
            "pending_to_posted_summary",
            `Handled ${pendingToPostedCount} pending-to-posted transitions while preserving categories`,
            {
               itemId,
               userId,
               count: pendingToPostedCount,
               remainingAdded: added.length,
               remainingRemoved: removed.length,
               timestamp: new Date().toISOString(),
            },
            userId
         );
      }

      // Process added transactions
      if (added.length > 0) {
         // Process added transactions logic
         const linkedAccountId = plaidItem.linkedAccountId;

         // Log debug info about linkedAccountId
         await logInfo(
            "plaid-sync",
            "linked_account_debug",
            `Processing transactions with linkedAccountId: ${linkedAccountId}`,
            {
               itemId,
               userId,
               linkedAccountId,
               plaidItemData: {
                  itemId: plaidItem.itemId,
                  linkedAccountId: plaidItem.linkedAccountId,
                  institutionName: plaidItem.institutionName,
               },
               timestamp: new Date().toISOString(),
            },
            userId
         );

         // If linkedAccountId is null, we can't create transactions
         if (!linkedAccountId) {
            await logError(
               "plaid-sync",
               "missing_linked_account",
               `Cannot create transactions: linkedAccountId is null for item ${itemId}`,
               {
                  itemId,
                  userId,
                  plaidItemData: {
                     itemId: plaidItem.itemId,
                     linkedAccountId: plaidItem.linkedAccountId,
                     institutionName: plaidItem.institutionName,
                  },
                  timestamp: new Date().toISOString(),
               },
               userId
            );
            throw new Error(
               "Account is not linked to a local account. Please complete account setup in settings."
            );
         }

         // Create normalized transactions
         const processedTransactions = added.map((plaidTx) => {
            return {
               userId,
               type: plaidTx.amount > 0 ? "Expense" : "Income",
               amount:
                  plaidTx.amount > 0
                     ? -plaidTx.amount
                     : Math.abs(plaidTx.amount),
               payee: plaidTx.name || plaidTx.merchant_name,
               date: new Date(plaidTx.date),
               accountId: linkedAccountId, // Remove the || null since we validated it above
               status: plaidTx.pending ? "pending" : "cleared",
               plaidData: plaidTx,
               plaidTransactionId: plaidTx.transaction_id,
               plaidAccountId: plaidTx.account_id,
               isPlaidTransaction: true,
               pendingPlaidTransactionId:
                  plaidTx.pending_transaction_id || null,
               assignedTo: null,
               assignedToType: null,
            };
         });

         // Insert all new transactions
         if (processedTransactions.length > 0) {
            await Transaction.insertMany(processedTransactions, {
               session: mongoSession,
            });

            // Update account balance
            if (linkedAccountId) {
               const totalAmount = processedTransactions.reduce(
                  (sum, tx) => sum + tx.amount,
                  0
               );
               await User.updateOne(
                  {
                     _id: userId,
                     "accounts._id": linkedAccountId,
                  },
                  {
                     $inc: { "accounts.$.balance": totalAmount },
                  },
                  { session: mongoSession }
               );
            }

            newTransactions = processedTransactions.length;
         }
      }

      // Process modified transactions
      if (modified.length > 0) {
         for (const modifiedTx of modified) {
            const existingTx = await Transaction.findOne(
               { plaidTransactionId: modifiedTx.transaction_id },
               null,
               { session: mongoSession }
            );

            if (existingTx) {
               const newAmount =
                  modifiedTx.amount > 0
                     ? -Math.abs(modifiedTx.amount)
                     : Math.abs(modifiedTx.amount);
               const amountDifference = newAmount - existingTx.amount;

               // Update associated income/expense records if the transaction is categorized
               if (existingTx.assignedTo && existingTx.assignedToType) {
                  if (existingTx.assignedToType === "Income") {
                     // Update income amounts
                     const income = await Income.findById(
                        existingTx.assignedTo
                     ).session(mongoSession);
                     if (income) {
                        // Remove old amount
                        const oldReceivedAmount = roundToTwo(
                           (income.receivedAmount || 0) - existingTx.amount
                        );
                        // Add new amount
                        const newReceivedAmount = roundToTwo(
                           oldReceivedAmount + newAmount
                        );

                        await Income.findByIdAndUpdate(
                           existingTx.assignedTo,
                           { receivedAmount: newReceivedAmount },
                           { session: mongoSession }
                        );

                        // Update status if needed
                        const newStatus =
                           roundToTwo(newReceivedAmount) >=
                           roundToTwo(income.expectedAmount)
                              ? "received"
                              : "scheduled";

                        if (income.status !== newStatus) {
                           await Income.findByIdAndUpdate(
                              existingTx.assignedTo,
                              { status: newStatus },
                              { session: mongoSession }
                           );
                        }
                     }
                  } else if (existingTx.assignedToType === "Expense") {
                     // Update expense amounts
                     const expense = await Expense.findById(
                        existingTx.assignedTo
                     ).session(mongoSession);
                     if (expense) {
                        // Remove old amount
                        const oldAmountSpent = roundToTwo(
                           (expense.amountSpent || 0) - existingTx.amount
                        );
                        // Add new amount
                        const newAmountSpent = roundToTwo(
                           oldAmountSpent + newAmount
                        );

                        await Expense.findByIdAndUpdate(
                           existingTx.assignedTo,
                           { amountSpent: newAmountSpent },
                           { session: mongoSession }
                        );

                        // Update status if needed
                        const newStatus =
                           newAmountSpent >= expense.amountDue
                              ? "paid"
                              : expense.amountAssigned >= expense.amountDue
                              ? "funded"
                              : "scheduled";

                        if (expense.status !== newStatus) {
                           await Expense.findByIdAndUpdate(
                              existingTx.assignedTo,
                              { status: newStatus },
                              { session: mongoSession }
                           );
                        }

                        // Handle debt payment updates if needed
                        if (expense.isDebtPayment && expense.debtId) {
                           // Calculate payment amount difference
                           const oldPaymentAmount = Math.abs(existingTx.amount);
                           const newPaymentAmount = Math.abs(newAmount);
                           const paymentDifference =
                              newPaymentAmount - oldPaymentAmount;

                           if (Math.abs(paymentDifference) > 0.01) {
                              const debt = await Debt.findById(
                                 expense.debtId
                              ).session(mongoSession);
                              if (debt) {
                                 const oldBalance = debt.balance;
                                 // Reduce balance by the payment difference
                                 const newBalance = roundToTwo(
                                    oldBalance - paymentDifference
                                 );

                                 // Create history entry
                                 const historyEntry = {
                                    date: new Date(),
                                    oldBalance: oldBalance,
                                    newBalance: newBalance,
                                    oldMinimumPayment: debt.minimumPayment,
                                    newMinimumPayment: debt.minimumPayment,
                                    oldAPR: debt.apr,
                                    newAPR: debt.apr,
                                    oldDueDate: debt.dueDate,
                                    newDueDate: debt.dueDate,
                                    note: `Payment amount updated from $${oldPaymentAmount.toFixed(
                                       2
                                    )} to $${newPaymentAmount.toFixed(2)}`,
                                 };

                                 await Debt.findByIdAndUpdate(
                                    expense.debtId,
                                    {
                                       $set: { balance: newBalance },
                                       $push: { history: historyEntry },
                                    },
                                    { session: mongoSession }
                                 );
                              }
                           }
                        }
                     }
                  }
               }

               // Update transaction fields while preserving assignedTo information
               await Transaction.updateOne(
                  { plaidTransactionId: modifiedTx.transaction_id },
                  {
                     $set: {
                        amount: newAmount,
                        type: modifiedTx.amount > 0 ? "Expense" : "Income",
                        payee: modifiedTx.name || modifiedTx.merchant_name,
                        date: new Date(modifiedTx.date),
                        status: modifiedTx.pending ? "pending" : "cleared",
                        plaidData: modifiedTx,
                        // Preserve existing assignedTo information
                        assignedTo: existingTx.assignedTo,
                        assignedToType: existingTx.assignedToType,
                     },
                  },
                  { session: mongoSession }
               );

               // Update account balance if amount changed
               if (Math.abs(amountDifference) > 0.01 && existingTx.accountId) {
                  await User.updateOne(
                     {
                        _id: userId,
                        "accounts._id": existingTx.accountId,
                     },
                     {
                        $inc: { "accounts.$.balance": amountDifference },
                     },
                     { session: mongoSession }
                  );
               }

               updatedTransactions++;
            }
         }
      }

      // Process removed transactions
      if (removed.length > 0) {
         const removedTransactionIds = removed.map((tx) => tx.transaction_id);
         const txToRemove = await Transaction.find(
            { plaidTransactionId: { $in: removedTransactionIds } },
            null,
            { session: mongoSession }
         );

         if (txToRemove.length > 0) {
            // Calculate balance adjustments
            const accountAdjustments = txToRemove.reduce((acc, tx) => {
               if (tx.accountId) {
                  acc[tx.accountId] = (acc[tx.accountId] || 0) - tx.amount;
               }
               return acc;
            }, {});

            // Update account balances
            for (const [accountId, adjustment] of Object.entries(
               accountAdjustments
            )) {
               await User.updateOne(
                  {
                     _id: userId,
                     "accounts._id": accountId,
                  },
                  {
                     $inc: { "accounts.$.balance": adjustment },
                  },
                  { session: mongoSession }
               );
            }

            // Handle assignedTo adjustments for removed transactions
            for (const tx of txToRemove) {
               if (tx.assignedTo && tx.assignedToType) {
                  if (tx.assignedToType === "Income") {
                     // For income, decrease the receivedAmount
                     const income = await Income.findById(
                        tx.assignedTo
                     ).session(mongoSession);
                     if (income) {
                        const newReceivedAmount = roundToTwo(
                           (income.receivedAmount || 0) - Math.abs(tx.amount)
                        );

                        await Income.findByIdAndUpdate(
                           tx.assignedTo,
                           { receivedAmount: newReceivedAmount },
                           { session: mongoSession }
                        );

                        // Update status if needed
                        const newStatus =
                           roundToTwo(newReceivedAmount) >=
                           roundToTwo(income.expectedAmount)
                              ? "received"
                              : "scheduled";

                        if (income.status !== newStatus) {
                           await Income.findByIdAndUpdate(
                              tx.assignedTo,
                              { status: newStatus },
                              { session: mongoSession }
                           );
                        }
                     }
                  } else if (tx.assignedToType === "Expense") {
                     // For expense, decrease the amountSpent
                     const expense = await Expense.findById(
                        tx.assignedTo
                     ).session(mongoSession);
                     if (expense) {
                        const newAmountSpent = roundToTwo(
                           (expense.amountSpent || 0) - tx.amount
                        );

                        await Expense.findByIdAndUpdate(
                           tx.assignedTo,
                           { amountSpent: newAmountSpent },
                           { session: mongoSession }
                        );

                        // Update status if needed
                        const newStatus =
                           newAmountSpent >= expense.amountDue
                              ? "paid"
                              : expense.amountAssigned >= expense.amountDue
                              ? "funded"
                              : "scheduled";

                        if (expense.status !== newStatus) {
                           await Expense.findByIdAndUpdate(
                              tx.assignedTo,
                              { status: newStatus },
                              { session: mongoSession }
                           );
                        }

                        // Handle debt payment adjustments if needed
                        if (expense.isDebtPayment && expense.debtId) {
                           const debt = await Debt.findById(
                              expense.debtId
                           ).session(mongoSession);
                           if (debt) {
                              const paymentAmount = Math.abs(tx.amount);
                              const oldBalance = debt.balance;
                              const newBalance = roundToTwo(
                                 oldBalance + paymentAmount
                              );

                              // Create history entry
                              const historyEntry = {
                                 date: new Date(),
                                 oldBalance: oldBalance,
                                 newBalance: newBalance,
                                 oldMinimumPayment: debt.minimumPayment,
                                 newMinimumPayment: debt.minimumPayment,
                                 oldAPR: debt.apr,
                                 newAPR: debt.apr,
                                 oldDueDate: debt.dueDate,
                                 newDueDate: debt.dueDate,
                                 note: `Payment of $${paymentAmount.toFixed(
                                    2
                                 )} removed`,
                              };

                              await Debt.findByIdAndUpdate(
                                 expense.debtId,
                                 {
                                    $set: { balance: newBalance },
                                    $push: { history: historyEntry },
                                 },
                                 { session: mongoSession }
                              );
                           }
                        }
                     }
                  }
               }
            }

            // Delete removed transactions
            await Transaction.deleteMany(
               { plaidTransactionId: { $in: removedTransactionIds } },
               { session: mongoSession }
            );

            removedTransactions = txToRemove.length;
         }
      }

      // Update the cursor and lastSync
      const updateFields = {
         "plaidItems.$.lastSync": new Date(),
      };

      if (next_cursor) {
         updateFields["plaidItems.$.cursor"] = next_cursor;
      }

      await User.updateOne(
         {
            _id: userId,
            "plaidItems.itemId": itemId,
         },
         {
            $set: updateFields,
         },
         { session: mongoSession }
      );

      if (next_cursor) {
         // Log the cursor update
         console.log(`Updated cursor for item ${itemId}:`, {
            oldCursor: cursor || "(empty)",
            newCursor: next_cursor,
            itemId,
            userId,
            isWebhook,
            timestamp: new Date().toISOString(),
         });

         await logInfo(
            "plaid-sync",
            "cursor_updated",
            `Updated Plaid cursor for item: ${itemId}`,
            {
               itemId,
               userId,
               oldCursor: cursor || "(empty)",
               newCursor: next_cursor,
               isWebhook,
               timestamp: new Date().toISOString(),
            },
            userId
         );
      } else {
         // Log that lastSync was updated even without cursor
         await logInfo(
            "plaid-sync",
            "sync_timestamp_updated",
            `Updated lastSync timestamp for item: ${itemId}`,
            {
               itemId,
               userId,
               isWebhook,
               timestamp: new Date().toISOString(),
            },
            userId
         );
      }

      // Before committing, log completion
      await logInfo(
         "plaid-sync",
         "sync_complete",
         `Completed Plaid transaction sync for item: ${itemId}`,
         {
            itemId,
            userId,
            isWebhook,
            newTransactions,
            updatedTransactions: updatedTransactions + pendingToPostedCount,
            removedTransactions,
            pendingToPostedCount,
            totalUpdated: updatedTransactions + pendingToPostedCount,
            timestamp: new Date().toISOString(),
         },
         userId
      );

      // Commit the transaction
      await mongoSession.commitTransaction();

      // Return the result
      return {
         success: true,
         newTransactions,
         updatedTransactions: updatedTransactions + pendingToPostedCount,
         removedTransactions,
         pendingToPostedCount,
         message: `Successfully synced ${newTransactions} new, updated ${
            updatedTransactions + pendingToPostedCount
         } existing (including ${pendingToPostedCount} pending-to-posted transitions), and removed ${removedTransactions} transactions`,
      };
   } catch (error) {
      // If any error occurs during synchronization, abort the transaction
      try {
         await mongoSession.abortTransaction();
      } catch (abortError) {
         console.error("Error aborting transaction:", abortError);
      }

      // Log the error
      await logError(
         "plaid-sync",
         "sync_error",
         `Error syncing transactions for item: ${itemId}`,
         {
            itemId,
            userId,
            isWebhook,
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
         },
         userId
      );

      throw error;
   } finally {
      // End the session
      try {
         await mongoSession.endSession();
      } catch (sessionError) {
         console.error("Error ending session:", sessionError);
      }
   }
}

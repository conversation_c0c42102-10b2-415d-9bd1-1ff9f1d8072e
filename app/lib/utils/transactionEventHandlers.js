"use client";

import { TransactionsUtils } from "./transactionsUtils";

export const TransactionEventHandlers = {
   handleAssignedToClick: (
      transaction,
      event,
      currentCategoryCellRef,
      onAssignedToClick,
      setShowCategoryModal,
      setDropdownPosition,
      editingTransaction,
      TransactionHelpers
   ) => {
      event.preventDefault();
      event.stopPropagation();

      // Skip for adjustment transactions
      if (transaction.isAdjustment) {
         return;
      }

      // Close if clicking the same cell
      if (editingTransaction?._id === transaction._id) {
         onAssignedToClick(null, event);
         setShowCategoryModal(false);
         return;
      }

      const rect = event.currentTarget.getBoundingClientRect();
      const dropdownPosition = TransactionHelpers.calculateDropdownPosition(
         rect,
         320,
         200 // Use a realistic height for checking available space
      );
      setDropdownPosition(dropdownPosition);

      currentCategoryCellRef.current = event.currentTarget;
      onCategoryClick(transaction, event);
      setShowCategoryModal(true);
   },

   handleStatusClick: (
      transactionId,
      event,
      showStatusDropdown,
      setShowStatusDropdown,
      setDropdownPosition,
      TransactionHelpers
   ) => {
      event.preventDefault();
      event.stopPropagation();

      if (showStatusDropdown === transactionId) {
         setShowStatusDropdown(null);
      } else {
         const rect = event.currentTarget.getBoundingClientRect();
         const dropdownPosition = TransactionHelpers.calculateDropdownPosition(
            rect,
            150, // Width of status dropdown
            100 // Height of status dropdown
         );
         setDropdownPosition(dropdownPosition);
         setShowStatusDropdown(transactionId);
      }
   },

   handleAccountClick: (
      transactionId,
      event,
      showAccountDropdown,
      setShowAccountDropdown,
      setDropdownPosition,
      TransactionHelpers
   ) => {
      event.preventDefault();
      event.stopPropagation();

      if (showAccountDropdown === transactionId) {
         setShowAccountDropdown(null);
      } else {
         const rect = event.currentTarget.getBoundingClientRect();
         const dropdownPosition = TransactionHelpers.calculateDropdownPosition(
            rect,
            200, // Width of account dropdown
            120 // Height of account dropdown
         );
         setDropdownPosition(dropdownPosition);
         setShowAccountDropdown(transactionId);
      }
   },

   handleDateClick: (
      transactionId,
      event,
      showDatePicker,
      setShowDatePicker,
      setDropdownPosition,
      TransactionHelpers
   ) => {
      // If no event is provided, just close the date picker
      if (!event) {
         setShowDatePicker(null);
         return;
      }

      if (showDatePicker === transactionId) {
         setShowDatePicker(null);
      } else {
         const rect = event.currentTarget.getBoundingClientRect();
         const dropdownPosition = TransactionHelpers.calculateDropdownPosition(
            rect,
            200, // Approximate width of date picker
            50 // Approximate height of date picker
         );
         setDropdownPosition(dropdownPosition);
         setShowDatePicker(transactionId);
      }
   },

   handlePayeeClick: (transactionId, setEditingPayee) => {
      setEditingPayee(transactionId);
   },

   handlePayeeSubmit: (
      transactionId,
      value,
      onPayeeChange,
      setEditingPayee
   ) => {
      if (value.trim()) {
         onPayeeChange(transactionId, value.trim());
      }
      setEditingPayee(null);
   },

   handleAmountClick: (transactionId, setEditingAmount) => {
      setEditingAmount(transactionId);
   },

   handleAmountSubmit: (
      transactionId,
      value,
      transaction,
      onAmountChange,
      setEditingAmount
   ) => {
      const numericValue = parseFloat(value.replace(/[^0-9.-]/g, ""));
      if (!isNaN(numericValue)) {
         const finalAmount =
            transaction.type === "Expense"
               ? -Math.abs(numericValue)
               : Math.abs(numericValue);
         onAmountChange(transactionId, finalAmount);
      }
      setEditingAmount(null);
   },

   handleDelete: async (transactionId, transactions, accounts, onDelete) => {
      try {
         // Make the API call first
         const response = await fetch(`/api/transactions/${transactionId}`, {
            method: "DELETE",
         });
         if (!response.ok) throw new Error("Failed to delete transaction");

         // Then get the optimistic updates
         const {
            transactions: updatedTransactions,
            accounts: updatedAccounts,
         } = await TransactionsUtils.handleDeleteTransaction(
            transactionId,
            transactions,
            accounts
         );
         onDelete(transactionId, updatedTransactions, updatedAccounts);
         return { success: true }; // Return a resolved promise
      } catch (error) {
         console.error("Error deleting transaction:", error);
         throw error; // This will be caught by the caller
      }
   },

   handleCategoryChange: (
      editingTransaction,
      option,
      availableAssignedTo,
      onAssignedToChange,
      setShowCategoryModal,
      onCategoryClick,
      currentCategoryCellRef
   ) => {
      if (!editingTransaction) return;

      // Skip for adjustment transactions
      if (editingTransaction.isAdjustment) {
         setShowCategoryModal(false);
         onCategoryClick(null);
         return;
      }

      // If option.value is empty string, set assignedTo and assignedToType to null
      if (!option.value) {
         onAssignedToChange(editingTransaction._id, null, null);
         setShowCategoryModal(false);
         onCategoryClick(null);
         return;
      }

      // Find the selected category by ID (should be a string comparison for reliability)
      const selectedCategory = availableAssignedTo.find(
         (c) => c._id?.toString() === option.value?.toString()
      );

      if (!selectedCategory) {
         console.error("Selected category not found:", option.value);
         return;
      }

      const assignedToType = selectedCategory.expectedAmount
         ? "Income"
         : "Expense";

      console.log("Assigning assignedTo:", {
         transactionId: editingTransaction._id,
         assignedToId: selectedCategory._id,
         assignedToDescription: selectedCategory.description,
         assignedToType,
      });

      // Pass the assignedTo ObjectId, ensure it's consistent
      onAssignedToChange(
         editingTransaction._id,
         selectedCategory._id,
         assignedToType
      );
      setShowCategoryModal(false);
      onCategoryClick(null);

      requestAnimationFrame(() => {
         currentCategoryCellRef.current?.focus();
      });
   },

   getCategoryDescription: (category) => {
      if (category.expectedAmount) {
         return `${new Date(category.date).toLocaleDateString(undefined, {
            month: "numeric",
            day: "numeric",
         })} • Received: $${(category.receivedAmount || 0).toFixed(2)}`;
      }

      return `${
         category.weeklyChargeType === "spread"
            ? `${new Date(category.startDate).toLocaleDateString(undefined, {
                 month: "numeric",
                 day: "numeric",
              })} - ${new Date(category.endDate).toLocaleDateString(undefined, {
                 month: "numeric",
                 day: "numeric",
              })}`
            : new Date(category.date).toLocaleDateString(undefined, {
                 month: "numeric",
                 day: "numeric",
              })
      } • Available: $${(category.amountAvailable || 0).toFixed(2)}`;
   },
};

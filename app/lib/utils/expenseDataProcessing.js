// Simplified expense data processing - removed complex filtering and aggregation

export function processExpenseData({
   expenses,
   expectedExpenses,
   futureExpenses,
   showFutureExpenses,
   dateRange,
   filterExpensesByDate,
   filterExpensesByStatus,
   filterProjectedExpensesByDate,
   isProjectionHidden,
}) {
   // Simplified: Just combine all expenses without complex filtering

   // Start with regular expenses
   let allExpenses = [...expenses];

   // Add future expenses if toggle is on
   if (showFutureExpenses) {
      allExpenses = [...allExpenses, ...futureExpenses];
   }

   // Simple filter: Remove projected expenses that have matching actual expenses
   // BUT keep hidden projections so they can be shown in the skipped section
   const filteredProjectedExpenses = expectedExpenses.filter((projectedExp) => {
      // Check if there's already an actual expense with the same description and date
      const hasMatchingActual = allExpenses.some((actualExp) => {
         // Match by description (case insensitive)
         const descriptionMatch =
            actualExp.description?.toLowerCase().trim() ===
            projectedExp.description?.toLowerCase().trim();

         // Match by date (handle both single date and date ranges)
         let dateMatch = false;

         if (projectedExp.date && actualExp.date) {
            // Simple date match
            dateMatch = projectedExp.date === actualExp.date;
         } else if (
            projectedExp.startDate &&
            projectedExp.endDate &&
            actualExp.startDate &&
            actualExp.endDate
         ) {
            // Date range match
            dateMatch =
               projectedExp.startDate === actualExp.startDate &&
               projectedExp.endDate === actualExp.endDate;
         } else if (
            projectedExp.date &&
            actualExp.startDate &&
            actualExp.endDate
         ) {
            // Check if projected date falls within actual expense range
            dateMatch =
               projectedExp.date >= actualExp.startDate &&
               projectedExp.date <= actualExp.endDate;
         } else if (
            actualExp.date &&
            projectedExp.startDate &&
            projectedExp.endDate
         ) {
            // Check if actual date falls within projected range
            dateMatch =
               actualExp.date >= projectedExp.startDate &&
               actualExp.date <= projectedExp.endDate;
         }

         return descriptionMatch && dateMatch;
      });

      // Include projected expense if:
      // 1. No matching actual expense exists, OR
      // 2. It's hidden (so it can be shown in skipped section)
      return (
         !hasMatchingActual ||
         (isProjectionHidden && isProjectionHidden(projectedExp))
      );
   });

   // Add the filtered projected expenses (includes hidden ones)
   allExpenses = [...allExpenses, ...filteredProjectedExpenses];

   // Simple sort by date
   allExpenses.sort((a, b) => {
      const dateA = new Date(a.date || a.startDate);
      const dateB = new Date(b.date || b.startDate);
      return dateA - dateB;
   });

   // No more weekly aggregation - treat all expenses individually
   const finalExpenses = allExpenses;

   // Separate visible and hidden expenses for UI display
   const visibleExpenses = [];
   const hiddenExpenses = [];

   finalExpenses.forEach((expense) => {
      // For all expenses, check if hidden
      if (isProjectionHidden && isProjectionHidden(expense)) {
         hiddenExpenses.push(expense);
      } else {
         visibleExpenses.push(expense);
      }
   });

   // Simple organization by frequency (using visible expenses)
   const organizedExpenses = {
      oneoff: visibleExpenses.filter(
         (exp) => !exp.frequency || exp.frequency === "oneoff"
      ),
      weekly: visibleExpenses.filter((exp) => exp.frequency === "weekly"),
      monthly: visibleExpenses.filter(
         (exp) => exp.frequency === "monthly" || exp.frequency === "biweekly"
      ),
      annual: visibleExpenses.filter((exp) => exp.frequency === "annually"),
   };

   // Calculate simple totals from visible expenses only
   const totals = visibleExpenses.reduce(
      (acc, expense) => ({
         due: acc.due + (expense.amountDue || 0),
         assigned: acc.assigned + (expense.amountAssigned || 0),
         spent: acc.spent + (expense.amountSpent || 0),
         available: acc.available + (expense.amountAvailable || 0),
      }),
      { due: 0, assigned: 0, spent: 0, available: 0 }
   );

   return {
      organizedExpenses,
      allExpenses: finalExpenses,
      visibleExpenses,
      hiddenExpenses,
      totals,
   };
}

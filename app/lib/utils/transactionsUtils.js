import mongoose from "mongoose";
import { format, parseISO, startOfDay, addDays, subMonths } from "date-fns";

// Helper function to ensure all monetary values are rounded to 2 decimal places
const roundToTwo = (num) => Number(Number(num).toFixed(2));

/**
 * Safely parse a date string to avoid timezone issues
 * Creates a local date at noon to prevent timezone conversion problems
 * @param {string} dateStr - Date string (ISO format or YYYY-MM-DD)
 * @returns {Date} - Local date object at noon
 */
export const parseTimezoneDate = (dateStr) => {
   if (!dateStr) return new Date();

   if (dateStr.includes("T")) {
      // ISO string - extract date components
      const [year, month, day] = dateStr.split("T")[0].split("-").map(Number);
      return new Date(year, month - 1, day, 12, 0, 0);
   } else {
      // Regular date string format (YYYY-MM-DD)
      const [year, month, day] = dateStr.split("-").map(Number);
      return new Date(year, month - 1, day, 12, 0, 0);
   }
};

// Helper function to calculate amount difference for transactions
export const calculateAmountDiff = (newAmount, oldAmount) => {
   return parseFloat(newAmount) - parseFloat(oldAmount);
};

// Request deduplication cache for API calls
const requestCache = new Map();

/**
 * Centralized utilities for transaction-related operations
 *
 * Contains:
 * - API request handlers
 * - State management helpers
 * - Data processing utilities
 * - Validation functions
 */
export const TransactionsUtils = {
   /**
    * Initializes the transactions page by fetching required data
    * @param {string} selectedAccountFilter - The currently selected account filter
    * @param {boolean} [unassigned] - Whether to fetch only unassigned transactions
    * @returns {Promise<Object>} Object containing:
    *   - accounts: Array of account objects
    *   - transactions: Array of transaction objects
    *   - pagination: Pagination metadata
    *   - balances: Current balance information
    *   - selectedAccountFilter: The validated account filter
    * @throws {Error} If initialization fails
    */
   initializeTransactionsPage: async (
      selectedAccountFilter,
      unassigned = false
   ) => {
      try {
         const { accounts } = await TransactionsUtils.fetchAccounts();

         // Handle single account case
         if (accounts?.length === 1) {
            selectedAccountFilter = accounts[0]._id.toString();
            localStorage.setItem(
               "selectedAccountFilter",
               selectedAccountFilter
            );
         } else {
            // Existing validation logic for multiple accounts
            if (selectedAccountFilter !== "all") {
               const accountExists = accounts?.some(
                  (account) => account._id === Number(selectedAccountFilter)
               );
               if (!accountExists) {
                  selectedAccountFilter = "all";
                  localStorage.setItem("selectedAccountFilter", "all");
               }
            }
         }

         // Fetch transactions and balances
         const [transactionsData, balances] = await Promise.all([
            TransactionsUtils.fetchTransactions({
               selectedAccountFilter,
               page: 1,
               limit: 50,
               unassigned,
            }),
            TransactionsUtils.fetchBalances(),
         ]);

         return {
            accounts,
            transactions: transactionsData.transactions || [],
            pagination: transactionsData.pagination || {
               total: 0,
               totalPages: 0,
            },
            balances,
            selectedAccountFilter,
         };
      } catch (error) {
         throw new Error("Failed to initialize page: " + error.message);
      }
   },

   /**
    * Handles adding a new transaction and updating the UI state
    * @param {Object} transaction - The new transaction to add
    * @param {Object} currentPageState - Current page state containing:
    *   - accounts: Array of account objects
    *   - transactions: Array of transaction objects
    *   - pagination: Pagination metadata
    *   - selectedAccountFilter: Currently selected account filter
    * @returns {Promise<Object>} Updated state containing:
    *   - accounts: Updated accounts array
    *   - transactions: Updated transactions array
    *   - pagination: Updated pagination metadata
    * @throws {Error} If the transaction addition fails
    */
   handleTransactionAdded: async (transaction, currentPageState) => {
      try {
         const { accounts, transactions, pagination, selectedAccountFilter } =
            currentPageState;

         // Update transactions array with the new transaction
         const updatedTransactions = [transaction, ...transactions];
         updatedTransactions.sort((a, b) => {
            // Sort by status first (pending before cleared)
            if (a.status === "pending" && b.status === "cleared") return -1;
            if (a.status === "cleared" && b.status === "pending") return 1;
            // Then sort by date within each status group
            return new Date(b.date) - new Date(a.date);
         });

         // Update account balances for the transaction
         let updatedAccounts = [...accounts];
         if (transaction.accountId) {
            updatedAccounts = accounts.map((account) => {
               if (account._id === transaction.accountId) {
                  return {
                     ...account,
                     balance: roundToTwo(account.balance + transaction.amount),
                  };
               }
               return account;
            });
         }

         // Handle transfers specially
         if (transaction.type === "Transfer" && transaction.transferDetails) {
            updatedAccounts = accounts.map((account) => {
               if (account._id === transaction.transferDetails.fromAccountId) {
                  return {
                     ...account,
                     balance: roundToTwo(
                        account.balance - Math.abs(transaction.amount)
                     ),
                  };
               }
               if (account._id === transaction.transferDetails.toAccountId) {
                  return {
                     ...account,
                     balance: roundToTwo(
                        account.balance + Math.abs(transaction.amount)
                     ),
                  };
               }
               return account;
            });
         }

         const updatedPagination = {
            ...pagination,
            total: pagination.total + 1,
            totalPages: Math.ceil((pagination.total + 1) / pagination.limit),
         };

         return {
            transactions: updatedTransactions,
            accounts: updatedAccounts,
            pagination: updatedPagination,
            success: true,
         };
      } catch (error) {
         console.error("Error in handleTransactionAdded:", error);
         throw new Error("Failed to process transaction addition");
      }
   },

   /**
    * Handles deleting a transaction and updating the UI state
    * @param {string} transactionId - ID of the transaction to delete
    * @param {Array} transactions - Current array of transactions
    * @param {Array} accounts - Current array of accounts
    * @returns {Promise<Object>} Updated state containing:
    *   - transactions: Updated transactions array
    *   - accounts: Updated accounts array
    * @throws {Error} If the transaction is not found or deletion fails
    */
   handleDeleteTransaction: async (transactionId, transactions, accounts) => {
      const transactionToDelete = transactions.find(
         (t) => t._id === transactionId
      );
      if (!transactionToDelete) throw new Error("Transaction not found");

      // Optimistically update state
      const updatedTransactions = transactions.filter(
         (t) => t._id !== transactionId
      );

      // Only update accounts if accounts array exists and transaction has an accountId
      let updatedAccounts = accounts;
      if (accounts && transactionToDelete.accountId) {
         updatedAccounts = accounts.map((account) =>
            account._id === transactionToDelete.accountId
               ? {
                    ...account,
                    balance: roundToTwo(
                       account.balance - transactionToDelete.amount
                    ),
                 }
               : account
         );
      }

      return {
         transactions: updatedTransactions,
         accounts: updatedAccounts,
      };
   },

   /**
    * Fetches user accounts from the API
    * @returns {Promise<Object>} Object containing array of account objects
    * @throws {Error} If the API request fails
    */
   fetchAccounts: async () => {
      try {
         const response = await fetch("/api/user/accounts");
         if (!response.ok) throw new Error("Failed to fetch accounts");
         return await response.json();
      } catch (error) {
         console.error("Error fetching accounts:", error);
         throw error;
      }
   },

   /**
    * Fetches balance information from the API
    * @returns {Promise<Object>} Object containing:
    *   - currentBalance: Current account balance
    *   - totalIncome: Total income amount
    *   - totalAssigned: Total assigned amount
    * @throws {Error} If the API request fails
    */
   fetchBalances: async () => {
      try {
         const response = await fetch("/api/user/balance", { method: "POST" });
         if (!response.ok) throw new Error("Failed to fetch balances");
         const data = await response.json();
         return {
            currentBalance: Number(data.currentBalance || 0),
            totalIncome: Number(data.totalIncome || 0),
            totalAssigned: Number(data.totalAssigned || 0),
            readyToAssign: Number(data.readyToAssign || 0),
         };
      } catch (error) {
         console.error("Error fetching balances:", error);
         throw error;
      }
   },

   /**
    * Fetches transactions from the API with optional filtering and pagination
    * @param {Object} params - Query parameters
    * @param {number} [params.page=1] - Page number for pagination
    * @param {number} [params.limit=50] - Number of transactions per page
    * @param {string} [params.searchQuery] - Search query string
    * @param {string} [params.selectedAccountFilter] - Selected account filter
    * @param {boolean} [params.unassigned] - Whether to fetch only unassigned transactions
    * @returns {Promise<Object>} Object containing:
    *   - transactions: Array of transaction objects
    *   - pagination: Pagination metadata
    * @throws {Error} If the API request fails
    */
   fetchTransactions: async (params = {}) => {
      try {
         const { page, limit, searchQuery, selectedAccountFilter, unassigned } =
            params;

         // Use unassigned endpoint if unassigned filter is active
         let url = unassigned
            ? "/api/transactions/unassigned"
            : "/api/transactions";
         const searchParams = new URLSearchParams();

         searchParams.append("page", page?.toString() || "1");
         searchParams.append("limit", limit?.toString() || "50");

         if (unassigned) {
            // Unassigned endpoint supports account filtering but not search yet
            if (selectedAccountFilter !== "all") {
               searchParams.append("accountId", selectedAccountFilter);
            }
         } else {
            // Regular endpoint supports both search and account filtering
            if (searchQuery) searchParams.append("search", searchQuery);
            if (selectedAccountFilter !== "all") {
               searchParams.append("accountId", selectedAccountFilter);
            }
         }

         const response = await fetch(`${url}?${searchParams.toString()}`);
         if (!response.ok) {
            const errorText = await response.text();
            console.error("Server error:", {
               status: response.status,
               statusText: response.statusText,
               body: errorText,
            });
            throw new Error(
               `Failed to fetch transactions: ${response.status} ${response.statusText}`
            );
         }
         const data = await response.json();

         // Format transactions to ensure assignedTo handling is correct
         if (data.transactions) {
            data.transactions = data.transactions.map((transaction) => ({
               ...transaction,
               // If assignedTo is null or undefined, use "Unassigned"
               // If assignedTo is an object, use its description
               // If assignedTo is a string ID, keep it for reference
               assignedTo: !transaction.assignedTo
                  ? "Unassigned"
                  : transaction.assignedTo?.description ||
                    transaction.assignedTo,
               // Keep track of the original assignedTo ID if it exists
               assignedToId: transaction.assignedTo?._id || null,
               // Ensure assignedToType is null when assignedTo is unassigned
               assignedToType: !transaction.assignedTo
                  ? null
                  : transaction.assignedToType,
            }));
         }

         return data;
      } catch (error) {
         console.error("Error fetching transactions:", error);
         throw error;
      }
   },

   /**
    * Updates a transaction via API
    * @param {string} transactionId - ID of the transaction to update
    * @param {Object} updateData - Data to update
    * @returns {Promise<Object>} Updated transaction object
    * @throws {Error} If the API request fails
    */
   updateTransaction: async (transactionId, updateData) => {
      try {
         const response = await fetch(`/api/transactions/${transactionId}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(updateData),
         });
         if (!response.ok) throw new Error("Failed to update transaction");
         return await response.json();
      } catch (error) {
         console.error("Error updating transaction:", error);
         throw error;
      }
   },

   /**
    * Deletes a transaction via API
    * @param {string} transactionId - ID of the transaction to delete
    * @returns {Promise<boolean>} True if deletion was successful
    * @throws {Error} If the API request fails
    */
   deleteTransaction: async (transactionId) => {
      // This function is now deprecated as the API call is handled in TransactionEventHandlers.handleDelete
      return true;
   },

   /**
    * Creates multiple transactions in bulk via API
    * @param {Array} transactions - Array of transaction objects to create
    * @returns {Promise<Object>} Response object from the API
    * @throws {Error} If the API request fails
    */
   bulkCreateTransactions: async (transactions) => {
      try {
         const response = await fetch("/api/transactions/bulk", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ transactions }),
         });
         if (!response.ok) throw new Error("Failed to import transactions");
         return await response.json();
      } catch (error) {
         console.error("Error bulk creating transactions:", error);
         throw error;
      }
   },

   /**
    * Fetches both expenses and income categories from the API with request deduplication
    * @returns {Promise<Object>} Object containing:
    *   - expenses: Array of expense categories
    *   - incomes: Array of income categories
    * @throws {Error} If either API request fails
    */
   fetchCategories: async () => {
      const cacheKey = "fetchCategories";

      // Check if there's already a request in flight
      if (requestCache.has(cacheKey)) {
         return requestCache.get(cacheKey);
      }

      // Create the request promise
      const requestPromise = (async () => {
         try {
            const [expensesResponse, incomesResponse] = await Promise.all([
               fetch("/api/expenses"),
               fetch("/api/incomes?status=scheduled"),
            ]);

            if (!expensesResponse.ok)
               throw new Error("Failed to fetch expenses");
            if (!incomesResponse.ok) throw new Error("Failed to fetch incomes");

            const expenses = await expensesResponse.json();
            const incomes = await incomesResponse.json();

            return { expenses, incomes };
         } catch (error) {
            console.error("Error fetching categories:", error);
            throw error;
         } finally {
            // Remove from cache when request completes (success or failure)
            requestCache.delete(cacheKey);
         }
      })();

      // Cache the promise to deduplicate concurrent requests
      requestCache.set(cacheKey, requestPromise);

      return requestPromise;
   },

   /**
    * Updates a transaction's status via API
    * @param {string} transactionId - ID of the transaction to update
    * @param {string} newStatus - New status to set ('pending' or 'cleared')
    * @returns {Promise<Object>} Updated transaction object
    * @throws {Error} If the API request fails
    */
   handleStatusChange: async (transactionId, newStatus) => {
      try {
         const response = await fetch(`/api/transactions/${transactionId}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ status: newStatus }),
         });
         if (!response.ok)
            throw new Error("Failed to update transaction status");
         return await response.json();
      } catch (error) {
         console.error("Error updating transaction status:", error);
         throw error;
      }
   },

   /**
    * Updates a transaction's associated account via API
    * @param {string} transactionId - ID of the transaction to update
    * @param {string} newAccountId - ID of the new account to associate
    * @param {Object} transaction - The transaction object being updated
    * @returns {Promise<Object>} Updated transaction object
    * @throws {Error} If the API request fails or account isn't found
    */
   handleAccountChange: async (transactionId, newAccountId, transaction) => {
      try {
         const response = await fetch(`/api/transactions/${transactionId}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
               accountId: Number(newAccountId),
               previousAccountId: transaction.accountId,
               amount: transaction.amount,
            }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
               errorData.error || "Failed to update transaction account"
            );
         }

         return await response.json();
      } catch (error) {
         console.error("Error updating transaction account:", error);
         throw error;
      }
   },

   /**
    * Updates a transaction's assignedTo via API
    * @param {string} transactionId - ID of the transaction to update
    * @param {string} assignedToId - ID of the new assignedTo to associate
    * @param {string} assignedToType - Type of assignedTo ('Income' or 'Expense')
    * @returns {Promise<Object>} Updated transaction object
    * @throws {Error} If the API request fails or assignedTo isn't found
    */
   handleAssignedToChange: async (
      transactionId,
      assignedToId,
      assignedToType
   ) => {
      try {
         // If assignedToId is null, undefined, empty string, or "Unassigned", remove the assignedTo
         const shouldUnassign = !assignedToId || assignedToId === "Unassigned";

         const updateData = shouldUnassign
            ? {
                 assignedTo: null,
                 assignedToType: null,
              }
            : {
                 assignedTo: assignedToId, // Send the string ID directly
                 assignedToType,
              };

         const response = await fetch(`/api/transactions/${transactionId}`, {
            method: "PATCH",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(updateData),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
               errorData.error || "Failed to update transaction assignedTo"
            );
         }
         return await response.json();
      } catch (error) {
         console.error("Error updating transaction assignedTo:", error);
         throw error;
      }
   },

   // Compatibility alias for backwards compatibility
   handleCategoryChange: async function (
      transactionId,
      categoryId,
      categoryType
   ) {
      return this.handleAssignedToChange(
         transactionId,
         categoryId,
         categoryType
      );
   },

   /**
    * Creates a reusable click outside handler for dropdowns/modals
    * @param {React.RefObject} ref - Reference to the element to watch
    * @param {Function} callback - Callback to execute when clicking outside
    * @returns {Function} Cleanup function to remove event listener
    */
   handleCSVUpload: async (
      file,
      existingTransactions,
      selectedAccountFilter
   ) => {
      if (!file) return { error: "No file provided" };

      // Validate file type
      if (!file.name.endsWith(".csv")) {
         return { error: "Please upload a CSV file" };
      }

      try {
         const text = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
         });

         const rows = text.split("\n");

         // Validate header row
         const headerRow = rows[0].toLowerCase().trim();
         const expectedHeaders = ["date", "description", "amount", "status"];
         const actualHeaders = headerRow.split(",").map((h) => h.trim());

         if (
            !expectedHeaders.every((header) => actualHeaders.includes(header))
         ) {
            return {
               error: "Invalid CSV format. Please download and use the template.",
            };
         }

         // Skip header row and process each line
         const newTransactions = rows
            .slice(1)
            .filter((row) => row.trim()) // Skip empty lines
            .map((row, index) =>
               TransactionHelpers.validateCSVRow(row, index, expectedHeaders)
            )
            .map((transaction) => ({
               ...transaction,
               accountId:
                  selectedAccountFilter === "all"
                     ? null
                     : Number(selectedAccountFilter),
            }));

         // Check for duplicates
         const duplicates = [];
         const nonDuplicates = [];

         newTransactions.forEach((newTrans) => {
            const isDuplicate = TransactionHelpers.checkForDuplicates(
               newTrans,
               existingTransactions
            );
            if (isDuplicate) {
               duplicates.push({ ...newTrans, approved: false });
            } else {
               nonDuplicates.push(newTrans);
            }
         });

         return {
            duplicates,
            nonDuplicates,
            success: true,
         };
      } catch (err) {
         return {
            error: "Failed to import transactions: " + err.message,
         };
      }
   },

   exportCSV: async () => {
      try {
         const response = await fetch("/api/transactions/export");
         if (!response.ok) throw new Error("Failed to export transactions");
         return await response.json();
      } catch (error) {
         console.error("Error exporting transactions:", error);
         throw error;
      }
   },

   fetchUnassignedCount: async () => {
      try {
         const response = await fetch("/api/transactions/unassigned/count");
         if (!response.ok) throw new Error("Failed to fetch unassigned count");
         const data = await response.json();
         return data.count;
      } catch (error) {
         console.error("Error fetching unassigned count:", error);
         throw error;
      }
   },

   /**
    * Deduplicate categories by their _id
    * @param {Array} categories - Array of category objects
    * @returns {Array} Deduplicated array of categories
    */
   deduplicateCategories: (categories) => {
      if (!Array.isArray(categories)) {
         console.warn(
            "deduplicateCategories: Input is not an array",
            categories
         );
         return [];
      }

      const seen = new Set();
      const duplicates = [];
      const deduplicated = categories.filter((category) => {
         if (!category._id) {
            console.warn(
               "deduplicateCategories: Category without _id found",
               category
            );
            return false; // Skip categories without _id
         }

         const id = category._id.toString();
         if (seen.has(id)) {
            duplicates.push({
               id,
               description: category.description,
               type: category.expectedAmount ? "Income" : "Expense",
            });
            return false;
         }
         seen.add(id);
         return true;
      });

      if (duplicates.length > 0) {
         console.log("Duplicates found and removed:", duplicates);
      }
      return deduplicated;
   },

   /**
    * Clear the category filter cache
    * This should be called when categories are updated or added
    */
   clearCategoryCache: () => {
      if (typeof window !== "undefined" && window._filterCategoriesCache) {
         window._filterCategoriesCache = {};
         console.log("Category cache cleared");
      }
   },

   /**
    * Force clear cache and refresh category filtering
    * This can be called when users report filtering issues
    */
   forceClearCategoryCache: () => {
      if (typeof window !== "undefined") {
         window._filterCategoriesCache = {};
         console.log("Category cache force cleared");
      }
   },
};

/**
 * Determines if an account is a cash account (cash, checking, or savings)
 * @param {Object} account - The account object to check
 * @returns {boolean} - True if the account is a cash account
 */
export function isCashAccount(account) {
   return ["cash", "checking", "savings"].includes(account.accountType);
}

export const TransactionHelpers = {
   /**
    * Creates a reusable click outside handler for dropdowns/modals
    * @param {React.RefObject} ref - Reference to the element to watch
    * @param {Function} callback - Callback to execute when clicking outside
    * @returns {Function} Cleanup function to remove event listener
    */
   setupClickOutsideHandler: (ref, callback) => {
      const handleClickOutside = (event) => {
         if (ref.current && !ref.current.contains(event.target)) {
            callback();
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   },

   /**
    * Calculates optimal position for dropdowns based on available screen space
    * @param {DOMRect} rect - Bounding rect of the trigger element
    * @param {number} dropdownWidth - Width of the dropdown
    * @param {number} dropdownHeight - Height of the dropdown
    * @returns {Object} Position coordinates and placement direction
    */
   calculateDropdownPosition: (rect, dropdownWidth, dropdownHeight) => {
      // Calculate available space below and above the element
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;

      // Use the actual dropdown height for calculation, with a minimum of 150px
      const effectiveDropdownHeight = Math.max(dropdownHeight, 150);

      // Check if dropdown should appear above the element
      const shouldShowAbove =
         spaceBelow < effectiveDropdownHeight &&
         spaceAbove > Math.min(spaceBelow, effectiveDropdownHeight);

      // Calculate horizontal position - center the dropdown on the element
      const leftPosition = Math.max(
         0,
         rect.left + window.scrollX + (rect.width - dropdownWidth) / 2
      );

      // Ensure dropdown doesn't go off-screen to the right
      const rightEdge = leftPosition + dropdownWidth;
      const adjustedLeft =
         rightEdge > window.innerWidth
            ? Math.max(0, window.innerWidth - dropdownWidth)
            : leftPosition;

      // For above placement, position at the element's top
      // When below, position at the element's bottom
      return {
         top: shouldShowAbove
            ? rect.top + window.scrollY // Position at the element's top (transform will move it up)
            : rect.bottom + window.scrollY + 4, // Small gap when below
         left: adjustedLeft,
         isAbove: shouldShowAbove,
      };
   },

   handleAccountFilterChange: (accountId, currentState) => {
      localStorage.setItem("selectedAccountFilter", accountId.toString());
      return {
         selectedAccountFilter: accountId,
         pagination: { ...currentState.pagination, page: 1 },
      };
   },

   handleSearchInput: (searchQuery, currentState) => {
      return {
         searchQuery,
         pagination: { ...currentState.pagination, page: 1 },
      };
   },

   getStatusPillColor: (status) => {
      switch (status) {
         case "pending":
            return "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300 ring-1 ring-yellow-600/20 dark:ring-yellow-500/20";
         case "cleared":
            return "bg-green-50 text-green-700 dark:bg-green-900/50 dark:text-green-300 ring-1 ring-green-600/20 dark:ring-green-500/20";
         default:
            return "bg-gray-50 text-gray-700 dark:bg-gray-900/50 dark:text-gray-300 ring-1 ring-gray-600/20 dark:ring-gray-500/20";
      }
   },

   filterAvailableAssignedTo: (transaction, storedExpenses, storedIncomes) => {
      const transactionDate = new Date(transaction.date);
      transactionDate.setHours(12, 0, 0, 0);
      const transactionMonth = transactionDate.getMonth();
      const transactionYear = transactionDate.getFullYear();

      // Create a unique cache key that includes transaction details to avoid stale cache
      const transactionCacheKey = `${
         transaction._id
      }_${transactionMonth}_${transactionYear}_${
         transaction.assignedToId || "none"
      }`;

      // Check for cached result with more specific key
      if (
         window._filterCategoriesCache &&
         window._filterCategoriesCache[transactionCacheKey]
      ) {
         return window._filterCategoriesCache[transactionCacheKey];
      }

      // Handle special case: if transaction already has a category name/description but no matching expense
      // This can happen if the category is from a different month and not in storedExpenses
      const existingAssignedToInfo =
         transaction.assignedTo && typeof transaction.assignedTo === "object"
            ? transaction.assignedTo
            : typeof transaction.assignedTo === "string" &&
              transaction.assignedTo !== "Unassigned"
            ? {
                 description: transaction.assignedTo,
                 _id: transaction.assignedToId,
              }
            : null;

      const filterExpenses = (expenses) => {
         // Find the currently assigned category if it exists
         let currentCategory = null;

         // IMPORTANT: Search in ALL expenses first to find the exact ID match
         // regardless of month, because the assigned category might be from a different month
         if (transaction.assignedToId) {
            // Try to find the exact ID match from all expenses first
            // Convert IDs to strings for consistent comparison
            const assignedToIdStr = transaction.assignedToId?.toString();

            // First look in all expenses regardless of month for exact ID match
            currentCategory = expenses.find(
               (expense) => expense._id?.toString() === assignedToIdStr
            );

            // If not found but we have category info from the transaction, use that
            if (!currentCategory && existingAssignedToInfo) {
               currentCategory = existingAssignedToInfo;
            }

            console.log(
               "Found current category:",
               currentCategory
                  ? {
                       id: currentCategory._id,
                       description: currentCategory.description,
                    }
                  : "Not found"
            );
         }

         // Filter expenses for the same month/year as the transaction
         const sameMonthExpenses = expenses.filter((expense) => {
            const expenseDate = new Date(expense.date);
            expenseDate.setHours(12, 0, 0, 0);

            // For weekly expenses, handle one-time vs spread differently
            if (expense.frequency === "weekly") {
               if (expense.weeklyChargeType === "one-time") {
                  // For one-time weekly expenses, use the date field like regular expenses
                  return (
                     expenseDate.getMonth() === transactionMonth &&
                     expenseDate.getFullYear() === transactionYear
                  );
               } else if (expense.weeklyChargeType === "spread") {
                  // For spread weekly expenses, check date range alignment
                  if (!expense.startDate || !expense.endDate) {
                     return false;
                  }

                  const expenseStart = new Date(expense.startDate);
                  const expenseEnd = new Date(expense.endDate);
                  expenseStart.setHours(0, 0, 0, 0);
                  expenseEnd.setHours(23, 59, 59, 999);

                  // Only include weekly expenses that contain the transaction date
                  return (
                     transactionDate >= expenseStart &&
                     transactionDate <= expenseEnd
                  );
               } else {
                  // Fallback for weekly expenses without weeklyChargeType - treat as one-time
                  return (
                     expenseDate.getMonth() === transactionMonth &&
                     expenseDate.getFullYear() === transactionYear
                  );
               }
            }

            // For non-weekly expenses, use the original month/year logic
            return (
               expenseDate.getMonth() === transactionMonth &&
               expenseDate.getFullYear() === transactionYear
            );
         });

         // Primary eligibility: expenses from the transaction's month (including date-aligned weekly expenses)
         // This ensures users can see relevant expenses for the transaction's date
         let eligibleExpenses = [...sameMonthExpenses];

         // Additionally include expenses that have available funds from other months
         // but give priority to same-month expenses
         const fundedExpensesFromOtherMonths = expenses.filter((expense) => {
            const available = expense.amountAvailable || 0;
            if (available <= 0) return false;

            // For weekly expenses, handle one-time vs spread differently
            if (expense.frequency === "weekly") {
               if (expense.weeklyChargeType === "one-time") {
                  // For one-time weekly expenses, they're available regardless of specific date alignment
                  // but we'll still check basic availability
                  return true; // Let the availability check below handle the rest
               } else if (expense.weeklyChargeType === "spread") {
                  // For spread weekly expenses, check if they align with the transaction date
                  if (!expense.startDate || !expense.endDate) {
                     return false;
                  }

                  const expenseStart = new Date(expense.startDate);
                  const expenseEnd = new Date(expense.endDate);
                  expenseStart.setHours(0, 0, 0, 0);
                  expenseEnd.setHours(23, 59, 59, 999);

                  // Only include weekly expenses that contain the transaction date
                  if (
                     !(
                        transactionDate >= expenseStart &&
                        transactionDate <= expenseEnd
                     )
                  ) {
                     return false;
                  }
               } else {
                  // Fallback for weekly expenses without weeklyChargeType - treat as one-time
                  return true; // Let the availability check below handle the rest
               }
            }

            // Check if this expense is NOT already included in same month expenses
            const expenseDate = new Date(expense.date);
            expenseDate.setHours(12, 0, 0, 0);
            const isFromSameMonth =
               expenseDate.getMonth() === transactionMonth &&
               expenseDate.getFullYear() === transactionYear;

            // Only include funded expenses that are NOT from the same month
            // (same month expenses are already included above)
            return !isFromSameMonth;
         });

         // Combine all eligible expenses - same month first, then funded from other months
         eligibleExpenses = [
            ...eligibleExpenses,
            ...fundedExpensesFromOtherMonths,
         ];

         // Use the deduplication utility to remove duplicates
         eligibleExpenses =
            TransactionsUtils.deduplicateCategories(eligibleExpenses);

         // Now remove the current category if it exists - we'll add it back at the top
         if (currentCategory && currentCategory._id) {
            eligibleExpenses = eligibleExpenses.filter(
               (expense) =>
                  !expense._id ||
                  !currentCategory._id ||
                  expense._id.toString() !== currentCategory._id.toString()
            );
         }

         // Build final result array
         let result = [];

         if (currentCategory) {
            // Ensure we properly identify the current category in the UI
            const markedCurrentCategory = {
               ...currentCategory,
               isCurrentCategory: true,
               sortPriority: 1,
               // Add a description property if not present (could happen with our fallback)
               description:
                  currentCategory.description ||
                  (typeof transaction.assignedTo === "string"
                     ? transaction.assignedTo
                     : "Unknown"),
               // Always include the original ID to ensure proper identification
               _id: transaction.assignedToId || currentCategory._id,
            };

            result.push(markedCurrentCategory);
         }

         // Add filtered expenses with lower priority
         const prioritizedExpenses = eligibleExpenses.map((expense) => ({
            ...expense,
            sortPriority: 2,
         }));

         result = [...result, ...prioritizedExpenses];

         // Final sort to ensure order is maintained
         const sortedResult = result.sort(
            (a, b) => a.sortPriority - b.sortPriority
         );

         return sortedResult;
      };

      let finalResult = [];

      if (transaction.type === "Income") {
         const expensesList = filterExpenses(storedExpenses);
         // Give income categories a lower priority
         const incomesList = storedIncomes.map((income) => ({
            ...income,
            sortPriority: 3,
         }));

         const combinedList = [...expensesList, ...incomesList];
         // Deduplicate the combined list
         finalResult = TransactionsUtils.deduplicateCategories(
            combinedList
         ).sort((a, b) => a.sortPriority - b.sortPriority);
      } else {
         finalResult = filterExpenses(storedExpenses);
      }

      // Cache the result with improved key
      if (!window._filterCategoriesCache) {
         window._filterCategoriesCache = {};
      }
      window._filterCategoriesCache[transactionCacheKey] = finalResult;

      return finalResult;
   },

   formatTransactionForUI: (transaction, field, value) => {
      if (field === "date") {
         return {
            ...transaction,
            [field]: new Date(new Date(value).setHours(12, 0, 0, 0)),
         };
      }
      return { ...transaction, [field]: value };
   },

   validateCSVRow: (row, index, expectedHeaders) => {
      const columns = row.split(",").map((col) => col.trim());

      if (columns.length !== expectedHeaders.length) {
         throw new Error(`Row ${index + 2} has incorrect number of columns`);
      }

      const postingDate = columns[0];
      const description = columns[1].replace(/\s+/g, " ").trim();
      const amount = parseFloat(columns[2].replace(/[^0-9.-]/g, ""));
      const status = columns[3].trim().toLowerCase();

      // Validate date format (M/D/YY or M/D/YYYY)
      const dateRegex =
         /^([1-9]|0[1-9]|1[0-2])\/([1-9]|0[1-9]|[12]\d|3[01])\/(\d{2}|\d{4})$/;
      if (!dateRegex.test(postingDate)) {
         throw new Error(
            `Invalid date format in row ${
               index + 2
            }. Use M/D/YY or M/D/YYYY format (e.g., 1/5/24 or 01/05/2024).`
         );
      }

      // Parse and validate the date
      const [month, day, year] = postingDate.split("/").map(Number);
      const fullYear = year < 100 ? 2000 + year : year;

      // Validate month and day
      if (month < 1 || month > 12) {
         throw new Error(`Invalid month in row ${index + 2}`);
      }

      const daysInMonth = new Date(fullYear, month, 0).getDate();
      if (day < 1 || day > daysInMonth) {
         throw new Error(`Invalid day in row ${index + 2}`);
      }

      // Create date object (set to noon to avoid timezone issues)
      const date = new Date(fullYear, month - 1, day, 12, 0, 0);

      // Validate amount
      if (isNaN(amount)) {
         throw new Error(`Invalid amount in row ${index + 2}`);
      }

      // Validate status
      const TRANSACTION_STATUS = ["pending", "cleared"];
      if (!TRANSACTION_STATUS.includes(status)) {
         throw new Error(
            `Invalid status in row ${index + 2}. Use 'pending' or 'cleared'.`
         );
      }

      return {
         date,
         payee: description,
         amount,
         type: amount >= 0 ? "Income" : "Expense",
         status,
         category: null,
      };
   },

   checkForDuplicates: (newTransaction, existingTransactions) => {
      return existingTransactions.some((existingTrans) => {
         const sameDate =
            new Date(existingTrans.date).toDateString() ===
            new Date(newTransaction.date).toDateString();
         const sameAmount = existingTrans.amount === newTransaction.amount;
         return sameDate && sameAmount;
      });
   },

   generateCSVTemplate: () => {
      const headers = ["Date", "Description", "Amount", "Status"];
      const exampleRows = [
         "12/31/23,Grocery Store,-50.00,pending",
         "01/01/24,Salary,2000.00,cleared",
      ];
      return [headers.join(","), ...exampleRows].join("\n");
   },
};

import { getServerSession } from "next-auth/next";
import { authOptions } from "../../api/auth/[...nextauth]/route";
import { NextResponse } from "next/server";
import dbConnect from "../mongodb/dbConnect";
import User from "../mongodb/models/User";

/**
 * Get the current MongoDB user document using NextAuth session
 */
export async function getCurrentMongoUser() {
   try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
         return null;
      }

      await dbConnect();

      // Find user by MongoDB ID from the session

      const user = await User.findById(session.user.id);

      return user;
   } catch (error) {
      console.error("getCurrentMongoUser: Error occurred:", {
         error: error.message,
         stack: error.stack,
         timestamp: new Date().toISOString(),
      });
      return null;
   }
}

/**
 * Get session and user ID for API routes
 * Returns { session, userId, error } where error is a NextResponse if unauthorized
 */
export async function getSessionForAPI() {
   try {
      const session = await getServerSession(authOptions);

      if (!session?.user?.id) {
         return {
            session: null,
            userId: null,
            error: NextResponse.json(
               { error: "Unauthorized" },
               { status: 401 }
            ),
         };
      }

      return {
         session,
         userId: session.user.id,
         error: null,
      };
   } catch (error) {
      console.error("Error getting session for API:", error);
      return {
         session: null,
         userId: null,
         error: NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
         ),
      };
   }
}

/**
 * Update user onboarding status
 */
export async function updateUserOnboardingStatus(userId, onboardingComplete) {
   try {
      await dbConnect();

      const user = await User.findByIdAndUpdate(
         userId,
         { onboardingComplete },
         { new: true }
      );

      return user;
   } catch (error) {
      console.error("Error updating onboarding status:", error);
      return null;
   }
}

/**
 * Create a new user (used during registration)
 */
export async function createUser(userData) {
   try {
      await dbConnect();

      const user = await User.create(userData);
      return user;
   } catch (error) {
      console.error("Error creating user:", error);
      throw error;
   }
}

/**
 * Get user by email
 */
export async function getUserByEmail(email) {
   try {
      await dbConnect();

      const user = await User.findOne({ email: email.toLowerCase() });
      return user;
   } catch (error) {
      console.error("Error getting user by email:", error);
      return null;
   }
}

/**
 * Get user by ID
 */
export async function getUserById(id) {
   try {
      await dbConnect();

      const user = await User.findById(id);
      return user;
   } catch (error) {
      console.error("Error getting user by ID:", error);
      return null;
   }
}

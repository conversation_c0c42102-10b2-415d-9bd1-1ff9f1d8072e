import { format, parseISO, addDays } from "date-fns";
import { debounce } from "lodash";
import useBalanceStore from "../stores/balanceStore";

// Helper function to round numbers to two decimal places
export const roundToTwoDecimals = (num) => {
   return Number((Math.round(num * 100) / 100).toFixed(2));
};

// Helper function to evaluate expressions like "+50" or "-30" or "100+50"
export const evaluateExpression = (expression, currentValue) => {
   try {
      // Remove any whitespace, $ signs, and commas
      expression = expression.toString().replace(/[\s$,]/g, "");
      if (!expression) return currentValue;

      // If it's a simple number, return it
      if (!isNaN(expression)) {
         return parseFloat(expression);
      }

      // For relative changes (e.g., +50 or -30)
      if (expression.startsWith("+")) {
         const number = parseFloat(expression.slice(1));
         return !isNaN(number) ? currentValue + number : currentValue;
      }

      if (expression.startsWith("-")) {
         const number = parseFloat(expression.slice(1));
         return !isNaN(number) ? currentValue - number : currentValue;
      }

      // For expressions with operators in the middle (e.g., "100+50" or "200-30")
      if (expression.includes("+") || expression.includes("-")) {
         try {
            // Use Function instead of eval for better security
            const result = new Function(`return ${expression}`)();
            return !isNaN(result) ? result : currentValue;
         } catch {
            return currentValue;
         }
      }

      // If no valid expression pattern is found, return the parsed expression or current value
      return !isNaN(expression) ? parseFloat(expression) : currentValue;
   } catch (error) {
      console.error("Error evaluating expression:", error);
      return currentValue;
   }
};

// Format amount for display
export const formatAmount = (amount) => {
   const value = parseFloat(amount || 0);
   const formattedValue = isNaN(value)
      ? "0.00"
      : Math.abs(value).toLocaleString("en-US", {
           minimumFractionDigits: 2,
           maximumFractionDigits: 2,
        });
   return {
      value,
      formattedValue,
      isNegative: value < 0,
   };
};

// Get status badge styling
export const getStatusBadgeStyle = (status, isFutureFunded = false) => {
   // Special case: if it's a future expense that's funded, use green styling
   if (status === "future" && isFutureFunded) {
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
   }

   const badges = {
      projected:
         "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      scheduled:
         "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      funded:
         "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      paid: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      received:
         "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      late: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      overpaid:
         "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
      underpaid:
         "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 after:content-['🎉'] after:ml-1 flex items-center",
      underbudget:
         "bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200 before:content-['🎉'] before:mr-1 flex items-center",
      hidden: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
      future:
         "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
   };
   return badges[status] || badges.scheduled;
};

// Get available pill styling
export const getAvailablePillStyle = (value) => {
   if (value > 0) {
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
   } else if (value < 0) {
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
   }
   return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
};

// Helper function to determine expense status
export const determineExpenseStatus = (
   amountSpent,
   amountAssigned,
   amountDue,
   dueDate = null,
   startDate = null,
   endDate = null,
   currentStatus = null,
   isFutureExpense = false
) => {
   // If the expense is already paid, preserve that status
   if (currentStatus === "paid") {
      return "paid";
   }

   // If the expense is marked as a future expense, check if it's fully funded
   // Preserve handling of future expenses as requested
   if (currentStatus === "future" || isFutureExpense) {
      // For future expenses, if the assigned amount equals or exceeds the due amount,
      // consider it "funded" instead of just "future"
      const assigned = Number(amountAssigned || 0);
      const due = Number(amountDue || 0);

      return assigned >= due ? "funded" : "future";
   }

   // Convert all inputs to numbers and handle null/undefined
   const spent = Math.abs(Number(amountSpent || 0));
   const assigned = Number(amountAssigned || 0);
   const due = Number(amountDue || 0);

   // Check if due date has passed using string comparison
   const today = new Date().toISOString().split("T")[0]; // Get today as YYYY-MM-DD

   let dueDatePassed = false;

   // For weekly expenses, check against endDate
   if (endDate) {
      dueDatePassed = endDate < today;
   }
   // For regular expenses, check against dueDate
   else if (dueDate) {
      dueDatePassed = dueDate < today;
   }

   // If spent amount exceeds due amount, it's overpaid (regardless of due date)
   if (spent > due) {
      return "overpaid";
   }

   // If spent amount equals due amount exactly, it's paid
   if (spent === due) {
      return "paid";
   }

   // Handle past due expenses
   if (dueDatePassed) {
      // If there's some spending but less than due, it's underpaid
      if (spent > 0) {
         return "underpaid";
      }
      // If no spending and due date passed, it's late
      return "late";
   }

   // For expenses not yet due
   // If assigned covers the due amount, it's funded
   if (assigned >= due) {
      return "funded";
   }

   // Default to scheduled for all other cases
   return "scheduled";
};

// Aggregate weekly expenses
export const aggregateWeeklyExpenses = (
   expenses,
   dateRange,
   isProjectionHidden
) => {
   // First, apply status determination to all expenses except future expenses
   const statusUpdatedExpenses = expenses.map((expense) => {
      // For future expenses, preserve their existing status (funded or future)
      if (expense.isFutureExpense) {
         return expense;
      }

      // For other expenses, calculate status normally
      // But preserve "paid" status to prevent it from being overwritten
      if (expense.status === "paid") {
         return expense;
      }

      return {
         ...expense,
         status:
            expense.status === "projected"
               ? "projected"
               : determineExpenseStatus(
                    expense.amountSpent,
                    expense.amountAssigned,
                    expense.amountDue,
                    expense.date,
                    expense.startDate,
                    expense.endDate
                 ),
      };
   });

   // If not in month view, return the status-updated expenses
   if (dateRange?.viewType !== "month" && dateRange?.viewType !== "payPeriod")
      return statusUpdatedExpenses;

   const weeklyGroups = new Map();
   const processedDates = new Set();

   statusUpdatedExpenses.forEach((expense) => {
      if (expense.frequency === "weekly") {
         // Handle one-time weekly expenses differently
         if (expense.weeklyChargeType === "one-time") {
            // For one-time weekly expenses, treat them like regular expenses with just a due date
            const expenseDate =
               expense.date instanceof Date
                  ? expense.date
                  : parseISO(expense.date);

            // Skip if expense date is outside the date range
            if (dateRange?.start && dateRange?.end) {
               if (
                  expenseDate < dateRange.start ||
                  expenseDate > dateRange.end
               ) {
                  return;
               }
            }

            const key = expense.description.toLowerCase();
            const dateKey = `${format(expenseDate, "yyyy-MM-dd")}-${key}`;

            if (processedDates.has(dateKey)) return;
            processedDates.add(dateKey);

            if (!weeklyGroups.has(key)) {
               weeklyGroups.set(key, {
                  _id: expense._id,
                  description: expense.description,
                  category: expense.category,
                  status: "projected",
                  frequency: "weekly",
                  amountDue: 0,
                  amountAssigned: 0,
                  amountSpent: 0,
                  amountAvailable: 0,
                  date: expenseDate,
                  expenses: [],
                  isAggregated: true,
                  hasProjected: false,
                  hasScheduled: false,
                  hasReadyToPay: false,
                  weeklyChargeType: "one-time",
               });
            }

            const group = weeklyGroups.get(key);
            const weekExpense = {
               ...expense,
               date: expenseDate,
               amountDue: roundToTwoDecimals(expense.amountDue || 0),
               amountAssigned: roundToTwoDecimals(expense.amountAssigned || 0),
               amountSpent: roundToTwoDecimals(expense.amountSpent || 0),
               amountAvailable: roundToTwoDecimals(
                  (expense.amountAssigned || 0) -
                     Math.abs(expense.amountSpent || 0)
               ),
               isHidden: isProjectionHidden(expense),
               weeklyChargeType: "one-time",
            };
            group.expenses.push(weekExpense);

            // Update group totals and status for non-hidden expenses
            if (!weekExpense.isHidden) {
               updateGroupTotalsAndStatus(group, weekExpense);
            }
         } else {
            // Handle spread weekly expenses (existing logic)
            const expenseStartDate = expense.startDate || expense.date;

            const expenseEndDate =
               expense.endDate ||
               format(addDays(new Date(expenseStartDate), 6), "yyyy-MM-dd");

            // Skip if expense period doesn't overlap with the date range
            if (dateRange?.start && dateRange?.end) {
               const dateRangeStartStr = format(dateRange.start, "yyyy-MM-dd");
               const dateRangeEndStr = format(dateRange.end, "yyyy-MM-dd");

               // Check if the expense period overlaps with the date range using string comparison
               const overlaps =
                  expenseStartDate <= dateRangeEndStr &&
                  expenseEndDate >= dateRangeStartStr;

               if (!overlaps) {
                  return;
               }
            }

            const key = expense.description.toLowerCase();
            const dateKey = `${expense.date}-${key}`;

            if (processedDates.has(dateKey)) return;
            processedDates.add(dateKey);

            if (!weeklyGroups.has(key)) {
               weeklyGroups.set(key, {
                  _id: expense._id,
                  description: expense.description,
                  category: expense.category,
                  status: "projected",
                  frequency: "weekly",
                  amountDue: 0,
                  amountAssigned: 0,
                  amountSpent: 0,
                  amountAvailable: 0,
                  startDate: null,
                  endDate: null,
                  date: expense.date,
                  expenses: [],
                  isAggregated: true,
                  hasProjected: false,
                  hasScheduled: false,
                  hasReadyToPay: false,
                  weeklyChargeType: "spread",
               });
            }

            const group = weeklyGroups.get(key);
            const weekExpense = {
               ...expense,
               amountDue: roundToTwoDecimals(expense.amountDue || 0),
               amountAssigned: roundToTwoDecimals(expense.amountAssigned || 0),
               amountSpent: roundToTwoDecimals(expense.amountSpent || 0),
               amountAvailable: roundToTwoDecimals(
                  (expense.amountAssigned || 0) -
                     Math.abs(expense.amountSpent || 0)
               ),
               isHidden: isProjectionHidden(expense),
            };
            group.expenses.push(weekExpense);

            // Update group totals and status for non-hidden expenses
            if (!weekExpense.isHidden) {
               updateGroupTotalsAndStatus(group, weekExpense);
            }

            // Update date range for spread expenses
            if (!group.startDate || expenseStartDate < group.startDate) {
               group.startDate = expenseStartDate;
               group.date = expenseStartDate;
            }
            if (!group.endDate || expenseEndDate > group.endDate) {
               group.endDate = expenseEndDate;
            }
         }
      }
   });

   // Process groups to determine if all weeks are hidden
   const processedGroups = Array.from(weeklyGroups.values()).map((group) => {
      // Sort expenses by date (handle mixed date formats)
      group.expenses.sort((a, b) => {
         let dateA = a.date;
         let dateB = b.date;

         // Convert Date objects to YYYY-MM-DD strings if needed
         if (dateA instanceof Date) {
            dateA = dateA.toISOString().split("T")[0];
         }
         if (dateB instanceof Date) {
            dateB = dateB.toISOString().split("T")[0];
         }

         // Ensure we have strings for comparison
         if (typeof dateA !== "string" || typeof dateB !== "string") {
            return 0; // Keep original order if dates are invalid
         }

         return dateA.localeCompare(dateB);
      });

      const allWeeksHidden = group.expenses.every((exp) => exp.isHidden);
      const someWeeksHidden = group.expenses.some((exp) => exp.isHidden);

      // Update group dates based on first and last visible weeks
      const visibleWeeks = group.expenses.filter((exp) => !exp.isHidden);
      if (visibleWeeks.length > 0) {
         const firstWeek = visibleWeeks[0];
         const lastWeek = visibleWeeks[visibleWeeks.length - 1];

         // Check if all visible weeks are paid
         const allVisibleWeeksPaid = visibleWeeks.every(
            (exp) => exp.status === "paid"
         );
         if (allVisibleWeeksPaid) {
            group.status = "paid";
         }

         if (group.weeklyChargeType === "spread") {
            // Update date range only for spread expenses
            group.startDate = firstWeek.startDate || firstWeek.date;
            group.date = group.startDate;
            group.endDate = lastWeek.endDate || addDays(lastWeek.date, 6);
         }
      }

      return {
         ...group,
         isHidden: allWeeksHidden,
         partiallyHidden: someWeeksHidden && !allWeeksHidden,
      };
   });

   // Return combined expenses
   return [
      ...statusUpdatedExpenses.filter(
         (expense) =>
            expense.frequency !== "weekly" &&
            !(expense.startDate && expense.endDate)
      ),
      ...processedGroups,
   ];
};

// Helper function to update group totals and status
const updateGroupTotalsAndStatus = (group, weekExpense) => {
   // Update totals
   group.amountDue = roundToTwoDecimals(
      (group.amountDue || 0) + (weekExpense.amountDue || 0)
   );
   group.amountAssigned = roundToTwoDecimals(
      (group.amountAssigned || 0) + (weekExpense.amountAssigned || 0)
   );
   group.amountSpent = roundToTwoDecimals(
      (group.amountSpent || 0) + (weekExpense.amountSpent || 0)
   );
   group.amountAvailable = roundToTwoDecimals(
      (group.amountAssigned || 0) - Math.abs(group.amountSpent || 0)
   );

   // Update status flags based on expense status
   if (weekExpense.status === "projected") {
      group.hasProjected = true;
   } else if (weekExpense.status === "scheduled") {
      group.hasScheduled = true;
   } else if (weekExpense.status === "funded") {
      group.hasFunded = true;
   } else if (weekExpense.status === "paid") {
      group.hasPaid = true;
   } else if (weekExpense.status === "late") {
      group.hasLate = true;
   } else if (weekExpense.status === "overpaid") {
      group.hasOverpaid = true;
   } else if (weekExpense.status === "underpaid") {
      group.hasUnderpaid = true;
   }

   // Determine overall group status
   // Priority order: late > underpaid > overpaid > paid > funded > scheduled > projected
   if (group.hasLate) {
      group.status = "late";
   } else if (group.hasUnderpaid) {
      group.status = "underpaid";
   } else if (group.hasOverpaid) {
      group.status = "overpaid";
   } else if (group.hasPaid) {
      group.status = "paid";
   } else if (group.hasFunded) {
      group.status = "funded";
   } else if (group.hasScheduled) {
      group.status = "scheduled";
   } else if (group.hasProjected) {
      group.status = "projected";
   }
};

// Add this function to identify future expenses based on the current date range
export const identifyFutureExpenses = (expenses, dateRange) => {
   if (!dateRange?.end) return [];

   // Check if we're viewing a past period by comparing endDate with current date
   const currentDate = new Date();
   currentDate.setHours(0, 0, 0, 0); // Set to start of day for fair comparison

   const endDate = new Date(dateRange.end);
   const isPastPeriod = endDate < currentDate;

   // Get the start date of the period
   let startDate;
   if (dateRange.start) {
      startDate = new Date(dateRange.start);
      startDate.setHours(0, 0, 0, 0);
   } else {
      // Calculate start date based on view type if not provided
      startDate = new Date(endDate);
      if (dateRange.viewType === "month") {
         // Set to first day of the month
         startDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
      } else if (dateRange.viewType === "payPeriod") {
         // Default to 14 days before end for pay periods if not specified
         startDate.setDate(startDate.getDate() - 14);
      }
      startDate.setHours(0, 0, 0, 0);
   }

   // Get the start of the next period (based on viewType)
   let nextPeriodStartDate = new Date(dateRange.end);

   if (dateRange.viewType === "month") {
      // Move to the first day of the next month
      nextPeriodStartDate = new Date(
         nextPeriodStartDate.getFullYear(),
         nextPeriodStartDate.getMonth() + 1,
         1
      );
   } else if (dateRange.viewType === "payPeriod") {
      // For pay periods, we'll add a day to the end date to get the start of the next period
      nextPeriodStartDate.setDate(nextPeriodStartDate.getDate() + 1);
   }

   // Filter expenses based on whether we're viewing a past period or not
   return expenses.filter((expense) => {
      // Only consider one-off expenses
      if (expense.frequency && expense.frequency !== "oneoff") return false;

      // Skip expenses with start/end dates (they are not one-off)
      if (expense.startDate && expense.endDate) return false;

      // Get the expense date and createdAt date
      const expenseDate =
         expense.date instanceof Date ? expense.date : new Date(expense.date);

      const createdAtDate =
         expense.createdAt instanceof Date
            ? expense.createdAt
            : new Date(expense.createdAt);

      let isFuture = false;

      if (isPastPeriod) {
         // For past periods, include future expenses that were created on or before the end of this period
         // The expense date must be after the period end
         const isAfterPeriod = expenseDate > endDate;

         // The expense must have been created on or before the period end date
         // This allows expenses to appear in any month on or after their creation
         const wasCreatedBeforeOrDuringPeriod = createdAtDate <= endDate;

         isFuture = isAfterPeriod && wasCreatedBeforeOrDuringPeriod;
      } else {
         // For current/future periods, use the original logic
         // Check if this expense is in or after the next period
         isFuture = expenseDate >= nextPeriodStartDate;
      }

      // If it's a future expense, ensure we tag it as such
      if (isFuture) {
         expense.isFutureExpense = true;

         // Check if the expense should be funded based on its assigned amount
         // If the expense has a prorated amount, use that, otherwise use the regular amountDue
         const amountToCheck = expense.proratedAmountDue || expense.amountDue;
         const isFunded = expense.amountAssigned >= amountToCheck;

         // Set the status based on funding level
         expense.status = isFunded ? "funded" : "future";
      }

      return isFuture;
   });
};

// Calculate prorated amount for future expenses
export const calculateProratedAmount = (expense, dateRange) => {
   if (!expense || !dateRange?.end) return expense?.amountDue || 0;

   // Use the due date
   const expenseDate =
      expense.date instanceof Date ? expense.date : new Date(expense.date);

   // Use the createdAt date
   const createdAtDate =
      expense.createdAt instanceof Date
         ? expense.createdAt
         : new Date(expense.createdAt);

   // Current period end date
   const currentPeriodEnd = new Date(dateRange.end);

   // If in month view, calculate based on months
   if (dateRange.viewType === "month") {
      // Check if we're in the month immediately before the due date
      // This means the expense is due next month
      const isOneMonthAway =
         expenseDate.getMonth() - currentPeriodEnd.getMonth() === 1 &&
         expenseDate.getFullYear() === currentPeriodEnd.getFullYear();

      // Special case: If we're one month away from the due date, return full amount
      // We always want the expense to be fully funded by the month before it's due
      if (
         isOneMonthAway ||
         // Also handle December to January transition for following year
         (currentPeriodEnd.getMonth() === 11 &&
            expenseDate.getMonth() === 0 &&
            expenseDate.getFullYear() === currentPeriodEnd.getFullYear() + 1)
      ) {
         return roundToTwoDecimals(expense.amountDue || 0);
      }

      // Calculate months from creation to due date
      // 1. Count the full months between (inclusive of creation month, exclusive of due month)
      // For example: Feb 15 to June 1 = 4 months (Feb, Mar, Apr, May - not counting June)

      // First get the months difference
      let monthsToSave =
         (expenseDate.getFullYear() - createdAtDate.getFullYear()) * 12 +
         (expenseDate.getMonth() - createdAtDate.getMonth());

      // If the expense is due on the 1st of the month, we don't need to save in that month
      // as we want it fully funded by the end of the previous month
      if (expenseDate.getDate() === 1) {
         monthsToSave -= 1;
      }

      // Ensure we have at least 1 month to save
      monthsToSave = Math.max(1, monthsToSave);

      // Calculate the amount needed per month (rounded to two decimal places)
      const amountPerMonth = roundToTwoDecimals(
         (expense.amountDue || 0) / monthsToSave
      );

      // Calculate how many months have passed since creation
      // We want to include the creation month as one full month passed, regardless of the day
      let passedMonths;

      if (
         currentPeriodEnd.getFullYear() === createdAtDate.getFullYear() &&
         currentPeriodEnd.getMonth() === createdAtDate.getMonth()
      ) {
         // If we're in the same month as creation, count it as 1 full month
         passedMonths = 1;
      } else {
         // Otherwise calculate the difference and add 1 to include the creation month
         passedMonths =
            (currentPeriodEnd.getFullYear() - createdAtDate.getFullYear()) *
               12 +
            (currentPeriodEnd.getMonth() - createdAtDate.getMonth());

         // Only add 1 if the current date is on or after the creation date
         // This prevents counting an extra month when viewing a period before creation
         if (currentPeriodEnd >= createdAtDate) {
            passedMonths += 1;
         }
      }

      // Don't exceed the total months to save
      passedMonths = Math.min(passedMonths, monthsToSave);

      // Calculate the progressive amount based on months passed (rounded to two decimal places)
      const progressiveAmount = roundToTwoDecimals(
         amountPerMonth * passedMonths
      );

      // For debugging clarity: if we're in the creation month and viewing that month,
      // this should return at least 1/N of the total amount
      if (
         currentPeriodEnd.getFullYear() === createdAtDate.getFullYear() &&
         currentPeriodEnd.getMonth() === createdAtDate.getMonth() &&
         currentPeriodEnd >= createdAtDate
      ) {
         // Ensure we return at least one month's worth (rounded to two decimal places)
         return roundToTwoDecimals(Math.max(progressiveAmount, amountPerMonth));
      }

      // If we've saved for all the required months, return the full amount
      if (passedMonths >= monthsToSave) {
         return roundToTwoDecimals(expense.amountDue || 0);
      }

      return progressiveAmount;
   }
   // If in pay period view, calculate based on pay periods
   else if (dateRange.viewType === "payPeriod") {
      // Get the pay period type from userPaySettings
      const payPeriodType = dateRange.userPaySettings?.payPeriod || "biweekly";

      // Determine period length in days
      let periodLengthDays = 14; // default biweekly
      if (payPeriodType === "weekly") {
         periodLengthDays = 7;
      } else if (payPeriodType === "biweekly") {
         periodLengthDays = 14;
      } else if (payPeriodType === "semimonthly") {
         periodLengthDays = 15;
      } else if (payPeriodType === "monthly") {
         periodLengthDays = 30;
      }

      // Calculate total days between creation and due date
      const totalDays = Math.max(
         1,
         Math.ceil((expenseDate - createdAtDate) / (1000 * 60 * 60 * 24))
      );

      // Calculate days that have passed since creation until current period end
      const daysPassed = Math.max(
         0,
         Math.ceil((currentPeriodEnd - createdAtDate) / (1000 * 60 * 60 * 24))
      );

      // Calculate days until due date
      const daysUntilDue = Math.max(
         0,
         Math.ceil((expenseDate - currentPeriodEnd) / (1000 * 60 * 60 * 24))
      );

      // Calculate the precise number of full pay periods from creation to due date
      // Using Math.floor ensures we don't count partial periods
      const totalPayPeriods = Math.max(
         1,
         Math.floor(totalDays / periodLengthDays)
      );

      // Amount to save per full pay period (rounded to two decimal places)
      const amountPerPeriod = roundToTwoDecimals(
         (expense.amountDue || 0) / totalPayPeriods
      );

      // If we're before the creation date, return 0
      if (currentPeriodEnd < createdAtDate) {
         return 0;
      }

      // Calculate which pay period we're in (0-indexed from creation date)
      // This gives us the exact period number, potentially including a partial period
      const periodsPassedExact = daysPassed / periodLengthDays;

      // Calculate the number of full periods that have passed
      const fullPeriodsPassed = Math.floor(periodsPassedExact);

      // If we're at or past the due date, return full amount
      if (daysUntilDue <= 0) {
         return roundToTwoDecimals(expense.amountDue || 0);
      }

      // Initialize progress with the amount for full periods (rounded to two decimal places)
      let progressiveAmount = roundToTwoDecimals(
         amountPerPeriod * fullPeriodsPassed
      );

      // Calculate how far we are into the current period (0 to 1)
      const currentPeriodProgress = periodsPassedExact - fullPeriodsPassed;

      // Add the partial amount for the current period (with final rounding to two decimal places)
      progressiveAmount = roundToTwoDecimals(
         progressiveAmount + amountPerPeriod * currentPeriodProgress
      );

      // Final check: If we've accumulated more than the total amount due, cap it
      if (progressiveAmount >= expense.amountDue) {
         return roundToTwoDecimals(expense.amountDue || 0);
      }

      return progressiveAmount;
   }

   // Default: return original amount (rounded to two decimal places)
   return roundToTwoDecimals(expense.amountDue || 0);
};

// Organize expenses by frequency
export const organizeExpensesByFrequency = (expenses, dateRange) => {
   const weeklyExpenses = expenses.filter(
      (expense) =>
         expense.frequency === "weekly" ||
         (expense.startDate && expense.endDate)
   );

   const monthlyExpenses = expenses.filter(
      (expense) =>
         (expense.frequency === "monthly" ||
            expense.frequency === "biweekly" ||
            expense.frequency === "quarterly") &&
         !(expense.startDate && expense.endDate)
   );

   const annualExpenses = expenses.filter(
      (expense) =>
         expense.frequency === "annually" &&
         !(expense.startDate && expense.endDate)
   );

   // Include all one-off expenses (including those marked as future)
   const oneoffExpenses = expenses.filter((expense) => {
      return (
         (!expense.frequency || expense.frequency === "oneoff") &&
         !(expense.startDate && expense.endDate)
      );
   });

   return {
      weekly: weeklyExpenses,
      monthly: monthlyExpenses,
      annual: annualExpenses,
      oneoff: oneoffExpenses,
   };
};

// Calculate category statistics
export const getCategoryStats = (expenses) => {
   const total = expenses.reduce(
      (acc, expense) => acc + (expense.amountDue || 0),
      0
   );
   return {
      count: expenses.length,
      total: total,
   };
};

// Calculate totals for visible expenses
export const calculateVisibleTotals = (expenses) => {
   return expenses.reduce(
      (acc, expense) => ({
         due: acc.due + (expense.amountDue || 0),
         assigned: acc.assigned + (expense.amountAssigned || 0),
         spent: acc.spent + (expense.amountSpent || 0),
         available: acc.available + (expense.amountAvailable || 0),
      }),
      { due: 0, assigned: 0, spent: 0, available: 0 }
   );
};

// Combine and sort expenses
export const combineAndSortExpenses = (expenses, expectedExpenses) => {
   // Use a Map to track unique expenses by description and date
   const uniqueExpenses = new Map();

   // Additional Map to track expenses by recurringExpenseId for faster lookup
   const expensesByRecurringId = new Map();

   // Track expenses by recurringExpenseId AND date range for weekly expenses
   const weeklyExpensesByRecurringIdAndDate = new Map();

   // First add actual expenses and create a Set to track scheduled expenses
   const scheduledExpenses = new Set();
   expenses.forEach((expense) => {
      // Track expenses by recurringExpenseId if available
      if (expense.recurringExpenseId) {
         expensesByRecurringId.set(expense.recurringExpenseId, expense);

         // For weekly expenses, also track by recurringExpenseId + date
         if (expense.frequency === "weekly") {
            // Create a key that combines recurringExpenseId and date range
            let dateKey;
            if (expense.weeklyChargeType === "one-time") {
               dateKey = format(new Date(expense.date), "yyyy-MM-dd");
            } else {
               // For spread weekly expenses, use start date
               const startDate = expense.startDate || expense.date;
               dateKey = format(new Date(startDate), "yyyy-MM-dd");
            }

            const weeklyKey = `${expense.recurringExpenseId}-${dateKey}`;
            weeklyExpensesByRecurringIdAndDate.set(weeklyKey, expense);
         }
      }

      // For weekly expenses or expenses with start/end dates, use a different key format
      if (expense.frequency === "weekly") {
         if (expense.weeklyChargeType === "one-time") {
            // For one-time weekly expenses, use description and exact date
            const key = `${expense.description.toLowerCase()}-${format(
               new Date(expense.date),
               "yyyy-MM-dd"
            )}`;
            uniqueExpenses.set(key, {
               ...expense,
               status: expense.status || "scheduled",
            });
            // Track that this expense is scheduled for this date
            if (expense.status !== "projected") {
               scheduledExpenses.add(key);
            }
         } else {
            // For spread weekly expenses, use the date range
            const key = `${expense.description.toLowerCase()}-${format(
               new Date(expense.startDate || expense.date),
               "yyyy-MM-dd"
            )}-${format(
               new Date(expense.endDate || addDays(new Date(expense.date), 6)),
               "yyyy-MM-dd"
            )}`;
            uniqueExpenses.set(key, {
               ...expense,
               status: expense.status || "scheduled",
            });
         }
      } else {
         // For non-weekly expenses, track both the full key and just the description
         const key = `${expense.description.toLowerCase()}-${format(
            new Date(expense.date),
            "yyyy-MM-dd"
         )}`;
         uniqueExpenses.set(key, {
            ...expense,
            status: expense.status || "scheduled",
         });
         // Track that this expense is scheduled for this date
         if (expense.status !== "projected") {
            scheduledExpenses.add(key);
         }
      }
   });

   // Then add expected expenses only if there isn't an actual expense for that date and description
   // or with a matching recurringExpenseId
   if (expectedExpenses && Array.isArray(expectedExpenses)) {
      expectedExpenses.forEach((expected) => {
         // For weekly expenses, only filter out if we have a matching recurringExpenseId AND date range
         if (expected.recurringExpenseId && expected.frequency === "weekly") {
            let dateKey;
            if (expected.weeklyChargeType === "one-time") {
               dateKey = format(new Date(expected.date), "yyyy-MM-dd");
            } else {
               // For spread weekly expenses, use start date
               const startDate = expected.startDate || expected.date;
               dateKey = format(new Date(startDate), "yyyy-MM-dd");
            }

            const weeklyKey = `${expected.recurringExpenseId}-${dateKey}`;

            // Skip only if we have this specific week with this recurringExpenseId
            if (weeklyExpensesByRecurringIdAndDate.has(weeklyKey)) {
               return;
            }
         }
         // For non-weekly expenses, use the original check
         else if (
            expected.recurringExpenseId &&
            expensesByRecurringId.has(expected.recurringExpenseId) &&
            expected.frequency !== "weekly"
         ) {
            // Skip this expected expense as we already have an actual expense with this recurringExpenseId
            return;
         }

         const expectedDate = new Date(expected.date);

         if (expected.frequency === "weekly") {
            if (expected.weeklyChargeType === "one-time") {
               // For one-time weekly expenses, use the same key format as actual expenses
               const key = `${expected.description.toLowerCase()}-${format(
                  expectedDate,
                  "yyyy-MM-dd"
               )}`;

               // Skip if this expense is already scheduled for this date
               if (scheduledExpenses.has(key)) {
                  return;
               }

               // Only add if there isn't already a non-projected expense for this date
               const existingExpense = uniqueExpenses.get(key);
               if (!existingExpense || existingExpense.status === "projected") {
                  uniqueExpenses.set(key, {
                     ...expected,
                     status: "projected",
                     date: expectedDate,
                     amountAssigned: expected.amountAssigned || 0,
                     amountSpent: expected.amountSpent || 0,
                     amountAvailable:
                        (expected.amountAssigned || 0) -
                        Math.abs(expected.amountSpent || 0),
                     weeklyChargeType: "one-time",
                  });
               }
            } else {
               // For spread weekly expenses, use the date range
               const startDate = expected.startDate || expected.date;
               const endDate =
                  expected.endDate || addDays(new Date(startDate), 6);

               const key = `${expected.description.toLowerCase()}-${format(
                  new Date(startDate),
                  "yyyy-MM-dd"
               )}-${format(new Date(endDate), "yyyy-MM-dd")}`;

               // Only add if there isn't already a non-projected expense for this period
               const existingExpense = uniqueExpenses.get(key);
               if (!existingExpense || existingExpense.status === "projected") {
                  uniqueExpenses.set(key, {
                     ...expected,
                     status: "projected",
                     startDate: startDate,
                     endDate: endDate,
                     date: startDate,
                     amountAssigned: expected.amountAssigned || 0,
                     amountSpent: expected.amountSpent || 0,
                     amountAvailable:
                        (expected.amountAssigned || 0) -
                        Math.abs(expected.amountSpent || 0),
                     weeklyChargeType: "spread",
                  });
               }
            }
         } else {
            const key = `${expected.description.toLowerCase()}-${format(
               expectedDate,
               "yyyy-MM-dd"
            )}`;
            // Only add if there isn't already a non-projected expense for this date
            const existingExpense = uniqueExpenses.get(key);
            if (!existingExpense || existingExpense.status === "projected") {
               uniqueExpenses.set(key, {
                  ...expected,
                  status: "projected",
                  amountAssigned: expected.amountAssigned || 0,
                  amountSpent: expected.amountSpent || 0,
                  amountAvailable:
                     (expected.amountAssigned || 0) -
                     Math.abs(expected.amountSpent || 0),
               });
            }
         }
      });
   }

   // Convert Map back to array and sort by date
   return Array.from(uniqueExpenses.values()).sort((a, b) => {
      // Handle mixed date formats (strings and Date objects) and ensure we have valid dates
      let dateA = a.startDate || a.date;
      let dateB = b.startDate || b.date;

      // Convert Date objects to YYYY-MM-DD strings if needed
      if (dateA instanceof Date) {
         dateA = dateA.toISOString().split("T")[0];
      }
      if (dateB instanceof Date) {
         dateB = dateB.toISOString().split("T")[0];
      }

      // Ensure we have strings for comparison
      if (typeof dateA !== "string" || typeof dateB !== "string") {
         return 0; // Keep original order if dates are invalid
      }

      return dateA.localeCompare(dateB);
   });
};

// Calculate available amount for an expense
export const calculateAvailableAmount = (amountAssigned, amountSpent) => {
   return Number(((amountAssigned || 0) + (amountSpent || 0)).toFixed(2));
};

// Handle collecting available amounts from expenses
export const handleCollectAmount = async ({
   expense,
   onExpensesChange,
   onAlertsRefresh,
   onRefreshBalances,
   setIsProcessing,
   setShowAvailableDropdown,
}) => {
   try {
      setIsProcessing(true);
      if (expense.isAggregated) {
         let totalCollected = 0;

         // Create a copy of all sub-expenses to track updates
         const updatedSubExpenses = [...expense.expenses];

         for (const weekExpense of expense.expenses) {
            const availableAmount = calculateAvailableAmount(
               weekExpense.amountAssigned,
               weekExpense.amountSpent
            );
            if (availableAmount <= 0) continue;

            const amountToCollect = availableAmount;
            const newAssignedAmount =
               weekExpense.amountAssigned - amountToCollect;

            // Update the sub-expense in our tracking array
            const subExpenseIndex = updatedSubExpenses.findIndex(
               (subExp) => subExp._id === weekExpense._id
            );
            if (subExpenseIndex !== -1) {
               updatedSubExpenses[subExpenseIndex] = {
                  ...weekExpense,
                  amountAssigned: newAssignedAmount,
               };
            }

            // Optimistically update the entire expense structure
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((parentExpense) => {
                  if (parentExpense._id === expense._id) {
                     return {
                        ...parentExpense,
                        expenses: parentExpense.expenses.map((subExp) =>
                           subExp._id === weekExpense._id
                              ? {
                                   ...subExp,
                                   amountAssigned: newAssignedAmount,
                                }
                              : subExp
                        ),
                     };
                  }
                  return parentExpense;
               })
            );

            const response = await fetch(`/api/expenses/${weekExpense._id}`, {
               method: "PUT",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  amountAssigned: newAssignedAmount,
                  updateUserAssigned: -amountToCollect,
               }),
            });

            if (!response.ok) {
               throw new Error("Failed to update expense");
            }

            totalCollected += amountToCollect;
            await new Promise((resolve) => setTimeout(resolve, 100));
         }

         // Final state update with all changes
         onExpensesChange((prevExpenses) =>
            prevExpenses.map((parentExpense) =>
               parentExpense._id === expense._id
                  ? {
                       ...parentExpense,
                       expenses: updatedSubExpenses,
                    }
                  : parentExpense
            )
         );

         // Update store with collected amount (negative because we're removing assigned)
         const { updateAssignedAmount } = useBalanceStore.getState();
         updateAssignedAmount(-totalCollected);
      } else {
         const availableAmount = calculateAvailableAmount(
            expense.amountAssigned,
            expense.amountSpent
         );
         const amountToCollect = availableAmount;
         const newAssignedAmount = expense.amountAssigned - amountToCollect;

         // For sub-expenses of an aggregated expense
         if (expense.parentId) {
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((parentExpense) => {
                  if (parentExpense._id === expense.parentId) {
                     const updatedSubExpenses = parentExpense.expenses.map(
                        (subExp) =>
                           subExp._id === expense._id
                              ? {
                                   ...subExp,
                                   amountAssigned: newAssignedAmount,
                                }
                              : subExp
                     );
                     return {
                        ...parentExpense,
                        expenses: updatedSubExpenses,
                     };
                  }
                  return parentExpense;
               })
            );
         } else {
            // For regular non-aggregated expenses
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((e) =>
                  e._id === expense._id
                     ? {
                          ...e,
                          amountAssigned: newAssignedAmount,
                       }
                     : e
               )
            );
         }

         const response = await fetch(`/api/expenses/${expense._id}`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               amountAssigned: newAssignedAmount,
               updateUserAssigned: -amountToCollect,
            }),
         });

         if (!response.ok) {
            throw new Error("Failed to update expense");
         }

         // Get the server response and update with the actual data if different
         const serverExpense = await response.json();
         if (
            JSON.stringify(serverExpense) !==
            JSON.stringify({ ...expense, amountAssigned: newAssignedAmount })
         ) {
            // Update with server response, maintaining the nested structure
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((e) => {
                  if (e._id === (expense.parentId || expense._id)) {
                     if (expense.parentId) {
                        return {
                           ...e,
                           expenses: e.expenses.map((subExp) =>
                              subExp._id === expense._id
                                 ? serverExpense
                                 : subExp
                           ),
                        };
                     }
                     return serverExpense;
                  }
                  return e;
               })
            );
         }

         // Update store with collected amount (negative because we're removing assigned)
         const { updateAssignedAmount } = useBalanceStore.getState();
         updateAssignedAmount(-amountToCollect);
      }

      if (onAlertsRefresh) {
         onAlertsRefresh();
      }

      onRefreshBalances();
   } catch (error) {
      console.error("Error collecting amount:", error);
      // Revert optimistic update on error
      onExpensesChange((prevExpenses) =>
         prevExpenses.map((e) => {
            if (e._id === (expense.parentId || expense._id)) {
               if (expense.parentId) {
                  return {
                     ...e,
                     expenses: e.expenses.map((subExp) =>
                        subExp._id === expense._id ? expense : subExp
                     ),
                  };
               }
               return expense;
            }
            return e;
         })
      );

      // Revert the store changes
      const { updateAssignedAmount } = useBalanceStore.getState();
      if (expense.isAggregated) {
         // Calculate what would have been collected
         let totalCollected = 0;
         expense.expenses.forEach((weekExpense) => {
            const availableAmount = calculateAvailableAmount(
               weekExpense.amountAssigned,
               weekExpense.amountSpent
            );
            if (availableAmount > 0) {
               totalCollected += availableAmount;
            }
         });
         // Revert the change by passing positive amount (opposite of -totalCollected)
         updateAssignedAmount(totalCollected);
      } else {
         // For individual expense, revert by passing positive amount
         const amountToCollect = calculateAvailableAmount(
            expense.amountAssigned,
            expense.amountSpent
         );
         updateAssignedAmount(amountToCollect);
      }
   } finally {
      setIsProcessing(false);
      setShowAvailableDropdown(null);
   }
};

// Add these utility functions for expense updates
export const createDebouncedExpenseUpdater = (debounceMs = 300) => {
   const updateQueue = new Map();
   const processingQueue = new Map();

   const processUpdate = async (
      key,
      updateData,
      customEndpoint = null,
      method = "PUT"
   ) => {
      try {
         // Use custom endpoint if provided, otherwise use the default expense endpoint
         const url = customEndpoint || `/api/expenses/${key}`;

         const response = await fetch(url, {
            method,
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(updateData),
         });

         if (!response.ok) {
            throw new Error(`Failed to update expense: ${response.statusText}`);
         }

         return await response.json();
      } catch (error) {
         console.error("Error updating expense:", error);
         throw error;
      }
   };

   const debouncedUpdate = debounce(async (key) => {
      const updates = updateQueue.get(key);
      if (!updates || processingQueue.get(key)) return;

      processingQueue.set(key, true);
      updateQueue.delete(key);

      try {
         // Extract the endpoint and method if provided
         const { endpoint, method, updates: queuedUpdates } = updates;

         // Combine all queued updates
         const combinedUpdate = queuedUpdates.reduce(
            (acc, update) => ({
               ...acc,
               ...update.data,
               // Sum up any updateUserAssigned values
               updateUserAssigned:
                  (acc.updateUserAssigned || 0) +
                  (update.data.updateUserAssigned || 0),
            }),
            {}
         );

         const result = await processUpdate(
            key,
            combinedUpdate,
            endpoint,
            method
         );
         queuedUpdates.forEach((update) => update.onSuccess?.(result));
      } catch (error) {
         updates.updates.forEach((update) => update.onError?.(error));
      } finally {
         processingQueue.delete(key);
      }
   }, debounceMs);

   return {
      queueUpdate: (idOrEndpoint, updateData, options = {}) => {
         const { onSuccess, onError, method = "PUT" } = options;

         // Check if the first parameter is a custom endpoint
         const isCustomEndpoint =
            typeof idOrEndpoint === "string" &&
            idOrEndpoint.startsWith("/api/");

         // Generate a key - either the expense ID or a unique key for custom endpoints
         const key = isCustomEndpoint
            ? `${idOrEndpoint}_${Date.now()}` // Unique key for custom endpoints
            : idOrEndpoint; // Use ID for regular expense updates

         // Get current queue for this key or initialize it
         const currentData = updateQueue.get(key) || {
            endpoint: isCustomEndpoint ? idOrEndpoint : null,
            method,
            updates: [],
         };

         // Add the update to the queue
         currentData.updates.push({
            data: updateData,
            onSuccess,
            onError,
         });

         updateQueue.set(key, currentData);
         debouncedUpdate(key);
      },
      cancelPendingUpdates: () => {
         debouncedUpdate.cancel();
         updateQueue.clear();
         processingQueue.clear();
      },
   };
};

// Helper function to validate expense updates
export const validateExpenseUpdate = (
   newValue,
   currentValue,
   type = "amount"
) => {
   if (type === "amount") {
      const value = parseFloat(newValue);
      return !isNaN(value) && value >= 0;
   }
   return true;
};

// Calculate the relative time until a future expense is due
export const calculateTimeUntilDue = (expenseDate, dateRange) => {
   if (!dateRange || !expenseDate) return "";

   const expenseDateObj =
      expenseDate instanceof Date ? expenseDate : new Date(expenseDate);
   const currentPeriodEnd =
      dateRange.end instanceof Date ? dateRange.end : new Date(dateRange.end);

   // Calculate the time difference based on view type
   if (dateRange.viewType === "month") {
      // Calculate months difference
      const monthsDiff =
         (expenseDateObj.getFullYear() - currentPeriodEnd.getFullYear()) * 12 +
         (expenseDateObj.getMonth() - currentPeriodEnd.getMonth());

      if (monthsDiff === 1) return "1 month away";
      return `${monthsDiff} months away`;
   }
   // If in pay period view, calculate by pay periods
   else if (dateRange.viewType === "payPeriod") {
      // Calculate days difference
      const daysDiff = Math.ceil(
         (expenseDateObj - currentPeriodEnd) / (1000 * 60 * 60 * 24)
      );

      // Get the pay period type from userPaySettings
      const payPeriodType = dateRange.userPaySettings?.payPeriod || "biweekly";

      // Determine how many pay periods this represents
      let periodsDiff = 1;
      let periodLabel = "period";

      if (payPeriodType === "weekly") {
         periodsDiff = Math.ceil(daysDiff / 7);
         periodLabel = "week";
      } else if (payPeriodType === "biweekly") {
         periodsDiff = Math.ceil(daysDiff / 14);
         periodLabel = "pay period";
      } else if (payPeriodType === "semimonthly") {
         periodsDiff = Math.ceil(daysDiff / 15);
         periodLabel = "pay period";
      } else if (payPeriodType === "monthly") {
         periodsDiff = Math.ceil(daysDiff / 30);
         periodLabel = "month";
      }

      if (periodsDiff === 1) return `1 ${periodLabel} away`;
      return `${periodsDiff} ${periodLabel}s away`;
   }

   // For other view types, calculate in days
   const daysDiff = Math.ceil(
      (expenseDateObj - currentPeriodEnd) / (1000 * 60 * 60 * 24)
   );

   if (daysDiff === 1) return "1 day away";
   return `${daysDiff} days away`;
};

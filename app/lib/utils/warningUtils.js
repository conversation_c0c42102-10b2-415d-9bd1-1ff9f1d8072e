import {
   startOfDay,
   endOfDay,
   addDays,
   addWeeks,
   addMonths,
   format,
   isBefore,
   isAfter,
   eachDayOfInterval,
} from "date-fns";
import { findMostRecentPayDate } from "./payPeriodUtils";

/**
 * Calculate cash flow warnings for a user based on their pay periods
 * @param {Object} user - User object with recurring incomes/expenses and accounts
 * @param {Array} scheduledIncomes - Scheduled incomes from database
 * @param {Array} scheduledExpenses - Scheduled expenses from database
 * @param {Array} debts - User's debts for calculating debt payments
 * @param {Date} currentDate - Current date to start calculations from
 * @param {number} periodsAhead - Number of pay periods to check ahead
 * @returns {Object|null} Warning object if negative cash flow found, null otherwise
 */
export const calculateCashFlowWarnings = (
   user,
   scheduledIncomes = [],
   scheduledExpenses = [],
   debts = [],
   currentDate = new Date(),
   periodsAhead = 4
) => {
   try {
      // Get user's main pay period settings
      const paySettings = getUserPaySettings(user);
      if (!paySettings) {
         return null; // No pay settings available
      }

      // Calculate pay periods to check
      const payPeriods = calculatePayPeriods(
         paySettings,
         currentDate,
         periodsAhead
      );

      // Check each pay period for negative cash flow
      for (let i = 0; i < payPeriods.length; i++) {
         const period = payPeriods[i];
         const cashFlow = calculatePeriodCashFlow(
            period,
            user,
            scheduledIncomes,
            scheduledExpenses,
            debts
         );

         // Return warning for the first negative period
         if (cashFlow.netFlow < 0) {
            return {
               periodNumber: i + 1,
               period: period,
               cashFlow: cashFlow,
               warning: `Negative cash flow of $${Math.abs(
                  cashFlow.netFlow
               ).toFixed(2)} expected ${
                  i + 1 === 1
                     ? "in your next pay period"
                     : `in ${i + 1} pay periods`
               }`,
               severity: "warning",
            };
         }
      }

      return null; // No warnings found
   } catch (error) {
      console.error("Error calculating cash flow warnings:", error);
      return null;
   }
};

/**
 * Get user's pay period settings from their main income
 */
const getUserPaySettings = (user) => {
   if (!user.mainIncomeId || !user.recurringIncomes?.length) {
      return null;
   }

   const mainIncome = user.recurringIncomes.find(
      (income) =>
         income._id === user.mainIncomeId ||
         income._id.toString() === user.mainIncomeId.toString()
   );

   if (!mainIncome) {
      return null;
   }

   return {
      payPeriod: mainIncome.payPeriod,
      payDay: mainIncome.payDay,
      payDayOfWeek: mainIncome.payDayOfWeek,
      payWeekDay: mainIncome.payWeekDay,
      lastPaymentDate: mainIncome.lastPaymentDate,
   };
};

/**
 * Calculate the next X pay periods from current date
 */
const calculatePayPeriods = (paySettings, currentDate, periodsCount) => {
   const periods = [];
   let periodStart = findMostRecentPayDate(
      currentDate,
      paySettings.payPeriod,
      paySettings.payDay,
      paySettings.payDayOfWeek,
      paySettings.lastPaymentDate
   );

   // If the most recent pay date is in the past, move to the next one
   if (isBefore(periodStart, startOfDay(currentDate))) {
      periodStart = getNextPayDate(periodStart, paySettings);
   }

   for (let i = 0; i < periodsCount; i++) {
      const periodEnd = getPeriodEndDate(periodStart, paySettings.payPeriod);

      periods.push({
         number: i + 1,
         startDate: startOfDay(periodStart),
         endDate: endOfDay(periodEnd),
         label: `${format(periodStart, "MMM d")} - ${format(
            periodEnd,
            "MMM d, yyyy"
         )}`,
      });

      // Move to next period
      periodStart = getNextPayDate(periodStart, paySettings);
   }

   return periods;
};

/**
 * Get the next pay date based on pay period
 */
const getNextPayDate = (currentDate, paySettings) => {
   const { payPeriod } = paySettings;

   switch (payPeriod) {
      case "weekly":
         return addWeeks(currentDate, 1);
      case "biweekly":
         return addDays(currentDate, 14);
      case "semimonthly":
         // For semimonthly, alternate between 1st and 15th
         const day = currentDate.getDate();
         if (day === 1) {
            return new Date(
               currentDate.getFullYear(),
               currentDate.getMonth(),
               15
            );
         } else {
            return new Date(
               currentDate.getFullYear(),
               currentDate.getMonth() + 1,
               1
            );
         }
      case "monthly":
         return addMonths(currentDate, 1);
      default:
         return addDays(currentDate, 14); // Default to biweekly
   }
};

/**
 * Get the end date of a pay period
 */
const getPeriodEndDate = (startDate, payPeriod) => {
   switch (payPeriod) {
      case "weekly":
         return addDays(startDate, 6);
      case "biweekly":
         return addDays(startDate, 13);
      case "semimonthly":
         const day = startDate.getDate();
         if (day === 1) {
            return new Date(startDate.getFullYear(), startDate.getMonth(), 14);
         } else {
            return new Date(
               startDate.getFullYear(),
               startDate.getMonth() + 1,
               0
            ); // Last day of month
         }
      case "monthly":
         const nextMonth = addMonths(startDate, 1);
         return addDays(nextMonth, -1);
      default:
         return addDays(startDate, 13); // Default to biweekly
   }
};

/**
 * Calculate cash flow for a specific pay period
 */
const calculatePeriodCashFlow = (
   period,
   user,
   scheduledIncomes,
   scheduledExpenses,
   debts
) => {
   const income = calculatePeriodIncome(period, user, scheduledIncomes);
   const expenses = calculatePeriodExpenses(
      period,
      user,
      scheduledExpenses,
      debts
   );

   return {
      income: income,
      expenses: expenses,
      assigned: expenses.assigned,
      overspend: expenses.overspend,
      netFlow:
         income.total +
         expenses.assigned.total -
         expenses.total -
         expenses.overspend.total,
      details: {
         incomeBreakdown: income.breakdown,
         expenseBreakdown: expenses.breakdown,
         assignedBreakdown: expenses.assigned.breakdown,
         overspendBreakdown: expenses.overspend.breakdown,
      },
   };
};

/**
 * Calculate total income for a period (projected + scheduled)
 */
const calculatePeriodIncome = (period, user, scheduledIncomes) => {
   let total = 0;
   const breakdown = [];

   // Add projected income from recurring incomes
   if (user.recurringIncomes?.length) {
      const days = eachDayOfInterval({
         start: period.startDate,
         end: period.endDate,
      });

      for (const day of days) {
         for (const income of user.recurringIncomes) {
            if (!income.enabled) continue;

            if (shouldProcessRecurringIncome(income, day)) {
               // Check if this income is already scheduled to avoid double counting
               // Match on description and date, with optional recurringId check
               const isScheduled = scheduledIncomes.some((scheduled) => {
                  const sameDescription =
                     scheduled.description === income.description;
                  // Compare date strings directly instead of using Date objects
                  const scheduledDateStr = scheduled.date; // Already YYYY-MM-DD string
                  const dayStr = format(day, "yyyy-MM-dd");
                  const sameDate = scheduledDateStr === dayStr;
                  const sameAmount =
                     Math.abs(
                        (scheduled.expectedAmount ||
                           scheduled.receivedAmount ||
                           0) - income.payAmount
                     ) < 0.01;

                  // Primary matching: description + date + amount
                  const primaryMatch =
                     sameDescription && sameDate && sameAmount;

                  // Secondary matching: recurringId if it exists
                  const recurringIdMatch =
                     scheduled.recurringId === income._id?.toString();

                  return primaryMatch || recurringIdMatch;
               });

               if (!isScheduled) {
                  total += income.payAmount;
                  breakdown.push({
                     type: "projected",
                     description: income.description,
                     amount: income.payAmount,
                     date: day,
                  });
               }
            }
         }
      }
   }

   // Add scheduled incomes that fall within the period
   for (const income of scheduledIncomes) {
      // Compare date strings directly for efficiency
      const incomeDateStr = income.date; // Already YYYY-MM-DD string
      const periodStartStr = format(period.startDate, "yyyy-MM-dd");
      const periodEndStr = format(period.endDate, "yyyy-MM-dd");

      if (incomeDateStr >= periodStartStr && incomeDateStr <= periodEndStr) {
         total += income.expectedAmount || income.receivedAmount || 0;
         breakdown.push({
            type: "scheduled",
            description: income.description,
            amount: income.expectedAmount || income.receivedAmount || 0,
            date: new Date(incomeDateStr), // Convert to Date for breakdown display
         });
      }
   }

   return { total, breakdown };
};

/**
 * Calculate total expenses for a period (projected + scheduled + assigned amounts)
 */
const calculatePeriodExpenses = (period, user, scheduledExpenses, debts) => {
   let total = 0;
   let assignedTotal = 0;
   let overspendTotal = 0;
   const breakdown = [];
   const assignedBreakdown = [];
   const overspendBreakdown = [];

   // Add projected expenses from recurring expenses
   if (user.recurringExpenses?.length) {
      const days = eachDayOfInterval({
         start: period.startDate,
         end: period.endDate,
      });

      for (const day of days) {
         for (const expense of user.recurringExpenses) {
            if (!expense.enabled) continue;

            if (shouldProcessRecurringExpense(expense, day)) {
               // Check if this expense is already scheduled to avoid double counting
               // Match on description and date, with optional recurringExpenseId check
               const isScheduled = scheduledExpenses.some((scheduled) => {
                  const sameDescription =
                     scheduled.description === expense.name;
                  // Compare date strings directly instead of using Date objects
                  const scheduledDateStr = scheduled.date; // Already YYYY-MM-DD string
                  const dayStr = format(day, "yyyy-MM-dd");
                  const sameDate = scheduledDateStr === dayStr;
                  const sameAmount =
                     Math.abs((scheduled.amountDue || 0) - expense.amount) <
                     0.01;

                  // Primary matching: description + date + amount
                  const primaryMatch =
                     sameDescription && sameDate && sameAmount;

                  // Secondary matching: recurringExpenseId if it exists
                  const recurringIdMatch =
                     scheduled.recurringExpenseId === expense.id;

                  return primaryMatch || recurringIdMatch;
               });

               if (!isScheduled) {
                  total += expense.amount;
                  breakdown.push({
                     type: "projected",
                     description: expense.name,
                     amount: expense.amount,
                     date: day,
                  });
               }
            }
         }
      }
   }

   // Add scheduled expenses that fall within the period
   for (const expense of scheduledExpenses) {
      // Use string comparison for date filtering
      const expenseDateStr = expense.date; // Already YYYY-MM-DD string
      const periodStartStr = format(period.startDate, "yyyy-MM-dd");
      const periodEndStr = format(period.endDate, "yyyy-MM-dd");

      if (expenseDateStr >= periodStartStr && expenseDateStr <= periodEndStr) {
         const dueAmount = expense.amountDue || 0;
         const spentAmount = Math.abs(expense.amountSpent || 0);
         const assignedAmount = expense.amountAssigned || 0;

         // Add base due amount to total
         total += dueAmount;
         breakdown.push({
            type: "scheduled",
            description: expense.description,
            amount: dueAmount,
            date: new Date(expenseDateStr), // Convert to Date for breakdown display
         });

         // Track overspend separately (when spent > due)
         if (spentAmount > dueAmount) {
            const overspendAmount = spentAmount - dueAmount;
            overspendTotal += overspendAmount;
            overspendBreakdown.push({
               type: "overspend",
               description: expense.description,
               amount: overspendAmount,
               date: new Date(expenseDateStr), // Convert to Date for breakdown display
            });
         }

         // Track assigned amounts separately
         if (assignedAmount > 0) {
            assignedTotal += assignedAmount;
            assignedBreakdown.push({
               type: "assigned",
               description: expense.description,
               amount: assignedAmount,
               date: new Date(expenseDateStr), // Convert to Date for breakdown display
            });
         }
      }
   }

   // Add debt payments that fall within the period
   if (debts?.length) {
      const days = eachDayOfInterval({
         start: period.startDate,
         end: period.endDate,
      });

      for (const day of days) {
         for (const debt of debts) {
            if (!debt.active) continue;

            // Debt payments are always monthly on the due date
            if (shouldProcessMonthlyDebt(debt, day)) {
               // Check if this debt payment is already scheduled as an expense to avoid double counting
               const isAlreadyScheduled = scheduledExpenses.some(
                  (scheduled) => {
                     const scheduledDateStr = scheduled.date; // Already YYYY-MM-DD string
                     const dayStr = format(day, "yyyy-MM-dd");
                     return (
                        scheduled.isDebtPayment === true &&
                        scheduled.debtId === debt._id?.toString() &&
                        scheduledDateStr === dayStr
                     );
                  }
               );

               if (!isAlreadyScheduled) {
                  total += debt.minimumPayment;
                  breakdown.push({
                     type: "debt",
                     description: `${debt.lender} - Payment`,
                     amount: debt.minimumPayment,
                     date: day,
                  });
               }
            }
         }
      }
   }

   return {
      total,
      breakdown,
      assigned: {
         total: assignedTotal,
         breakdown: assignedBreakdown,
      },
      overspend: {
         total: overspendTotal,
         breakdown: overspendBreakdown,
      },
   };
};

/**
 * Check if a recurring income should be processed on a given day
 */
const shouldProcessRecurringIncome = (income, targetDate) => {
   return shouldProcessRecurringItem(
      income,
      targetDate,
      income.payPeriod,
      income.payDay,
      income.payWeekDay,
      income.lastPaymentDate
   );
};

/**
 * Check if a recurring expense should be processed on a given day
 */
const shouldProcessRecurringExpense = (expense, targetDate) => {
   return shouldProcessRecurringItem(
      expense,
      targetDate,
      expense.frequency,
      expense.dueDay
   );
};

/**
 * Check if a monthly debt payment should be processed on a given day
 */
const shouldProcessMonthlyDebt = (debt, targetDate) => {
   return shouldProcessRecurringItem(debt, targetDate, "monthly", debt.dueDate);
};

/**
 * Generic function to determine if a recurring item should be processed on a given day
 * (Reused from existing codebase logic)
 */
const shouldProcessRecurringItem = (
   item,
   targetDate,
   frequency,
   dueDay,
   dayOfWeek = null,
   lastPaymentDate = null
) => {
   const day = targetDate.getDate();
   const dayOfWeekNum = targetDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
   const month = targetDate.getMonth();

   switch (frequency) {
      case "monthly":
         // Handle end-of-month edge cases
         const targetDay = parseInt(dueDay);
         const lastDayOfMonth = new Date(
            targetDate.getFullYear(),
            month + 1,
            0
         ).getDate();
         const effectiveDay = Math.min(targetDay, lastDayOfMonth);
         return day === effectiveDay;

      case "weekly":
         // For weekly items, use dayOfWeek if provided (for incomes), otherwise use dueDay
         if (dayOfWeek) {
            const dayMap = {
               Sunday: 0,
               Monday: 1,
               Tuesday: 2,
               Wednesday: 3,
               Thursday: 4,
               Friday: 5,
               Saturday: 6,
            };
            return dayOfWeekNum === dayMap[dayOfWeek];
         } else {
            // For expenses, dueDay might be a number (1-7) or day name
            if (isNaN(parseInt(dueDay))) {
               const dayMap = {
                  Sunday: 0,
                  Monday: 1,
                  Tuesday: 2,
                  Wednesday: 3,
                  Thursday: 4,
                  Friday: 5,
                  Saturday: 6,
               };
               return dayOfWeekNum === dayMap[dueDay];
            } else {
               // dueDay is a number (1-7, where 1 = Monday, 7 = Sunday)
               const adjustedDay =
                  parseInt(dueDay) === 7 ? 0 : parseInt(dueDay);
               return dayOfWeekNum === adjustedDay;
            }
         }

      case "biweekly":
         // For biweekly, we need to check against the lastPaymentDate if available
         if (lastPaymentDate) {
            const lastPayment = new Date(lastPaymentDate);
            const daysSinceLastPayment = Math.floor(
               (targetDate.getTime() - lastPayment.getTime()) /
                  (1000 * 60 * 60 * 24)
            );
            return daysSinceLastPayment > 0 && daysSinceLastPayment % 14 === 0;
         }
         return false;

      case "quarterly":
         // Quarterly on the same day of month every 3 months
         const targetQuarterlyDay = parseInt(dueDay);
         const lastDayOfQuarter = new Date(
            targetDate.getFullYear(),
            month + 1,
            0
         ).getDate();
         const effectiveQuarterlyDay = Math.min(
            targetQuarterlyDay,
            lastDayOfQuarter
         );
         return day === effectiveQuarterlyDay && month % 3 === 0;

      case "annually":
         // Annually on the same day and month
         const targetAnnualDay = parseInt(dueDay);
         const targetMonth = item.dueMonth ? parseInt(item.dueMonth) - 1 : 0; // dueMonth is 1-based
         return day === targetAnnualDay && month === targetMonth;

      default:
         return false;
   }
};

/**
 * Check if two dates are the same day
 */
const isSameDay = (date1, date2) => {
   return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
   );
};

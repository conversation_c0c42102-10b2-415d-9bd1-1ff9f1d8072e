import { format, parseISO } from "date-fns";

// Helper function to round numbers to two decimal places
export const roundToTwoDecimals = (num) => {
   return Number((Math.round(num * 100) / 100).toFixed(2));
};

// Format amount for display
export const formatAmount = (amount) => {
   const value = parseFloat(amount || 0);
   const formattedValue = isNaN(value)
      ? "0.00"
      : Math.abs(value).toLocaleString("en-US", {
           minimumFractionDigits: 2,
           maximumFractionDigits: 2,
        });
   return {
      value,
      formattedValue,
      isNegative: value < 0,
   };
};

// Get status dot styling
export const getStatusDotStyle = (status) => {
   const dots = {
      projected: "bg-blue-400 dark:bg-blue-500",
      scheduled: "bg-yellow-400 dark:bg-yellow-500",
      received: "bg-green-400 dark:bg-green-500",
      hidden: "bg-gray-400 dark:bg-gray-500",
   };
   return dots[status] || dots.scheduled;
};

// Aggregate incomes by description
export const aggregateIncomesByDescription = (incomes, dateRange) => {
   // Check if we should aggregate based on view type or date range span
   const shouldAggregate =
      dateRange?.viewType === "payPeriod" ||
      (dateRange?.start &&
         dateRange?.end &&
         Math.abs(dateRange.end - dateRange.start) >= 25 * 24 * 60 * 60 * 1000); // 25+ days suggests monthly view

   // If not in aggregation-eligible view, return the incomes as is
   if (!shouldAggregate) return incomes;

   const groups = new Map();
   const processedDates = new Set();

   // First pass: count occurrences of each description
   const descriptionCounts = new Map();
   incomes.forEach((income) => {
      const key = income.description.toLowerCase();
      descriptionCounts.set(key, (descriptionCounts.get(key) || 0) + 1);
   });

   // Second pass: group only if there are multiple incomes with same description
   incomes.forEach((income) => {
      const key = income.description.toLowerCase();
      const dateKey = `${format(
         income.date instanceof Date ? income.date : parseISO(income.date),
         "yyyy-MM-dd"
      )}-${key}`;

      if (processedDates.has(dateKey)) return;
      processedDates.add(dateKey);

      // If there's only one income with this description, don't group it
      if (descriptionCounts.get(key) === 1) {
         groups.set(dateKey, {
            ...income,
            isAggregated: false,
         });
         return;
      }

      if (!groups.has(key)) {
         groups.set(key, {
            _id: income._id,
            description: income.description,
            category: income.category || "Salary",
            status: "projected",
            expectedAmount: 0,
            receivedAmount: 0,
            date: income.date,
            incomes: [],
            isAggregated: true,
            hasProjected: false,
            hasScheduled: false,
            hasReceived: false,
         });
      }

      const group = groups.get(key);

      // Add the income to the group's incomes array
      const groupIncome = {
         ...income,
         expectedAmount: roundToTwoDecimals(income.expectedAmount || 0),
         receivedAmount: roundToTwoDecimals(income.receivedAmount || 0),
      };
      group.incomes.push(groupIncome);

      // Update group totals
      group.expectedAmount = roundToTwoDecimals(
         group.expectedAmount + (income.expectedAmount || 0)
      );
      group.receivedAmount = roundToTwoDecimals(
         group.receivedAmount + (income.receivedAmount || 0)
      );

      // Track status
      if (income.status === "projected") {
         group.hasProjected = true;
      } else if (income.status === "scheduled") {
         group.hasScheduled = true;
      } else if (income.status === "received") {
         group.hasReceived = true;
      }

      // Update group status based on individual income statuses
      if (group.hasProjected && group.hasScheduled && group.hasReceived) {
         group.status = "projected-scheduled-received";
      } else if (group.hasProjected && group.hasScheduled) {
         group.status = "projected-scheduled";
      } else if (group.hasProjected && group.hasReceived) {
         group.status = "projected-received";
      } else if (group.hasScheduled && group.hasReceived) {
         group.status = "scheduled-received";
      } else if (group.hasProjected) {
         group.status = "projected";
      } else if (group.hasScheduled) {
         group.status = "scheduled";
      } else if (group.hasReceived) {
         group.status = "received";
      }

      // If all incomes in the group are hidden, mark the group as hidden
      if (group.incomes.every((inc) => inc.isHidden)) {
         group.status = "hidden";
      }
   });

   // Process groups and sort incomes by date
   const processedGroups = Array.from(groups.values()).map((group) => {
      // Only sort if it's an aggregated group
      if (group.isAggregated) {
         // Sort incomes by date
         group.incomes.sort((a, b) => {
            const dateA = a.date instanceof Date ? a.date : parseISO(a.date);
            const dateB = b.date instanceof Date ? b.date : parseISO(b.date);
            return dateA - dateB;
         });
      }
      return group;
   });

   // Sort all items by date
   return processedGroups.sort((a, b) => {
      const dateA = a.date instanceof Date ? a.date : parseISO(a.date);
      const dateB = b.date instanceof Date ? b.date : parseISO(b.date);
      return dateA - dateB;
   });
};

// Calculate totals for visible incomes
export const calculateVisibleTotals = (incomes) => {
   return incomes.reduce(
      (acc, income) => ({
         expected: acc.expected + (income.expectedAmount || 0),
         received: acc.received + (income.receivedAmount || 0),
      }),
      { expected: 0, received: 0 }
   );
};

// Combine and sort incomes
export const combineAndSortIncomes = (incomes, expectedIncomes, dateRange) => {
   // Use a Map to track unique incomes by description and date
   const uniqueIncomes = new Map();

   // First add actual incomes
   if (incomes && Array.isArray(incomes)) {
      incomes.forEach((income) => {
         // Date is already a YYYY-MM-DD string, no need to convert
         const incomeDateStr = income.date;

         // Only include incomes that fall within the date range using string comparison
         if (dateRange?.start && dateRange?.end) {
            const startDateStr = format(dateRange.start, "yyyy-MM-dd");
            const endDateStr = format(dateRange.end, "yyyy-MM-dd");

            if (incomeDateStr < startDateStr || incomeDateStr > endDateStr) {
               return;
            }
         }

         // Create a unique key that includes both date and description
         const key = `${incomeDateStr}-${income.description.toLowerCase()}`;

         uniqueIncomes.set(key, income);
      });
   }

   // Then add expected incomes only if there isn't an actual income for that date and description
   if (expectedIncomes && Array.isArray(expectedIncomes)) {
      expectedIncomes.forEach((expected) => {
         // Expected date is a Date object from generation, convert to string
         const expectedDateStr =
            expected.date instanceof Date
               ? format(expected.date, "yyyy-MM-dd")
               : expected.date;

         // Only include incomes that fall within the date range using string comparison
         if (dateRange?.start && dateRange?.end) {
            const startDateStr = format(dateRange.start, "yyyy-MM-dd");
            const endDateStr = format(dateRange.end, "yyyy-MM-dd");

            if (
               expectedDateStr < startDateStr ||
               expectedDateStr > endDateStr
            ) {
               return;
            }
         }

         // Create a unique key that includes both date and description
         const key = `${expectedDateStr}-${expected.description.toLowerCase()}`;

         if (!uniqueIncomes.has(key)) {
            uniqueIncomes.set(key, {
               ...expected,
               date: expectedDateStr, // Ensure date is stored as string
               status: "projected",
               receivedAmount: 0,
            });
         }
      });
   }

   // Convert Map back to array and sort by date (handle mixed date formats)
   return Array.from(uniqueIncomes.values()).sort((a, b) => {
      let dateA = a.date;
      let dateB = b.date;

      // Convert Date objects to YYYY-MM-DD strings if needed
      if (dateA instanceof Date) {
         dateA = dateA.toISOString().split("T")[0];
      }
      if (dateB instanceof Date) {
         dateB = dateB.toISOString().split("T")[0];
      }

      // Ensure we have strings for comparison
      if (typeof dateA !== "string" || typeof dateB !== "string") {
         return 0; // Keep original order if dates are invalid
      }

      return dateA.localeCompare(dateB);
   });
};

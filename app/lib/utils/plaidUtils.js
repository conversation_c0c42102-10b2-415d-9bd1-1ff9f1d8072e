import { plaidClient } from "../plaid/config";

/**
 * Check if a Plaid item needs re-authentication
 * @param {Object} plaidItem - The plaid item from user document
 * @returns {boolean} - True if item needs re-authentication
 */
export function needsReAuthentication(plaidItem) {
   if (!plaidItem) return false;

   return (
      plaidItem.status === "login_required" ||
      plaidItem.status === "pending_expiration" ||
      plaidItem.status === "pending_disconnect"
   );
}

/**
 * Get user-friendly status message for Plaid connection
 * @param {Object} plaidItem - The plaid item from user document
 * @returns {Object} - Status info with message and severity
 */
export function getConnectionStatusInfo(plaidItem) {
   if (!plaidItem) {
      return {
         status: "unknown",
         message: "Connection status unknown",
         severity: "warning",
         needsAction: false,
      };
   }

   switch (plaidItem.status) {
      case "good":
         return {
            status: "connected",
            message: "Connected",
            severity: "success",
            needsAction: false,
         };

      case "login_required":
         return {
            status: "login_required",
            message: "Login required - Please re-authenticate",
            severity: "error",
            needsAction: true,
         };

      case "pending_expiration":
         return {
            status: "pending_expiration",
            message: "Connection expiring soon - Re-authenticate to continue",
            severity: "warning",
            needsAction: true,
         };

      case "pending_disconnect":
         return {
            status: "pending_disconnect",
            message:
               "Connection will be disconnected soon - Re-authenticate to continue",
            severity: "warning",
            needsAction: true,
         };

      case "error":
         return {
            status: "error",
            message:
               plaidItem.error?.errorMessage ||
               "Connection error - Please try reconnecting",
            severity: "error",
            needsAction: true,
         };

      case "suspended":
         return {
            status: "suspended",
            message: "Connection suspended - Upgrade to reactivate",
            severity: "warning",
            needsAction: false,
         };

      default:
         return {
            status: "unknown",
            message: "Connection status unknown",
            severity: "warning",
            needsAction: false,
         };
   }
}

/**
 * Check Plaid item status via API call
 * @param {string} accessToken - The access token for the item
 * @returns {Promise<Object>} - Item status information
 */
export async function checkPlaidItemStatus(accessToken) {
   try {
      const response = await plaidClient.itemGet({
         access_token: accessToken,
      });

      return {
         success: true,
         item: response.data.item,
         status: response.data.status,
      };
   } catch (error) {
      console.error("Error checking Plaid item status:", error);

      // Check if this is a specific Plaid error that indicates re-auth needed
      if (error.error_code === "ITEM_LOGIN_REQUIRED") {
         console.log("ITEM_LOGIN_REQUIRED detected:", {
            error_code: error.error_code,
            error_message: error.error_message,
            error_type: error.error_type,
            timestamp: new Date().toISOString(),
         });

         return {
            success: false,
            needsReauth: true,
            error: {
               errorCode: error.error_code,
               errorMessage: error.error_message,
               errorType: error.error_type,
            },
         };
      }

      // Handle other common Plaid errors
      const reauthErrorCodes = [
         "ITEM_LOGIN_REQUIRED",
         "USER_SETUP_REQUIRED",
         "PASSWORD_RESET_REQUIRED",
         "INSUFFICIENT_CREDENTIALS",
         "USER_INPUT_TIMEOUT",
         "INVALID_ACCESS_TOKEN",
         "ACCESS_TOKEN_EXPIRED",
      ];

      // Handle 400 errors that might indicate token issues
      if (
         error.response?.status === 400 ||
         reauthErrorCodes.includes(error.error_code)
      ) {
         console.log("Re-authentication required:", {
            error_code: error.error_code,
            error_message: error.error_message,
            error_type: error.error_type,
            timestamp: new Date().toISOString(),
         });

         return {
            success: false,
            needsReauth: true,
            error: {
               errorCode: error.error_code,
               errorMessage: error.error_message,
               errorType: error.error_type,
            },
         };
      }

      return {
         success: false,
         needsReauth: false,
         error: {
            errorCode: error.error_code || "UNKNOWN_ERROR",
            errorMessage:
               error.error_message || "Failed to check connection status",
            errorType: error.error_type || "API_ERROR",
         },
      };
   }
}

/**
 * Check Plaid account status via API call
 * @param {string} accessToken - The access token for the item
 * @returns {Promise<Object>} - Account status information
 */
export async function checkPlaidAccountStatus(accessToken) {
   try {
      const response = await plaidClient.accountsGet({
         access_token: accessToken,
      });

      return {
         success: true,
         accounts: response.data.accounts,
      };
   } catch (error) {
      console.error("Error checking Plaid account status:", error);

      // Check if this is a specific Plaid error that indicates re-auth needed
      const reauthErrorCodes = [
         "ITEM_LOGIN_REQUIRED",
         "USER_SETUP_REQUIRED",
         "PASSWORD_RESET_REQUIRED",
         "INSUFFICIENT_CREDENTIALS",
         "USER_INPUT_TIMEOUT",
         "INVALID_ACCESS_TOKEN",
         "ACCESS_TOKEN_EXPIRED",
      ];

      // Handle 400 errors that might indicate token issues
      if (
         error.response?.status === 400 ||
         reauthErrorCodes.includes(error.error_code)
      ) {
         return {
            success: false,
            needsReauth: true,
            error: {
               errorCode: error.error_code || "INVALID_ACCESS_TOKEN",
               errorMessage:
                  error.error_message || "Access token is invalid or expired",
               errorType: error.error_type || "API_ERROR",
            },
         };
      }

      return {
         success: false,
         needsReauth: false,
         error: {
            errorCode: error.error_code || "UNKNOWN_ERROR",
            errorMessage:
               error.error_message || "Failed to check account status",
            errorType: error.error_type || "API_ERROR",
         },
      };
   }
}

/**
 * Update plaid item status in user document
 * @param {Object} user - User document
 * @param {string} itemId - Plaid item ID
 * @param {string} status - New status
 * @param {Object} error - Error information (optional)
 */
export function updatePlaidItemStatus(user, itemId, status, error = null) {
   const plaidItem = user.plaidItems.find((item) => item.itemId === itemId);

   if (plaidItem) {
      plaidItem.status = status;

      if (error) {
         plaidItem.error = {
            errorCode: error.errorCode,
            errorMessage: error.errorMessage,
            errorType: error.errorType,
            lastErrorDate: new Date(),
         };
      } else if (status === "good") {
         // Clear error when status is good
         plaidItem.error = undefined;
      }
   }
}

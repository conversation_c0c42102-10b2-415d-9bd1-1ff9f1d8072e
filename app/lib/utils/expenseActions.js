import { handleCollectAmount as handleCollectAmountUtil } from "./expenseUtils";
import { addDays } from "date-fns";
import useBalanceStore from "../stores/balanceStore";
import { format } from "date-fns";

export const handleDelete = async (
   id,
   isGroup = false,
   groupExpenses = null,
   onExpensesChange,
   onRefreshBalances
) => {
   const confirmMessage = isGroup
      ? "Are you sure you want to delete all weeks in this group?"
      : "Are you sure you want to delete this expense?";

   if (!window.confirm(confirmMessage)) {
      return;
   }

   try {
      if (isGroup && groupExpenses) {
         // Use bulk delete endpoint for group deletes
         const response = await fetch(`/api/expenses/bulk-delete`, {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               expenseIds: groupExpenses
                  .filter((expense) => expense._id)
                  .map((expense) => expense._id),
            }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete expenses");
         }

         // Update transactions for each deleted expense
         for (const expense of groupExpenses) {
            if (expense._id) {
               await fetch(`/api/transactions`, {
                  method: "PATCH",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                     categoryId: expense._id,
                     type: "Expense",
                  }),
               });
            }
         }

         // Update local state to remove all deleted expenses
         onExpensesChange((prev) =>
            prev.filter(
               (expense) =>
                  !groupExpenses.some(
                     (groupExp) => groupExp._id === expense._id
                  )
            )
         );

         // Refresh balances to reflect freed up assigned amounts
         onRefreshBalances();
      } else {
         // Check if this is a weekly header expense
         const expenses =
            typeof onExpensesChange === "function" ? [] : onExpensesChange; // This needs to be passed in properly
         const expense = expenses.find((exp) => exp._id === id);
         if (expense?.isAggregated && expense?.expenses) {
            // Use bulk delete endpoint for weekly expenses
            const expenseIds = expense.expenses
               .filter((weekExp) => weekExp._id)
               .map((weekExp) => weekExp._id);

            const response = await fetch(`/api/expenses/bulk-delete`, {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({ expenseIds }),
            });

            if (!response.ok) {
               const errorData = await response.json();
               throw new Error(errorData.error || "Failed to delete expenses");
            }

            // Update transactions for each deleted expense
            for (const weekExpense of expense.expenses) {
               if (weekExpense._id) {
                  await fetch(`/api/transactions`, {
                     method: "PATCH",
                     headers: {
                        "Content-Type": "application/json",
                     },
                     body: JSON.stringify({
                        categoryId: weekExpense._id,
                        type: "Expense",
                     }),
                  });
               }
            }

            // Update local state to remove all deleted expenses
            onExpensesChange((prev) =>
               prev.filter(
                  (exp) =>
                     !expense.expenses.some(
                        (weekExp) => weekExp._id === exp._id
                     )
               )
            );

            // Refresh balances to reflect freed up assigned amounts
            onRefreshBalances();
         } else {
            // Delete single expense
            const response = await fetch(`/api/expenses/${id}`, {
               method: "DELETE",
            });

            if (!response.ok) {
               const errorData = await response.json();
               throw new Error(errorData.error || "Failed to delete expense");
            }

            // Update transactions for the deleted expense
            await fetch(`/api/transactions`, {
               method: "PATCH",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  categoryId: id,
                  type: "Expense",
               }),
            });

            onExpensesChange((prev) =>
               prev.filter((expense) => expense._id !== id)
            );

            // Refresh balances to reflect freed up assigned amounts
            onRefreshBalances();
         }
      }
   } catch (error) {
      console.error("Error deleting expense:", error);
      throw new Error(error.message || "Failed to delete expense");
   }
};

export const handleScheduleExpense = async (
   expense,
   onExpensesChange,
   onRefreshBalances,
   expenses
) => {
   try {
      // Create optimistic updates
      const optimisticUpdates = [];

      if (expense.isAggregated && expense.expenses) {
         // Filter out hidden weeks
         const weeksToSchedule = expense.expenses.filter(
            (weekExp) => !weekExp.isHidden
         );

         // Create optimistic updates for each week
         weeksToSchedule.forEach((expense) => {
            optimisticUpdates.push({
               ...expense,
               status: "scheduled",
               amountAssigned: 0,
               amountSpent: 0,
               amountAvailable: 0,
            });
         });

         // Apply optimistic update
         onExpensesChange(
            expenses.map((exp) => {
               const optimisticUpdate = optimisticUpdates.find(
                  (update) =>
                     update.description === exp.description &&
                     update.date === exp.date
               );
               return optimisticUpdate || exp;
            })
         );

         // Prepare expense data for bulk creation
         const expenseDataArray = weeksToSchedule.map((expense) => {
            const baseExpenseData = {
               date:
                  expense.date instanceof Date
                     ? format(expense.date, "yyyy-MM-dd")
                     : expense.date,
               amountDue: expense.amountDue,
               amountAssigned: 0,
               amountSpent: 0,
               amountAvailable: 0,
               description: expense.description,
               category: expense.category,
               status: "scheduled",
               frequency: expense.frequency || "weekly",
               recurringExpenseId: expense.recurringExpenseId || null,
            };

            // Add debt payment specific properties if this is a debt payment
            if (expense.isDebtPayment && expense.debtId) {
               baseExpenseData.isDebtPayment = true;
               baseExpenseData.debtId = expense.debtId;
            }

            // Only include weekly-specific properties if the frequency is weekly
            if ((expense.frequency || "weekly") === "weekly") {
               return {
                  ...baseExpenseData,
                  weeklyChargeType: expense.weeklyChargeType || "one-time",
                  startDate: expense.startDate
                     ? expense.startDate instanceof Date
                        ? format(expense.startDate, "yyyy-MM-dd")
                        : expense.startDate
                     : expense.date instanceof Date
                     ? format(expense.date, "yyyy-MM-dd")
                     : expense.date,
                  endDate: expense.endDate
                     ? expense.endDate instanceof Date
                        ? format(expense.endDate, "yyyy-MM-dd")
                        : expense.endDate
                     : null,
               };
            }

            return baseExpenseData;
         });

         // Use bulk create endpoint
         const response = await fetch("/api/expenses/bulk-create", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ expenses: expenseDataArray }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to schedule expense");
         }

         // Get the created expenses from response
         const createdExpenses = await response.json();

         // Update the expenses state with the actual server data
         onExpensesChange((prevExpenses) => {
            const updatedExpenses = [...prevExpenses];

            // Replace or add the newly created expenses
            createdExpenses.forEach((createdExpense) => {
               const index = updatedExpenses.findIndex(
                  (exp) =>
                     exp.description === createdExpense.description &&
                     new Date(exp.date).getTime() ===
                        new Date(createdExpense.date).getTime()
               );
               if (index !== -1) {
                  updatedExpenses[index] = createdExpense;
               } else {
                  updatedExpenses.push(createdExpense);
               }
            });

            // Also update the parent aggregated expense object
            const parentIndex = updatedExpenses.findIndex(
               (exp) =>
                  exp.isAggregated &&
                  exp.description === expense.description &&
                  exp._id === expense._id
            );

            if (parentIndex !== -1) {
               // Get all child expenses that belong to this parent
               const childExpenses = updatedExpenses.filter(
                  (exp) =>
                     !exp.isAggregated &&
                     exp.description === expense.description &&
                     (exp.frequency === "weekly" ||
                        (exp.startDate && exp.endDate))
               );

               // Update the parent's expenses array and status flags
               updatedExpenses[parentIndex] = {
                  ...updatedExpenses[parentIndex],
                  expenses: childExpenses,
                  hasProjected: childExpenses.some(
                     (exp) => exp.status === "projected"
                  ),
                  hasScheduled: childExpenses.some(
                     (exp) => exp.status === "scheduled"
                  ),
                  hasReadyToPay: childExpenses.some(
                     (exp) => exp.status === "funded"
                  ),
               };

               // If no more projected expenses, update parent status
               if (
                  !updatedExpenses[parentIndex].hasProjected &&
                  updatedExpenses[parentIndex].hasScheduled
               ) {
                  updatedExpenses[parentIndex].status = "scheduled";
               }
            }

            return updatedExpenses;
         });
      } else {
         // Create optimistic update for single expense
         const optimisticUpdate = {
            ...expense,
            status: "scheduled",
            amountAssigned: 0,
            amountSpent: 0,
            amountAvailable: 0,
         };

         // Apply optimistic update immediately
         onExpensesChange(
            expenses.map((exp) => {
               // Match by both description and date for weekly expenses
               if (exp.description === expense.description) {
                  if (
                     exp.date === expense.date ||
                     (exp.date &&
                        expense.date &&
                        new Date(exp.date).getTime() ===
                           new Date(expense.date).getTime())
                  ) {
                     return optimisticUpdate;
                  }
               }
               return exp;
            })
         );

         // Prepare the expense data
         const baseExpenseData = {
            date:
               expense.date instanceof Date
                  ? format(expense.date, "yyyy-MM-dd")
                  : expense.date,
            amountDue: expense.amountDue,
            amountAssigned: 0,
            amountSpent: 0,
            amountAvailable: 0,
            description: expense.description,
            category: expense.category,
            status: "scheduled",
            frequency: expense.frequency || "weekly",
            recurringExpenseId: expense.recurringExpenseId || null,
         };

         // Add debt payment specific properties if this is a debt payment
         if (expense.isDebtPayment && expense.debtId) {
            baseExpenseData.isDebtPayment = true;
            baseExpenseData.debtId = expense.debtId;
         }

         // Only include weekly-specific properties if the frequency is weekly
         const expenseData =
            (expense.frequency || "weekly") === "weekly"
               ? {
                    ...baseExpenseData,
                    weeklyChargeType: expense.weeklyChargeType || "one-time",
                    startDate: expense.startDate
                       ? expense.startDate instanceof Date
                          ? format(expense.startDate, "yyyy-MM-dd")
                          : expense.startDate
                       : expense.date instanceof Date
                       ? format(expense.date, "yyyy-MM-dd")
                       : expense.date,
                    endDate: expense.endDate
                       ? expense.endDate instanceof Date
                          ? format(expense.endDate, "yyyy-MM-dd")
                          : expense.endDate
                       : null,
                 }
               : baseExpenseData;

         const response = await fetch("/api/expenses", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(expenseData),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to schedule expense");
         }

         // Get the created expense from response
         const createdExpense = await response.json();

         // Update the expenses state with the actual server data
         onExpensesChange((prevExpenses) => {
            const updatedExpenses = [...prevExpenses];
            const index = updatedExpenses.findIndex(
               (exp) =>
                  exp.description === createdExpense.description &&
                  new Date(exp.date).getTime() ===
                     new Date(createdExpense.date).getTime()
            );
            if (index !== -1) {
               updatedExpenses[index] = createdExpense;
            } else {
               updatedExpenses.push(createdExpense);
            }

            // If this is a weekly expense, we need to update any parent aggregated expense
            if (
               expense.frequency === "weekly" ||
               (expense.startDate && expense.endDate)
            ) {
               // Find the parent aggregated expense by description (without requiring matching _id)
               const parentIndex = updatedExpenses.findIndex(
                  (exp) =>
                     exp.isAggregated && exp.description === expense.description
               );

               if (parentIndex !== -1) {
                  // Get all child expenses that belong to this parent
                  const childExpenses = updatedExpenses.filter(
                     (exp) =>
                        !exp.isAggregated &&
                        exp.description === expense.description &&
                        (exp.frequency === "weekly" ||
                           (exp.startDate && exp.endDate))
                  );

                  // Update the parent's expenses array and status flags
                  updatedExpenses[parentIndex] = {
                     ...updatedExpenses[parentIndex],
                     expenses: childExpenses,
                     hasProjected: childExpenses.some(
                        (exp) => exp.status === "projected"
                     ),
                     hasScheduled: childExpenses.some(
                        (exp) => exp.status === "scheduled"
                     ),
                     hasReadyToPay: childExpenses.some(
                        (exp) => exp.status === "funded"
                     ),
                  };

                  // If no more projected expenses, update parent status
                  if (
                     !updatedExpenses[parentIndex].hasProjected &&
                     updatedExpenses[parentIndex].hasScheduled
                  ) {
                     updatedExpenses[parentIndex].status = "scheduled";
                  }
               }
            }

            return updatedExpenses;
         });
      }

      // Refresh balances after state update
      onRefreshBalances();
   } catch (error) {
      console.error("Error scheduling expense:", error);
      // Revert optimistic update on error
      onExpensesChange(expenses);
      throw new Error(error.message || "Failed to schedule expense");
   }
};

export const handleScheduleAllExpenses = async (
   allExpenses,
   isProjectionHidden,
   onExpensesChange,
   onRefreshBalances,
   onFetchPeriodData,
   setScheduleAllLoading,
   setErrorMessage
) => {
   try {
      // Clear any previous errors
      setErrorMessage("");
      // Set loading state
      setScheduleAllLoading(true);

      // Get all expenses that are not hidden
      // Modified to include aggregated expenses that contain projected weeks
      const projectedExpenses = allExpenses.filter((expense) => {
         if (expense.isHidden) return false;

         // For aggregated expenses, check if any week is projected and not hidden
         if (expense.isAggregated && expense.expenses) {
            return expense.expenses.some(
               (weekExp) =>
                  weekExp.status === "projected" &&
                  !weekExp.isHidden &&
                  !isProjectionHidden(weekExp)
            );
         }

         // For non-aggregated expenses
         return expense.status === "projected";
      });

      console.log("All projected expenses:", projectedExpenses.length);

      // Flatten aggregated expenses and collect all expenses to schedule
      const expensesToSchedule = projectedExpenses.reduce((acc, expense) => {
         if (expense.isAggregated) {
            // For aggregated expenses, add each individual week that is projected and not hidden
            const weeklyExpenses = expense.expenses
               .filter(
                  (weekExp) =>
                     weekExp.status === "projected" &&
                     !weekExp.isHidden &&
                     !isProjectionHidden(weekExp)
               )
               .map((weekExp) => ({
                  ...weekExp,
                  frequency: expense.frequency || "oneoff", // Preserve the parent's frequency
               }));

            console.log(
               `Found ${weeklyExpenses.length} weekly expenses in aggregated group: ${expense.description}`
            );
            return [...acc, ...weeklyExpenses];
         } else if (expense.status === "projected") {
            // For non-aggregated expenses, add them directly if projected
            const frequency = expense.frequency || "oneoff";
            console.log(
               `Adding non-aggregated ${frequency} expense: ${expense.description}`
            );
            return [...acc, expense];
         }
         return acc;
      }, []);

      // Count expenses by type
      const weeklyCount = expensesToSchedule.filter(
         (exp) => exp.frequency === "weekly" || (exp.startDate && exp.endDate)
      ).length;
      const monthlyCount = expensesToSchedule.filter(
         (exp) => exp.frequency === "monthly"
      ).length;
      const oneoffCount = expensesToSchedule.filter(
         (exp) => !exp.frequency || exp.frequency === "oneoff"
      ).length;

      console.log(`Total expenses to schedule: ${expensesToSchedule.length}`);
      console.log(`Weekly expenses: ${weeklyCount}`);
      console.log(`Monthly expenses: ${monthlyCount}`);
      console.log(`One-off expenses: ${oneoffCount}`);

      if (expensesToSchedule.length === 0) {
         setErrorMessage("No projected expenses to schedule");
         setScheduleAllLoading(false);
         return;
      }

      // Prepare expense data for bulk creation
      const expenseDataArray = expensesToSchedule.map((expense) => {
         const baseExpenseData = {
            date:
               expense.date instanceof Date
                  ? format(expense.date, "yyyy-MM-dd")
                  : expense.date,
            amountDue: expense.amountDue,
            amountAssigned: 0,
            amountSpent: 0,
            amountAvailable: 0,
            description: expense.description,
            category: expense.category || "Other",
            status: "scheduled",
            frequency: expense.frequency || "oneoff",
            recurringExpenseId: expense.recurringExpenseId || null,
         };

         // Add debt payment specific properties if this is a debt payment
         if (expense.isDebtPayment && expense.debtId) {
            baseExpenseData.isDebtPayment = true;
            baseExpenseData.debtId = expense.debtId;
         }

         // Add start and end dates for weekly expenses
         if (
            (expense.frequency === "weekly" &&
               expense.weeklyChargeType === "spread") ||
            (expense.startDate && expense.endDate)
         ) {
            const startDate =
               expense.startDate instanceof Date
                  ? expense.startDate
                  : expense.date instanceof Date
                  ? expense.date
                  : new Date(expense.startDate || expense.date);
            const endDate =
               expense.endDate instanceof Date
                  ? expense.endDate
                  : addDays(startDate, 6);

            baseExpenseData.startDate = format(startDate, "yyyy-MM-dd");
            baseExpenseData.endDate = format(endDate, "yyyy-MM-dd");

            // Explicitly log weekly expense details
            console.log(
               `Preparing weekly expense: ${expense.description}, start: ${baseExpenseData.startDate}, end: ${baseExpenseData.endDate}`
            );
         }

         // Add weeklyChargeType for weekly expenses
         if (expense.frequency === "weekly") {
            baseExpenseData.weeklyChargeType =
               expense.weeklyChargeType || "one-time";
            console.log(
               `Weekly expense charge type: ${baseExpenseData.weeklyChargeType}`
            );
         }

         return baseExpenseData;
      });

      // Use bulk create endpoint
      console.log(
         `Sending ${expenseDataArray.length} expenses to server for scheduling...`
      );
      const response = await fetch("/api/expenses/bulk-create", {
         method: "POST",
         headers: {
            "Content-Type": "application/json",
         },
         body: JSON.stringify({ expenses: expenseDataArray }),
      });

      if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.error || "Failed to schedule expenses");
      }

      const result = await response.json();
      console.log(
         `Successfully scheduled ${
            result.length || result.count || "all"
         } expenses`
      );

      // Show success message with details
      setErrorMessage(
         `Successfully scheduled ${weeklyCount} weekly, ${monthlyCount} monthly, and ${oneoffCount} one-off expenses.`
      );

      // Update the expenses state with the actual server data
      // First, ensure result is an array of the scheduled expenses
      const createdExpenses = Array.isArray(result) ? result : [];

      if (createdExpenses.length > 0) {
         // Update using the server response data
         onExpensesChange((prevExpenses) => {
            // Create a copy of the current expenses
            let updatedExpenses = [...prevExpenses];

            // For each created expense, find and replace the corresponding projected expense
            createdExpenses.forEach((createdExpense) => {
               // Find existing expense with matching description and date
               const matchIndex = updatedExpenses.findIndex(
                  (exp) =>
                     exp.description === createdExpense.description &&
                     (exp.date === createdExpense.date ||
                        (exp.date &&
                           createdExpense.date &&
                           new Date(exp.date).getTime() ===
                              new Date(createdExpense.date).getTime()))
               );

               if (matchIndex !== -1) {
                  // Replace with the scheduled expense
                  updatedExpenses[matchIndex] = createdExpense;
               } else {
                  // If no match found, add the new expense
                  updatedExpenses.push(createdExpense);
               }
            });

            // Handle weekly expenses - find and update any aggregated expenses
            const weeklyGroups = new Map();

            // Group expenses by description for weekly expenses
            updatedExpenses.forEach((exp) => {
               if (
                  exp.frequency === "weekly" ||
                  (exp.startDate && exp.endDate)
               ) {
                  const key = exp.description.toLowerCase();
                  if (!weeklyGroups.has(key)) {
                     weeklyGroups.set(key, []);
                  }
                  weeklyGroups.get(key).push(exp);
               }
            });

            // For each weekly group, update any aggregated parent
            weeklyGroups.forEach((groupExpenses, key) => {
               // Find any aggregated parent
               const aggregatedIndex = updatedExpenses.findIndex(
                  (exp) =>
                     exp.isAggregated && exp.description.toLowerCase() === key
               );

               if (aggregatedIndex !== -1) {
                  // Update the aggregated expense with new child expenses
                  updatedExpenses[aggregatedIndex] = {
                     ...updatedExpenses[aggregatedIndex],
                     expenses: groupExpenses,
                     // Update status flags to reflect that some children are now scheduled
                     hasProjected: groupExpenses.some(
                        (exp) => exp.status === "projected"
                     ),
                     hasScheduled: groupExpenses.some(
                        (exp) => exp.status === "scheduled"
                     ),
                  };
               }
            });

            return updatedExpenses;
         });
      } else {
         // If we didn't get the created expenses back, refresh the data
         onFetchPeriodData();
      }

      onRefreshBalances();
   } catch (error) {
      console.error("Error scheduling expenses:", error);
      setErrorMessage(error.message || "Failed to schedule expenses");
   } finally {
      setScheduleAllLoading(false);
   }
};

export const handleCollectAmount = async (
   expense,
   onExpensesChange,
   onAlertsRefresh,
   onRefreshBalances,
   setIsProcessing,
   setShowAvailableDropdown
) => {
   await handleCollectAmountUtil({
      expense,
      onExpensesChange,
      onAlertsRefresh,
      onRefreshBalances,
      setIsProcessing,
      setShowAvailableDropdown,
   });
};

export const handleAssignAmount = async (
   expense,
   onExpensesChange,
   onAlertsRefresh,
   setIsProcessing,
   setShowAvailableDropdown
) => {
   try {
      setIsProcessing(true);
      if (expense.isAggregated) {
         let totalNeeded = 0;

         // First calculate total needed amount
         expense.expenses.forEach((weekExpense) => {
            if (weekExpense.amountAvailable < 0) {
               totalNeeded += Math.abs(weekExpense.amountAvailable);
            }
         });

         // Optimistically update Ready to Assign amount using store
         const { updateAssignedAmount } = useBalanceStore.getState();
         updateAssignedAmount(totalNeeded);

         // Handle aggregated expense collection
         for (const weekExpense of expense.expenses) {
            if (weekExpense.amountAvailable < 0) {
               const amountNeeded = Math.abs(weekExpense.amountAvailable);
               const newAssignedAmount =
                  weekExpense.amountAssigned + amountNeeded;

               // Update the expense in the database
               const response = await fetch(
                  `/api/expenses/${weekExpense._id}`,
                  {
                     method: "PUT",
                     headers: {
                        "Content-Type": "application/json",
                     },
                     body: JSON.stringify({
                        amountAssigned: newAssignedAmount,
                        updateUserAssigned: amountNeeded,
                     }),
                  }
               );

               if (!response.ok) {
                  throw new Error("Failed to update expense");
               }
            }
         }
      } else {
         // Handle individual expense
         const amountNeeded = Math.abs(expense.amountAvailable);
         const newAssignedAmount = expense.amountAssigned + amountNeeded;

         // Optimistically update Ready to Assign amount using store
         const { updateAssignedAmount } = useBalanceStore.getState();
         updateAssignedAmount(amountNeeded);

         // Create updated expense for optimistic UI update
         const updatedExpense = {
            ...expense,
            amountAssigned: newAssignedAmount,
            amountAvailable: 0, // Set available to 0 since we're assigning exactly what's needed
         };

         // Optimistically update the expense state
         onExpensesChange((prevExpenses) =>
            prevExpenses.map((e) =>
               e._id === expense._id ? updatedExpense : e
            )
         );

         // Update the expense in the database
         const response = await fetch(`/api/expenses/${expense._id}`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               amountAssigned: newAssignedAmount,
               updateUserAssigned: amountNeeded,
            }),
         });

         if (!response.ok) {
            // Revert optimistic updates on error
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((e) => (e._id === expense._id ? expense : e))
            );
            // Revert the assigned change using store
            const { updateAssignedAmount } = useBalanceStore.getState();
            updateAssignedAmount(-amountNeeded);
            throw new Error("Failed to update expense");
         }

         // Get the updated expense from the server
         const serverExpense = await response.json();

         // Only update the state if there's a difference from our optimistic update
         if (JSON.stringify(serverExpense) !== JSON.stringify(updatedExpense)) {
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((e) =>
                  e._id === expense._id ? serverExpense : e
               )
            );
         }

         // Refresh alerts
         if (onAlertsRefresh) {
            onAlertsRefresh();
         }
      }
   } catch (error) {
      console.error("Error assigning amount:", error);
      throw new Error(error.message || "Failed to assign amount");
   } finally {
      setIsProcessing(false);
      setShowAvailableDropdown(null);
   }
};

import {
   startOfDay,
   endOfDay,
   addDays,
   addMonths,
   subDays,
   setDate,
   isBefore,
   isAfter,
   parseISO,
   isSameMonth,
   isWithinInterval,
   startOfMonth,
   endOfMonth,
   getDate,
   format,
} from "date-fns";
import { roundToTwoDecimals } from "./expenseUtils";

/**
 * Finds the "most recent" pay date for weekly, biweekly, etc.
 * If payPeriod === "biweekly" and we have lastPaymentDate, it anchors from that.
 */
export const findMostRecentPayDate = (
   date,
   payPeriod,
   payDay,
   payDayOfWeek,
   lastPaymentDate = null
) => {
   const currentDate = startOfDay(date);

   switch (payPeriod) {
      case "weekly": {
         const targetDay = parseInt(payDayOfWeek) % 7;
         const currentDay = currentDate.getDay();
         let daysToSubtract = currentDay - targetDay;
         if (daysToSubtract < 0) daysToSubtract += 7;
         return subDays(currentDate, daysToSubtract);
      }

      case "biweekly": {
         if (lastPaymentDate) {
            // Anchor strictly from lastPaymentDate in 14-day increments
            const anchor = startOfDay(
               lastPaymentDate instanceof Date
                  ? lastPaymentDate
                  : parseISO(lastPaymentDate)
            );
            let candidate = anchor;

            // If anchor is after currentDate, go backward in 14-day steps
            while (isAfter(candidate, currentDate)) {
               candidate = subDays(candidate, 14);
            }
            // Otherwise go forward in 14-day steps until adding 14 days overshoots currentDate
            while (isBefore(addDays(candidate, 14), currentDate)) {
               candidate = addDays(candidate, 14);
            }
            return candidate;
         }
         // No lastPaymentDate fallback: just return currentDate (or your creation-date logic)
         return startOfDay(currentDate);
      }

      case "monthly": {
         const targetDay = parseInt(payDay);
         const currentDay = getDate(currentDate);
         if (currentDay >= targetDay) {
            return startOfDay(setDate(currentDate, targetDay));
         } else {
            // Move back to last month's targetDay
            const prevMonth = setDate(addMonths(currentDate, -1), targetDay);
            return startOfDay(prevMonth);
         }
      }

      case "semimonthly": {
         const currentDay = getDate(currentDate);
         if (currentDay < 15) {
            // 1–14
            return startOfDay(setDate(currentDate, 1));
         } else {
            // 15–end
            return startOfDay(setDate(currentDate, 15));
         }
      }

      default:
         return startOfDay(currentDate);
   }
};

/**
 * For a given startDate + payPeriod, returns the end date of that period.
 * e.g. weekly = +6 days, biweekly = +13 days, etc.
 */
const getPeriodEndDate = (startDate, payPeriod) => {
   switch (payPeriod) {
      case "weekly":
         return endOfDay(addDays(startDate, 6));
      case "biweekly":
         return endOfDay(addDays(startDate, 13));
      case "semimonthly": {
         const day = getDate(startDate);
         if (day < 15) {
            return endOfDay(setDate(startDate, 14));
         } else {
            return endOfDay(endOfMonth(startDate));
         }
      }
      case "monthly": {
         const nextMonth = addMonths(startDate, 1);
         return endOfDay(subDays(nextMonth, 1));
      }
      default:
         // fallback if undefined
         return endOfDay(addDays(startDate, 6));
   }
};

/**
 * Sums up expenses/incomes within [periodStart, periodEnd].
 */
const calculatePeriodTotals = (transactions, periodStart, periodEnd, field) => {
   const pStart = startOfDay(periodStart);
   const pEnd = endOfDay(periodEnd);

   return transactions.reduce((total, t) => {
      // If "spread" or multi-day
      if (t.startDate && t.endDate) {
         const tStart = startOfDay(
            t.startDate instanceof Date ? t.startDate : new Date(t.startDate)
         );
         const tEnd = endOfDay(
            t.endDate instanceof Date ? t.endDate : new Date(t.endDate)
         );

         const overlapping =
            (tStart <= pEnd && tEnd >= pStart) ||
            (pStart <= tEnd && pEnd >= tStart);

         if (!overlapping) return total;

         // For expenses: max of spent vs. due
         if (field === "amountDue" && t.amountSpent !== undefined) {
            const spentAmount = Math.abs(t.amountSpent || 0);
            const dueAmount = t.amountDue || 0;
            return total + Math.max(spentAmount, dueAmount);
         }
         return total + (t[field] || 0);
      }

      // Single date
      const tDate = startOfDay(
         t.date instanceof Date ? t.date : new Date(t.date)
      );
      const inRange = !isBefore(tDate, pStart) && !isAfter(tDate, pEnd);
      if (!inRange) return total;

      if (field === "expectedAmount") {
         const amt = t.expectedAmount || t.receivedAmount || 0;
         return total + amt;
      }
      if (field === "amountDue" && t.amountSpent !== undefined) {
         const spent = Math.abs(t.amountSpent || 0);
         const due = t.amountDue || 0;
         return total + Math.max(spent, due);
      }
      return total + (t[field] || 0);
   }, 0);
};

/**
 * Calculate period totals with separate tracking of overspend amounts
 */
const calculatePeriodTotalsWithOverspend = (
   transactions,
   periodStart,
   periodEnd,
   field
) => {
   const pStart = startOfDay(periodStart);
   const pEnd = endOfDay(periodEnd);

   let baseTotal = 0;
   let overspendTotal = 0;

   transactions.forEach((t) => {
      let inRange = false;

      // Check if transaction is in range
      if (t.startDate && t.endDate) {
         const tStart = startOfDay(
            t.startDate instanceof Date ? t.startDate : parseISO(t.startDate)
         );
         const tEnd = endOfDay(
            t.endDate instanceof Date ? t.endDate : parseISO(t.endDate)
         );

         const overlapping =
            (tStart <= pEnd && tEnd >= pStart) ||
            (pStart <= tEnd && pEnd >= tStart);

         inRange = overlapping;
      } else {
         const tDate = startOfDay(
            t.date instanceof Date ? t.date : parseISO(t.date)
         );
         inRange = !isBefore(tDate, pStart) && !isAfter(tDate, pEnd);
      }

      if (!inRange) return;

      if (field === "expectedAmount") {
         const amt = t.expectedAmount || t.receivedAmount || 0;
         baseTotal += amt;
      } else if (field === "amountDue" && t.amountSpent !== undefined) {
         const spentAmount = Math.abs(t.amountSpent || 0);
         const dueAmount = t.amountDue || 0;

         // Add base due amount
         baseTotal += dueAmount;

         // Add overspend if spent > due
         if (spentAmount > dueAmount) {
            overspendTotal += spentAmount - dueAmount;
         }
      } else {
         baseTotal += t[field] || 0;
      }
   });

   return { baseTotal, overspendTotal };
};

/**
 * Main function:
 * - If userPaySettings.payPeriod == "biweekly"
 *   and we are in "month" view, we will produce sub-periods (14-day blocks)
 *   anchored from `lastPaymentDate`, BUT we only push those blocks that overlap
 *   the monthly bounding range [dateRange.start, dateRange.end].
 * - If userPaySettings.payPeriod == "monthly", we do the old monthly logic, etc.
 * - If userPaySettings.payPeriod == "biweekly" and the user is in a "payPeriod" view,
 *   we do the standard step-by-14 logic that covers the entire dateRange.
 *
 * This code merges the concept of "monthly bounding" with "biweekly sub-period anchoring."
 */
export const calculatePayPeriodBalances = (
   expenses,
   incomes,
   dateRange,
   userPaySettings,
   // Optional: pass `forceMonthlySubPeriods` if you know the user specifically wants monthly bounding
   forceMonthlySubPeriods = true
) => {
   if (!dateRange || !userPaySettings) return [];

   const { payPeriod, payDay, payDayOfWeek } = userPaySettings;
   // If we want monthly bounding, define boundingStart & boundingEnd
   // from the dateRange passed in (the 1st - 31st, presumably).
   const boundingStart = startOfDay(dateRange.start);
   const boundingEnd = endOfDay(dateRange.end);

   // If user is "monthly" or we specifically want "monthly bounding"
   // but the userPaySettings says "biweekly", we handle sub-period logic below.
   // We'll produce an array of pay periods, each partial or full, that overlap boundingStart..boundingEnd.
   if (forceMonthlySubPeriods && payPeriod === "biweekly") {
      // 1) Identify `lastPaymentDate` if any - check both recurring incomes and actual incomes
      let lastPaymentDate = null;

      // First check recurring incomes from userPaySettings
      const recurringIncome = userPaySettings.recurringIncomes?.find(
         (i) => i.payPeriod === "biweekly" && i.lastPaymentDate && i.enabled
      );
      if (recurringIncome?.lastPaymentDate) {
         lastPaymentDate = recurringIncome.lastPaymentDate;
      }

      // If not found in recurring incomes, check actual incomes
      if (!lastPaymentDate) {
         const foundIncome = incomes.find(
            (i) => i.payPeriod === "biweekly" && i.lastPaymentDate && i.enabled
         );
         if (foundIncome) {
            lastPaymentDate = foundIncome.lastPaymentDate;
         }
      }

      console.log("Found lastPaymentDate:", lastPaymentDate);

      // 2) If we have no lastPaymentDate, fallback to boundingStart as an anchor
      let anchor = startOfDay(boundingStart);
      if (lastPaymentDate) {
         let anchorDate = startOfDay(
            lastPaymentDate instanceof Date
               ? lastPaymentDate
               : parseISO(lastPaymentDate)
         );

         console.log("Biweekly period calculation:", {
            initialAnchor: format(anchorDate, "yyyy-MM-dd"),
            boundingStart: format(boundingStart, "yyyy-MM-dd"),
            boundingEnd: format(boundingEnd, "yyyy-MM-dd"),
         });

         // Step backward in 14-day increments if anchorDate is > boundingStart
         while (isAfter(anchorDate, boundingStart)) {
            anchorDate = subDays(anchorDate, 14);
            console.log(
               "Stepping backward to:",
               format(anchorDate, "yyyy-MM-dd")
            );
         }

         // Step forward in 14-day increments until we're at the most recent period before or at boundingStart
         while (isBefore(addDays(anchorDate, 14), boundingStart)) {
            anchorDate = addDays(anchorDate, 14);
            console.log(
               "Stepping forward to:",
               format(anchorDate, "yyyy-MM-dd")
            );
         }

         console.log("Final anchor date:", format(anchorDate, "yyyy-MM-dd"));
         anchor = anchorDate;
      }

      const payPeriods = [];
      let currentStart = anchor;

      // 3) Keep stepping by 14 days until we pass boundingEnd
      while (isBefore(currentStart, boundingEnd)) {
         const periodEnd = endOfDay(addDays(currentStart, 13));

         console.log("Calculating period:", {
            currentStart: format(currentStart, "yyyy-MM-dd"),
            periodEnd: format(periodEnd, "yyyy-MM-dd"),
            boundingStart: format(boundingStart, "yyyy-MM-dd"),
            boundingEnd: format(boundingEnd, "yyyy-MM-dd"),
         });

         // Overlap check - a period overlaps if any part of it intersects with the bounding window
         const overlaps = !(
            (
               periodEnd < boundingStart || // entirely before the bounding window
               currentStart > boundingEnd
            ) // entirely after the bounding window
         );

         if (overlaps) {
            // For biweekly periods, we want to show the full period even if it crosses month boundaries
            // Only clip the start/end if the period is completely outside the extended range
            const finalStart = currentStart;
            const finalEnd = periodEnd;

            console.log("Adding period:", {
               start: format(finalStart, "yyyy-MM-dd"),
               end: format(finalEnd, "yyyy-MM-dd"),
            });

            // Calculate incomes/expenses for the full period [finalStart..finalEnd]
            const periodIncome = calculatePeriodTotals(
               incomes,
               finalStart,
               finalEnd,
               "expectedAmount"
            );
            const expenseTotals = calculatePeriodTotalsWithOverspend(
               expenses,
               finalStart,
               finalEnd,
               "amountDue"
            );
            const periodAssigned = calculatePeriodTotals(
               expenses,
               finalStart,
               finalEnd,
               "amountAssigned"
            );
            const periodBalance = roundToTwoDecimals(
               periodIncome -
                  expenseTotals.baseTotal -
                  expenseTotals.overspendTotal +
                  periodAssigned
            );

            // For debugging or if you want to see all transactions in this window
            const periodIncomeList = incomes.filter((inc) => {
               const incDate = startOfDay(
                  inc.date instanceof Date ? inc.date : parseISO(inc.date)
               );
               return incDate >= finalStart && incDate <= finalEnd;
            });
            const periodExpenseList = expenses.filter((exp) => {
               if (exp.startDate && exp.endDate) {
                  const eStart = startOfDay(
                     exp.startDate instanceof Date
                        ? exp.startDate
                        : parseISO(exp.startDate)
                  );
                  const eEnd = endOfDay(
                     exp.endDate instanceof Date
                        ? exp.endDate
                        : parseISO(exp.endDate)
                  );
                  return eEnd >= finalStart && eStart <= finalEnd;
               }
               const eDate = startOfDay(
                  exp.date instanceof Date ? exp.date : parseISO(exp.date)
               );
               return eDate >= finalStart && eDate <= finalEnd;
            });

            payPeriods.push({
               startDate: finalStart,
               endDate: finalEnd,
               income: roundToTwoDecimals(periodIncome),
               expenses: roundToTwoDecimals(expenseTotals.baseTotal),
               overspend: roundToTwoDecimals(expenseTotals.overspendTotal),
               assigned: roundToTwoDecimals(periodAssigned),
               balance: periodBalance,
               crossesMonthBoundary: !isSameMonth(finalStart, finalEnd),
               _debug: {
                  incomes: periodIncomeList,
                  expenses: periodExpenseList,
               },
            });
         }

         // Step forward 14 days
         currentStart = addDays(currentStart, 14);
      }

      return payPeriods;
   }

   // ----------------------------------------------------------------------
   // If we get here, we do the "standard" approach for monthly/weekly/biweekly
   // (the user might be in actual payPeriod view, or payPeriod != biweekly, etc.)
   // This is your existing logic, mostly unchanged:

   const payPeriods = [];
   let lastPaymentDate = null;

   if (payPeriod === "biweekly" && incomes && incomes.length > 0) {
      const bwIncome = incomes.find(
         (i) => i.payPeriod === "biweekly" && i.lastPaymentDate && i.enabled
      );
      if (bwIncome) {
         lastPaymentDate = bwIncome.lastPaymentDate;
      }
   }

   let currentStart;
   if (payPeriod === "biweekly" && lastPaymentDate) {
      currentStart = startOfDay(
         lastPaymentDate instanceof Date
            ? lastPaymentDate
            : parseISO(lastPaymentDate)
      );
      while (isAfter(currentStart, boundingEnd)) {
         currentStart = subDays(currentStart, 14);
      }
   } else {
      currentStart = findMostRecentPayDate(
         boundingStart,
         payPeriod,
         payDay,
         payDayOfWeek,
         lastPaymentDate
      );
   }

   const rangeEnd = boundingEnd;

   while (currentStart <= rangeEnd) {
      const periodEnd = getPeriodEndDate(currentStart, payPeriod);

      // Overlap check with [boundingStart..boundingEnd]
      const overlapsRange =
         isWithinInterval(startOfDay(currentStart), {
            start: boundingStart,
            end: boundingEnd,
         }) ||
         isWithinInterval(endOfDay(periodEnd), {
            start: boundingStart,
            end: boundingEnd,
         }) ||
         (isBefore(currentStart, boundingStart) &&
            isAfter(periodEnd, boundingEnd));

      if (overlapsRange) {
         const periodIncomeList = incomes.filter((inc) => {
            const incDate = startOfDay(
               inc.date instanceof Date ? inc.date : parseISO(inc.date)
            );
            return incDate >= currentStart && incDate <= periodEnd;
         });

         const periodExpenseList = expenses.filter((exp) => {
            if (exp.startDate && exp.endDate) {
               const expStart = startOfDay(
                  exp.startDate instanceof Date
                     ? exp.startDate
                     : parseISO(exp.startDate)
               );
               const expEnd = endOfDay(
                  exp.endDate instanceof Date
                     ? exp.endDate
                     : parseISO(exp.endDate)
               );
               const overlap = expEnd >= currentStart && expStart <= periodEnd;
               return overlap;
            }
            const expDate = startOfDay(
               exp.date instanceof Date ? exp.date : parseISO(exp.date)
            );
            return expDate >= currentStart && expDate <= periodEnd;
         });

         const periodIncome = calculatePeriodTotals(
            incomes,
            currentStart,
            periodEnd,
            "expectedAmount"
         );
         const expenseTotals = calculatePeriodTotalsWithOverspend(
            expenses,
            currentStart,
            periodEnd,
            "amountDue"
         );
         const periodAssigned = calculatePeriodTotals(
            expenses,
            currentStart,
            periodEnd,
            "amountAssigned"
         );
         const periodBalance = roundToTwoDecimals(
            periodIncome -
               expenseTotals.baseTotal -
               expenseTotals.overspendTotal +
               periodAssigned
         );

         payPeriods.push({
            startDate: currentStart,
            endDate: periodEnd,
            income: roundToTwoDecimals(periodIncome),
            expenses: roundToTwoDecimals(expenseTotals.baseTotal),
            overspend: roundToTwoDecimals(expenseTotals.overspendTotal),
            assigned: roundToTwoDecimals(periodAssigned),
            balance: periodBalance,
            crossesMonthBoundary: !isSameMonth(currentStart, periodEnd),
            _debug: {
               incomes: periodIncomeList,
               expenses: periodExpenseList,
            },
         });
      }

      // Advance to next period
      if (payPeriod === "biweekly") {
         currentStart = addDays(currentStart, 14);
      } else if (payPeriod === "semimonthly") {
         const dayNum = getDate(currentStart);
         if (dayNum < 15) {
            currentStart = startOfDay(setDate(currentStart, 15));
         } else {
            currentStart = startOfDay(setDate(addMonths(currentStart, 1), 1));
         }
      } else {
         currentStart = addDays(periodEnd, 1);
      }

      if (currentStart > rangeEnd) break;
   }

   return payPeriods;
};

import {
   startOfDay,
   endOfDay,
   addMonths,
   subDays,
   isBefore,
   format,
   addDays,
} from "date-fns";
import { findMostRecentPayDate } from "./payPeriodUtils";

/**
 * Calculates the extended date range for pay period calculations
 * @param {Object} viewRange - The current view range with start and end dates
 * @param {Object} paySettings - User's pay settings
 * @param {Array} incomes - List of user's incomes
 * @returns {Object|null} Extended date range object or null if invalid input
 */
export const getExtendedDateRange = (viewRange, paySettings, incomes = []) => {
   if (!viewRange || !paySettings) return null;

   // For pay period view, don't extend the range
   if (paySettings.viewType === "payPeriod") {
      return {
         start: startOfDay(viewRange.start),
         end: endOfDay(viewRange.end),
      };
   }

   // For month view, only extend slightly to catch recurring expenses at month boundaries
   if (paySettings.viewType === "month") {
      return {
         start: startOfDay(subDays(viewRange.start, 7)), // Look back a week
         end: endOfDay(addDays(viewRange.end, 7)), // Look ahead a week
      };
   }

   // For other views, extend the range as before
   const extendedStart = findMostRecentPayDate(
      subDays(viewRange.start, paySettings.payPeriod === "biweekly" ? 42 : 31),
      paySettings.payPeriod,
      paySettings.payDay,
      paySettings.payDayOfWeek,
      paySettings.payPeriod === "biweekly" && incomes?.length > 0
         ? incomes.find(
              (income) =>
                 income.payPeriod === "biweekly" &&
                 income.lastPaymentDate &&
                 income.enabled
           )?.lastPaymentDate
         : null
   );

   // Don't extend as far into the future
   const extendedEnd = endOfDay(addDays(viewRange.end, 14));

   return {
      start: startOfDay(extendedStart),
      end: extendedEnd,
   };
};

/**
 * Calculates budget totals from expenses and incomes
 * @param {Array} expenses - List of expenses
 * @param {Array} incomes - List of incomes
 * @returns {Object} Budget totals including currentBalance, totalIncome, and totalAssigned
 */
export const calculateBudgetTotals = (expenses = [], incomes = []) => {
   const totalIncome = incomes.reduce((sum, income) => {
      if (income.enabled !== false) {
         return sum + (income.amount || 0);
      }
      return sum;
   }, 0);

   const totalAssigned = expenses.reduce((sum, expense) => {
      if (expense.enabled !== false) {
         return sum + (expense.amountAssigned || 0);
      }
      return sum;
   }, 0);

   const readyToAssign = totalIncome - totalAssigned;

   return {
      currentBalance: totalIncome - totalAssigned,
      totalIncome,
      totalAssigned,
      readyToAssign,
   };
};

/**
 * Formats currency amount to a standard format
 * @param {number} amount - The amount to format
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount) => {
   return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
   }).format(amount || 0);
};

/**
 * Gets the next occurrence of a semimonthly date (1st and 15th of each month)
 * @param {Date} currentDate - The reference date
 * @returns {Date|null} The next semimonthly occurrence date or null if error
 */
export const getNextSemimonthlyOccurrence = (currentDate) => {
   try {
      const date = new Date(currentDate);
      const currentDay = date.getDate();

      // If we're before the 15th, next date is the 15th
      if (currentDay < 15) {
         date.setDate(15);
         return date;
      }

      // If we're on or after the 15th, next date is 1st of next month
      date.setMonth(date.getMonth() + 1);
      date.setDate(1);
      return date;
   } catch (error) {
      console.error("Error generating semimonthly date:", error);
      return null;
   }
};

/**
 * Gets the next occurrence of a monthly date
 * @param {number|string} targetDay - The target day of the month
 * @param {Date} startDate - The reference start date
 * @returns {Date} The next monthly occurrence date
 */
export const getNextMonthlyOccurrence = (targetDay, startDate) => {
   try {
      const date = new Date(startDate);
      const targetDayNum = parseInt(targetDay);

      // Get the last day of the current month
      const lastDayOfMonth = new Date(
         date.getFullYear(),
         date.getMonth() + 1,
         0
      ).getDate();

      // Use the last day of the month if targetDay exceeds it
      const adjustedDay = Math.min(targetDayNum, lastDayOfMonth);

      // Create new date with adjusted day
      const result = new Date(date.getFullYear(), date.getMonth(), adjustedDay);

      // If the result is before the start date, move to next month
      if (isBefore(result, startDate)) {
         const nextMonth = addMonths(date, 1);
         const nextMonthLastDay = new Date(
            nextMonth.getFullYear(),
            nextMonth.getMonth() + 1,
            0
         ).getDate();
         const nextMonthAdjustedDay = Math.min(targetDayNum, nextMonthLastDay);
         return new Date(
            nextMonth.getFullYear(),
            nextMonth.getMonth(),
            nextMonthAdjustedDay
         );
      }

      return result;
   } catch (error) {
      console.error("Error in getNextMonthlyOccurrence:", error);
      return startDate; // Fallback to start date if there's an error
   }
};

/**
 * Fetches budget data for a given date range
 * @param {Object} dateRange - The date range to fetch data for
 * @param {Object} userPaySettings - User's pay settings
 * @returns {Promise<Object>} Object containing expenses and incomes data
 */
export const fetchPeriodData = async (dateRange, userPaySettings) => {
   if (!dateRange || !userPaySettings) return { expenses: [], incomes: [] };

   const extendedRange = getExtendedDateRange(dateRange, userPaySettings);
   if (!extendedRange) return { expenses: [], incomes: [] };

   const params = new URLSearchParams({
      start: format(extendedRange.start, "yyyy-MM-dd"),
      end: format(extendedRange.end, "yyyy-MM-dd"),
   });

   try {
      const [expensesResponse, incomesResponse] = await Promise.all([
         fetch(`/api/expenses?${params}`),
         fetch(`/api/incomes?${params}`),
      ]);

      let expenses = [];
      let incomes = [];

      // Handle responses individually, allowing one to fail without affecting the other
      if (expensesResponse.ok) {
         expenses = await expensesResponse.json();
      } else {
         console.warn(
            "Failed to fetch expenses, using empty array:",
            expensesResponse.status
         );
      }

      if (incomesResponse.ok) {
         incomes = await incomesResponse.json();
      } else {
         console.warn(
            "Failed to fetch incomes, using empty array:",
            incomesResponse.status
         );
      }

      // Filter expenses to match the actual date range, not the extended range
      const filteredExpenses = expenses.filter((expense) => {
         // Check for regular expense date
         if (expense.date) {
            // Use string comparison for dates (YYYY-MM-DD format)
            const startDateStr = format(dateRange.start, "yyyy-MM-dd");
            const endDateStr = format(dateRange.end, "yyyy-MM-dd");

            if (expense.date >= startDateStr && expense.date <= endDateStr) {
               return true;
            }
         }

         // Check for spread expenses (weekly, etc.)
         if (expense.startDate && expense.endDate) {
            const startDateStr = format(dateRange.start, "yyyy-MM-dd");
            const endDateStr = format(dateRange.end, "yyyy-MM-dd");
            // Include if there's any overlap with the date range
            return (
               expense.startDate <= endDateStr &&
               expense.endDate >= startDateStr
            );
         }

         return false;
      });

      // Filter incomes to match the actual date range, not the extended range
      const filteredIncomes = incomes.filter((income) => {
         if (income.date) {
            // Use string comparison for dates (YYYY-MM-DD format)
            const startDateStr = format(dateRange.start, "yyyy-MM-dd");
            const endDateStr = format(dateRange.end, "yyyy-MM-dd");
            return income.date >= startDateStr && income.date <= endDateStr;
         }
         return false;
      });

      return { expenses: filteredExpenses, incomes: filteredIncomes };
   } catch (error) {
      console.error("Error fetching period data:", error);
      // Return empty arrays instead of throwing error
      return { expenses: [], incomes: [] };
   }
};

/**
 * Refreshes user's budget balances
 * @returns {Promise<Object>} Updated balance data
 */
export const refreshBalances = async () => {
   try {
      const response = await fetch("/api/user/balance", {
         method: "POST",
      });

      if (response.ok) {
         const data = await response.json();
         return {
            currentBalance: Number(data.currentBalance || 0),
            totalIncome: Number(data.totalIncome || 0),
            totalAssigned: Number(data.totalAssigned || 0),
            readyToAssign: Number(data.readyToAssign || 0),
         };
      } else {
         console.warn(
            "Failed to refresh balances, using default values:",
            response.status
         );
         // Return default values instead of throwing error
         return {
            currentBalance: 0,
            totalIncome: 0,
            totalAssigned: 0,
            readyToAssign: 0,
         };
      }
   } catch (error) {
      console.error("Error refreshing balances:", error);
      // Return default values instead of throwing error
      return {
         currentBalance: 0,
         totalIncome: 0,
         totalAssigned: 0,
         readyToAssign: 0,
      };
   }
};

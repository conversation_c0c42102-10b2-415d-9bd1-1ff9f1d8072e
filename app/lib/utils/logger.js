import dbConnect from "../mongodb/dbConnect";
import Log from "../mongodb/models/Log";

/**
 * Log an event to the database
 * @param {Object} options - Logging options
 * @param {string} options.source - Source of the log (e.g., 'plaid-webhook', 'transaction-sync')
 * @param {string} options.event - Event name/type
 * @param {string} options.level - Log level: 'info', 'warning', 'error', 'debug'
 * @param {string} [options.message] - Optional message
 * @param {Object} [options.data] - Additional data to log
 * @param {string} [options.userId] - Optional user ID
 * @returns {Promise<Object>} - The created log entry
 */
export async function logToDatabase({
   source,
   event,
   level = "info",
   message = "",
   data = null,
   userId = null,
}) {
   try {
      // Ensure DB connection
      await dbConnect();

      // Create the log entry
      const logEntry = new Log({
         timestamp: new Date(),
         source,
         event,
         level,
         message,
         data,
         userId,
      });

      // Save to database
      await logEntry.save();

      // Also log to console in development
      if (process.env.NODE_ENV !== "production") {
         console.log(`[${level.toUpperCase()}][${source}][${event}]`, message);
         if (data) {
            console.dir(data, { depth: null, colors: true });
         }
      }

      return logEntry;
   } catch (error) {
      // Fallback to console logging if database logging fails
      console.error("Failed to write log to database:", error);
      console.error(
         `[${level.toUpperCase()}][${source}][${event}]`,
         message,
         data
      );

      // Return null to indicate failure
      return null;
   }
}

/**
 * Shorthand for info level logs
 */
export function logInfo(
   source,
   event,
   message = "",
   data = null,
   userId = null
) {
   return logToDatabase({
      source,
      event,
      level: "info",
      message,
      data,
      userId,
   });
}

/**
 * Shorthand for error level logs
 */
export function logError(
   source,
   event,
   message = "",
   data = null,
   userId = null
) {
   return logToDatabase({
      source,
      event,
      level: "error",
      message,
      data,
      userId,
   });
}

/**
 * Shorthand for warning level logs
 */
export function logWarning(
   source,
   event,
   message = "",
   data = null,
   userId = null
) {
   return logToDatabase({
      source,
      event,
      level: "warning",
      message,
      data,
      userId,
   });
}

/**
 * Shorthand for debug level logs
 */
export function logDebug(
   source,
   event,
   message = "",
   data = null,
   userId = null
) {
   return logToDatabase({
      source,
      event,
      level: "debug",
      message,
      data,
      userId,
   });
}

import { getServerSession } from "next-auth/next";
import { authOptions } from "../../api/auth/[...nextauth]/route";
import { NextResponse } from "next/server";
import {
   checkUserFeatureAccess,
   getUserSubscriptionPlan,
} from "@/app/lib/stripe/subscriptionUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";

/**
 * Check if the current user has access to a specific feature
 * @param {string} feature - The feature to check access for
 * @returns {Promise<boolean>} - Whether the user has access to the feature
 */
export async function checkFeatureAccess(feature) {
   try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) return false;

      await dbConnect();
      const user = await User.findById(session.user.id);
      if (!user) return false;

      return await checkUserFeatureAccess(user._id, feature);
   } catch (error) {
      console.error("Error checking feature access:", error);
      return false;
   }
}

/**
 * Middleware to protect API routes based on feature access
 * @param {string} feature - The feature to check access for
 * @returns {Function} - Express middleware function
 */
export function requireFeature(feature) {
   return async (req) => {
      try {
         const session = await getServerSession(authOptions);
         if (!session?.user?.id) {
            return NextResponse.json(
               { error: "Unauthorized" },
               { status: 401 }
            );
         }

         await dbConnect();
         const user = await User.findById(session.user.id);
         if (!user) {
            return NextResponse.json(
               { error: "User not found" },
               { status: 404 }
            );
         }

         const hasAccess = await checkUserFeatureAccess(user._id, feature);

         if (!hasAccess) {
            const plan = await getUserSubscriptionPlan(user._id);
            return NextResponse.json(
               {
                  error: "Subscription required",
                  message: `This feature requires a subscription plan with access to "${feature}". Your current plan: ${plan.name}`,
                  feature,
                  currentPlan: plan.id,
               },
               { status: 403 }
            );
         }

         // If the user has access, continue to the next middleware/handler
         return null;
      } catch (error) {
         console.error("Error checking feature access:", error);
         return NextResponse.json(
            {
               error: "Internal server error",
               message: "Failed to check feature access",
            },
            { status: 500 }
         );
      }
   };
}

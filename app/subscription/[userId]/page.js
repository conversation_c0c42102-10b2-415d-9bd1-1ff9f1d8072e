"use client";

import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { use } from "react";
import { OnboardingSubscriptionForm } from "@/app/components/onboarding/OnboardingSubscriptionForm";
import { LogOut } from "lucide-react";

export default function SubscriptionPage({ params }) {
   const { data: session, status } = useSession();
   const router = useRouter();
   const { userId } = use(params);
   const [loading, setLoading] = useState(true);

   useEffect(() => {
      const handleSubscriptionPageLogic = async () => {
         // Redirect if not authenticated
         if (status !== "loading" && !session?.user) {
            router.push("/auth/login");
            return;
         }

         // Redirect if trying to access someone else's subscription page
         if (session?.user?.id && session.user.id !== userId) {
            router.push("/budget");
            return;
         }

         // Check if user has already completed onboarding
         if (status !== "loading" && session?.user?.id) {
            // First check session data for onboarding status
            if (session.user?.onboardingComplete !== undefined) {
               console.log(
                  "Subscription page: Using session data for onboarding status:",
                  session.user.onboardingComplete
               );

               if (session.user.onboardingComplete) {
                  console.log(
                     "Subscription page: User onboarding complete, redirecting to budget"
                  );
                  router.push("/budget");
                  return;
               }
               // User hasn't completed onboarding - continue with subscription selection
            } else {
               // Fallback to API call if session doesn't have onboarding status
               try {
                  console.log(
                     "Subscription page: Session missing onboarding status, checking via API for user:",
                     session.user.id
                  );

                  // Add timeout to prevent hanging
                  const controller = new AbortController();
                  const timeoutId = setTimeout(() => {
                     console.warn(
                        "Subscription page: API call timing out after 3 seconds"
                     );
                     controller.abort();
                  }, 3000); // Reduced timeout to 3 seconds

                  const response = await fetch("/api/user", {
                     signal: controller.signal,
                     headers: {
                        "Cache-Control": "no-cache",
                     },
                  });

                  clearTimeout(timeoutId);

                  if (response.ok) {
                     const userData = await response.json();
                     console.log("Subscription page: User data received:", {
                        userId: userData.id,
                        onboardingComplete: userData.onboardingComplete,
                     });

                     if (userData.onboardingComplete) {
                        console.log(
                           "Subscription page: User onboarding complete, redirecting to budget"
                        );
                        router.push("/budget");
                        return;
                     }
                  } else if (response.status === 404) {
                     // User not found in MongoDB yet - this is expected for new users
                     console.log(
                        "Subscription page: User not found in MongoDB yet, continuing with subscription selection"
                     );
                  } else {
                     console.warn(
                        "Subscription page: API call failed:",
                        response.status,
                        response.statusText
                     );
                  }
               } catch (error) {
                  if (error.name === "AbortError") {
                     console.warn(
                        "Subscription page: API call timed out, continuing with subscription selection"
                     );
                  } else {
                     console.error(
                        "Subscription page: Error checking onboarding status:",
                        error
                     );
                  }
                  // Don't block the subscription page - let user continue
               }
            }
         }

         setLoading(false);
      };

      handleSubscriptionPageLogic();
   }, [session, status, userId, router]);

   const handleSubscriptionSubmit = (selectedPlan) => {
      // Redirect to onboarding after subscription selection
      router.push(`/onboarding/${userId}?from=subscription`);
   };

   const handleSkipSubscription = () => {
      // Skip subscription and go to onboarding
      router.push(`/onboarding/${userId}?from=subscription`);
   };

   if (loading || status === "loading") {
      return (
         <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
            <div className="text-center">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto"></div>
               <p className="mt-4 text-gray-600 dark:text-gray-300">
                  Loading...
               </p>
            </div>
         </div>
      );
   }

   if (!session?.user) {
      return null; // Will redirect
   }

   if (session.user.id !== userId) {
      return null; // Will redirect
   }

   return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
         <div className="max-w-6xl mx-auto px-4">
            {/* Logout button */}
            <div className="flex justify-end mb-6">
               <button
                  onClick={() => signOut({ callbackUrl: "/auth/login" })}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
               >
                  <LogOut className="w-4 h-4" />
                  <span>Sign Out</span>
               </button>
            </div>

            <div className="text-center mb-8">
               <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Welcome to Your Budget App!
               </h1>
               <p className="text-gray-600 dark:text-gray-300">
                  Before we set up your financial information, let's choose the
                  plan that works best for you.
               </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
               <OnboardingSubscriptionForm
                  onSubmit={handleSubscriptionSubmit}
                  onSkip={handleSkipSubscription}
               />
            </div>
         </div>
      </div>
   );
}

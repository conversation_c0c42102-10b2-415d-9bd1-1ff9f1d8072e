"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchPara<PERSON>, usePathname } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import RecurringIncomes from "@/app/components/settings/RecurringIncomes";
import EditExpenseModal from "@/app/components/settings/EditExpenseModal";
import EditAccountModal from "@/app/components/settings/EditAccountModal";
import AddAccountModal from "@/app/components/settings/AddAccountModal";
import { RecurringExpenseDeletionModal } from "@/app/components/settings/RecurringExpenseDeletionModal";
import DeleteAccountModal from "@/app/components/settings/DeleteAccountModal";
import PlaidConnectionStatus from "@/app/components/settings/PlaidConnectionStatus";
import { usePlaidLink } from "react-plaid-link";
import { Badge } from "@/components/ui/badge";
import {
   Accordion,
   AccordionContent,
   AccordionItem,
   AccordionTrigger,
} from "@/components/ui/accordion";
import PricingTable from "@/app/components/subscription/PricingTable";
import SubscriptionManager from "@/app/components/subscription/SubscriptionManager";

const PAY_PERIODS = [
   { value: "weekly", label: "Weekly" },
   { value: "biweekly", label: "Bi-weekly" },
   { value: "monthly", label: "Monthly" },
];

const DAYS_OF_WEEK = [
   { value: "1", label: "Monday" },
   { value: "2", label: "Tuesday" },
   { value: "3", label: "Wednesday" },
   { value: "4", label: "Thursday" },
   { value: "5", label: "Friday" },
   { value: "6", label: "Saturday" },
   { value: "7", label: "Sunday" },
];

const EXPENSE_FREQUENCIES = [
   { value: "weekly", label: "Weekly" },
   { value: "biweekly", label: "Bi-weekly" },
   { value: "monthly", label: "Monthly" },
   { value: "quarterly", label: "Quarterly" },
   { value: "annually", label: "Annually" },
];

const TABS = [
   { id: "profile", label: "Profile" },
   { id: "subscription", label: "Subscription" },
   { id: "income", label: "Recurring Income" },
   { id: "expenses", label: "Recurring Expenses" },
   { id: "accounts", label: "Accounts & Bank Connections" },
];

const ACCOUNT_TYPES = [
   {
      value: "cash",
      label: "Cash",
      description:
         "For checking, savings, and other cash accounts. These accounts will be included in your budget.",
   },
   {
      value: "credit",
      label: "Credit Card",
      description:
         "For credit cards and other revolving credit accounts. Track your credit card spending and payments.",
   },
   {
      value: "loan",
      label: "Loan",
      description:
         "For mortgages, personal loans, and other debt accounts. Track your loan balances and payments.",
   },
   {
      value: "investment",
      label: "Investment",
      description:
         "For retirement accounts, brokerage accounts, and other investments. These accounts are for tracking only.",
   },
];

const formatIncomeOption = (income) => {
   const amount = parseFloat(income.payAmount).toFixed(2);
   const period =
      income.payPeriod.charAt(0).toUpperCase() + income.payPeriod.slice(1);
   return `${income.description} (${period} - $${amount})`;
};

// Add a number formatting helper function at the top
const formatNumber = (number) => {
   return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
   }).format(Math.abs(number));
};

// Helper function to group expenses by frequency
const groupExpensesByFrequency = (expenses) => {
   const activeGroups = {};
   const disabledGroups = {};
   expenses.forEach((expense) => {
      const freq = expense.frequency;
      if (expense.enabled) {
         if (!activeGroups[freq]) activeGroups[freq] = [];
         activeGroups[freq].push(expense);
      } else {
         if (!disabledGroups[freq]) disabledGroups[freq] = [];
         disabledGroups[freq].push(expense);
      }
   });
   return { activeGroups, disabledGroups };
};

const SettingsPage = () => {
   return (
      <Suspense fallback={<div>Loading...</div>}>
         <SettingsContent />
      </Suspense>
   );
};

const SettingsContent = () => {
   const router = useRouter();
   const searchParams = useSearchParams();
   const pathname = usePathname();
   const { data: session, status } = useSession();
   const [loading, setLoading] = useState(true);
   const [saving, setSaving] = useState(false);
   const [error, setError] = useState("");
   const [success, setSuccess] = useState("");
   const [showExpenseForm, setShowExpenseForm] = useState(false);
   const [editingExpense, setEditingExpense] = useState(null);
   const [showDisabledExpenses, setShowDisabledExpenses] = useState(false);
   const [subscriptionPlan, setSubscriptionPlan] = useState("free");
   const [subscriptionFeatures, setSubscriptionFeatures] = useState([]);
   const [formData, setFormData] = useState({
      name: "",
      email: "",
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      mainIncomeId: null,
      payPeriod: "monthly",
      payAmount: "",
      payDay: "1",
      recurringExpenses: [],
      recurringIncomes: [],
      accounts: [],
      plaidItems: [],
   });
   const [newExpense, setNewExpense] = useState({
      name: "",
      amount: "",
      frequency: "monthly",
      dueDay: "1",
      dueMonth: "1",
      enabled: true,
   });
   const [newAccount, setNewAccount] = useState({
      name: "",
      bank: "",
      accountType: "cash",
      balance: "",
      active: true,
      dueDate: "",
      minimumPayment: "",
      interestRate: "",
   });
   const [showAccountForm, setShowAccountForm] = useState(false);
   const [editingAccount, setEditingAccount] = useState(null);
   const [showAddAccountModal, setShowAddAccountModal] = useState(false);
   const [activeTab, setActiveTab] = useState(
      searchParams.get("tab") || "profile"
   );
   const [linkToken, setLinkToken] = useState(null);
   const [plaidConnection, setPlaidConnection] = useState(null);
   const [pendingAccountId, setPendingAccountId] = useState(null);
   const [syncStartDate, setSyncStartDate] = useState(null);
   const [connectionStatuses, setConnectionStatuses] = useState({});
   const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);
   const [resetPricingTableLoading, setResetPricingTableLoading] =
      useState(false);

   // Redirect if not authenticated
   useEffect(() => {
      if (status !== "loading" && !session?.user) {
         router.push("/auth/login");
      }
   }, [status, session?.user, router]);

   // Update tab when URL changes
   useEffect(() => {
      const tab = searchParams.get("tab");
      if (tab && TABS.some((t) => t.id === tab)) {
         // Only update if it's a valid tab
         const params = new URLSearchParams(searchParams);
         params.set("tab", tab);
         router.push(`${pathname}?${params.toString()}`, { replace: true });
      } else {
         // Default to first tab
         setActiveTab(TABS[0].id);
      }
   }, [searchParams, pathname, router]);

   // Fetch user data when user changes
   useEffect(() => {
      if (!session?.user) return;
      fetchUserData();
   }, [session?.user]);

   // Individual accounts will check their own connection status

   // Check subscription plan separately to avoid refetching on focus
   useEffect(() => {
      if (!session?.user) return;

      const checkSubscriptionPlan = async () => {
         try {
            const response = await fetch("/api/stripe/subscription");
            if (response.ok) {
               const data = await response.json();
               const plan = data.plan;

               setSubscriptionPlan(plan.id);
               setSubscriptionFeatures(plan.features);
            } else {
               setSubscriptionPlan("free");
               setSubscriptionFeatures([
                  "Basic budget tracking",
                  "Up to 3 accounts",
                  "Basic reports",
               ]);
            }
         } catch (error) {
            console.error("Error checking subscription:", error);
            setSubscriptionPlan("free");
            setSubscriptionFeatures([
               "Basic budget tracking",
               "Up to 3 accounts",
               "Basic reports",
            ]);
         }
      };

      checkSubscriptionPlan();
   }, [session?.user]);

   // Handle URL parameters from Stripe checkout
   useEffect(() => {
      const success = searchParams.get("success");
      const canceled = searchParams.get("canceled");

      if (success === "true") {
         setSuccess("Subscription updated successfully!");
         // Refresh subscription data
         if (session?.user) {
            const checkSubscriptionPlan = async () => {
               try {
                  const response = await fetch("/api/stripe/subscription");
                  if (response.ok) {
                     const data = await response.json();
                     const plan = data.plan;
                     setSubscriptionPlan(plan.id);
                     setSubscriptionFeatures(plan.features);
                  }
               } catch (error) {
                  console.error("Error checking subscription:", error);
               }
            };
            checkSubscriptionPlan();
         }
      } else if (canceled === "true") {
         setError("Checkout was canceled. You can try again anytime.");
         // Clear any loading states that might prevent subsequent attempts
         setResetPricingTableLoading(true);
         // Reset the flag after a brief delay
         setTimeout(() => setResetPricingTableLoading(false), 100);
      }

      // Clean up URL parameters after handling them
      if (success || canceled) {
         const params = new URLSearchParams(searchParams);
         params.delete("success");
         params.delete("canceled");
         const newUrl = `${pathname}?${params.toString()}`;
         router.replace(newUrl);
      }
   }, [searchParams, pathname, router, session?.user]);

   const fetchUserData = async () => {
      try {
         setLoading(true);
         const response = await fetch("/api/user");
         if (!response.ok) throw new Error("Failed to fetch user data");
         const data = await response.json();

         // Use a function to only update state if data has actually changed
         setFormData((prev) => {
            // Check if accounts array has changed to avoid unnecessary re-renders
            const accountsChanged =
               !prev.accounts ||
               prev.accounts.length !== (data.accounts || []).length ||
               JSON.stringify(prev.accounts) !==
                  JSON.stringify(data.accounts || []);

            // Only update state if something has actually changed
            if (
               prev.name !== (data.name || "") ||
               prev.email !== (data.email || "") ||
               prev.mainIncomeId !== (data.mainIncomeId || "") ||
               prev.payPeriod !== (data.payPeriod || "monthly") ||
               prev.payAmount !== (data.payAmount || "") ||
               prev.payDay !== (data.payDay || "1") ||
               prev.payDayOfWeek !== (data.payDayOfWeek || "1") ||
               prev.payWeekDay !== (data.payWeekDay || "Sunday") ||
               JSON.stringify(prev.recurringExpenses) !==
                  JSON.stringify(data.recurringExpenses || []) ||
               JSON.stringify(prev.recurringIncomes) !==
                  JSON.stringify(data.recurringIncomes || []) ||
               accountsChanged ||
               JSON.stringify(prev.plaidItems) !==
                  JSON.stringify(data.plaidItems || []) ||
               JSON.stringify(prev.preferences) !==
                  JSON.stringify(data.preferences || {})
            ) {
               return {
                  ...prev,
                  name: data.name || "",
                  email: data.email || "",
                  mainIncomeId: data.mainIncomeId || "",
                  payPeriod: data.payPeriod || "monthly",
                  payAmount: data.payAmount || "",
                  payDay: data.payDay || "1",
                  payDayOfWeek: data.payDayOfWeek || "1",
                  payWeekDay: data.payWeekDay || "Sunday",
                  recurringExpenses: data.recurringExpenses || [],
                  recurringIncomes: data.recurringIncomes || [],
                  accounts: data.accounts || [],
                  plaidItems: data.plaidItems || [],
                  preferences: data.preferences || {
                     showFutureExpenses: true,
                     cashFlowWarnings: { enabled: true, periodsAhead: 4 },
                  },
               };
            }

            // Return previous state if nothing changed
            return prev;
         });
      } catch (err) {
         setError("Failed to load user data");
         console.error("Error:", err);
      } finally {
         setLoading(false);
      }
   };

   const handleChange = async (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));

      // If changing the main income, update the user's pay period settings
      if (name === "mainIncomeId" && value) {
         try {
            setSaving(true);
            const selectedIncome = formData.recurringIncomes.find(
               (income) => income._id === value
            );

            if (!selectedIncome) {
               throw new Error("Selected income not found");
            }

            // Create update object with all pay period fields
            const updateData = {
               mainIncomeId: selectedIncome._id,
               payPeriod: selectedIncome.payPeriod,
               payAmount: selectedIncome.payAmount,
               payDay: selectedIncome.payDay,
               payDayOfWeek: selectedIncome.payDayOfWeek,
               payWeekDay: selectedIncome.payWeekDay,
            };

            const response = await fetch("/api/user", {
               method: "PUT",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify(updateData),
            });

            if (!response.ok) {
               const data = await response.json();
               throw new Error(data.error || "Failed to update main income");
            }

            // Update local state with all fields
            setFormData((prev) => ({
               ...prev,
               ...updateData,
            }));

            handleSuccess("Main income updated successfully");
         } catch (err) {
            handleError(err.message);
            // Revert the selection if there was an error
            setFormData((prev) => ({
               ...prev,
               mainIncomeId: prev.mainIncomeId,
            }));
         } finally {
            setSaving(false);
         }
      }
   };

   const handleWarningSettingChange = async (setting, value) => {
      try {
         setSaving(true);

         // Update local state immediately for better UX
         setFormData((prev) => ({
            ...prev,
            preferences: {
               ...prev.preferences,
               cashFlowWarnings: {
                  ...prev.preferences?.cashFlowWarnings,
                  [setting]: value,
               },
            },
         }));

         // Save to server
         const response = await fetch("/api/user/preferences", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               cashFlowWarnings: {
                  [setting]: value,
               },
            }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to update warning settings");
         }

         handleSuccess(`Warning settings updated successfully`);
      } catch (err) {
         handleError(err.message);
         // Revert the change if there was an error
         await fetchUserData();
      } finally {
         setSaving(false);
      }
   };

   const handleExpenseChange = (e) => {
      const { name, value } = e.target;
      setNewExpense((prev) => {
         const updated = {
            ...prev,
            [name]: value,
         };

         // Add weeklyChargeType only when frequency is weekly
         if (name === "frequency" && value === "weekly") {
            updated.weeklyChargeType = "one-time";
            updated.dueDay = "1"; // Reset to Monday
         } else if (name === "frequency" && value !== "weekly") {
            // Remove weeklyChargeType when switching away from weekly
            const { weeklyChargeType, ...rest } = updated;

            // Reset dueDay and dueMonth when changing frequency
            if (value === "annually") {
               rest.dueDay = "1"; // Reset to January
               rest.dueMonth = "1"; // Reset to 1st day
            } else {
               rest.dueDay = "1"; // Reset to 1st day for monthly/quarterly
            }

            return rest;
         }

         return updated;
      });
   };

   const handleExpenseSubmit = async () => {
      const expenseToSubmit = {
         ...newExpense,
         id: Date.now(),
         // Only add weeklyChargeType if frequency is weekly
         ...(newExpense.frequency === "weekly"
            ? { weeklyChargeType: "one-time" }
            : {}),
         // Only add dueMonth if frequency is annually
         ...(newExpense.frequency === "annually"
            ? { dueMonth: newExpense.dueMonth || "1" }
            : {}),
      };

      const updatedExpenses = [...formData.recurringExpenses, expenseToSubmit];

      // Update local state
      setFormData((prev) => ({
         ...prev,
         recurringExpenses: updatedExpenses,
      }));

      // Save to database
      try {
         setSaving(true);
         const response = await fetch("/api/user", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               ...formData,
               recurringExpenses: updatedExpenses,
            }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to save expense");
         }

         setSuccess("Expense added successfully");
         setNewExpense({
            name: "",
            amount: "",
            frequency: "monthly",
            dueDay: "1",
            dueMonth: "1",
            enabled: true,
         });
         setShowExpenseForm(false);
      } catch (err) {
         setError(err.message);
         // Revert local state if save failed
         setFormData((prev) => ({
            ...prev,
            recurringExpenses: formData.recurringExpenses,
         }));
      } finally {
         setSaving(false);
      }
   };

   const toggleExpense = async (expenseId) => {
      try {
         setSaving(true);

         // Find the expense
         const expense = formData.recurringExpenses.find(
            (exp) => String(exp.id) === String(expenseId)
         );

         if (!expense) {
            throw new Error("Expense not found");
         }

         // Toggle the enabled status
         const updatedExpense = {
            ...expense,
            enabled: !expense.enabled,
         };

         // Update in the database
         const response = await fetch(
            `/api/user/recurring-expenses/${expenseId}`,
            {
               method: "PATCH",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({ enabled: !expense.enabled }),
            }
         );

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to toggle expense");
         }

         // Update local state after successful update
         setFormData((prev) => ({
            ...prev,
            recurringExpenses: prev.recurringExpenses.map((exp) =>
               String(exp.id) === String(expenseId) ? updatedExpense : exp
            ),
         }));

         setSuccess(
            `Expense ${
               updatedExpense.enabled ? "enabled" : "disabled"
            } successfully`
         );
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   // State for deletion modal
   const [deletionModal, setDeletionModal] = useState({
      isOpen: false,
      recurringExpense: null,
   });

   const deleteExpense = async (expenseId) => {
      // Instead of simple confirm, open the deletion modal
      const expense = formData.recurringExpenses.find(
         (exp) => String(exp.id) === String(expenseId)
      );

      setDeletionModal({
         isOpen: true,
         recurringExpense: expense,
      });
   };

   const handleConfirmDeletion = async (expenseId, action) => {
      try {
         setSaving(true);

         const response = await fetch(
            `/api/user/recurring-expenses/${expenseId}`,
            {
               method: "DELETE",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({ action }),
            }
         );

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to delete expense");
         }

         const result = await response.json();

         // Update local state after successful deletion
         setFormData((prev) => ({
            ...prev,
            recurringExpenses: prev.recurringExpenses.filter(
               (expense) => String(expense.id) !== String(expenseId)
            ),
         }));

         setDeletionModal({ isOpen: false, recurringExpense: null });

         // Show detailed success message based on action taken
         const message =
            action === "template-only"
               ? `Template deleted. ${
                    result.standaloneExpenses || 0
                 } expenses converted to standalone items.`
               : `Template and ${
                    result.deletedExpenses || 0
                 } expenses deleted. ${
                    result.unassignedTransactions || 0
                 } transactions unassigned.`;

         setSuccess(message);
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const deleteAccount = async (accountId) => {
      if (!window.confirm("Are you sure you want to delete this account?"))
         return;

      try {
         setSaving(true);
         const response = await fetch("/api/user/accounts", {
            method: "DELETE",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ accountId: String(accountId) }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to delete account");
         }

         // Update local state after successful deletion
         setFormData((prev) => {
            // Find the account being deleted to get its plaidItemId
            const accountToDelete = prev.accounts.find(
               (acc) => String(acc._id) === String(accountId)
            );

            return {
               ...prev,
               accounts: prev.accounts.filter(
                  (account) => String(account._id) !== String(accountId)
               ),
               // Remove any associated plaidItems
               plaidItems: prev.plaidItems.filter(
                  (item) =>
                     !accountToDelete?.plaidItemId ||
                     item.itemId !== accountToDelete.plaidItemId
               ),
               // Remove associated credit card payment expenses
               recurringExpenses: prev.recurringExpenses.filter(
                  (expense) =>
                     !(
                        String(expense.linkedAccountId) === String(accountId) &&
                        expense.type === "credit_card_payment"
                     )
               ),
            };
         });

         setSuccess("Account deleted successfully");
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const toggleAccount = async (accountId) => {
      try {
         setSaving(true);

         // Find the account
         const account = formData.accounts.find(
            (acc) => String(acc._id) === String(accountId)
         );

         if (!account) {
            throw new Error("Account not found");
         }

         // Toggle the active status
         const updatedAccount = {
            ...account,
            active: !account.active,
         };

         // Update in the database
         const response = await fetch("/api/user/accounts", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               account: {
                  _id: String(accountId),
                  active: !account.active,
               },
            }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to toggle account");
         }

         // Update local state after successful update
         setFormData((prev) => ({
            ...prev,
            accounts: prev.accounts.map((acc) =>
               String(acc._id) === String(accountId) ? updatedAccount : acc
            ),
         }));

         setSuccess(
            `Account ${
               updatedAccount.active ? "activated" : "deactivated"
            } successfully`
         );
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const handlePasswordSubmit = async (e) => {
      e.preventDefault();
      try {
         setSaving(true);
         const response = await fetch("/api/user/password", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
               currentPassword: formData.currentPassword,
               newPassword: formData.newPassword,
            }),
         });

         if (!response.ok) throw new Error("Failed to update password");
         setSuccess("Password updated successfully");
         setFormData((prev) => ({
            ...prev,
            currentPassword: "",
            newPassword: "",
            confirmPassword: "",
         }));
         // Keep the user on the same tab after saving
         const params = new URLSearchParams(searchParams);
         params.set("tab", "profile");
         router.push(`${pathname}?${params.toString()}`);
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   // handleSaveEditedAccount removed - EditAccountModal now handles its own save logic

   const hasWeeklyExpenses = formData.recurringExpenses.some(
      (expense) => expense.frequency === "weekly"
   );

   // Add Plaid Link handler
   const handlePlaidSuccess = async (publicToken, metadata) => {
      try {
         console.log("handlePlaidSuccess called:", {
            pendingAccountId,
            pendingAccountIdType: typeof pendingAccountId,
            editingAccount: editingAccount
               ? {
                    id: editingAccount._id,
                    name: editingAccount.name,
                    bank: editingAccount.bank,
                    plaidItemId: editingAccount.plaidItemId,
                 }
               : null,
            metadata: {
               institution: metadata.institution?.name,
               account: metadata.account?.id,
            },
            timestamp: new Date().toISOString(),
         });

         // Exchange public token for access token
         const response = await fetch("/api/plaid/exchange-token", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               public_token: publicToken,
               institution: metadata.institution,
               // Pass the pending account ID for automatic linking
               ...(pendingAccountId ? { accountId: pendingAccountId } : {}),
               // Pass the sync start date if available
               ...(syncStartDate ? { syncStartDate: syncStartDate } : {}),
            }),
         });

         if (!response.ok) {
            throw new Error("Failed to exchange token");
         }

         const data = await response.json();
         console.log("Plaid exchange response:", {
            success: data.success,
            institutionName: data.institutionName,
            accountCount: data.accountCount,
            autoLinked: data.autoLinked,
            onboardingMode: data.onboardingMode,
            plaidItemsCount: data.plaidItems?.length || 0,
         });

         // Find the matching plaid item from the response
         const plaidItem = data.plaidItems?.find(
            (item) => item.plaidAccountId === metadata.account.id
         );

         if (!plaidItem) {
            console.error("Failed to find matching Plaid item:", {
               metadataAccountId: metadata.account.id,
               availablePlaidItems:
                  data.plaidItems?.map((item) => ({
                     plaidAccountId: item.plaidAccountId,
                     itemId: item.itemId,
                     name: item.name,
                  })) || [],
            });
            throw new Error("Failed to find matching Plaid item");
         }

         console.log("Found matching Plaid item:", {
            plaidItem: {
               plaidAccountId: plaidItem.plaidAccountId,
               itemId: plaidItem.itemId,
               name: plaidItem.name,
               linkedAccountId: plaidItem.linkedAccountId,
            },
         });

         // If we had a pending account ID and auto-linking was successful
         if (pendingAccountId && data.autoLinked) {
            console.log("Auto-linking successful, closing modal");
            setSuccess("Bank connected and linked to account successfully!");
            // Close the editing modal automatically since everything is done
            setEditingAccount(null);
            setPendingAccountId(null);
         } else if (pendingAccountId) {
            console.error("Auto-linking failed:", {
               pendingAccountId,
               autoLinked: data.autoLinked,
               onboardingMode: data.onboardingMode,
            });
            setError(
               "Bank connected but failed to link to account automatically."
            );
            setPendingAccountId(null);
         } else {
            console.log(
               "No pending account ID, setting up for new account creation"
            );
            // For new accounts, set the connection for the AddAccountModal to use
            setSuccess("Bank connected! Please complete account setup.");
            setPlaidConnection({
               institutionName: metadata.institution.name,
               institutionId: metadata.institution.institution_id,
               itemId: plaidItem.itemId,
               paired: false,
            });
         }

         // Refresh user data to get updated account and plaid item information
         fetchUserData();
      } catch (error) {
         console.error("Error connecting bank account:", error);
         setError("Failed to connect bank account. Please try again.");
      }
   };

   // Configure Plaid Link
   const config = {
      token: linkToken,
      onSuccess: (public_token, metadata) => {
         handlePlaidSuccess(public_token, metadata);
      },
      onExit: () => {
         // Handle user exit
         setLinkToken(null);
         setPendingAccountId(null);
      },
   };

   const { open, ready } = usePlaidLink(config);

   // Effect to open Plaid when token is ready
   useEffect(() => {
      if (linkToken && ready) {
         open();
      }
   }, [linkToken, ready, open]);

   // Function to initiate Plaid Link
   const handleLinkBank = async (linkTokenData = null) => {
      try {
         // If linkTokenData has a linkToken, use it directly (from EditAccountModal)
         if (linkTokenData && linkTokenData.linkToken) {
            console.log("handleLinkBank called with existing linkToken:", {
               linkToken: linkTokenData.linkToken ? "present" : "missing",
               accountId: linkTokenData.accountId,
               accountIdType: typeof linkTokenData.accountId,
               expiration: linkTokenData.expiration,
               syncStartDate: linkTokenData.syncStartDate,
            });

            setLinkToken(linkTokenData.linkToken);
            setPendingAccountId(linkTokenData.accountId);
            // Store syncStartDate for use in exchange-token
            if (linkTokenData.syncStartDate) {
               setSyncStartDate(linkTokenData.syncStartDate);
            }
            return;
         }

         // Otherwise, create the link token (for AddAccountModal or when date provided)
         console.log("handleLinkBank called, creating new linkToken:", {
            editingAccount: editingAccount
               ? {
                    id: editingAccount._id,
                    name: editingAccount.name,
                    bank: editingAccount.bank,
                    plaidItemId: editingAccount.plaidItemId,
                 }
               : null,
            syncStartDate: linkTokenData?.syncStartDate,
            timestamp: new Date().toISOString(),
         });

         const response = await fetch("/api/plaid/create-link-token", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               // Pass the account ID if we're editing an account (ensure it's a string)
               ...(editingAccount
                  ? { accountId: String(editingAccount._id) }
                  : {}),
               // Pass the syncStartDate if provided from AddAccountModal
               ...(linkTokenData?.syncStartDate
                  ? { syncStartDate: linkTokenData.syncStartDate }
                  : {}),
            }),
         });

         if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));

            if (response.status === 403 && errorData.requiresUpgrade) {
               setError(
                  "Bank connections require a Basic or Pro subscription plan. Please upgrade your subscription to connect your bank account."
               );
               return;
            }

            throw new Error(errorData.message || "Failed to create link token");
         }

         const data = await response.json();
         console.log("Link token response:", {
            linkToken: data.linkToken ? "present" : "missing",
            accountId: data.accountId,
            accountIdType: typeof data.accountId,
            expiration: data.expiration,
         });

         setLinkToken(data.linkToken);
         setPendingAccountId(data.accountId); // Store account ID for exchange-token call

         // Store syncStartDate if provided from AddAccountModal
         if (linkTokenData?.syncStartDate) {
            setSyncStartDate(linkTokenData.syncStartDate);
         }

         console.log("Set pendingAccountId and syncStartDate:", {
            pendingAccountId: data.accountId,
            pendingAccountIdType: typeof data.accountId,
            syncStartDate: linkTokenData?.syncStartDate,
         });
      } catch (error) {
         console.error("Error initiating bank link:", error);
         setError("Failed to initiate bank connection. Please try again.");
      }
   };

   const handleProfileSubmit = async (e) => {
      e.preventDefault();
      try {
         setSaving(true);
         const response = await fetch("/api/user", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
               name: formData.name,
               email: formData.email,
            }),
         });

         if (!response.ok) throw new Error("Failed to update profile");
         setSuccess("Profile updated successfully");
         // Keep the user on the same tab after saving
         const params = new URLSearchParams(searchParams);
         params.set("tab", "profile");
         router.push(`${pathname}?${params.toString()}`);
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const handleRecurringIncomesChange = async (newIncomes) => {
      try {
         setSaving(true);
         const response = await fetch("/api/user", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ recurringIncomes: newIncomes }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to update incomes");
         }

         // Update local state directly with the response data
         const updatedUser = await response.json();
         setFormData((prev) => ({
            ...prev,
            recurringIncomes: updatedUser.recurringIncomes,
         }));

         setSuccess("Incomes updated successfully");
         // Keep the user on the income tab
         const params = new URLSearchParams(searchParams);
         params.set("tab", "income");
         router.push(`${pathname}?${params.toString()}`);
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const handleError = (message) => {
      setError(message);
      setSuccess(""); // Clear any success message
      // Optionally scroll to the top where error messages are displayed
      window.scrollTo({ top: 0, behavior: "smooth" });
   };

   const handleSuccess = (message) => {
      setSuccess(message);
      setError(""); // Clear any error message
      // Optionally scroll to the top where success messages are displayed
      window.scrollTo({ top: 0, behavior: "smooth" });
   };

   const handleConnectionStatusUpdate = (accountId, statusInfo) => {
      setConnectionStatuses((prev) => ({
         ...prev,
         [accountId]: statusInfo,
      }));
   };

   // Removed handleCheckAllConnections since each account checks its own status

   // Function to update URL with new tab
   const handleTabChange = (tabId) => {
      const params = new URLSearchParams(searchParams);
      params.set("tab", tabId);
      router.push(`${pathname}?${params.toString()}`);
   };

   const currentTab = searchParams.get("tab") || "profile";

   const handleSaveEditedExpense = async (editedExpense) => {
      try {
         setSaving(true);
         const response = await fetch(
            `/api/user/recurring-expenses/${editedExpense.id}`,
            {
               method: "PATCH",
               headers: { "Content-Type": "application/json" },
               body: JSON.stringify(editedExpense),
            }
         );

         if (!response.ok) throw new Error("Failed to update expense");

         await fetchUserData();
         setSuccess("Expense updated successfully");
         setEditingExpense(null);
         // Keep the user on the expenses tab
         const params = new URLSearchParams(searchParams);
         params.set("tab", "expenses");
         router.push(`${pathname}?${params.toString()}`);
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const handleSaveAccount = async (accountData) => {
      try {
         setSaving(true);
         const response = await fetch("/api/user/accounts", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(accountData),
         });

         if (!response.ok) throw new Error("Failed to add account");

         await fetchUserData();
         setSuccess("Account added successfully");
         setShowAccountForm(false);
         // Keep the user on the accounts tab
         const params = new URLSearchParams(searchParams);
         params.set("tab", "accounts");
         router.push(`${pathname}?${params.toString()}`);
      } catch (err) {
         setError(err.message);
      } finally {
         setSaving(false);
      }
   };

   if (loading) {
      return (
         <div className="flex items-center justify-center min-h-screen">
            <div className="text-gray-500 dark:text-gray-400">Loading...</div>
         </div>
      );
   }

   if (!session?.user) {
      return null;
   }

   return (
      <div className="flex flex-col w-full h-full">
         <div className="flex-1 w-full px-4 py-8 pt-4 overflow-y-auto">
            <div className="mb-8">
               <Tabs
                  value={currentTab}
                  onValueChange={handleTabChange}
                  className="w-full"
               >
                  <div className="bg-white dark:bg-gray-900 rounded-lg shadow">
                     <div className="p-6 pb-0">
                        <TabsList className="grid w-full grid-cols-5">
                           {TABS.map((tab) => (
                              <TabsTrigger key={tab.id} value={tab.id}>
                                 {tab.label}
                              </TabsTrigger>
                           ))}
                        </TabsList>
                     </div>

                     {error && (
                        <Alert variant="destructive" className="m-6 mb-0">
                           <AlertDescription>{error}</AlertDescription>
                        </Alert>
                     )}

                     {success && (
                        <Alert className="m-6 mb-0 border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/50 dark:text-green-200">
                           <AlertDescription>{success}</AlertDescription>
                        </Alert>
                     )}
                     <TabsContent value="profile" className="p-6 space-y-6">
                        <div className="space-y-6">
                           {/* Profile Information Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Profile Information</CardTitle>
                                 <CardDescription>
                                    Update your personal details and display
                                    preferences.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <form
                                    onSubmit={handleProfileSubmit}
                                    className="space-y-6"
                                 >
                                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                       <div className="space-y-2">
                                          <Label htmlFor="name">Name</Label>
                                          <Input
                                             type="text"
                                             id="name"
                                             name="name"
                                             value={formData.name}
                                             onChange={handleChange}
                                          />
                                       </div>

                                       <div className="space-y-2">
                                          <Label htmlFor="email">Email</Label>
                                          <Input
                                             type="email"
                                             id="email"
                                             name="email"
                                             value={formData.email}
                                             onChange={handleChange}
                                          />
                                       </div>
                                    </div>

                                    <div className="flex justify-end">
                                       <Button type="submit" disabled={saving}>
                                          {saving
                                             ? "Saving..."
                                             : "Save Profile"}
                                       </Button>
                                    </div>
                                 </form>
                              </CardContent>
                           </Card>

                           {/* Cash Flow Warnings Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Cash Flow Warnings</CardTitle>
                                 <CardDescription>
                                    Configure warnings for upcoming negative
                                    cash flow periods.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent className="space-y-6">
                                 <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                       <Label
                                          htmlFor="warningsEnabled"
                                          className="text-base"
                                       >
                                          Enable Cash Flow Warnings
                                       </Label>
                                       <div className="text-sm text-gray-500 dark:text-gray-400">
                                          Get notified when negative cash flow
                                          is expected in upcoming pay periods
                                       </div>
                                    </div>
                                    <Switch
                                       id="warningsEnabled"
                                       checked={
                                          formData.preferences?.cashFlowWarnings
                                             ?.enabled !== false
                                       }
                                       onCheckedChange={(checked) =>
                                          handleWarningSettingChange(
                                             "enabled",
                                             checked
                                          )
                                       }
                                    />
                                 </div>

                                 <div className="space-y-2">
                                    <Label
                                       htmlFor="periodsAhead"
                                       className="text-base"
                                    >
                                       Check Ahead (Pay Periods)
                                    </Label>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                                       How many pay periods in the future to
                                       check for negative cash flow
                                    </div>
                                    <Select
                                       value={
                                          formData.preferences?.cashFlowWarnings?.periodsAhead?.toString() ||
                                          "4"
                                       }
                                       onValueChange={(value) =>
                                          handleWarningSettingChange(
                                             "periodsAhead",
                                             parseInt(value)
                                          )
                                       }
                                    >
                                       <SelectTrigger className="w-32">
                                          <SelectValue />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {[1, 2, 3, 4, 5, 6, 8, 10, 12].map(
                                             (num) => (
                                                <SelectItem
                                                   key={num}
                                                   value={num.toString()}
                                                >
                                                   {num} period
                                                   {num !== 1 ? "s" : ""}
                                                </SelectItem>
                                             )
                                          )}
                                       </SelectContent>
                                    </Select>
                                 </div>

                                 <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <div className="flex items-start space-x-3">
                                       <div className="text-blue-600 dark:text-blue-400 mt-0.5">
                                          ℹ️
                                       </div>
                                       <div className="text-sm text-blue-700 dark:text-blue-300">
                                          <p className="font-medium mb-1">
                                             How it works:
                                          </p>
                                          <ul className="space-y-1 text-blue-600 dark:text-blue-400">
                                             <li>
                                                • Analyzes your recurring income
                                                and expenses
                                             </li>
                                             <li>
                                                • Includes scheduled items and
                                                debt payments
                                             </li>
                                             <li>
                                                • Considers money already
                                                assigned to expenses
                                             </li>
                                             <li>
                                                • Shows the first period with
                                                negative cash flow
                                             </li>
                                          </ul>
                                       </div>
                                    </div>
                                 </div>
                              </CardContent>
                           </Card>

                           {/* Password Change Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Change Password</CardTitle>
                                 <CardDescription>
                                    Update your password to keep your account
                                    secure.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <form
                                    onSubmit={handlePasswordSubmit}
                                    className="space-y-6"
                                 >
                                    <div className="space-y-2">
                                       <Label htmlFor="currentPassword">
                                          Current Password
                                       </Label>
                                       <Input
                                          type="password"
                                          id="currentPassword"
                                          name="currentPassword"
                                          value={formData.currentPassword}
                                          onChange={handleChange}
                                       />
                                    </div>

                                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                       <div className="space-y-2">
                                          <Label htmlFor="newPassword">
                                             New Password
                                          </Label>
                                          <Input
                                             type="password"
                                             id="newPassword"
                                             name="newPassword"
                                             value={formData.newPassword}
                                             onChange={handleChange}
                                          />
                                       </div>

                                       <div className="space-y-2">
                                          <Label htmlFor="confirmPassword">
                                             Confirm New Password
                                          </Label>
                                          <Input
                                             type="password"
                                             id="confirmPassword"
                                             name="confirmPassword"
                                             value={formData.confirmPassword}
                                             onChange={handleChange}
                                          />
                                       </div>
                                    </div>

                                    <div className="flex justify-end">
                                       <Button type="submit" disabled={saving}>
                                          {saving
                                             ? "Saving..."
                                             : "Change Password"}
                                       </Button>
                                    </div>
                                 </form>
                              </CardContent>
                           </Card>

                           {/* Delete Account Card */}
                           <Card className="border-red-200 dark:border-red-800">
                              <CardHeader>
                                 <CardTitle className="text-red-600 dark:text-red-400">
                                    Delete Account
                                 </CardTitle>
                                 <CardDescription>
                                    Permanently delete your account and all
                                    associated data. This action cannot be
                                    undone.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <div className="space-y-4">
                                    <div className="p-4 border border-red-200 dark:border-red-800 rounded-md bg-red-50 dark:bg-red-900/20">
                                       <p className="text-sm text-red-700 dark:text-red-300">
                                          <strong>Warning:</strong> Deleting
                                          your account will permanently remove:
                                       </p>
                                       <ul className="mt-2 text-sm text-red-600 dark:text-red-400 list-disc list-inside ml-4">
                                          <li>
                                             All financial transactions and
                                             records
                                          </li>
                                          <li>All income and expense data</li>
                                          <li>All debt tracking information</li>
                                          <li>All connected bank accounts</li>
                                          <li>Your profile and preferences</li>
                                       </ul>
                                    </div>

                                    <div className="flex justify-end">
                                       <Button
                                          variant="destructive"
                                          onClick={() =>
                                             setShowDeleteAccountModal(true)
                                          }
                                          className="h-10"
                                       >
                                          Delete Account
                                       </Button>
                                    </div>
                                 </div>
                              </CardContent>
                           </Card>
                        </div>
                     </TabsContent>

                     <TabsContent
                        value="subscription"
                        className="p-6 space-y-6"
                     >
                        <div className="space-y-6">
                           {/* Subscription Plans Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Subscription Plans</CardTitle>
                                 <CardDescription>
                                    Choose a subscription plan that fits your
                                    needs. Upgrade to access premium features.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <div className="mb-6">
                                    <h3 className="text-lg font-medium mb-2">
                                       Your Current Plan
                                    </h3>
                                    <div className="p-4 border rounded-md bg-gray-50 dark:bg-gray-800/50">
                                       <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                          <div>
                                             <p className="font-medium">
                                                {subscriptionPlan
                                                   .charAt(0)
                                                   .toUpperCase() +
                                                   subscriptionPlan.slice(
                                                      1
                                                   )}{" "}
                                                Plan
                                             </p>
                                             <p className="text-sm text-gray-500 dark:text-gray-400">
                                                {subscriptionFeatures.map(
                                                   (feature, index) => (
                                                      <span key={index}>
                                                         {feature}
                                                         {index <
                                                         subscriptionFeatures.length -
                                                            1
                                                            ? ", "
                                                            : ""}
                                                      </span>
                                                   )
                                                )}
                                             </p>
                                          </div>
                                       </div>
                                    </div>
                                 </div>

                                 <div className="mb-8">
                                    <SubscriptionManager />
                                 </div>

                                 <div className="mb-8">
                                    <h3 className="text-lg font-medium mb-4">
                                       Available Plans
                                    </h3>
                                    <PricingTable
                                       currentPlan={subscriptionPlan}
                                       resetLoading={resetPricingTableLoading}
                                    />
                                 </div>
                              </CardContent>
                           </Card>

                           {/* FAQ Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Subscription FAQ</CardTitle>
                                 <CardDescription>
                                    Common questions about our subscription
                                    plans
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <Accordion
                                    type="single"
                                    collapsible
                                    className="w-full"
                                 >
                                    <AccordionItem value="item-1">
                                       <AccordionTrigger>
                                          How do I upgrade my plan?
                                       </AccordionTrigger>
                                       <AccordionContent>
                                          You can upgrade your plan by selecting
                                          a plan from the pricing table above
                                          and following the checkout process.
                                          Your new plan benefits will be
                                          available immediately after payment.
                                       </AccordionContent>
                                    </AccordionItem>
                                    <AccordionItem value="item-2">
                                       <AccordionTrigger>
                                          Can I downgrade my plan?
                                       </AccordionTrigger>
                                       <AccordionContent>
                                          Yes, you can downgrade your plan at
                                          any time. Your current plan benefits
                                          will remain active until the end of
                                          your billing period.
                                       </AccordionContent>
                                    </AccordionItem>
                                    <AccordionItem value="item-3">
                                       <AccordionTrigger>
                                          How do I cancel my subscription?
                                       </AccordionTrigger>
                                       <AccordionContent>
                                          You can cancel your subscription by
                                          clicking the "Manage Billing" button
                                          and selecting the cancel option. Your
                                          plan benefits will remain active until
                                          the end of your billing period.
                                       </AccordionContent>
                                    </AccordionItem>
                                 </Accordion>
                              </CardContent>
                           </Card>
                        </div>
                     </TabsContent>

                     <TabsContent value="income" className="p-6 space-y-6">
                        <div className="space-y-6">
                           {/* Main Income Selection Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Main Income Selection</CardTitle>
                                 <CardDescription>
                                    Select your primary income source for pay
                                    period filtering and budget calculations.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <div className="max-w-xl space-y-2">
                                    <Label htmlFor="mainIncomeId">
                                       Primary Income Source
                                    </Label>
                                    <Select
                                       value={formData.mainIncomeId || ""}
                                       onValueChange={(value) =>
                                          handleChange({
                                             target: {
                                                name: "mainIncomeId",
                                                value,
                                             },
                                          })
                                       }
                                    >
                                       <SelectTrigger>
                                          <SelectValue placeholder="Select an income" />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {formData.recurringIncomes?.map(
                                             (income) => (
                                                <SelectItem
                                                   key={income._id}
                                                   value={income._id}
                                                >
                                                   {formatIncomeOption(income)}
                                                </SelectItem>
                                             )
                                          )}
                                       </SelectContent>
                                    </Select>
                                 </div>
                              </CardContent>
                           </Card>

                           {/* Recurring Incomes Card */}
                           <Card>
                              <CardHeader>
                                 <CardTitle>Recurring Incomes</CardTitle>
                                 <CardDescription>
                                    Manage all your recurring income sources
                                    including salary, freelance work, and other
                                    regular income.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent className="p-0">
                                 <RecurringIncomes
                                    recurringIncomes={
                                       formData.recurringIncomes || []
                                    }
                                    onIncomesChange={
                                       handleRecurringIncomesChange
                                    }
                                    onError={handleError}
                                    onSuccess={handleSuccess}
                                 />
                              </CardContent>
                           </Card>
                        </div>
                     </TabsContent>

                     <TabsContent value="expenses" className="p-6 space-y-6">
                        <div className="space-y-6">
                           {/* Expenses List Card */}
                           <Card>
                              <CardHeader>
                                 <div className="flex justify-between items-center">
                                    <div>
                                       <CardTitle>
                                          Your Recurring Expenses
                                       </CardTitle>
                                       <CardDescription>
                                          View and manage all your recurring
                                          expenses organized by frequency.
                                       </CardDescription>
                                    </div>
                                    <Button
                                       variant={
                                          showExpenseForm
                                             ? "outline"
                                             : "default"
                                       }
                                       onClick={() =>
                                          setShowExpenseForm(!showExpenseForm)
                                       }
                                    >
                                       {showExpenseForm
                                          ? "Cancel"
                                          : "Add Expense"}
                                    </Button>
                                 </div>
                              </CardHeader>
                              <CardContent className="p-0">
                                 {showExpenseForm && (
                                    <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 m-6">
                                       <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                                          <div className="space-y-2">
                                             <Label htmlFor="expenseName">
                                                Name
                                             </Label>
                                             <Input
                                                type="text"
                                                id="expenseName"
                                                name="name"
                                                value={newExpense.name}
                                                onChange={handleExpenseChange}
                                                required
                                             />
                                          </div>

                                          <div className="space-y-2">
                                             <Label htmlFor="expenseAmount">
                                                Amount
                                             </Label>
                                             <div className="relative">
                                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                   <span className="text-gray-500 dark:text-gray-400 text-sm">
                                                      $
                                                   </span>
                                                </div>
                                                <Input
                                                   type="number"
                                                   id="expenseAmount"
                                                   name="amount"
                                                   value={newExpense.amount}
                                                   onChange={
                                                      handleExpenseChange
                                                   }
                                                   required
                                                   min="0"
                                                   step="0.01"
                                                   className="h-10 !pl-7 pr-3 py-2"
                                                />
                                             </div>
                                          </div>

                                          <div className="space-y-2">
                                             <Label htmlFor="expenseFrequency">
                                                Frequency
                                             </Label>
                                             <Select
                                                value={newExpense.frequency}
                                                onValueChange={(value) =>
                                                   handleExpenseChange({
                                                      target: {
                                                         name: "frequency",
                                                         value,
                                                      },
                                                   })
                                                }
                                             >
                                                <SelectTrigger>
                                                   <SelectValue placeholder="Select frequency" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                   {EXPENSE_FREQUENCIES.map(
                                                      (freq) => (
                                                         <SelectItem
                                                            key={freq.value}
                                                            value={freq.value}
                                                         >
                                                            {freq.label}
                                                         </SelectItem>
                                                      )
                                                   )}
                                                </SelectContent>
                                             </Select>
                                          </div>

                                          <div className="space-y-2">
                                             <Label htmlFor="expenseDueDay">
                                                {newExpense.frequency ===
                                                "annually"
                                                   ? "Due Month"
                                                   : "Due Day"}
                                             </Label>
                                             <Select
                                                value={newExpense.dueDay}
                                                onValueChange={(value) =>
                                                   handleExpenseChange({
                                                      target: {
                                                         name: "dueDay",
                                                         value,
                                                      },
                                                   })
                                                }
                                             >
                                                <SelectTrigger>
                                                   <SelectValue
                                                      placeholder={
                                                         newExpense.frequency ===
                                                         "annually"
                                                            ? "Select month"
                                                            : "Select day"
                                                      }
                                                   />
                                                </SelectTrigger>
                                                <SelectContent>
                                                   {newExpense.frequency ===
                                                   "annually"
                                                      ? Array.from(
                                                           { length: 12 },
                                                           (_, i) => {
                                                              const monthNames =
                                                                 [
                                                                    "January",
                                                                    "February",
                                                                    "March",
                                                                    "April",
                                                                    "May",
                                                                    "June",
                                                                    "July",
                                                                    "August",
                                                                    "September",
                                                                    "October",
                                                                    "November",
                                                                    "December",
                                                                 ];
                                                              return (
                                                                 <SelectItem
                                                                    key={i + 1}
                                                                    value={(
                                                                       i + 1
                                                                    ).toString()}
                                                                 >
                                                                    {
                                                                       monthNames[
                                                                          i
                                                                       ]
                                                                    }
                                                                 </SelectItem>
                                                              );
                                                           }
                                                        )
                                                      : newExpense.frequency ===
                                                           "monthly" ||
                                                        newExpense.frequency ===
                                                           "quarterly"
                                                      ? Array.from(
                                                           { length: 31 },
                                                           (_, i) => (
                                                              <SelectItem
                                                                 key={i + 1}
                                                                 value={(
                                                                    i + 1
                                                                 ).toString()}
                                                              >
                                                                 {i + 1}
                                                              </SelectItem>
                                                           )
                                                        )
                                                      : DAYS_OF_WEEK.map(
                                                           (day) => (
                                                              <SelectItem
                                                                 key={day.value}
                                                                 value={
                                                                    day.value
                                                                 }
                                                              >
                                                                 {day.label}
                                                              </SelectItem>
                                                           )
                                                        )}
                                                </SelectContent>
                                             </Select>
                                          </div>

                                          {newExpense.frequency ===
                                             "annually" && (
                                             <div className="space-y-2">
                                                <Label htmlFor="expenseDueMonth">
                                                   Due Day
                                                </Label>
                                                <Select
                                                   value={
                                                      newExpense.dueMonth || "1"
                                                   }
                                                   onValueChange={(value) =>
                                                      handleExpenseChange({
                                                         target: {
                                                            name: "dueMonth",
                                                            value,
                                                         },
                                                      })
                                                   }
                                                >
                                                   <SelectTrigger>
                                                      <SelectValue placeholder="Select day" />
                                                   </SelectTrigger>
                                                   <SelectContent>
                                                      {Array.from(
                                                         { length: 31 },
                                                         (_, i) => (
                                                            <SelectItem
                                                               key={i + 1}
                                                               value={(
                                                                  i + 1
                                                               ).toString()}
                                                            >
                                                               {i + 1}
                                                            </SelectItem>
                                                         )
                                                      )}
                                                   </SelectContent>
                                                </Select>
                                             </div>
                                          )}

                                          {newExpense.frequency ===
                                             "weekly" && (
                                             <div className="space-y-2">
                                                <Label htmlFor="weeklyChargeType">
                                                   Charge Type
                                                </Label>
                                                <Select
                                                   value={
                                                      newExpense.weeklyChargeType ||
                                                      "one-time"
                                                   }
                                                   onValueChange={(value) =>
                                                      handleExpenseChange({
                                                         target: {
                                                            name: "weeklyChargeType",
                                                            value,
                                                         },
                                                      })
                                                   }
                                                >
                                                   <SelectTrigger>
                                                      <SelectValue placeholder="Select charge type" />
                                                   </SelectTrigger>
                                                   <SelectContent>
                                                      <SelectItem value="one-time">
                                                         One-time charge on the
                                                         due day
                                                      </SelectItem>
                                                      <SelectItem value="spread">
                                                         Spread over the week
                                                      </SelectItem>
                                                   </SelectContent>
                                                </Select>
                                                <p className="text-xs text-muted-foreground">
                                                   {(newExpense.weeklyChargeType ||
                                                      "one-time") === "one-time"
                                                      ? "The entire amount will be charged on the specified day"
                                                      : "The amount will be spread across the entire week"}
                                                </p>
                                             </div>
                                          )}

                                          <div className="sm:col-span-2 lg:col-span-4 flex justify-end">
                                             <Button
                                                onClick={handleExpenseSubmit}
                                             >
                                                Add Expense
                                             </Button>
                                          </div>
                                       </div>
                                    </div>
                                 )}

                                 {formData.recurringExpenses.length === 0 ? (
                                    <p className="text-muted-foreground text-center py-6 px-6">
                                       No recurring expenses added yet.
                                    </p>
                                 ) : (
                                    <div className="space-y-8">
                                       {/* Active Expenses */}
                                       {Object.entries(
                                          groupExpensesByFrequency(
                                             formData.recurringExpenses
                                          ).activeGroups
                                       ).map(([frequency, expenses]) => (
                                          <div key={frequency} className="mb-8">
                                             <h3 className="text-base font-semibold px-6 pt-6 mb-2">
                                                {frequency
                                                   .charAt(0)
                                                   .toUpperCase() +
                                                   frequency.slice(1)}{" "}
                                                Expenses
                                             </h3>
                                             <div className="rounded-md border m-6">
                                                <Table>
                                                   <TableHeader>
                                                      <TableRow>
                                                         <TableHead>
                                                            Name
                                                         </TableHead>
                                                         <TableHead>
                                                            Amount
                                                         </TableHead>
                                                         <TableHead>
                                                            Due Day
                                                         </TableHead>
                                                         {frequency ===
                                                            "weekly" && (
                                                            <TableHead>
                                                               Charge Type
                                                            </TableHead>
                                                         )}
                                                         <TableHead>
                                                            Status
                                                         </TableHead>
                                                         <TableHead className="text-right">
                                                            Actions
                                                         </TableHead>
                                                      </TableRow>
                                                   </TableHeader>
                                                   <TableBody>
                                                      {expenses.map(
                                                         (expense) => (
                                                            <TableRow
                                                               key={expense.id}
                                                            >
                                                               <TableCell>
                                                                  {expense.name}
                                                               </TableCell>
                                                               <TableCell>
                                                                  $
                                                                  {parseFloat(
                                                                     expense.amount
                                                                  ).toFixed(2)}
                                                               </TableCell>
                                                               <TableCell>
                                                                  {frequency ===
                                                                  "annually"
                                                                     ? (() => {
                                                                          const monthNames =
                                                                             [
                                                                                "Jan",
                                                                                "Feb",
                                                                                "Mar",
                                                                                "Apr",
                                                                                "May",
                                                                                "Jun",
                                                                                "Jul",
                                                                                "Aug",
                                                                                "Sep",
                                                                                "Oct",
                                                                                "Nov",
                                                                                "Dec",
                                                                             ];
                                                                          const monthIndex =
                                                                             parseInt(
                                                                                expense.dueDay
                                                                             ) -
                                                                             1;
                                                                          const dayNum =
                                                                             expense.dueMonth ||
                                                                             "1";
                                                                          return `${monthNames[monthIndex]} ${dayNum}`;
                                                                       })()
                                                                     : frequency ===
                                                                          "monthly" ||
                                                                       frequency ===
                                                                          "quarterly"
                                                                     ? `Day ${expense.dueDay}`
                                                                     : DAYS_OF_WEEK.find(
                                                                          (d) =>
                                                                             d.value ===
                                                                             expense.dueDay
                                                                       )?.label}
                                                               </TableCell>
                                                               {frequency ===
                                                                  "weekly" && (
                                                                  <TableCell>
                                                                     <Badge
                                                                        variant={
                                                                           expense.weeklyChargeType ===
                                                                           "one-time"
                                                                              ? "default"
                                                                              : "secondary"
                                                                        }
                                                                     >
                                                                        {expense.weeklyChargeType ===
                                                                        "one-time"
                                                                           ? "One-time"
                                                                           : "Spread"}
                                                                     </Badge>
                                                                  </TableCell>
                                                               )}
                                                               <TableCell>
                                                                  <Switch
                                                                     checked={
                                                                        expense.enabled
                                                                     }
                                                                     onCheckedChange={() =>
                                                                        toggleExpense(
                                                                           expense.id
                                                                        )
                                                                     }
                                                                  />
                                                               </TableCell>
                                                               <TableCell className="text-right">
                                                                  <div className="flex gap-2 justify-end">
                                                                     <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() =>
                                                                           setEditingExpense(
                                                                              expense
                                                                           )
                                                                        }
                                                                     >
                                                                        Edit
                                                                     </Button>
                                                                     <Button
                                                                        variant="destructive"
                                                                        size="sm"
                                                                        onClick={() =>
                                                                           deleteExpense(
                                                                              expense.id
                                                                           )
                                                                        }
                                                                     >
                                                                        Delete
                                                                     </Button>
                                                                  </div>
                                                               </TableCell>
                                                            </TableRow>
                                                         )
                                                      )}
                                                   </TableBody>
                                                </Table>
                                             </div>
                                          </div>
                                       ))}

                                       {/* Disabled Expenses */}
                                       {Object.keys(
                                          groupExpensesByFrequency(
                                             formData.recurringExpenses
                                          ).disabledGroups
                                       ).length > 0 && (
                                          <Accordion
                                             type="single"
                                             collapsible
                                             className="w-full px-6 mt-4"
                                          >
                                             <AccordionItem value="disabled-expenses">
                                                <AccordionTrigger>
                                                   Disabled Expenses
                                                </AccordionTrigger>
                                                <AccordionContent className="pt-2">
                                                   {Object.entries(
                                                      groupExpensesByFrequency(
                                                         formData.recurringExpenses
                                                      ).disabledGroups
                                                   ).map(
                                                      ([
                                                         frequency,
                                                         expenses,
                                                      ]) => (
                                                         <Card
                                                            key={frequency}
                                                            className="mb-4 mt-2"
                                                         >
                                                            <CardHeader>
                                                               <CardTitle className="text-lg">
                                                                  {frequency
                                                                     .charAt(0)
                                                                     .toUpperCase() +
                                                                     frequency.slice(
                                                                        1
                                                                     )}{" "}
                                                                  Expenses
                                                               </CardTitle>
                                                            </CardHeader>
                                                            <CardContent className="p-0">
                                                               <div className="rounded-md border m-6">
                                                                  <Table>
                                                                     <TableHeader>
                                                                        <TableRow>
                                                                           <TableHead>
                                                                              Name
                                                                           </TableHead>
                                                                           <TableHead>
                                                                              Amount
                                                                           </TableHead>
                                                                           <TableHead>
                                                                              Due
                                                                              Day
                                                                           </TableHead>
                                                                           <TableHead>
                                                                              Status
                                                                           </TableHead>
                                                                           <TableHead className="text-right">
                                                                              Actions
                                                                           </TableHead>
                                                                        </TableRow>
                                                                     </TableHeader>
                                                                     <TableBody>
                                                                        {expenses.map(
                                                                           (
                                                                              expense
                                                                           ) => (
                                                                              <TableRow
                                                                                 key={
                                                                                    expense.id
                                                                                 }
                                                                              >
                                                                                 <TableCell>
                                                                                    {
                                                                                       expense.name
                                                                                    }
                                                                                 </TableCell>
                                                                                 <TableCell>
                                                                                    $
                                                                                    {parseFloat(
                                                                                       expense.amount
                                                                                    ).toFixed(
                                                                                       2
                                                                                    )}
                                                                                 </TableCell>
                                                                                 <TableCell>
                                                                                    {frequency ===
                                                                                    "annually"
                                                                                       ? (() => {
                                                                                            const monthNames =
                                                                                               [
                                                                                                  "Jan",
                                                                                                  "Feb",
                                                                                                  "Mar",
                                                                                                  "Apr",
                                                                                                  "May",
                                                                                                  "Jun",
                                                                                                  "Jul",
                                                                                                  "Aug",
                                                                                                  "Sep",
                                                                                                  "Oct",
                                                                                                  "Nov",
                                                                                                  "Dec",
                                                                                               ];
                                                                                            const monthIndex =
                                                                                               parseInt(
                                                                                                  expense.dueDay
                                                                                               ) -
                                                                                               1;
                                                                                            const dayNum =
                                                                                               expense.dueMonth ||
                                                                                               "1";
                                                                                            return `${monthNames[monthIndex]} ${dayNum}`;
                                                                                         })()
                                                                                       : frequency ===
                                                                                            "monthly" ||
                                                                                         frequency ===
                                                                                            "quarterly"
                                                                                       ? `Day ${expense.dueDay}`
                                                                                       : DAYS_OF_WEEK.find(
                                                                                            (
                                                                                               d
                                                                                            ) =>
                                                                                               d.value ===
                                                                                               expense.dueDay
                                                                                         )
                                                                                            ?.label}
                                                                                 </TableCell>
                                                                                 <TableCell>
                                                                                    <Switch
                                                                                       checked={
                                                                                          expense.enabled
                                                                                       }
                                                                                       onCheckedChange={() =>
                                                                                          toggleExpense(
                                                                                             expense.id
                                                                                          )
                                                                                       }
                                                                                    />
                                                                                 </TableCell>
                                                                                 <TableCell className="text-right">
                                                                                    <div className="flex gap-2 justify-end">
                                                                                       <Button
                                                                                          variant="ghost"
                                                                                          size="sm"
                                                                                          onClick={() =>
                                                                                             setEditingExpense(
                                                                                                expense
                                                                                             )
                                                                                          }
                                                                                       >
                                                                                          Edit
                                                                                       </Button>
                                                                                       <Button
                                                                                          variant="destructive"
                                                                                          size="sm"
                                                                                          onClick={() =>
                                                                                             deleteExpense(
                                                                                                expense.id
                                                                                             )
                                                                                          }
                                                                                       >
                                                                                          Delete
                                                                                       </Button>
                                                                                    </div>
                                                                                 </TableCell>
                                                                              </TableRow>
                                                                           )
                                                                        )}
                                                                     </TableBody>
                                                                  </Table>
                                                               </div>
                                                            </CardContent>
                                                         </Card>
                                                      )
                                                   )}
                                                </AccordionContent>
                                             </AccordionItem>
                                          </Accordion>
                                       )}
                                    </div>
                                 )}
                              </CardContent>
                           </Card>
                        </div>
                     </TabsContent>

                     <TabsContent value="accounts" className="p-6 space-y-6">
                        <div className="space-y-6">
                           {/* Connection Status Banner */}
                           {Object.values(connectionStatuses).some(
                              (status) => status.needsAction
                           ) && (
                              <Alert className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800">
                                 <AlertTriangle className="h-4 w-4" />
                                 <AlertDescription className="text-yellow-800 dark:text-yellow-200">
                                    Some of your bank connections need
                                    attention. Please check the status
                                    indicators below and re-authenticate as
                                    needed.
                                 </AlertDescription>
                              </Alert>
                           )}

                           <Card>
                              <CardHeader>
                                 <CardTitle>
                                    Accounts & Bank Connections
                                 </CardTitle>
                                 <CardDescription>
                                    Manage your accounts and bank connections.
                                    Connect your bank accounts to automatically
                                    import transactions.
                                 </CardDescription>
                              </CardHeader>
                              <CardContent>
                                 <div className="flex justify-between items-center mb-4">
                                    <div className="flex gap-2">
                                       <Button
                                          onClick={() =>
                                             setShowAddAccountModal(true)
                                          }
                                       >
                                          Add Account
                                       </Button>
                                    </div>
                                 </div>

                                 {formData.accounts.length === 0 ? (
                                    <p className="text-muted-foreground text-center py-4">
                                       No accounts added yet.
                                    </p>
                                 ) : (
                                    <div className="rounded-md border">
                                       <Table>
                                          <TableHeader>
                                             <TableRow>
                                                <TableHead>Status</TableHead>
                                                <TableHead>
                                                   Account Name
                                                </TableHead>
                                                <TableHead>
                                                   Bank/Institution
                                                </TableHead>
                                                <TableHead>Type</TableHead>
                                                <TableHead>Balance</TableHead>
                                                <TableHead className="text-right">
                                                   Actions
                                                </TableHead>
                                             </TableRow>
                                          </TableHeader>
                                          <TableBody>
                                             {formData.accounts.map(
                                                (account) => (
                                                   <TableRow key={account._id}>
                                                      <TableCell>
                                                         <Switch
                                                            checked={
                                                               account.active
                                                            }
                                                            onCheckedChange={() =>
                                                               toggleAccount(
                                                                  account._id
                                                               )
                                                            }
                                                         />
                                                      </TableCell>
                                                      <TableCell>
                                                         {account.name}
                                                      </TableCell>
                                                      <TableCell>
                                                         <div className="flex items-center gap-2">
                                                            <span className="text-gray-600 dark:text-gray-400">
                                                               {account.plaidItemId &&
                                                               subscriptionPlan !==
                                                                  "free"
                                                                  ? (
                                                                       formData.plaidItems ||
                                                                       []
                                                                    ).find(
                                                                       (item) =>
                                                                          item.itemId ===
                                                                          account.plaidItemId
                                                                    )
                                                                       ?.institutionName ||
                                                                    "Linked Bank"
                                                                  : account.bank}
                                                            </span>
                                                            <PlaidConnectionStatus
                                                               account={account}
                                                               onStatusUpdate={
                                                                  handleConnectionStatusUpdate
                                                               }
                                                               inline={true}
                                                            />
                                                         </div>
                                                      </TableCell>
                                                      <TableCell>
                                                         <Badge
                                                            variant={
                                                               account.accountType ===
                                                               "cash"
                                                                  ? "success"
                                                                  : account.accountType ===
                                                                    "credit"
                                                                  ? "destructive"
                                                                  : account.accountType ===
                                                                    "loan"
                                                                  ? "warning"
                                                                  : "secondary"
                                                            }
                                                         >
                                                            {ACCOUNT_TYPES.find(
                                                               (type) =>
                                                                  type.value ===
                                                                  account.accountType
                                                            )?.label ||
                                                               account.accountType}
                                                         </Badge>
                                                      </TableCell>
                                                      <TableCell>
                                                         $
                                                         {formatNumber(
                                                            account.balance
                                                         )}
                                                      </TableCell>
                                                      <TableCell className="text-right">
                                                         <div className="flex gap-2 justify-end">
                                                            <Button
                                                               variant="ghost"
                                                               size="sm"
                                                               onClick={() => {
                                                                  setEditingAccount(
                                                                     account
                                                                  );
                                                                  setPlaidConnection(
                                                                     null
                                                                  ); // Clear any existing Plaid connection state
                                                               }}
                                                            >
                                                               Edit
                                                            </Button>
                                                            <Button
                                                               variant="destructive"
                                                               size="sm"
                                                               onClick={() =>
                                                                  deleteAccount(
                                                                     account._id
                                                                  )
                                                               }
                                                            >
                                                               Delete
                                                            </Button>
                                                         </div>
                                                      </TableCell>
                                                   </TableRow>
                                                )
                                             )}
                                          </TableBody>
                                       </Table>
                                    </div>
                                 )}
                              </CardContent>
                           </Card>
                        </div>
                     </TabsContent>
                  </div>
               </Tabs>
            </div>
         </div>

         {/* Edit Expense Modal */}
         {editingExpense && (
            <EditExpenseModal
               expense={editingExpense}
               onClose={() => setEditingExpense(null)}
               onSave={handleSaveEditedExpense}
            />
         )}

         {/* Add Account Modal */}
         {showAddAccountModal && (
            <AddAccountModal
               onClose={() => {
                  setShowAddAccountModal(false);
                  setPlaidConnection(null);
               }}
               onSave={(newAccount) => {
                  setFormData((prev) => ({
                     ...prev,
                     accounts: [...prev.accounts, newAccount],
                  }));
                  setShowAddAccountModal(false);
                  setPlaidConnection(null);
                  setSuccess("Account added successfully");
                  fetchUserData(); // Refresh to get updated Plaid data
               }}
               onLinkBank={handleLinkBank}
               plaidConnection={plaidConnection}
            />
         )}

         {/* Edit Account Modal */}
         {editingAccount && (
            <EditAccountModal
               account={editingAccount}
               onClose={async () => {
                  setEditingAccount(null);
                  setPlaidConnection(null);
                  // Silently refresh accounts data without showing loading state
                  try {
                     const response = await fetch("/api/user");
                     if (response.ok) {
                        const data = await response.json();
                        setFormData((prev) => ({
                           ...prev,
                           accounts: data.accounts || [],
                           plaidItems: data.plaidItems || [],
                        }));
                     }
                  } catch (error) {
                     console.error("Error refreshing account data:", error);
                  }
               }}
               onLinkBank={handleLinkBank}
               plaidConnection={plaidConnection}
            />
         )}

         {/* Recurring Expense Deletion Modal */}
         {deletionModal.isOpen && (
            <RecurringExpenseDeletionModal
               recurringExpense={deletionModal.recurringExpense}
               isOpen={deletionModal.isOpen}
               onClose={() =>
                  setDeletionModal({ isOpen: false, recurringExpense: null })
               }
               onConfirm={handleConfirmDeletion}
            />
         )}

         {/* Delete Account Modal */}
         <DeleteAccountModal
            isOpen={showDeleteAccountModal}
            onClose={() => setShowDeleteAccountModal(false)}
         />
      </div>
   );
};

export default SettingsPage;

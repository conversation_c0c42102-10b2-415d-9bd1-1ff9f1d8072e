import { getServerSession } from "next-auth/next";
import { authOptions } from "../api/auth/[...nextauth]/route";
import { redirect } from "next/navigation";
import dbConnect from "../lib/mongodb/dbConnect";
import User from "../lib/mongodb/models/User";

export default async function AdminLayout({ children }) {
   try {
      const session = await getServerSession(authOptions);

      // If not authenticated, redirect to login
      if (!session?.user?.id) {
         redirect("/auth/login");
      }

      // Check if user exists and is admin
      await dbConnect();
      const mongoUser = await User.findById(session.user.id);

      if (!mongoUser) {
         redirect("/auth/login");
      }

      // Check if user is admin
      if (!mongoUser.isAdmin) {
         redirect("/budget");
      }

      return <>{children}</>;
   } catch (error) {
      console.error("Error in admin layout:", error);
      redirect("/auth/login");
   }
}

"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import React from "react";

export default function LogsPage() {
   const { data: session, status } = useSession();
   const router = useRouter();
   const [logs, setLogs] = useState([]);
   const [loading, setLoading] = useState(true);
   const [error, setError] = useState(null);
   const [userData, setUserData] = useState(null);
   const [pagination, setPagination] = useState({
      total: 0,
      offset: 0,
      limit: 50,
      hasMore: false,
   });
   const [filters, setFilters] = useState({
      source: "",
      level: "",
      event: "",
      userId: "",
      startDate: "",
      endDate: "",
   });
   const [availableFilters, setAvailableFilters] = useState({
      sources: [],
      levels: [],
      events: [],
   });

   // Fetch user data from MongoDB to check if admin
   useEffect(() => {
      async function fetchUserData() {
         if (!session?.user) return;

         try {
            const response = await fetch("/api/user");
            const data = await response.json();
            setUserData(data);

            if (!data.isAdmin) {
               router.push("/");
            }
         } catch (error) {
            console.error("Error fetching user data:", error);
         }
      }

      if (status !== "loading") {
         if (!session?.user) {
            router.push("/auth/login");
         } else {
            fetchUserData();
         }
      }
   }, [status, session, router]);

   // Fetch logs
   useEffect(() => {
      async function fetchLogs() {
         try {
            setLoading(true);
            const queryParams = new URLSearchParams({
               limit: pagination.limit,
               offset: pagination.offset,
               ...Object.fromEntries(
                  Object.entries(filters).filter(([_, v]) => v !== "")
               ),
            });

            const response = await fetch(
               `/api/admin/logs?${queryParams.toString()}`
            );

            if (!response.ok) {
               const errorData = await response.json();
               throw new Error(errorData.error || "Failed to fetch logs");
            }

            const data = await response.json();
            setLogs(data.logs);
            setPagination(data.pagination);
            setAvailableFilters(data.filters);
            setError(null);
         } catch (err) {
            console.error("Error fetching logs:", err);
            setError(err.message);
         } finally {
            setLoading(false);
         }
      }

      if (userData?.isAdmin) {
         fetchLogs();
      }
   }, [userData, pagination.offset, pagination.limit, filters]);

   // Handle filter changes
   const handleFilterChange = (e) => {
      const { name, value } = e.target;
      setFilters((prev) => ({ ...prev, [name]: value }));
      setPagination((prev) => ({ ...prev, offset: 0 })); // Reset to first page when filters change
   };

   // Reset filters
   const resetFilters = () => {
      setFilters({
         source: "",
         level: "",
         event: "",
         userId: "",
         startDate: "",
         endDate: "",
      });
   };

   // Handle pagination
   const handlePrevPage = () => {
      if (pagination.offset > 0) {
         setPagination((prev) => ({
            ...prev,
            offset: Math.max(0, prev.offset - prev.limit),
         }));
      }
   };

   const handleNextPage = () => {
      if (pagination.hasMore) {
         setPagination((prev) => ({
            ...prev,
            offset: prev.offset + prev.limit,
         }));
      }
   };

   // Format date for display
   const formatDate = (dateString) => {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat("en-US", {
         year: "numeric",
         month: "short",
         day: "2-digit",
         hour: "2-digit",
         minute: "2-digit",
         second: "2-digit",
      }).format(date);
   };

   // If loading or not authenticated yet
   if (status === "loading" || loading) {
      return (
         <div className="flex justify-center items-center h-screen">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-500"></div>
         </div>
      );
   }

   // If not admin
   if (!userData?.isAdmin) {
      return null; // Will redirect in useEffect
   }

   return (
      <div className="container mx-auto px-4 py-4 max-w-full">
         <h1 className="text-xl font-bold mb-4">System Logs</h1>

         {/* Filters */}
         <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow mb-4">
            <h2 className="text-sm font-semibold mb-3">Filters</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
               <div>
                  <label className="block text-xs font-medium mb-1">
                     Source
                  </label>
                  <select
                     name="source"
                     value={filters.source}
                     onChange={handleFilterChange}
                     className="w-full p-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  >
                     <option value="">All Sources</option>
                     {availableFilters.sources.map((source) => (
                        <option key={source} value={source}>
                           {source}
                        </option>
                     ))}
                  </select>
               </div>

               <div>
                  <label className="block text-xs font-medium mb-1">
                     Level
                  </label>
                  <select
                     name="level"
                     value={filters.level}
                     onChange={handleFilterChange}
                     className="w-full p-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  >
                     <option value="">All Levels</option>
                     {availableFilters.levels.map((level) => (
                        <option key={level} value={level}>
                           {level}
                        </option>
                     ))}
                  </select>
               </div>

               <div>
                  <label className="block text-xs font-medium mb-1">
                     Event
                  </label>
                  <select
                     name="event"
                     value={filters.event}
                     onChange={handleFilterChange}
                     className="w-full p-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  >
                     <option value="">All Events</option>
                     {availableFilters.events.map((event) => (
                        <option key={event} value={event}>
                           {event}
                        </option>
                     ))}
                  </select>
               </div>

               <div>
                  <label className="block text-xs font-medium mb-1">
                     User ID
                  </label>
                  <input
                     type="text"
                     name="userId"
                     value={filters.userId}
                     onChange={handleFilterChange}
                     placeholder="Filter by user ID"
                     className="w-full p-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  />
               </div>

               <div>
                  <label className="block text-xs font-medium mb-1">
                     Start Date
                  </label>
                  <input
                     type="datetime-local"
                     name="startDate"
                     value={filters.startDate}
                     onChange={handleFilterChange}
                     className="w-full p-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  />
               </div>

               <div>
                  <label className="block text-xs font-medium mb-1">
                     End Date
                  </label>
                  <input
                     type="datetime-local"
                     name="endDate"
                     value={filters.endDate}
                     onChange={handleFilterChange}
                     className="w-full p-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded"
                  />
               </div>
            </div>

            <div className="mt-3 flex justify-end">
               <button
                  onClick={resetFilters}
                  className="px-3 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
               >
                  Reset Filters
               </button>
            </div>
         </div>

         {/* Error message */}
         {error && (
            <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-2 rounded-lg mb-4 text-xs">
               {error}
            </div>
         )}

         {/* Logs table */}
         <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            {loading ? (
               <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-500"></div>
               </div>
            ) : logs.length === 0 ? (
               <div className="flex justify-center items-center h-64 text-gray-500 dark:text-gray-400">
                  No logs found matching the current filters
               </div>
            ) : (
               <div className="overflow-x-auto max-h-[calc(100vh-300px)] overflow-y-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                     <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Timestamp
                           </th>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Level
                           </th>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Source
                           </th>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Event
                           </th>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Message
                           </th>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              User ID
                           </th>
                           <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Data
                           </th>
                        </tr>
                     </thead>
                     <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {logs.map((log) => (
                           <tr
                              key={log._id}
                              className="hover:bg-gray-50 dark:hover:bg-gray-700"
                           >
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                 {formatDate(log.timestamp)}
                              </td>
                              <td
                                 className={`px-3 py-2 whitespace-nowrap text-xs ${
                                    log.level === "error"
                                       ? "text-red-500 dark:text-red-400"
                                       : log.level === "warning"
                                       ? "text-yellow-500 dark:text-yellow-400"
                                       : log.level === "info"
                                       ? "text-gray-500 dark:text-gray-400"
                                       : "text-gray-500 dark:text-gray-400"
                                 }`}
                              >
                                 {log.level}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                 {log.source}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                 {log.event}
                              </td>
                              <td className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
                                 <div className="max-w-md truncate">
                                    {log.message}
                                 </div>
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                 {log.userId ? (
                                    <span className="text-xs font-mono">
                                       {log.userId}
                                    </span>
                                 ) : (
                                    <span className="text-xs text-gray-400">
                                       -
                                    </span>
                                 )}
                              </td>
                              <td className="px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
                                 {log.data && (
                                    <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-lg overflow-x-auto max-h-[150px] overflow-y-auto">
                                       <pre className="text-xs font-mono whitespace-pre-wrap">
                                          {JSON.stringify(log.data, null, 2)}
                                       </pre>
                                    </div>
                                 )}
                              </td>
                           </tr>
                        ))}
                     </tbody>
                  </table>
               </div>
            )}

            {/* Pagination */}
            <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex items-center justify-between">
               <div className="text-xs text-gray-700 dark:text-gray-300">
                  Showing {pagination.offset + 1} to{" "}
                  {Math.min(pagination.offset + logs.length, pagination.total)}{" "}
                  of {pagination.total} results
               </div>
               <div className="flex space-x-2">
                  <button
                     onClick={handlePrevPage}
                     disabled={pagination.offset === 0}
                     className={`px-3 py-1 text-xs rounded ${
                        pagination.offset === 0
                           ? "bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                           : "bg-gray-500 text-white hover:bg-gray-600"
                     }`}
                  >
                     Previous
                  </button>
                  <button
                     onClick={handleNextPage}
                     disabled={!pagination.hasMore}
                     className={`px-3 py-1 text-xs rounded ${
                        !pagination.hasMore
                           ? "bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                           : "bg-gray-500 text-white hover:bg-gray-600"
                     }`}
                  >
                     Next
                  </button>
               </div>
            </div>
         </div>
      </div>
   );
}

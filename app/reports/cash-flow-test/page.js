"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import CashFlowWarnings from "@/app/components/reports/CashFlowWarnings";
import {
   AlertTriangle,
   TestTube,
   Settings,
   RefreshCw,
   ArrowLeft,
   Info,
} from "lucide-react";

export default function CashFlowTestPage() {
   const { data: session, status } = useSession();
   const router = useRouter();
   const [mongoUser, setMongoUser] = useState(null);
   const [loading, setLoading] = useState(true);
   const [periodsAhead, setPeriodsAhead] = useState(4);
   const [rawApiResponse, setRawApiResponse] = useState(null);
   const [testResults, setTestResults] = useState([]);

   useEffect(() => {
      if (status !== "loading" && !session?.user) {
         router.push("/auth/login");
      }
   }, [status, session?.user, router]);

   useEffect(() => {
      async function fetchUserData() {
         if (status !== "loading" && session?.user) {
            try {
               const response = await fetch(`/api/user`);
               if (response.ok) {
                  const userData = await response.json();
                  setMongoUser(userData);
               } else {
                  console.error("Failed to fetch user data");
               }
            } catch (error) {
               console.error("Error fetching user data:", error);
            } finally {
               setLoading(false);
            }
         }
      }

      fetchUserData();
   }, [status, session?.user]);

   const runApiTest = async (useUserPreference = false) => {
      try {
         setLoading(true);

         // Use user preference if requested, otherwise use the input value
         const url = useUserPreference
            ? "/api/reports/cash-flow-warnings"
            : `/api/reports/cash-flow-warnings?periodsAhead=${periodsAhead}`;

         const response = await fetch(url);

         const data = await response.json();
         setRawApiResponse(data);

         // Add test result
         const testResult = {
            timestamp: new Date().toLocaleString(),
            periodsAhead: useUserPreference
               ? `User Pref (${data.userPreference || 4})`
               : periodsAhead,
            status: response.ok ? "success" : "error",
            hasWarning: !!data.warning,
            warningsEnabled: data.warningsEnabled,
            usingUserPreference: useUserPreference,
            response: data,
         };

         setTestResults((prev) => [testResult, ...prev.slice(0, 4)]); // Keep last 5 results
      } catch (error) {
         console.error("Test failed:", error);
         setTestResults((prev) => [
            {
               timestamp: new Date().toLocaleString(),
               periodsAhead: useUserPreference ? "User Pref" : periodsAhead,
               status: "error",
               error: error.message,
            },
            ...prev.slice(0, 4),
         ]);
      } finally {
         setLoading(false);
      }
   };

   const goToSettings = () => {
      router.push("/settings?tab=profile");
   };

   const goBackToReports = () => {
      router.push("/reports");
   };

   if (status === "loading" || !session?.user) {
      return null;
   }

   if (loading && !mongoUser) {
      return (
         <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
         </div>
      );
   }

   return (
      <div className="h-full overflow-auto">
         <div className="container mx-auto px-4 py-8">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
               <div className="flex items-center space-x-3">
                  <Button variant="outline" size="sm" onClick={goBackToReports}>
                     <ArrowLeft className="h-4 w-4 mr-2" />
                     Back to Reports
                  </Button>
                  <h1 className="text-2xl font-bold flex items-center space-x-2">
                     <TestTube className="h-6 w-6" />
                     <span>Cash Flow Warnings - Test Lab</span>
                     <Badge variant="outline" className="text-xs">
                        BETA
                     </Badge>
                  </h1>
               </div>
               <Button onClick={goToSettings} variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Warning Settings
               </Button>
            </div>

            <div className="grid gap-6">
               {/* User Info Card */}
               <Card>
                  <CardHeader>
                     <CardTitle className="flex items-center space-x-2">
                        <Info className="h-5 w-5" />
                        <span>User Configuration</span>
                     </CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                           <Label className="font-medium">
                              Main Income Setup:
                           </Label>
                           <p
                              className={
                                 mongoUser?.mainIncomeId
                                    ? "text-green-600"
                                    : "text-red-600"
                              }
                           >
                              {mongoUser?.mainIncomeId
                                 ? "✓ Configured"
                                 : "✗ Not Set"}
                           </p>
                        </div>
                        <div>
                           <Label className="font-medium">
                              Recurring Incomes:
                           </Label>
                           <p>{mongoUser?.recurringIncomes?.length || 0}</p>
                        </div>
                        <div>
                           <Label className="font-medium">
                              Recurring Expenses:
                           </Label>
                           <p>{mongoUser?.recurringExpenses?.length || 0}</p>
                        </div>
                        <div>
                           <Label className="font-medium">
                              Warning Settings:
                           </Label>
                           <p
                              className={
                                 mongoUser?.preferences?.cashFlowWarnings
                                    ?.enabled
                                    ? "text-green-600"
                                    : "text-red-600"
                              }
                           >
                              {mongoUser?.preferences?.cashFlowWarnings?.enabled
                                 ? "✓ Enabled"
                                 : "✗ Disabled"}
                           </p>
                           {mongoUser?.preferences?.cashFlowWarnings
                              ?.enabled && (
                              <p className="text-xs text-gray-500">
                                 {mongoUser?.preferences?.cashFlowWarnings
                                    ?.periodsAhead || 4}{" "}
                                 periods ahead
                              </p>
                           )}
                        </div>
                     </div>

                     {(!mongoUser?.mainIncomeId ||
                        !mongoUser?.recurringIncomes?.length) && (
                        <Alert className="mt-4 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
                           <AlertTriangle className="h-4 w-4" />
                           <AlertDescription>
                              <p className="font-medium">Setup Required</p>
                              <p className="text-sm">
                                 To test cash flow warnings effectively, you
                                 need:
                              </p>
                              <ul className="text-sm mt-2 space-y-1">
                                 {!mongoUser?.mainIncomeId && (
                                    <li>
                                       • Set up a main income in Settings →
                                       Recurring Income
                                    </li>
                                 )}
                                 {!mongoUser?.recurringIncomes?.length && (
                                    <li>• Add at least one recurring income</li>
                                 )}
                                 {!mongoUser?.recurringExpenses?.length && (
                                    <li>
                                       • Add some recurring expenses to create
                                       potential negative cash flow
                                    </li>
                                 )}
                              </ul>
                           </AlertDescription>
                        </Alert>
                     )}
                  </CardContent>
               </Card>

               {/* Test Controls */}
               <Card>
                  <CardHeader>
                     <CardTitle>Test Controls</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                     <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                           <Label htmlFor="periods">Periods to Check:</Label>
                           <Input
                              id="periods"
                              type="number"
                              min="1"
                              max="12"
                              value={periodsAhead}
                              onChange={(e) =>
                                 setPeriodsAhead(parseInt(e.target.value) || 4)
                              }
                              className="w-20"
                           />
                        </div>
                        <div className="flex gap-2">
                           <Button
                              onClick={() => runApiTest(false)}
                              disabled={loading}
                           >
                              <RefreshCw
                                 className={`h-4 w-4 mr-2 ${
                                    loading ? "animate-spin" : ""
                                 }`}
                              />
                              Test Override
                           </Button>
                           <Button
                              onClick={() => runApiTest(true)}
                              disabled={loading}
                              variant="outline"
                           >
                              <Settings className="h-4 w-4 mr-2" />
                              Test User Pref
                           </Button>
                        </div>
                     </div>

                     {/* Test Results */}
                     {testResults.length > 0 && (
                        <div className="mt-4">
                           <Label className="font-medium">
                              Recent Test Results:
                           </Label>
                           <div className="mt-2 space-y-2">
                              {testResults.map((result, index) => (
                                 <div
                                    key={index}
                                    className="text-xs p-2 border rounded"
                                 >
                                    <div className="flex items-center justify-between">
                                       <span>{result.timestamp}</span>
                                       <Badge
                                          variant={
                                             result.status === "success"
                                                ? "default"
                                                : "destructive"
                                          }
                                          className="text-xs"
                                       >
                                          {result.status}
                                       </Badge>
                                    </div>
                                    <div className="mt-1 text-gray-600 dark:text-gray-400">
                                       Periods: {result.periodsAhead} | Warning:{" "}
                                       {result.hasWarning ? "Yes" : "No"} |
                                       Enabled:{" "}
                                       {result.warningsEnabled ? "Yes" : "No"}
                                       {result.error && (
                                          <span className="text-red-600">
                                             {" "}
                                             | Error: {result.error}
                                          </span>
                                       )}
                                    </div>
                                 </div>
                              ))}
                           </div>
                        </div>
                     )}
                  </CardContent>
               </Card>

               {/* Live Warning Component */}
               <Card>
                  <CardHeader>
                     <CardTitle>Live Warning Component</CardTitle>
                  </CardHeader>
                  <CardContent>
                     <CashFlowWarnings
                        periodsAhead={periodsAhead}
                        showDebugInfo={true}
                     />
                  </CardContent>
               </Card>

               {/* Raw API Response */}
               {rawApiResponse && (
                  <Card>
                     <CardHeader>
                        <CardTitle>Raw API Response</CardTitle>
                     </CardHeader>
                     <CardContent>
                        <pre className="text-xs bg-gray-50 dark:bg-gray-800 p-4 rounded overflow-auto max-h-96">
                           {JSON.stringify(rawApiResponse, null, 2)}
                        </pre>
                     </CardContent>
                  </Card>
               )}

               {/* Test Scenarios */}
               <Card>
                  <CardHeader>
                     <CardTitle>Test Scenarios</CardTitle>
                  </CardHeader>
                  <CardContent>
                     <div className="space-y-4 text-sm">
                        <div>
                           <h4 className="font-medium mb-2">
                              Recommended Test Cases:
                           </h4>
                           <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                              <li>
                                 <strong>1. Positive Cash Flow:</strong> Set up
                                 income higher than expenses to see "All clear"
                                 message
                              </li>
                              <li>
                                 <strong>2. Negative Cash Flow:</strong> Add
                                 recurring expenses that exceed income to
                                 trigger warnings
                              </li>
                              <li>
                                 <strong>3. Scheduled vs Projected:</strong>{" "}
                                 Schedule some expenses to test double-counting
                                 prevention
                              </li>
                              <li>
                                 <strong>4. Assigned Amounts:</strong> Assign
                                 money to expenses to see how it affects
                                 calculations
                              </li>
                              <li>
                                 <strong>5. Debt Payments:</strong> Add debts to
                                 see if they appear in expense calculations
                              </li>
                              <li>
                                 <strong>6. Disabled Warnings:</strong> Turn off
                                 warnings in settings to test disabled state
                              </li>
                           </ul>
                        </div>

                        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                           <p className="font-medium text-blue-900 dark:text-blue-100">
                              💡 Pro Tip:
                           </p>
                           <p className="text-blue-800 dark:text-blue-200 text-sm">
                              Use the "Test API Directly" button to see raw
                              responses, then compare with the component
                              display. The debug info shows counts of different
                              data types.
                           </p>
                        </div>
                     </div>
                  </CardContent>
               </Card>
            </div>
         </div>
      </div>
   );
}

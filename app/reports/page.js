"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { TestTube } from "lucide-react";
import RunningBalanceChart from "../components/reports/RunningBalanceChart";
import CashFlowWarnings from "../components/reports/CashFlowWarnings";

export default function ReportsPage() {
   const { data: session, status } = useSession();
   const router = useRouter();
   const [mongoUser, setMongoUser] = useState(null);
   const [loading, setLoading] = useState(true);

   useEffect(() => {
      if (status !== "loading" && !session?.user) {
         router.push("/auth/login");
      }
   }, [status, session?.user, router]);

   useEffect(() => {
      async function fetchUserData() {
         if (status !== "loading" && session?.user) {
            try {
               const response = await fetch(`/api/user`);
               if (response.ok) {
                  const userData = await response.json();
                  setMongoUser(userData);
               } else {
                  console.error("Failed to fetch user data");
               }
            } catch (error) {
               console.error("Error fetching user data:", error);
            } finally {
               setLoading(false);
            }
         }
      }

      fetchUserData();
   }, [status, session?.user]);

   if (status === "loading" || !session?.user) {
      return null;
   }

   if (loading) {
      return (
         <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
         </div>
      );
   }

   const goToCashFlowTest = () => {
      router.push("/reports/cash-flow-test");
   };

   return (
      <div className="h-full overflow-auto">
         <div className="container mx-auto px-4 py-8">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
               <h1 className="text-2xl font-bold">Reports</h1>
               <Button onClick={goToCashFlowTest} variant="outline">
                  <TestTube className="h-4 w-4 mr-2" />
                  Cash Flow Test Lab
               </Button>
            </div>

            <div className="grid gap-6">
               {/* Cash Flow Warnings */}
               <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h2 className="text-xl font-semibold mb-4">
                     Cash Flow Warnings
                  </h2>
                  <CashFlowWarnings />
               </div>

               {/* Running Balance Chart */}
               <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h2 className="text-xl font-semibold mb-4">
                     Running Balance Forecast
                  </h2>
                  <RunningBalanceChart />
               </div>
            </div>
         </div>
      </div>
   );
}

"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function Home() {
   const { data: session, status } = useSession();
   const router = useRouter();
   const [loading, setLoading] = useState(true);

   useEffect(() => {
      const handleRedirect = async () => {
         if (status === "loading") return;

         if (!session) {
            router.push("/auth/login");
            return;
         }

         // First check session data for onboarding status
         if (session.user?.onboardingComplete !== undefined) {
            console.log(
               "Using session data for onboarding status:",
               session.user.onboardingComplete
            );
            if (session.user.onboardingComplete) {
               router.push("/budget");
            } else {
               router.push(`/subscription/${session.user.id}`);
            }
            setLoading(false);
            return;
         }

         // Fallback to API call if session doesn't have onboarding status
         try {
            // Add timeout to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch("/api/user", {
               signal: controller.signal,
               headers: {
                  "Cache-Control": "no-cache",
               },
            });

            clearTimeout(timeoutId);

            if (response.ok) {
               const userData = await response.json();
               if (userData.onboardingComplete) {
                  router.push("/budget");
               } else {
                  // User hasn't completed onboarding, redirect to subscription selection first
                  router.push(`/subscription/${session.user.id}`);
               }
            } else if (response.status === 404) {
               // User not found in MongoDB yet - redirect to subscription selection
               router.push(`/subscription/${session.user.id}`);
            } else {
               console.warn(
                  "API call failed:",
                  response.status,
                  response.statusText
               );
               // Fallback to subscription selection for new users
               router.push(`/subscription/${session.user.id}`);
            }
         } catch (error) {
            if (error.name === "AbortError") {
               console.warn("API call timed out");
            } else {
               console.error("Error checking user status:", error);
            }
            // Fallback to subscription selection
            router.push(`/subscription/${session.user.id}`);
         } finally {
            setLoading(false);
         }
      };

      handleRedirect();
   }, [session, status, router]);

   if (status === "loading" || loading) {
      return (
         <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
            <div className="text-center">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto"></div>
               <p className="mt-4 text-gray-600 dark:text-gray-300">
                  Loading...
               </p>
            </div>
         </div>
      );
   }

   return null; // This should never render since we always redirect
}

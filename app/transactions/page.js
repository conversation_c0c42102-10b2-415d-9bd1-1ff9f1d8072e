"use client";

import {
   useState,
   useEffect,
   useRef,
   useCallback,
   useMemo,
   Suspense,
} from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import TransactionForm from "../components/transactions/TransactionForm";
import TransactionList from "../components/transactions/TransactionList";
import MobileTransactionList from "../components/transactions/MobileTransactionList";
import { Input } from "../components/ui/input";
import { Modal } from "../components/ui/modal";
import ExpenseForm from "../components/expenses/ExpenseForm";
import AddIncomeModal from "../components/incomes/AddIncomeModal";
import PlaidConnectionStatus from "../components/settings/PlaidConnectionStatus";
import {
   ChevronLeftIcon,
   ChevronRightIcon,
   MagnifyingGlassIcon,
   PlusIcon,
   ChevronDownIcon,
   FunnelIcon,
   Bars3Icon,
   ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { createPortal } from "react-dom";
import { Combobox } from "../components/ui/combobox";
import {
   TransactionsUtils,
   TransactionHelpers,
   isCashAccount,
} from "../lib/utils/transactionsUtils";
import { useMobileMenu } from "../components/layouts/AuthenticatedLayout";
import useClickOutside from "../hooks/useClickOutside";
import React from "react";
import { useTransactions } from "../hooks/useTransactions";
import DuplicateTransactionsModal from "../components/transactions/DuplicateTransactionsModal";
import { toast } from "react-toastify";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const TRANSACTION_STATUS = ["pending", "cleared"];

// Memoize the TransactionList components
const MemoizedTransactionList = React.memo(TransactionList);
const MemoizedMobileTransactionList = React.memo(MobileTransactionList);

// Helper function to round to 2 decimal places
const roundToTwo = (num) => Number(Number(num).toFixed(2));

export default function TransactionsPage() {
   return (
      <Suspense fallback={<div>Loading...</div>}>
         <TransactionsContent />
      </Suspense>
   );
}

function TransactionsContent() {
   const { data: session, status } = useSession();
   const router = useRouter();
   const searchParams = useSearchParams();
   const [activeModals, setActiveModals] = useState({
      transaction: false,
      expense: false,
      income: false,
      duplicates: false,
   });
   const [searchQuery, setSearchQuery] = useState("");
   const [selectedAccountFilter, setSelectedAccountFilter] = useState(
      typeof window !== "undefined"
         ? localStorage.getItem("selectedAccountFilter") || "default"
         : "default"
   );
   const [accounts, setAccounts] = useState([]);
   const [showAccountDropdown, setShowAccountDropdown] = useState(null);
   const accountDropdownRef = useRef(null);
   const accountFilterRef = useRef(null);
   const currentCategoryCellRef = useRef(null);
   const searchTimeoutRef = useRef(null);
   const [balances, setBalances] = useState({
      currentBalance: 0,
      totalIncome: 0,
      totalAssigned: 0,
   });
   const [showAccountFilter, setShowAccountFilter] = useState(false);
   const [userData, setUserData] = useState(null);
   const [pagination, setPagination] = useState({
      page: 1,
      limit: 50,
      total: 0,
      totalPages: 0,
   });
   // Add state for tracking viewport width
   const [isMobileView, setIsMobileView] = useState(
      typeof window !== "undefined" ? window.innerWidth < 640 : false
   );
   const [editingTransaction, setEditingTransaction] = useState(null);
   const [availableAssignedTo, setAvailableAssignedTo] = useState([]);
   const [showCategoryModal, setShowCategoryModal] = useState(false);
   const [showStatusDropdown, setShowStatusDropdown] = useState(null);
   const statusDropdownRef = useRef(null);
   const [dropdownPosition, setDropdownPosition] = useState({
      top: 0,
      left: 0,
   });
   const datePickerRef = useRef(null);
   const [editingPayee, setEditingPayee] = useState(null);
   const [editingAmount, setEditingAmount] = useState(null);
   const payeeInputRef = useRef(null);
   const amountInputRef = useRef(null);
   const categoryDropdownRef = useRef(null);
   const isInitialRender = useRef(true);
   const [storedExpenses, setStoredExpenses] = useState([]);
   const [storedIncomes, setStoredIncomes] = useState([]);
   const [duplicateTransactions, setDuplicateTransactions] = useState(null);
   const [pendingTransactions, setPendingTransactions] = useState([]);
   const [showMoreOptions, setShowMoreOptions] = useState(false);
   const [unassignedCount, setUnassignedCount] = useState(0);
   const [showFilters, setShowFilters] = useState(false);
   const [filters, setFilters] = useState({
      type: "all", // 'all', 'income', 'expense', 'transfer'
      status: "all", // 'all', 'pending', 'cleared'
      date: "all", // 'all', 'today', 'thisWeek', 'thisMonth', 'custom'
      assignedTo: "all", // 'all', 'assigned', 'unassigned'
   });
   const filterDropdownRef = useRef(null);
   const moreOptionsRef = useRef(null);
   const { isMobileMenuOpen, setIsMobileMenuOpen } = useMobileMenu();

   const fileInputRef = useRef(null);
   const [isSearching, setIsSearching] = useState(false);
   const [error, setError] = useState("");
   const [success, setSuccess] = useState("");
   const [refreshKey, setRefreshKey] = useState(0);

   // Add new state for pre-computed filtered categories cache
   const [filteredCategoriesCache, setFilteredCategoriesCache] = useState(
      new Map()
   );

   // Add loading states to prevent multiple simultaneous requests
   const [loadingCategories, setLoadingCategories] = useState(false);
   const [loadingUnassignedCount, setLoadingUnassignedCount] = useState(false);

   // Add state for tracking last sync time
   const [lastSyncTime, setLastSyncTime] = useState(null);

   // Add state for connection status tracking
   const [connectionStatuses, setConnectionStatuses] = useState({});

   const {
      transactions,
      loading,
      fetchTransactions,
      setTransactions,
      handleSearch,
   } = useTransactions({
      selectedAccountFilter,
      page: pagination.page,
      limit: pagination.limit,
      searchQuery,
   });

   // Add useEffect to handle localStorage
   useEffect(() => {
      const savedFilter = localStorage.getItem("selectedAccountFilter");
      if (savedFilter) {
         setSelectedAccountFilter(savedFilter);
      }
   }, []);

   // Main initialization sequence
   useEffect(() => {
      if (status === "loading") return;

      if (!session?.user) {
         router.push("/auth/login");
         return;
      }

      const initialize = async () => {
         try {
            // Regular initialization
            const initialState =
               await TransactionsUtils.initializeTransactionsPage(
                  selectedAccountFilter,
                  filters.assignedTo === "unassigned"
               );

            setAccounts(initialState.accounts);
            setTransactions(initialState.transactions);
            setPagination((prev) => ({ ...prev, ...initialState.pagination }));
            setBalances(initialState.balances);
            setSelectedAccountFilter(initialState.selectedAccountFilter);

            // Fetch the real unassigned count from API
            await fetchUnassignedCount();
         } catch (error) {
            console.error("Error fetching transactions:", error);
         }
      };

      initialize();
   }, [status, session?.user, router, filters.assignedTo]);

   // Filter changes effect - only run when not searching and after initial render
   useEffect(() => {
      if (isInitialRender.current) {
         isInitialRender.current = false;
         return;
      }

      // Skip if we have a search query (search effect will handle it)
      if (searchQuery) {
         return;
      }

      // Only handle account filter changes when not searching
      const fetchData = async () => {
         try {
            await fetchTransactions({
               selectedAccountFilter,
               page: pagination.page,
               limit: pagination.limit,
               unassigned: filters.assignedTo === "unassigned",
            });
         } catch (error) {
            console.error("Error fetching transactions:", error);
         }
      };

      fetchData();
   }, [selectedAccountFilter, searchQuery, filters.assignedTo]);

   const resetToInitialState = async (clearSearch = false) => {
      try {
         const fetchParams = {
            selectedAccountFilter,
            page: 1,
            limit: pagination.limit,
         };

         const initialState =
            await TransactionsUtils.initializeTransactionsPage(
               selectedAccountFilter,
               filters.assignedTo === "unassigned"
            );

         setTransactions(initialState.transactions);
         setPagination((prev) => ({
            ...prev,
            page: 1,
            total: initialState.pagination?.total || 0,
            totalPages: initialState.pagination?.totalPages || 0,
         }));

         setBalances(initialState.balances);

         // Fetch the real unassigned count when resetting
         await fetchUnassignedCount();
         if (clearSearch) {
            setSearchQuery("");
         }
      } catch (error) {
         console.error("Error resetting to initial state:", error);
      }
   };

   // Search effect with debouncing
   useEffect(() => {
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      if (searchQuery) {
         setIsSearching(true);
         searchTimeoutRef.current = setTimeout(async () => {
            try {
               const searchResults = await TransactionsUtils.fetchTransactions({
                  selectedAccountFilter,
                  page: 1,
                  limit: pagination.limit,
                  searchQuery,
                  unassigned: filters.assignedTo === "unassigned",
               });

               setTransactions(searchResults.transactions);
               setPagination((prev) => ({
                  ...prev,
                  total: searchResults.pagination.total,
                  totalPages: searchResults.pagination.totalPages,
                  page: 1,
               }));

               // Fetch the real unassigned count (not just from search results)
               await fetchUnassignedCount();
            } catch (error) {
               console.error("Error searching transactions:", error);
            } finally {
               setIsSearching(false);
            }
         }, 150);
      } else {
         resetToInitialState();
      }

      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, [searchQuery, selectedAccountFilter, filters.assignedTo]);

   // Add click outside handler for date picker
   useClickOutside(datePickerRef, () => setShowDatePicker(null));

   // Add click outside handler for payee input
   useClickOutside(payeeInputRef, () => setEditingPayee(null));

   // Add click outside handler for category dropdown
   useClickOutside(categoryDropdownRef, () => {
      setShowCategoryModal(false);
      setEditingTransaction(null);
   });

   // Add click outside handler for account dropdown
   useClickOutside(accountDropdownRef, () => {
      setShowAccountDropdown(null);
   });

   // Add click outside handler for account filter dropdown
   useClickOutside(accountFilterRef, () => {
      setShowAccountFilter(false);
   });

   // Add effect to refetch when account filter changes
   useEffect(() => {
      // Skip the initial render and any renders where selectedAccountFilter is undefined
      if (isInitialRender.current || selectedAccountFilter === undefined) {
         isInitialRender.current = false;
         return;
      }

      localStorage.setItem(
         "selectedAccountFilter",
         selectedAccountFilter.toString()
      );
      setPagination((prev) => ({ ...prev, page: 1 }));
   }, [selectedAccountFilter]);

   // Function to refresh categories and clear all caches
   const refreshCategories = async (force = false) => {
      // Prevent multiple simultaneous category fetches
      if (loadingCategories && !force) {
         return;
      }

      try {
         setLoadingCategories(true);

         // Clear all category caches before fetching fresh data
         TransactionsUtils.clearCategoryCache();
         TransactionsUtils.forceClearCategoryCache();
         setFilteredCategoriesCache(new Map());

         const { expenses, incomes } =
            await TransactionsUtils.fetchCategories();

         // Deduplicate expenses and incomes separately
         const deduplicatedExpenses =
            TransactionsUtils.deduplicateCategories(expenses);
         const deduplicatedIncomes =
            TransactionsUtils.deduplicateCategories(incomes);

         setStoredExpenses(deduplicatedExpenses);
         setStoredIncomes(deduplicatedIncomes);

         // Set initial available categories with deduplication
         const combinedCategories = [
            ...deduplicatedExpenses,
            ...deduplicatedIncomes,
         ];
         setAvailableAssignedTo(
            TransactionsUtils.deduplicateCategories(combinedCategories)
         );

         // Pre-compute filtered categories for all current transactions
         const newCache = new Map();
         transactions.forEach((transaction) => {
            if (transaction._id) {
               const filteredCategories =
                  TransactionHelpers.filterAvailableAssignedTo(
                     transaction,
                     deduplicatedExpenses,
                     deduplicatedIncomes
                  );
               // Sort categories by date before caching
               const sortedCategories = [...filteredCategories].sort((a, b) => {
                  const getDate = (category) => {
                     if (category.date) return new Date(category.date);
                     if (category.startDate)
                        return new Date(category.startDate);
                     return new Date(0);
                  };
                  return getDate(a) - getDate(b);
               });
               newCache.set(transaction._id, sortedCategories);
            }
         });
         setFilteredCategoriesCache(newCache);
      } catch (err) {
         console.error("Error fetching categories:", err);
      } finally {
         setLoadingCategories(false);
      }
   };

   // Initial category loading on mount
   useEffect(() => {
      refreshCategories();
   }, []); // Empty dependency array - only run once on mount

   // Add page visibility listener to refresh categories when returning to page
   useEffect(() => {
      const handleVisibilityChange = () => {
         // Only refresh when page becomes visible (user returns to tab/page)
         if (!document.hidden) {
            console.log("Page became visible, refreshing categories...");
            refreshCategories(true); // Force refresh when page becomes visible
         }
      };

      const handleFocus = () => {
         console.log("Window focused, refreshing categories...");
         refreshCategories(true); // Force refresh when window gains focus
      };

      // Listen for page visibility changes
      document.addEventListener("visibilitychange", handleVisibilityChange);
      window.addEventListener("focus", handleFocus);

      return () => {
         document.removeEventListener(
            "visibilitychange",
            handleVisibilityChange
         );
         window.removeEventListener("focus", handleFocus);
      };
   }, [transactions]); // Include transactions as dependency to update cache when transactions change

   // Update cache when transactions change
   useEffect(() => {
      if (storedExpenses.length > 0 && storedIncomes.length > 0) {
         const newCache = new Map(filteredCategoriesCache);

         transactions.forEach((transaction) => {
            if (transaction._id && !newCache.has(transaction._id)) {
               const filteredCategories =
                  TransactionHelpers.filterAvailableAssignedTo(
                     transaction,
                     storedExpenses,
                     storedIncomes
                  );
               // Sort categories by date before caching
               const sortedCategories = [...filteredCategories].sort((a, b) => {
                  const getDate = (category) => {
                     if (category.date) return new Date(category.date);
                     if (category.startDate)
                        return new Date(category.startDate);
                     return new Date(0);
                  };
                  return getDate(a) - getDate(b);
               });
               newCache.set(transaction._id, sortedCategories);
            }
         });

         setFilteredCategoriesCache(newCache);
      }

      // Auto-disable unassigned filter if no unassigned transactions remain
      if (filters.assignedTo === "unassigned" && unassignedCount === 0) {
         setFilters((prev) => ({ ...prev, assignedTo: "all" }));
      }
   }, [
      transactions,
      storedExpenses,
      storedIncomes,
      filters.assignedTo,
      unassignedCount,
   ]);

   // Optimized category click handler that uses cached data
   const handleCategoryClick = (transaction, event) => {
      if (!transaction) {
         setEditingTransaction(null);
         setShowCategoryModal(false);
         return;
      }

      // Use cached filtered categories if available
      const cachedCategories = filteredCategoriesCache.get(transaction._id);
      if (cachedCategories) {
         setAvailableAssignedTo(cachedCategories);
      } else {
         // Fallback: compute on demand if not cached
         const filteredCategories =
            TransactionHelpers.filterAvailableAssignedTo(
               transaction,
               storedExpenses,
               storedIncomes
            );
         const sortedCategories = [...filteredCategories].sort((a, b) => {
            const getDate = (category) => {
               if (category.date) return new Date(category.date);
               if (category.startDate) return new Date(category.startDate);
               return new Date(0);
            };
            return getDate(a) - getDate(b);
         });
         setAvailableAssignedTo(sortedCategories);

         // Cache for future use
         const newCache = new Map(filteredCategoriesCache);
         newCache.set(transaction._id, sortedCategories);
         setFilteredCategoriesCache(newCache);
      }

      setEditingTransaction(transaction);
   };

   // Helper function to calculate unassigned count from current transactions
   const calculateUnassignedCount = (transactionsList) => {
      return transactionsList.filter(
         (transaction) =>
            !transaction.assignedTo ||
            transaction.assignedTo === "Unassigned" ||
            transaction.assignedTo === null
      ).length;
   };

   // Function to fetch the actual unassigned count from the API
   const fetchUnassignedCount = async () => {
      // Prevent multiple simultaneous unassigned count fetches
      if (loadingUnassignedCount) {
         console.log("Unassigned count already loading, skipping request");
         return unassignedCount;
      }

      try {
         setLoadingUnassignedCount(true);
         const response = await fetch("/api/transactions/unassigned/count");
         const data = await response.json();
         setUnassignedCount(data.count);
         return data.count;
      } catch (error) {
         console.error("Error fetching unassigned count:", error);
         return unassignedCount;
      } finally {
         setLoadingUnassignedCount(false);
      }
   };

   const handleAssignedToChange = async (
      transactionId,
      assignedToId,
      assignedToType,
      assignedToObject = null
   ) => {
      // Store original transaction and accounts for potential rollback
      const originalTransaction = transactions.find(
         (t) => t._id === transactionId
      );
      if (!originalTransaction) return;
      const originalAccounts = [...accounts];
      const originalUnassignedCount = unassignedCount;

      // Get the assignedTo description for display - use provided assignedTo object first,
      // then look in stored expenses and incomes, then availableAssignedTo
      let selectedAssignedTo = null;
      if (assignedToId) {
         selectedAssignedTo =
            assignedToObject ||
            storedExpenses.find((c) => c._id === assignedToId) ||
            storedIncomes.find((c) => c._id === assignedToId) ||
            availableAssignedTo.find((c) => c._id === assignedToId);
      }

      // Immediately update the UI
      const updatedTransactions = transactions.map((transaction) =>
         transaction._id === transactionId
            ? {
                 ...transaction,
                 assignedTo: selectedAssignedTo
                    ? selectedAssignedTo.description
                    : "Unassigned",
                 assignedToType: assignedToId ? assignedToType : null,
              }
            : transaction
      );

      setTransactions(updatedTransactions);

      // Update unassigned count optimistically based on the change
      const wasUnassigned =
         !originalTransaction.assignedTo ||
         originalTransaction.assignedTo === "Unassigned";
      const isNowUnassigned = !assignedToId;

      if (wasUnassigned && !isNowUnassigned) {
         // Transaction was unassigned, now assigned - decrease count
         setUnassignedCount((prev) => Math.max(0, prev - 1));
      } else if (!wasUnassigned && isNowUnassigned) {
         // Transaction was assigned, now unassigned - increase count
         setUnassignedCount((prev) => prev + 1);
      }

      try {
         const data = await TransactionsUtils.handleAssignedToChange(
            transactionId,
            assignedToId,
            assignedToType
         );

         // Update the accounts in the UI if they were returned
         if (data.accounts) {
            setAccounts(data.accounts);
         }
      } catch (err) {
         // If the backend update fails, revert the UI changes
         setTransactions((prevTransactions) =>
            prevTransactions.map((transaction) =>
               transaction._id === transactionId
                  ? originalTransaction
                  : transaction
            )
         );
         // Revert account balances and unassigned count
         setAccounts(originalAccounts);
         setUnassignedCount(originalUnassignedCount);
         console.error("Error:", err);
         // Refresh transactions to ensure consistency
         await fetchTransactions({
            selectedAccountFilter,
            page: pagination.page,
            limit: pagination.limit,
            searchQuery,
            unassigned: filters.assignedTo === "unassigned",
         });
      }
   };

   const handleStatusChange = async (transactionId, newStatus) => {
      // Store the original transaction for potential rollback
      const originalTransaction = transactions.find(
         (t) => t._id === transactionId
      );
      if (!originalTransaction) return;

      // Close the dropdown immediately
      setShowStatusDropdown(null);

      // Optimistically update the UI
      setTransactions((prevTransactions) =>
         prevTransactions.map((transaction) =>
            transaction._id === transactionId
               ? { ...transaction, status: newStatus }
               : transaction
         )
      );

      try {
         await TransactionsUtils.handleStatusChange(transactionId, newStatus);
      } catch (err) {
         // If the backend update fails, revert the UI change
         setTransactions((prevTransactions) =>
            prevTransactions.map((transaction) =>
               transaction._id === transactionId
                  ? { ...transaction, status: originalTransaction.status }
                  : transaction
            )
         );
         console.error("Error:", err);
      }
   };

   const handleFieldUpdate = async (transactionId, field, value) => {
      // Store the original transaction for potential rollback
      const originalTransaction = transactions.find(
         (t) =>
            t._id === transactionId ||
            t._id === `${transactionId}_from` ||
            t._id === `${transactionId}_to`
      );
      if (!originalTransaction) return;

      // Store original account balances for potential rollback
      const originalAccounts = [...accounts];

      // For transfer transactions, update both displays in the UI immediately
      if (
         field === "amount" &&
         (originalTransaction.type === "Transfer" ||
            originalTransaction.type === "Payment")
      ) {
         const baseId = transactionId.replace(/_from$|_to$/, "");
         setTransactions((prevTransactions) =>
            prevTransactions.map((transaction) => {
               if (transaction._id === `${baseId}_from`) {
                  return { ...transaction, amount: -Math.abs(value) };
               }
               if (transaction._id === `${baseId}_to`) {
                  return { ...transaction, amount: Math.abs(value) };
               }
               return transaction;
            })
         );
      } else {
         // For non-transfer transactions, update normally
         setTransactions((prevTransactions) =>
            prevTransactions.map((transaction) =>
               transaction._id === transactionId
                  ? TransactionHelpers.formatTransactionForUI(
                       transaction,
                       field,
                       value
                    )
                  : transaction
            )
         );
      }

      // If updating amount, calculate the difference for account balance updates
      if (field === "amount") {
         const amountDiff = TransactionHelpers.calculateAmountDiff(
            value,
            originalTransaction.amount
         );
         if (originalTransaction.accountId) {
            setAccounts((prevAccounts) =>
               prevAccounts.map((account) =>
                  account._id === originalTransaction.accountId
                     ? {
                          ...account,
                          balance: roundToTwo(account.balance + amountDiff),
                       }
                     : account
               )
            );
         }
      }

      try {
         const data = await TransactionsUtils.updateTransaction(
            transactionId.replace(/_from$|_to$/, ""),
            {
               [field]:
                  field === "date"
                     ? new Date(new Date(value).setHours(12, 0, 0, 0))
                     : value,
            }
         );

         // Update the accounts in the UI if they were returned
         if (data.accounts) {
            setAccounts(data.accounts);
         }
      } catch (err) {
         // If the backend update fails, revert the UI changes
         setTransactions((prevTransactions) =>
            prevTransactions.map((transaction) =>
               transaction._id === transactionId ||
               transaction._id === `${transactionId}_from` ||
               transaction._id === `${transactionId}_to`
                  ? originalTransaction
                  : transaction
            )
         );
         // Revert account balances
         setAccounts(originalAccounts);
         console.error("Error:", err);
         // Refresh transactions to ensure consistency
         await fetchTransactions({
            selectedAccountFilter,
            page: pagination.page,
            limit: pagination.limit,
            searchQuery,
            unassigned: filters.assignedTo === "unassigned",
         });
      }
   };

   const handleCSVUpload = async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      try {
         const { duplicates, nonDuplicates, error } =
            await TransactionsUtils.handleCSVUpload(
               file,
               transactions,
               selectedAccountFilter
            );

         if (error) {
            console.error("CSV Upload Error:", error);
         } else if (duplicates?.length > 0) {
            setDuplicateTransactions(duplicates);
            setPendingTransactions(nonDuplicates);
            setActiveModals((prev) => ({ ...prev, duplicates: true }));
         } else {
            await importTransactions(nonDuplicates);
         }
      } catch (err) {
         console.error("CSV Upload Error:", err);
      }
   };

   const importTransactions = async (transactionsToImport) => {
      try {
         const data = await TransactionsUtils.bulkCreateTransactions(
            transactionsToImport
         );

         // Update accounts with new balances if they were returned
         if (data.accounts) {
            setAccounts(data.accounts);
         }

         // Refresh the transactions list and unassigned count
         await fetchTransactions({
            selectedAccountFilter,
            page: pagination.page,
            limit: pagination.limit,
            searchQuery,
            unassigned: filters.assignedTo === "unassigned",
         });
         await fetchUnassignedCount();
      } catch (err) {
         console.error("Error importing transactions:", err);
      }
   };

   const handleDuplicateApproval = (index, approved) => {
      console.log("Handling duplicate approval:", { index, approved });
      setDuplicateTransactions((prev) => {
         const updated = [...prev];
         updated[index] = { ...updated[index], approved };
         return updated;
      });
   };

   const handleDuplicateReviewComplete = async () => {
      try {
         // Filter approved duplicates
         const approvedDuplicates = duplicateTransactions.filter(
            (t) => t.approved
         );

         // Combine with non-duplicate transactions
         const allApprovedTransactions = [
            ...pendingTransactions,
            ...approvedDuplicates,
         ];

         console.log("Importing transactions:", {
            approved: approvedDuplicates.length,
            nonDuplicates: pendingTransactions.length,
            total: allApprovedTransactions.length,
         });

         if (allApprovedTransactions.length > 0) {
            await importTransactions(allApprovedTransactions);
         }

         // Reset state
         setDuplicateTransactions([]);
         setPendingTransactions([]);
         setActiveModals((prev) => ({ ...prev, duplicates: false }));
      } catch (error) {
         console.error("Error completing import:", error);
      }
   };

   const downloadTemplate = () => {
      const csvContent = TransactionHelpers.generateCSVTemplate();
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "transactions_template.csv";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
   };

   const handleExpenseSuccess = async (newExpense) => {
      setActiveModals((prev) => ({ ...prev, expense: false }));

      // Refresh categories immediately since a new expense was added
      await refreshCategories(true);

      // Assign the new expense to the transaction
      if (newExpense && editingTransaction) {
         await handleAssignedToChange(
            editingTransaction._id,
            newExpense._id,
            newExpense.assignedToType
         );
      }
      await fetchTransactions({
         selectedAccountFilter,
         page: pagination.page,
         limit: pagination.limit,
         searchQuery,
         unassigned: filters.assignedTo === "unassigned",
      });
   };

   const handleIncomeSuccess = async (newIncome) => {
      setActiveModals((prev) => ({ ...prev, income: false }));

      // Refresh categories immediately since a new income was added
      await refreshCategories(true);

      // Assign the new income to the transaction
      if (newIncome && editingTransaction) {
         await handleAssignedToChange(
            editingTransaction._id,
            newIncome._id,
            newIncome.assignedToType
         );
      }
      await fetchTransactions({
         selectedAccountFilter,
         page: pagination.page,
         limit: pagination.limit,
         searchQuery,
         unassigned: filters.assignedTo === "unassigned",
      });
   };

   const handlePageChange = async (newPage) => {
      try {
         await fetchTransactions({
            selectedAccountFilter,
            page: newPage,
            limit: pagination.limit,
            searchQuery,
            unassigned: filters.assignedTo === "unassigned",
         });
         setPagination((prev) => ({ ...prev, page: newPage }));
      } catch (error) {
         console.error("Error fetching transactions:", error);
      }
   };

   const handleTransactionAdded = async (response) => {
      try {
         if (!response || typeof response !== "object") {
            throw new Error("Invalid response from transaction creation");
         }

         const transaction = response;
         let optimisticTransaction = { ...transaction };

         // Add assignedTo description
         if (transaction.assignedTo) {
            const selectedAssignedTo = availableAssignedTo.find(
               (c) => c._id === transaction.assignedTo
            );
            optimisticTransaction.assignedTo = selectedAssignedTo
               ? selectedAssignedTo.description
               : "Unassigned";
         } else {
            optimisticTransaction.assignedTo = "Unassigned";
         }

         const result = await TransactionsUtils.handleTransactionAdded(
            optimisticTransaction,
            {
               accounts,
               transactions,
               pagination,
               selectedAccountFilter,
            }
         );

         if (result) {
            setTransactions(result.transactions);
            setAccounts(result.accounts);
            setPagination(result.pagination);

            // Update unassigned count optimistically
            if (
               !transaction.assignedTo ||
               transaction.assignedTo === "Unassigned"
            ) {
               setUnassignedCount((prev) => prev + 1);
            }

            // Update balances state for non-transfer transactions
            if (transaction.type !== "Transfer") {
               setBalances((prev) => ({
                  ...prev,
                  currentBalance: Number(
                     (prev.currentBalance + transaction.amount).toFixed(2)
                  ),
                  totalIncome:
                     transaction.type === "Income" &&
                     (!transaction.assignedTo ||
                        transaction.assignedToType === "Income")
                        ? Number(
                             (prev.totalIncome + transaction.amount).toFixed(2)
                          )
                        : prev.totalIncome,
                  totalAssigned: transaction.assignedTo
                     ? Number(
                          (prev.totalAssigned + transaction.amount).toFixed(2)
                       )
                     : prev.totalAssigned,
               }));
            }
         }

         setActiveModals((prev) => ({ ...prev, transaction: false }));
      } catch (err) {
         console.error("Error updating transaction data:", err);
      }
   };

   const handleDelete = async (
      transactionId,
      updatedTransactions,
      updatedAccounts
   ) => {
      // Store original state for potential rollback
      const originalTransactions = [...transactions];
      const originalAccounts = [...accounts];
      const originalUnassignedCount = unassignedCount;

      // Optimistically update UI
      setTransactions(updatedTransactions);
      setAccounts(updatedAccounts);

      // Update unassigned count optimistically
      const deletedTransaction = originalTransactions.find(
         (t) => !updatedTransactions.find((ut) => ut._id === t._id)
      );
      if (
         deletedTransaction &&
         (!deletedTransaction.assignedTo ||
            deletedTransaction.assignedTo === "Unassigned")
      ) {
         setUnassignedCount((prev) => Math.max(0, prev - 1));
      }

      try {
         // Make the API call
         await TransactionsUtils.deleteTransaction(transactionId);
      } catch (error) {
         // If the API call fails, revert the optimistic updates
         setTransactions(originalTransactions);
         setAccounts(originalAccounts);
         setUnassignedCount(originalUnassignedCount);
         console.error("Error deleting transaction:", error);
         // Refresh transactions to ensure consistency
         await fetchTransactions({
            selectedAccountFilter,
            page: pagination.page,
            limit: pagination.limit,
            searchQuery,
            unassigned: filters.assignedTo === "unassigned",
         });
      }
   };

   const handleAccountFilterChange = (accountId) => {
      const newState = TransactionHelpers.handleAccountFilterChange(accountId, {
         pagination,
      });
      setSelectedAccountFilter(newState.selectedAccountFilter);
      setPagination(newState.pagination);
      setShowAccountFilter(false);
   };

   const handleAccountDropdownClick = (accountId) => {
      if (showAccountDropdown) {
         // This is a transaction account change
         handleAccountChange(showAccountDropdown, accountId);
      } else {
         // This is an account filter change
         handleAccountFilterChange(accountId);
      }
   };

   const handleAccountChange = async (transactionId, newAccountId) => {
      try {
         // Close the dropdown immediately
         setShowAccountDropdown(null);

         // Find the current transaction to get its details
         const transaction = transactions.find((t) => t._id === transactionId);
         if (!transaction) {
            throw new Error("Transaction not found");
         }

         // Store original state for rollback
         const originalTransaction = { ...transaction };
         const originalAccounts = [...accounts];

         // If the account isn't changing, just return
         if (transaction.accountId === Number(newAccountId)) {
            return;
         }

         // Optimistically update UI - transaction
         setTransactions((prevTransactions) =>
            prevTransactions.map((t) =>
               t._id === transactionId
                  ? { ...t, accountId: Number(newAccountId) }
                  : t
            )
         );

         // Optimistically update UI - accounts
         setAccounts((prevAccounts) =>
            prevAccounts.map((account) => {
               if (account._id === transaction.accountId) {
                  return {
                     ...account,
                     balance: roundToTwo(account.balance - transaction.amount),
                  };
               }
               if (account._id === Number(newAccountId)) {
                  return {
                     ...account,
                     balance: roundToTwo(account.balance + transaction.amount),
                  };
               }
               return account;
            })
         );

         const data = await TransactionsUtils.handleAccountChange(
            transactionId,
            newAccountId,
            transaction
         );

         // If the server returns updated account data, use it
         if (data.accounts) {
            setAccounts(data.accounts);
         }
      } catch (error) {
         console.error("Error updating transaction account:", error);
         // Revert optimistic updates on error
         setTransactions((prevTransactions) =>
            prevTransactions.map((t) =>
               t._id === transactionId ? originalTransaction : t
            )
         );
         setAccounts(originalAccounts);
      }
   };

   const handleDuplicateResolution = async (resolutions) => {
      try {
         setError("");
         setSuccess("");

         const response = await fetch("/api/plaid/resolve-duplicates", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ resolutions }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to resolve duplicates");
         }

         const data = await response.json();

         // Clear duplicate transactions and show success message
         setDuplicateTransactions(null);
         toast.success(
            data.message || "Successfully processed duplicate transactions"
         );

         // Refresh transactions list
         await fetchTransactions({
            selectedAccountFilter,
            page: pagination.page,
            limit: pagination.limit,
         });
      } catch (err) {
         setError(err.message);
         toast.error(err.message);
      }
   };

   // Add click outside handler for filter dropdown
   useClickOutside(filterDropdownRef, () => setShowFilters(false));

   // Add click outside handler for more options dropdown
   useClickOutside(moreOptionsRef, () => setShowMoreOptions(false));

   const filteredTransactions = useMemo(() => {
      if (!transactions) return [];

      return transactions.filter((transaction) => {
         // Apply client-side filters (server-side assignedTo filtering is handled by API)
         if (filters.type !== "all" && transaction.type !== filters.type)
            return false;
         if (filters.status !== "all" && transaction.status !== filters.status)
            return false;

         // Note: assignedTo filtering is now handled server-side
         // Add date filtering logic if needed
         return true;
      });
   }, [transactions, filters]);

   const handleCSVExport = async () => {
      try {
         const data = await TransactionsUtils.exportCSV();

         // Create CSV content
         const headers = [
            "Date,Description,Amount,Type,Status,Account,AssignedTo",
         ];
         const rows = data.transactions.map((t) => {
            const date = new Date(t.date).toLocaleDateString("en-US");
            const amount = t.amount || 0;
            const account = t.accountId || "Unassigned";

            return [
               `"${date}"`,
               `"${t.payee}"`,
               amount.toFixed(2),
               t.type,
               t.status,
               `"${account}"`,
               `"${t.assignedTo || ""}"`,
            ].join(",");
         });

         const csvContent = [...headers, ...rows].join("\n");
         const blob = new Blob([csvContent], { type: "text/csv" });
         const url = window.URL.createObjectURL(blob);
         const a = document.createElement("a");
         a.href = url;
         a.download = `transactions_${
            new Date().toISOString().split("T")[0]
         }.csv`;
         document.body.appendChild(a);
         a.click();
         document.body.removeChild(a);
         window.URL.revokeObjectURL(url);
      } catch (error) {
         console.error("Export failed:", error);
         // Add error toast here if needed
      }
   };

   // Add useEffect to fetch user data
   useEffect(() => {
      const fetchUser = async () => {
         try {
            const response = await fetch("/api/user");
            const data = await response.json();
            setUserData(data);
         } catch (error) {
            console.error("Error fetching user data:", error);
         }
      };

      if (session?.user) {
         fetchUser();
      }
   }, [session?.user]);

   // Add this effect to handle single account case
   useEffect(() => {
      if (accounts.length === 1) {
         const accountId = accounts[0]._id.toString();
         setSelectedAccountFilter(accountId);
         localStorage.setItem("selectedAccountFilter", accountId);
      }
   }, [accounts]);

   // Add memoized category descriptions to avoid expensive date formatting in each row
   const categoryDescriptions = useMemo(() => {
      const cache = new Map();

      const createDescription = (category) => {
         if (!category) return "";

         if (category.expectedAmount) {
            // Income category
            return new Date(category.date).toLocaleDateString(undefined, {
               month: "numeric",
               day: "numeric",
            });
         } else {
            // Expense category
            const dateStr =
               category.weeklyChargeType === "spread"
                  ? `${new Date(category.startDate).toLocaleDateString(
                       undefined,
                       {
                          month: "numeric",
                          day: "numeric",
                       }
                    )} - ${new Date(category.endDate).toLocaleDateString(
                       undefined,
                       {
                          month: "numeric",
                          day: "numeric",
                       }
                    )}`
                  : new Date(category.date).toLocaleDateString(undefined, {
                       month: "numeric",
                       day: "numeric",
                    });
            return dateStr;
         }
      };

      [...storedExpenses, ...storedIncomes].forEach((category) => {
         if (category && category._id) {
            cache.set(category._id, createDescription(category));
         }
      });

      return cache;
   }, [storedExpenses, storedIncomes]);

   // Pre-compute assignedTo options for each transaction to avoid expensive operations in TransactionRow
   const transactionAssignedToOptions = useMemo(() => {
      const cache = new Map();

      transactions.forEach((transaction) => {
         if (!transaction._id || transaction.type === "Transfer") {
            return;
         }

         // Get cached filtered categories or compute them
         const cachedCategories = filteredCategoriesCache.get(transaction._id);
         const relevantCategories = cachedCategories || availableAssignedTo;

         // Create options with pre-computed descriptions
         const options = [
            { value: "", label: "Unassigned" },
            ...relevantCategories
               .filter((category) => category && category._id)
               .filter(
                  (category, index, self) =>
                     self.findIndex(
                        (c) => c._id?.toString() === category._id?.toString()
                     ) === index
               )
               .map((category) => ({
                  value: category._id,
                  label: category.description,
                  description: categoryDescriptions.get(category._id) || "",
                  assignedToType: category.expectedAmount
                     ? "Income"
                     : "Expense",
               })),
         ];

         cache.set(transaction._id, options);
      });

      return cache;
   }, [
      transactions,
      filteredCategoriesCache,
      availableAssignedTo,
      categoryDescriptions,
   ]);

   // Function to get last sync time for the selected account
   const getLastSyncTime = useCallback(() => {
      if (!userData?.plaidItems || selectedAccountFilter === "all") {
         return null;
      }

      const selectedAccount = accounts.find(
         (acc) => acc._id === Number(selectedAccountFilter)
      );

      if (!selectedAccount?.plaidItemId) {
         return null;
      }

      const plaidItem = userData.plaidItems.find(
         (item) => item.itemId === selectedAccount.plaidItemId
      );

      return plaidItem?.lastSync || null;
   }, [userData, selectedAccountFilter, accounts]);

   // Update last sync time when dependencies change
   useEffect(() => {
      const syncTime = getLastSyncTime();
      setLastSyncTime(syncTime);
   }, [getLastSyncTime]);

   // Add effect to handle window resize
   useEffect(() => {
      const handleResize = () => {
         setIsMobileView(window.innerWidth < 640);
      };

      // Set initial value
      handleResize();

      // Add event listener
      window.addEventListener("resize", handleResize);

      // Clean up
      return () => window.removeEventListener("resize", handleResize);
   }, []);

   // Render the account filter section with the shadcn dropdown
   const renderAccountFilter = () => {
      if (isMobileView) {
         return (
            <div className="flex items-center gap-2 px-4 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg w-auto h-[42px]">
               <span className="truncate max-w-[100px]">
                  {accounts.length === 0 ? (
                     <span className="inline-flex items-center">
                        <svg
                           className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500"
                           xmlns="http://www.w3.org/2000/svg"
                           fill="none"
                           viewBox="0 0 24 24"
                        >
                           <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                           ></circle>
                           <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                           ></path>
                        </svg>
                        Loading...
                     </span>
                  ) : accounts.length === 1 ? (
                     accounts[0].name
                  ) : (
                     "All Accounts"
                  )}
               </span>
               <span
                  className={`font-mono ${
                     accounts.length === 0
                        ? "text-gray-400"
                        : accounts.length === 1
                        ? accounts[0].balance >= 0
                           ? "text-green-600 dark:text-green-400"
                           : "text-red-600 dark:text-red-400"
                        : accounts
                             .filter(
                                (account) =>
                                   isCashAccount(account) ||
                                   account.accountType === "credit"
                             )
                             .reduce(
                                (sum, account) => sum + account.balance,
                                0
                             ) >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                  }`}
               >
                  $
                  {accounts.length === 0
                     ? "0.00"
                     : accounts.length === 1
                     ? accounts[0].balance.toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                       })
                     : accounts
                          .filter(
                             (account) =>
                                isCashAccount(account) ||
                                account.accountType === "credit"
                          )
                          .reduce((sum, account) => sum + account.balance, 0)
                          .toLocaleString("en-US", {
                             minimumFractionDigits: 2,
                             maximumFractionDigits: 2,
                          })}
               </span>
            </div>
         );
      }

      // Show static display for single account (desktop)
      const filteredAccounts = accounts.filter(
         (account) => isCashAccount(account) || account.accountType === "credit"
      );

      if (filteredAccounts.length === 1) {
         const account = filteredAccounts[0];
         return (
            <div className="flex items-center gap-4 px-4 py-2.5 text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg w-auto h-[42px]">
               <span className="truncate max-w-[100px] sm:max-w-[120px]">
                  {account.name}
               </span>
               <span
                  className={`font-mono ${
                     account.balance >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                  }`}
               >
                  $
                  {account.balance.toLocaleString("en-US", {
                     minimumFractionDigits: 2,
                     maximumFractionDigits: 2,
                  })}
               </span>
            </div>
         );
      }

      // Show dropdown for multiple accounts (desktop)
      return (
         <DropdownMenu>
            <DropdownMenuTrigger asChild>
               <button
                  type="button"
                  className="inline-flex items-center gap-2 px-4 py-2.5 text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 w-auto h-[42px]"
               >
                  <div className="flex items-center gap-4 whitespace-nowrap">
                     <span className="truncate max-w-[100px] sm:max-w-[120px]">
                        {accounts.length === 0 ? (
                           <span className="inline-flex items-center">
                              <svg
                                 className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500"
                                 xmlns="http://www.w3.org/2000/svg"
                                 fill="none"
                                 viewBox="0 0 24 24"
                              >
                                 <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                 ></circle>
                                 <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                 ></path>
                              </svg>
                              Loading...
                           </span>
                        ) : selectedAccountFilter === "all" ? (
                           "All Accounts"
                        ) : (
                           accounts.find(
                              (a) =>
                                 Number(a._id) === Number(selectedAccountFilter)
                           )?.name || "Unknown Account"
                        )}
                     </span>
                     <span
                        className={`font-mono ${
                           (selectedAccountFilter === "all"
                              ? accounts
                                   .filter(
                                      (account) =>
                                         isCashAccount(account) ||
                                         account.accountType === "credit"
                                   )
                                   .reduce(
                                      (sum, account) => sum + account.balance,
                                      0
                                   )
                              : accounts.find(
                                   (a) =>
                                      Number(a._id) ===
                                      Number(selectedAccountFilter)
                                )?.balance || 0) >= 0
                              ? "text-green-600 dark:text-green-400"
                              : "text-red-600 dark:text-red-400"
                        }`}
                     >
                        $
                        {(selectedAccountFilter === "all"
                           ? accounts
                                .filter(
                                   (account) =>
                                      isCashAccount(account) ||
                                      account.accountType === "credit"
                                )
                                .reduce(
                                   (sum, account) => sum + account.balance,
                                   0
                                )
                           : accounts.find(
                                (a) =>
                                   Number(a._id) ===
                                   Number(selectedAccountFilter)
                             )?.balance || 0
                        ).toLocaleString("en-US", {
                           minimumFractionDigits: 2,
                           maximumFractionDigits: 2,
                        })}
                     </span>
                  </div>
                  <ChevronDownIcon className="h-5 w-5 text-gray-400 flex-shrink-0" />
               </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
               align="start"
               className="w-80 sm:w-72 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600"
            >
               {accounts.length === 0 ? (
                  <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                     <div className="flex items-center justify-center">
                        <svg
                           className="animate-spin -ml-1 mr-2 h-4"
                           xmlns="http://www.w3.org/2000/svg"
                           fill="none"
                           viewBox="0 0 24 24"
                        >
                           <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                           ></circle>
                           <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                           ></path>
                        </svg>
                        Loading accounts...
                     </div>
                  </div>
               ) : (
                  <>
                     <DropdownMenuItem
                        onClick={() => handleAccountFilterChange("all")}
                        className={`w-full hover:bg-gray-50 dark:hover:bg-gray-700 ${
                           selectedAccountFilter === "all"
                              ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                              : "text-gray-900 dark:text-gray-100"
                        }`}
                     >
                        <div className="flex justify-between items-center w-full">
                           <span>All Accounts</span>
                           <span
                              className={`font-mono ${
                                 accounts
                                    .filter(
                                       (account) =>
                                          isCashAccount(account) ||
                                          account.accountType === "credit"
                                    )
                                    .reduce(
                                       (sum, account) => sum + account.balance,
                                       0
                                    ) >= 0
                                    ? "text-green-600 dark:text-green-400"
                                    : "text-red-600 dark:text-red-400"
                              }`}
                           >
                              $
                              {accounts
                                 .filter(
                                    (account) =>
                                       isCashAccount(account) ||
                                       account.accountType === "credit"
                                 )
                                 .reduce(
                                    (sum, account) => sum + account.balance,
                                    0
                                 )
                                 .toLocaleString("en-US", {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                 })}
                           </span>
                        </div>
                     </DropdownMenuItem>
                     {accounts
                        .filter(
                           (account) =>
                              isCashAccount(account) ||
                              account.accountType === "credit"
                        )
                        .map((account) => (
                           <DropdownMenuItem
                              key={account._id}
                              onClick={() =>
                                 handleAccountDropdownClick(account._id)
                              }
                              className={`w-full hover:bg-gray-50 dark:hover:bg-gray-700 ${
                                 (
                                    showAccountDropdown
                                       ? showAccountDropdown === account._id
                                       : Number(selectedAccountFilter) ===
                                         account._id
                                 )
                                    ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                    : "text-gray-900 dark:text-gray-100"
                              }`}
                           >
                              <div className="flex justify-between items-center w-full">
                                 <span>{account.name}</span>
                                 <span
                                    className={`font-mono ${
                                       account.balance >= 0
                                          ? "text-green-600 dark:text-green-400"
                                          : "text-red-600 dark:text-red-400"
                                    }`}
                                 >
                                    $
                                    {account.balance.toLocaleString("en-US", {
                                       minimumFractionDigits: 2,
                                       maximumFractionDigits: 2,
                                    })}
                                 </span>
                              </div>
                           </DropdownMenuItem>
                        ))}
                  </>
               )}
            </DropdownMenuContent>
         </DropdownMenu>
      );
   };

   // Handle connection status updates from the PlaidConnectionStatus component
   const handleConnectionStatusUpdate = (accountId, status) => {
      setConnectionStatuses((prev) => ({
         ...prev,
         [accountId]: status,
      }));
   };

   if (status === "loading") {
      return (
         <div className="flex items-center justify-center h-full">
            <div className="text-gray-500 dark:text-gray-400">Loading...</div>
         </div>
      );
   }

   if (!session?.user) {
      return null;
   }

   return (
      <div className="h-full flex flex-col">
         <div className="flex-1 flex flex-col h-[calc(100vh-4rem)]">
            {/* Header and Controls */}
            <div className="sticky top-0 z-10 bg-white dark:bg-gray-900 pb-4 pt-2 sm:pt-4 px-3 lg:px-4">
               {/* Mobile Layout - Account Row */}
               <div className="flex sm:hidden flex-row gap-3 w-full items-center justify-between mb-3">
                  {/* Account Selector and Sync Button */}
                  <div className="flex items-center space-x-2 flex-1">
                     {renderAccountFilter()}
                     {selectedAccountFilter !== "all" &&
                        accounts.find(
                           (acc) => acc._id === Number(selectedAccountFilter)
                        )?.plaidItemId && (
                           <div className="ml-2 flex flex-col items-start text-xs text-gray-500 dark:text-gray-400">
                              {/* Plaid Connection Status Badge */}
                              {selectedAccountFilter !== "all" && (
                                 <div className="mb-1">
                                    <PlaidConnectionStatus
                                       account={accounts.find(
                                          (acc) =>
                                             acc._id ===
                                             Number(selectedAccountFilter)
                                       )}
                                       inline={true}
                                       onStatusUpdate={
                                          handleConnectionStatusUpdate
                                       }
                                    />
                                 </div>
                              )}
                              {lastSyncTime ? (
                                 <div className="flex items-center text-xs">
                                    <svg
                                       className="h-3 w-3 mr-1"
                                       xmlns="http://www.w3.org/2000/svg"
                                       fill="none"
                                       viewBox="0 0 24 24"
                                       stroke="currentColor"
                                    >
                                       <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                       />
                                    </svg>
                                    <span>
                                       Last updated:{" "}
                                       {new Date(lastSyncTime).toLocaleString()}
                                    </span>
                                 </div>
                              ) : (
                                 <span className="text-xs">Never synced</span>
                              )}
                           </div>
                        )}
                  </div>
               </div>

               {/* Mobile Search and Actions Row */}
               <div className="flex sm:hidden flex-row items-center space-x-3">
                  {/* Search Input */}
                  <div className="relative flex-1">
                     <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        {isSearching ? (
                           <svg
                              className="animate-spin h-5 w-5 text-gray-400"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                           >
                              <circle
                                 className="opacity-25"
                                 cx="12"
                                 cy="12"
                                 r="10"
                                 stroke="currentColor"
                                 strokeWidth="4"
                              ></circle>
                              <path
                                 className="opacity-75"
                                 fill="currentColor"
                                 d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                           </svg>
                        ) : (
                           <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                        )}
                     </div>
                     <Input
                        type="text"
                        placeholder="Search transactions..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="!pl-10 pr-3 py-2 w-full h-[42px] bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 rounded-lg"
                     />
                     {searchQuery && (
                        <button
                           onClick={() => resetToInitialState(true)}
                           className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                           <svg
                              className="h-5 w-5 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                           >
                              <path
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                                 d="M6 18L18 6M6 6l12 12"
                              />
                           </svg>
                        </button>
                     )}
                  </div>

                  {/* Unassigned Filter Toggle */}
                  <button
                     onClick={() =>
                        setFilters((prev) => ({
                           ...prev,
                           assignedTo:
                              prev.assignedTo === "unassigned"
                                 ? "all"
                                 : "unassigned",
                        }))
                     }
                     disabled={unassignedCount === 0}
                     className={`px-3 py-2 rounded-lg text-sm font-medium border transition-colors h-[42px] flex items-center gap-2 ${
                        filters.assignedTo === "unassigned"
                           ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                           : unassignedCount === 0
                           ? "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600 cursor-not-allowed"
                           : "bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"
                     }`}
                  >
                     <svg
                        className="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                     >
                        <path
                           fillRule="evenodd"
                           d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                           clipRule="evenodd"
                        />
                     </svg>
                     Unassigned ({unassignedCount})
                  </button>

                  {/* More Options Dropdown */}
                  <div className="relative">
                     <button
                        onClick={() => setShowMoreOptions((prev) => !prev)}
                        className="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 inline-flex items-center justify-center w-12 h-[42px] rounded-lg transition-colors"
                        aria-label="More options"
                     >
                        <svg
                           className="h-5 w-5"
                           viewBox="0 0 24 24"
                           fill="none"
                           xmlns="http://www.w3.org/2000/svg"
                        >
                           <path
                              d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
                              fill="currentColor"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                           />
                           <path
                              d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z"
                              fill="currentColor"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                           />
                           <path
                              d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z"
                              fill="currentColor"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                           />
                        </svg>
                     </button>
                     {showMoreOptions && (
                        <div
                           ref={moreOptionsRef}
                           className="absolute right-0 mt-2 w-52 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-700 py-1 z-10"
                        >
                           {/* Add Transaction - Mobile Only */}
                           <button
                              onClick={() => {
                                 setActiveModals((prev) => ({
                                    ...prev,
                                    transaction: true,
                                 }));
                                 setShowMoreOptions(false);
                              }}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 gap-2"
                           >
                              <PlusIcon className="h-4 w-4" />
                              Add Transaction
                           </button>

                           {/* Add the hidden file input */}
                           <input
                              type="file"
                              id="csvUpload"
                              ref={fileInputRef}
                              className="hidden"
                              accept=".csv"
                              onChange={handleCSVUpload}
                           />
                           <label
                              htmlFor="csvUpload"
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer gap-2"
                           >
                              <svg
                                 className="h-4 w-4"
                                 viewBox="0 0 24 24"
                                 fill="none"
                                 xmlns="http://www.w3.org/2000/svg"
                              >
                                 <path
                                    d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 8L12 4M12 4L8 8M12 4L12 16"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                 />
                              </svg>
                              Import CSV
                           </label>
                           <button
                              onClick={handleCSVExport}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 gap-2"
                           >
                              <svg
                                 className="h-4 w-4"
                                 viewBox="0 0 24 24"
                                 fill="none"
                                 xmlns="http://www.w3.org/2000/svg"
                              >
                                 <path
                                    d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 8L12 4M12 4L8 8M12 4L12 16"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                 />
                              </svg>
                              Export to CSV
                           </button>
                           <button
                              onClick={downloadTemplate}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 gap-2"
                           >
                              <svg
                                 className="h-4 w-4"
                                 viewBox="0 0 24 24"
                                 fill="none"
                                 xmlns="http://www.w3.org/2000/svg"
                              >
                                 <path
                                    d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 8L12 4M12 4L8 8M12 4L12 16"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                 />
                              </svg>
                              Export to CSV
                           </button>
                        </div>
                     )}
                  </div>
               </div>

               {/* Desktop Layout - Single Row */}
               <div className="hidden sm:flex flex-row gap-3 w-full items-center justify-between align-middle">
                  {/* Account Selector and Sync Button */}
                  <div className="flex items-center space-x-2 flex-nowrap">
                     {renderAccountFilter()}
                     {selectedAccountFilter !== "all" &&
                        accounts.find(
                           (acc) => acc._id === Number(selectedAccountFilter)
                        )?.plaidItemId && (
                           <div className="ml-2 flex flex-col items-start text-sm text-gray-500 dark:text-gray-400">
                              {/* Plaid Connection Status Badge */}
                              {selectedAccountFilter !== "all" && (
                                 <div className="mb-1">
                                    <PlaidConnectionStatus
                                       account={accounts.find(
                                          (acc) =>
                                             acc._id ===
                                             Number(selectedAccountFilter)
                                       )}
                                       inline={true}
                                       onStatusUpdate={
                                          handleConnectionStatusUpdate
                                       }
                                    />
                                 </div>
                              )}
                              {lastSyncTime ? (
                                 <div className="flex items-center text-xs">
                                    <svg
                                       className="h-3 w-3 mr-1"
                                       xmlns="http://www.w3.org/2000/svg"
                                       fill="none"
                                       viewBox="0 0 24 24"
                                       stroke="currentColor"
                                    >
                                       <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                       />
                                    </svg>
                                    <span>
                                       Last updated:{" "}
                                       {new Date(lastSyncTime).toLocaleString()}
                                    </span>
                                 </div>
                              ) : (
                                 <span className="text-xs">Never synced</span>
                              )}
                           </div>
                        )}
                  </div>

                  {/* Search and Action Buttons */}
                  <div className="flex flex-row items-center space-x-3">
                     {/* Search Bar - Desktop */}
                     <div className="hidden sm:block relative w-64">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                           {isSearching ? (
                              <svg
                                 className="animate-spin h-5 w-5 text-gray-400"
                                 xmlns="http://www.w3.org/2000/svg"
                                 fill="none"
                                 viewBox="0 0 24 24"
                              >
                                 <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                 ></circle>
                                 <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                 ></path>
                              </svg>
                           ) : (
                              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                           )}
                        </div>
                        <Input
                           type="text"
                           placeholder="Search transactions..."
                           value={searchQuery}
                           onChange={(e) => setSearchQuery(e.target.value)}
                           className="!pl-10 pr-3 py-2 w-full h-[42px] bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 rounded-lg"
                        />
                        {searchQuery && (
                           <button
                              onClick={() => resetToInitialState(true)}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                           >
                              <svg
                                 className="h-5 w-5 text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
                                 fill="none"
                                 viewBox="0 0 24 24"
                                 stroke="currentColor"
                              >
                                 <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                 />
                              </svg>
                           </button>
                        )}
                     </div>

                     {/* Unassigned Filter Toggle - Desktop */}
                     <button
                        onClick={() =>
                           setFilters((prev) => ({
                              ...prev,
                              assignedTo:
                                 prev.assignedTo === "unassigned"
                                    ? "all"
                                    : "unassigned",
                           }))
                        }
                        disabled={unassignedCount === 0}
                        className={`hidden sm:flex px-3 py-2 rounded-lg text-sm font-medium border transition-colors h-[42px] items-center gap-2 ${
                           filters.assignedTo === "unassigned"
                              ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                              : unassignedCount === 0
                              ? "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600 cursor-not-allowed"
                              : "bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"
                        }`}
                     >
                        <svg
                           className="h-4 w-4"
                           viewBox="0 0 20 20"
                           fill="currentColor"
                        >
                           <path
                              fillRule="evenodd"
                              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clipRule="evenodd"
                           />
                        </svg>
                        Unassigned ({unassignedCount})
                     </button>

                     {/* Add Transaction Button - Hidden on mobile */}
                     <button
                        onClick={() =>
                           setActiveModals((prev) => ({
                              ...prev,
                              transaction: true,
                           }))
                        }
                        className="hidden sm:flex px-2.5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg items-center justify-center h-[42px] w-[42px] text-sm font-medium"
                     >
                        <PlusIcon className="h-5 w-5" />
                     </button>

                     {/* More Options Dropdown */}
                     <div className="relative">
                        <button
                           onClick={() => setShowMoreOptions((prev) => !prev)}
                           className="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 inline-flex items-center justify-center w-9 h-[42px] rounded-lg transition-colors"
                           aria-label="More options"
                        >
                           <svg
                              className="h-5 w-5"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                           >
                              <path
                                 d="M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z"
                                 fill="currentColor"
                                 stroke="currentColor"
                                 strokeWidth="2"
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                              />
                              <path
                                 d="M19 13C19.5523 13 20 12.5523 20 12C20 11.4477 19.5523 11 19 11C18.4477 11 18 11.4477 18 12C18 12.5523 18.4477 13 19 13Z"
                                 fill="currentColor"
                                 stroke="currentColor"
                                 strokeWidth="2"
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                              />
                              <path
                                 d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z"
                                 fill="currentColor"
                                 stroke="currentColor"
                                 strokeWidth="2"
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                              />
                           </svg>
                        </button>
                        {showMoreOptions && (
                           <div
                              ref={moreOptionsRef}
                              className="absolute right-0 mt-2 w-52 sm:w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-700 py-1 z-10"
                           >
                              {/* Add Transaction - Mobile Only */}
                              <button
                                 onClick={() => {
                                    setActiveModals((prev) => ({
                                       ...prev,
                                       transaction: true,
                                    }));
                                    setShowMoreOptions(false);
                                 }}
                                 className="sm:hidden flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 gap-2"
                              >
                                 <PlusIcon className="h-4 w-4" />
                                 Add Transaction
                              </button>

                              {/* Add the hidden file input */}
                              <input
                                 type="file"
                                 id="csvUpload"
                                 ref={fileInputRef}
                                 className="hidden"
                                 accept=".csv"
                                 onChange={handleCSVUpload}
                              />
                              <label
                                 htmlFor="csvUpload"
                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer gap-2"
                              >
                                 <svg
                                    className="h-4 w-4"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                 >
                                    <path
                                       d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 8L12 4M12 4L8 8M12 4L12 16"
                                       stroke="currentColor"
                                       strokeWidth="2"
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                    />
                                 </svg>
                                 Import CSV
                              </label>
                              <button
                                 onClick={handleCSVExport}
                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 gap-2"
                              >
                                 <svg
                                    className="h-4 w-4"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                 >
                                    <path
                                       d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 8L12 4M12 4L8 8M12 4L12 16"
                                       stroke="currentColor"
                                       strokeWidth="2"
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                    />
                                 </svg>
                                 Export to CSV
                              </button>
                              <button
                                 onClick={downloadTemplate}
                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 gap-2"
                              >
                                 <svg
                                    className="h-4 w-4"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                 >
                                    <path
                                       d="M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M8 8L12 12M12 12L16 8M12 12L12 4"
                                       stroke="currentColor"
                                       strokeWidth="2"
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                    />
                                 </svg>
                                 Download Template
                              </button>
                           </div>
                        )}
                     </div>
                  </div>
               </div>
            </div>

            {/* Error Message */}
            {error && (
               <div className="mt-4 rounded-md bg-red-50 dark:bg-red-900/50 p-4">
                  <div className="text-sm text-red-600 dark:text-red-200">
                     {error}
                  </div>
               </div>
            )}

            {/* Lists Container - Full width */}
            <div className="flex-1 min-h-0 hidden sm:block">
               {/* Desktop List */}
               <div className="h-full">
                  <MemoizedTransactionList
                     transactions={filteredTransactions}
                     availableAssignedTo={availableAssignedTo}
                     accounts={accounts}
                     onAssignedToChange={handleAssignedToChange}
                     onStatusChange={handleStatusChange}
                     onAccountChange={handleAccountChange}
                     onDelete={handleDelete}
                     onDateChange={(transactionId, field, value) =>
                        handleFieldUpdate(transactionId, field, value)
                     }
                     onPayeeChange={(transactionId, field, value) =>
                        handleFieldUpdate(transactionId, field, value)
                     }
                     onAmountChange={(transactionId, field, value) =>
                        handleFieldUpdate(transactionId, field, value)
                     }
                     onAddIncome={handleIncomeSuccess}
                     onAddExpense={handleExpenseSuccess}
                     onCategoryCreated={refreshCategories}
                     TRANSACTION_STATUS={TRANSACTION_STATUS}
                     currentCategoryCellRef={currentCategoryCellRef}
                     selectedAccountFilter={selectedAccountFilter}
                     loading={loading}
                     searchLoading={isSearching}
                     editingTransaction={editingTransaction}
                     onCategoryClick={handleCategoryClick}
                     pagination={pagination}
                     onPageChange={handlePageChange}
                     transactionAssignedToOptions={transactionAssignedToOptions}
                  />
               </div>
            </div>

            {/* Mobile Lists Container - No border */}
            <div className="flex-1 min-h-0 block sm:hidden">
               <MemoizedMobileTransactionList
                  transactions={filteredTransactions}
                  availableAssignedTo={availableAssignedTo}
                  accounts={accounts}
                  onAssignedToChange={handleAssignedToChange}
                  onStatusChange={handleStatusChange}
                  onAccountChange={handleAccountChange}
                  onDelete={handleDelete}
                  onDateChange={(transactionId, field, value) =>
                     handleFieldUpdate(transactionId, field, value)
                  }
                  onPayeeChange={(transactionId, field, value) =>
                     handleFieldUpdate(transactionId, field, value)
                  }
                  onAmountChange={(transactionId, field, value) =>
                     handleFieldUpdate(transactionId, field, value)
                  }
                  onAddIncome={handleIncomeSuccess}
                  onAddExpense={handleExpenseSuccess}
                  onCategoryCreated={refreshCategories}
                  TRANSACTION_STATUS={TRANSACTION_STATUS}
                  currentCategoryCellRef={currentCategoryCellRef}
                  selectedAccountFilter={selectedAccountFilter}
                  loading={loading}
                  searchLoading={isSearching}
                  editingTransaction={editingTransaction}
                  onCategoryClick={handleCategoryClick}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  transactionAssignedToOptions={transactionAssignedToOptions}
               />
            </div>

            {/* Transaction Form Modal */}
            {activeModals.transaction && (
               <TransactionForm
                  isOpen={activeModals.transaction}
                  onSuccess={handleTransactionAdded}
                  onClose={() =>
                     setActiveModals((prev) => ({
                        ...prev,
                        transaction: false,
                     }))
                  }
               />
            )}

            {/* Add Expense Form Modal */}
            {activeModals.expense && (
               <Modal
                  isOpen={activeModals.expense}
                  onClose={() =>
                     setActiveModals((prev) => ({ ...prev, expense: false }))
                  }
                  title="Add New Expense"
               >
                  <ExpenseForm
                     onSuccess={handleExpenseSuccess}
                     onCancel={() =>
                        setActiveModals((prev) => ({ ...prev, expense: false }))
                     }
                     defaultDate={editingTransaction?.date}
                  />
               </Modal>
            )}

            {/* Add Income Form Modal */}
            {activeModals.income && (
               <Modal
                  isOpen={activeModals.income}
                  onClose={() =>
                     setActiveModals((prev) => ({ ...prev, income: false }))
                  }
                  title="Add New Income"
               >
                  <AddIncomeModal
                     onClose={() =>
                        setActiveModals((prev) => ({ ...prev, income: false }))
                     }
                     onSave={handleIncomeSuccess}
                     defaultDate={editingTransaction?.date}
                  />
               </Modal>
            )}

            {/* Category Edit Dropdown */}
            {showCategoryModal &&
               editingTransaction &&
               createPortal(
                  <div
                     className="fixed z-[9999] rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5"
                     style={{
                        top: dropdownPosition.top,
                        left: dropdownPosition.left,
                        width: "320px", // w-80 = 20rem = 320px
                     }}
                     ref={categoryDropdownRef}
                  >
                     <Combobox
                        options={[
                           ...(editingTransaction?.assignedTo &&
                           editingTransaction.assignedTo !== "Unassigned"
                              ? [
                                   availableAssignedTo.find(
                                      (c) =>
                                         c.description ===
                                         editingTransaction.assignedTo
                                   ),
                                ]
                                   .filter(Boolean)
                                   .map((category) => ({
                                      value: category._id,
                                      label: category.description,
                                      assignedToType: category.expectedAmount
                                         ? "Income"
                                         : "Expense",
                                      description: category.expectedAmount
                                         ? new Date(
                                              category.date
                                           ).toLocaleDateString(undefined, {
                                              month: "numeric",
                                              day: "numeric",
                                           })
                                         : category.weeklyChargeType ===
                                           "spread"
                                         ? `${new Date(
                                              category.startDate
                                           ).toLocaleDateString(undefined, {
                                              month: "numeric",
                                              day: "numeric",
                                           })} - ${new Date(
                                              category.endDate
                                           ).toLocaleDateString(undefined, {
                                              month: "numeric",
                                              day: "numeric",
                                           })}`
                                         : new Date(
                                              category.date
                                           ).toLocaleDateString(undefined, {
                                              month: "numeric",
                                              day: "numeric",
                                           }),
                                   }))
                              : []),
                           { value: "", label: "Unassigned" },
                           ...availableAssignedTo
                              .filter(
                                 (c) =>
                                    c.description !==
                                    editingTransaction?.assignedTo
                              )
                              .map((category) => ({
                                 value: category._id,
                                 label: category.description,
                                 assignedToType: category.expectedAmount
                                    ? "Income"
                                    : "Expense",
                                 description: category.expectedAmount
                                    ? new Date(
                                         category.date
                                      ).toLocaleDateString(undefined, {
                                         month: "numeric",
                                         day: "numeric",
                                      })
                                    : category.weeklyChargeType === "spread"
                                    ? `${new Date(
                                         category.startDate
                                      ).toLocaleDateString(undefined, {
                                         month: "numeric",
                                         day: "numeric",
                                      })} - ${new Date(
                                         category.endDate
                                      ).toLocaleDateString(undefined, {
                                         month: "numeric",
                                         day: "numeric",
                                      })}`
                                    : new Date(
                                         category.date
                                      ).toLocaleDateString(undefined, {
                                         month: "numeric",
                                         day: "numeric",
                                      }),
                              })),
                        ]}
                        value={
                           editingTransaction?.assignedTo
                              ? {
                                   value:
                                      availableAssignedTo.find(
                                         (c) =>
                                            c.description ===
                                            editingTransaction.assignedTo
                                      )?._id || "",
                                   label: editingTransaction.assignedTo,
                                }
                              : { value: "", label: "Unassigned" }
                        }
                        onChange={(option) =>
                           handleAssignedToChange(
                              editingTransaction._id,
                              option.value,
                              option.assignedToType
                           )
                        }
                        placeholder="Search categories..."
                        autoFocus={true}
                        onEscape={() => {
                           setShowCategoryModal(false);
                           setEditingTransaction(null);
                           // Focus the category cell
                           requestAnimationFrame(() => {
                              currentCategoryCellRef.current?.focus();
                           });
                        }}
                        position={dropdownPosition.isAbove ? "top" : "bottom"}
                        noResultsAction={
                           editingTransaction?.type === "Income"
                              ? {
                                   label: "Add New Income",
                                   onClick: () => {
                                      setActiveModals((prev) => ({
                                         ...prev,
                                         income: true,
                                      }));
                                      setShowCategoryModal(false);
                                   },
                                }
                              : {
                                   label: "Add New Expense",
                                   onClick: () => {
                                      setActiveModals((prev) => ({
                                         ...prev,
                                         expense: true,
                                      }));
                                      setShowCategoryModal(false);
                                   },
                                }
                        }
                     />
                  </div>,
                  document.body
               )}

            {/* Status Dropdown Portal */}
            {showStatusDropdown &&
               createPortal(
                  <div
                     className="fixed rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 overflow-hidden"
                     style={{
                        zIndex: 9999,
                        top: dropdownPosition.top,
                        left: dropdownPosition.left,
                     }}
                     ref={statusDropdownRef}
                  >
                     <div className="py-1">
                        {TRANSACTION_STATUS.map((status) => (
                           <button
                              key={status}
                              onClick={() =>
                                 handleStatusChange(showStatusDropdown, status)
                              }
                              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                                 transactions.find(
                                    (t) => t._id === showStatusDropdown
                                 )?.status === status
                                    ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                    : "text-gray-700 dark:text-gray-300"
                              }`}
                           >
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                           </button>
                        ))}
                     </div>
                  </div>,
                  document.body
               )}

            {/* Duplicate Review Modal */}
            {activeModals.duplicates && (
               <Modal
                  isOpen={activeModals.duplicates}
                  title="Review Duplicate Transactions"
                  onClose={() =>
                     setActiveModals((prev) => ({ ...prev, duplicates: false }))
                  }
               >
                  <div className="space-y-6">
                     <div className="flex items-center justify-between">
                        <p className="text-base text-gray-500 dark:text-gray-400">
                           The following transactions appear to be duplicates.
                           Please review each one.
                           {duplicateTransactions?.length > 0 &&
                              ` Found ${
                                 duplicateTransactions.length
                              } potential duplicate${
                                 duplicateTransactions.length === 1 ? "" : "s"
                              }.`}
                        </p>
                     </div>

                     <div className="max-h-[70vh] overflow-y-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                           <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
                              <tr>
                                 <th
                                    scope="col"
                                    className="px-8 py-4 text-left text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider w-[180px]"
                                 >
                                    Date
                                 </th>
                                 <th
                                    scope="col"
                                    className="px-8 py-4 text-left text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                                 >
                                    Description
                                 </th>
                                 <th
                                    scope="col"
                                    className="px-8 py-4 text-left text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider w-[180px]"
                                 >
                                    Amount
                                 </th>
                                 <th
                                    scope="col"
                                    className="px-8 py-4 text-left text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider w-[120px]"
                                 >
                                    Status
                                 </th>
                                 <th
                                    scope="col"
                                    className="px-8 py-4 text-left text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider w-[200px]"
                                 >
                                    Action
                                 </th>
                              </tr>
                           </thead>
                           <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                              {duplicateTransactions.map(
                                 (transaction, index) => (
                                    <tr
                                       key={index}
                                       className={
                                          transaction.approved
                                             ? "bg-green-50 dark:bg-green-900/20"
                                             : "bg-red-50 dark:bg-red-900/20"
                                       }
                                    >
                                       <td className="px-8 py-4 text-sm text-gray-900 dark:text-gray-100">
                                          {new Date(
                                             transaction.date
                                          ).toLocaleDateString()}
                                       </td>
                                       <td className="px-8 py-4 text-sm text-gray-900 dark:text-gray-100">
                                          {transaction.payee}
                                       </td>
                                       <td className="px-8 py-4 text-sm text-gray-900 dark:text-gray-100">
                                          {transaction.amount.toLocaleString(
                                             "en-US",
                                             {
                                                style: "currency",
                                                currency: "USD",
                                             }
                                          )}
                                       </td>
                                       <td className="px-8 py-4 text-sm text-gray-900 dark:text-gray-100 capitalize">
                                          {transaction.status}
                                       </td>
                                       <td className="px-8 py-4">
                                          <div className="flex space-x-3">
                                             <button
                                                onClick={() =>
                                                   handleDuplicateApproval(
                                                      index,
                                                      true
                                                   )
                                                }
                                                className={`px-5 py-2 rounded-md text-sm font-medium transition-all ${
                                                   transaction.approved
                                                      ? "bg-green-600 text-white"
                                                      : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200 hover:bg-green-500 hover:text-white"
                                                }`}
                                             >
                                                Import
                                             </button>
                                             <button
                                                onClick={() =>
                                                   handleDuplicateApproval(
                                                      index,
                                                      false
                                                   )
                                                }
                                                className={`px-5 py-2 rounded-md text-sm font-medium transition-all ${
                                                   !transaction.approved
                                                      ? "bg-red-600 text-white"
                                                      : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200 hover:bg-red-500 hover:text-white"
                                                }`}
                                             >
                                                Skip
                                             </button>
                                          </div>
                                       </td>
                                    </tr>
                                 )
                              )}
                           </tbody>
                        </table>
                     </div>

                     <div className="flex justify-between items-center pt-6">
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                           {
                              duplicateTransactions.filter((t) => t.approved)
                                 .length
                           }{" "}
                           selected for import
                        </div>
                        <div className="flex space-x-4">
                           <button
                              onClick={() =>
                                 setActiveModals((prev) => ({
                                    ...prev,
                                    duplicates: false,
                                 }))
                              }
                              className="px-6 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors"
                           >
                              Cancel
                           </button>
                           <button
                              onClick={handleDuplicateReviewComplete}
                              className="px-6 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                           >
                              Complete Import
                           </button>
                        </div>
                     </div>
                  </div>
               </Modal>
            )}

            {/* Account Dropdown Portal */}
            {showAccountDropdown &&
               createPortal(
                  <div
                     className="fixed rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 overflow-hidden"
                     style={{
                        zIndex: 9999,
                        top: dropdownPosition.top,
                        left: dropdownPosition.left,
                     }}
                     ref={accountDropdownRef}
                  >
                     <div className="py-1">
                        <button
                           onClick={() => handleAccountDropdownClick(null)}
                           className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                              !showAccountDropdown
                                 ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                 : "text-gray-700 dark:text-gray-300"
                           }`}
                        >
                           Unassigned
                        </button>
                        {accounts
                           .filter(
                              (account) =>
                                 isCashAccount(account) ||
                                 account.accountType === "credit"
                           )
                           .map((account) => (
                              <button
                                 key={account._id}
                                 onClick={() =>
                                    handleAccountDropdownClick(account._id)
                                 }
                                 className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                                    (
                                       showAccountDropdown
                                          ? showAccountDropdown === account._id
                                          : Number(selectedAccountFilter) ===
                                            account._id
                                    )
                                       ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                       : "text-gray-700 dark:text-gray-300"
                                 }`}
                              >
                                 <div className="flex justify-between items-center">
                                    <span>{account.name}</span>
                                    <span
                                       className={`font-mono ${
                                          account.balance >= 0
                                             ? "text-green-600 dark:text-green-400"
                                             : "text-red-600 dark:text-red-400"
                                       }`}
                                    >
                                       $
                                       {account.balance.toLocaleString(
                                          "en-US",
                                          {
                                             minimumFractionDigits: 2,
                                             maximumFractionDigits: 2,
                                          }
                                       )}
                                    </span>
                                 </div>
                              </button>
                           ))}
                     </div>
                  </div>,
                  document.body
               )}
         </div>

         {duplicateTransactions && (
            <DuplicateTransactionsModal
               duplicates={duplicateTransactions}
               onClose={() => setDuplicateTransactions(null)}
               onResolve={handleDuplicateResolution}
            />
         )}
      </div>
   );
}

"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

export default function OnboardingLayout({ children }) {
   const { data: session, status } = useSession();
   const router = useRouter();

   useEffect(() => {
      // Redirect if not authenticated
      if (status !== "loading" && !session) {
         router.push("/auth/login");
      }
   }, [status, session, router]);

   if (status === "loading") {
      return (
         <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
            <div className="text-center">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto"></div>
               <p className="mt-4 text-gray-600 dark:text-gray-300">
                  Loading...
               </p>
            </div>
         </div>
      );
   }

   // This is a custom layout that doesn't include the sidebar
   return (
      <div className="min-h-screen h-screen bg-gray-50 dark:bg-gray-900">
         <main className="flex-1 overflow-auto">{children}</main>
      </div>
   );
}

"use client";

import { OnboardingWizard } from "@/app/components/onboarding/OnboardingWizard";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { use } from "react";

export default function OnboardingPage({ params }) {
   const { data: session, status } = useSession();
   const router = useRouter();
   const searchParams = useSearchParams();
   const { userId } = use(params);
   const [loading, setLoading] = useState(true);

   useEffect(() => {
      const handleOnboardingPageLogic = async () => {
         // Redirect if not authenticated
         if (status !== "loading" && !session?.user) {
            router.push("/auth/login");
            return;
         }

         // Redirect if trying to access someone else's onboarding
         if (session?.user?.id && session.user.id !== userId) {
            router.push("/budget");
            return;
         }

         // Check if onboarding is already complete
         if (status !== "loading" && session?.user?.id) {
            // First check session data for onboarding status
            if (session.user?.onboardingComplete !== undefined) {
               console.log(
                  "Onboarding page: Using session data for onboarding status:",
                  session.user.onboardingComplete
               );

               if (session.user.onboardingComplete) {
                  console.log(
                     "Onboarding page: User onboarding complete, redirecting to budget"
                  );
                  router.push("/budget");
                  return;
               } else {
                  // Check if they came from subscription selection
                  const fromSubscription =
                     searchParams.get("from") === "subscription";
                  if (!fromSubscription) {
                     console.log(
                        "Onboarding page: User didn't come from subscription, redirecting to subscription"
                     );
                     router.push(`/subscription/${userId}`);
                     return;
                  }
                  // User needs onboarding and came from subscription - continue to onboarding
               }
            } else {
               // Fallback to API call if session doesn't have onboarding status
               try {
                  console.log(
                     "Onboarding page: Session missing onboarding status, checking via API for user:",
                     session.user.id
                  );

                  // Add timeout to prevent hanging
                  const controller = new AbortController();
                  const timeoutId = setTimeout(() => {
                     console.warn(
                        "Onboarding page: API call timing out after 3 seconds"
                     );
                     controller.abort();
                  }, 3000); // Reduced timeout to 3 seconds

                  const response = await fetch("/api/user", {
                     signal: controller.signal,
                     headers: {
                        "Cache-Control": "no-cache",
                     },
                  });

                  clearTimeout(timeoutId);

                  if (response.ok) {
                     const userData = await response.json();
                     console.log("Onboarding page: User data received:", {
                        userId: userData.id,
                        onboardingComplete: userData.onboardingComplete,
                     });

                     if (userData.onboardingComplete) {
                        console.log(
                           "Onboarding page: User onboarding complete, redirecting to budget"
                        );
                        router.push("/budget");
                        return;
                     }
                  } else if (response.status === 404) {
                     // User not found in MongoDB yet - this is expected for new users
                     console.log(
                        "Onboarding page: User not found in MongoDB yet, continuing with onboarding"
                     );
                  } else {
                     console.warn(
                        "Onboarding page: API call failed:",
                        response.status,
                        response.statusText
                     );
                  }

                  // Check if they came from subscription selection
                  const fromSubscription =
                     searchParams.get("from") === "subscription";

                  if (!fromSubscription) {
                     console.log(
                        "Onboarding page: User didn't come from subscription, redirecting to subscription"
                     );
                     // They didn't come from subscription selection, redirect them there
                     router.push(`/subscription/${userId}`);
                     return;
                  }
               } catch (error) {
                  if (error.name === "AbortError") {
                     console.warn(
                        "Onboarding page: API call timed out, continuing with onboarding"
                     );
                  } else {
                     console.error(
                        "Onboarding page: Error checking onboarding status:",
                        error
                     );
                  }

                  // Check if they came from subscription selection
                  const fromSubscription =
                     searchParams.get("from") === "subscription";

                  if (!fromSubscription) {
                     console.log(
                        "Onboarding page: Error occurred and user didn't come from subscription, redirecting to subscription"
                     );
                     // If there's an error and they didn't come from subscription selection, redirect them there for safety
                     router.push(`/subscription/${userId}`);
                     return;
                  }
               }
            }
         }

         setLoading(false);
      };

      handleOnboardingPageLogic();
   }, [session, status, userId, router, searchParams]);

   if (loading || status === "loading") {
      return (
         <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
            <div className="text-center">
               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto"></div>
               <p className="mt-4 text-gray-600 dark:text-gray-300">
                  Loading...
               </p>
            </div>
         </div>
      );
   }

   if (!session?.user) {
      return null;
   }

   if (session.user.id !== userId) {
      return null;
   }

   return <OnboardingWizard userId={userId} />;
}

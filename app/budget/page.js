"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import {
   format,
   startOfDay,
   endOfDay,
   isSameDay,
   isSameMonth,
   isBefore,
   startOfMonth,
   parseISO,
} from "date-fns";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { calculatePayPeriodBalances } from "../lib/utils/payPeriodUtils";
import { combineAndSortExpenses } from "../lib/utils/expenseUtils";
import { combineAndSortIncomes } from "../lib/utils/incomeUtils";
import {
   getExtendedDateRange,
   fetchPeriodData,
   refreshBalances as refreshBudgetBalances,
} from "../lib/utils/budgetUtils";
import BudgetIncomeSection from "../components/budget/BudgetIncomeSection";
import BudgetExpenseSection from "../components/budget/BudgetExpenseSection";
import BudgetDebtSection from "../components/budget/BudgetDebtSection";
import BudgetHeader from "../components/budget/BudgetHeader";
import useBalanceStore from "../lib/stores/balanceStore";

export default function BudgetPage() {
   const { data: session, status } = useSession();
   const router = useRouter();

   // Get balances from Zustand store
   const {
      balances,
      isLoadingBalances,
      setBalances,
      setLoadingBalances,
      refreshBalances,
   } = useBalanceStore();

   const [expenses, setExpenses] = useState([]);
   const [incomes, setIncomes] = useState([]);
   const [debts, setDebts] = useState([]);
   const [loading, setLoading] = useState(true);
   const [error, setError] = useState("");
   const [dateRange, setDateRange] = useState(null);
   const [viewType, setViewType] = useState("month");
   const [currentDate, setCurrentDate] = useState(new Date());
   const [userPaySettings, setUserPaySettings] = useState(null);
   const [userCreatedAt, setUserCreatedAt] = useState(null);
   const [isInitialLoad, setIsInitialLoad] = useState(true);
   const [refreshKey, setRefreshKey] = useState(0);
   const [hasInitialBalanceLoad, setHasInitialBalanceLoad] = useState(false);
   const [payPeriods, setPayPeriods] = useState([]);
   const [projectedExpenses, setProjectedExpenses] = useState([]);
   const [projectedIncomes, setProjectedIncomes] = useState([]);
   const [accounts, setAccounts] = useState([]);
   const [userData, setUserData] = useState(null);
   const [dividerPosition, setDividerPosition] = useState(() => {
      if (typeof window !== "undefined") {
         const savedPosition = localStorage.getItem("budget-divider-position");
         return savedPosition ? parseFloat(savedPosition) : 35;
      }
      return 35; // Default to 35% if localStorage not available
   });
   const isDraggingRef = useRef(false);
   const [isDragging, setIsDragging] = useState(false);
   const initialDataFetchedRef = useRef(false); // Add a ref to track initial data fetch

   // Create stable refs for event handlers to fix cleanup issues
   const mouseMoveHandlerRef = useRef(null);
   const touchMoveHandlerRef = useRef(null);

   // Add state to track if we're in mobile view
   const [isMobileView, setIsMobileView] = useState(false);

   // Mobile collapsible section states

   // Redirect to auth if not authenticated
   useEffect(() => {
      if (status !== "loading" && !session) {
         router.push("/auth/login");
      }
   }, [status, session, router]);

   // Effect to detect mobile screen size
   useEffect(() => {
      const checkMobileView = () => {
         setIsMobileView(window.innerWidth < 768); // Standard md breakpoint
      };

      // Check on mount
      checkMobileView();

      // Add resize listener
      window.addEventListener("resize", checkMobileView);

      // Cleanup
      return () => window.removeEventListener("resize", checkMobileView);
   }, []);

   // Add fetchAccounts function
   const fetchAccounts = useCallback(async () => {
      try {
         const response = await fetch("/api/user/accounts");
         if (!response.ok) throw new Error("Failed to fetch accounts");
         const data = await response.json();
         setAccounts(data.accounts || []);
         return data.accounts || [];
      } catch (error) {
         console.error("Error fetching accounts:", error);
         setError("Failed to load accounts");
         return [];
      }
   }, []);

   // Define fetchDebts here before it's used in handleRefreshBalances
   const fetchDebts = useCallback(async () => {
      try {
         const response = await fetch("/api/user/debts");
         if (response.ok) {
            const data = await response.json();
            setDebts(data.debts || []);
         }
      } catch (error) {
         console.error("Error fetching debts:", error);
      }
   }, []);

   // Define handleRefreshBalances before it's used in the useEffect
   const handleRefreshBalances = useCallback(async () => {
      if (!session?.user?.id) return;
      try {
         await refreshBalances();
         // Refresh debts data
         await fetchDebts();
      } catch (error) {
         console.error("Error refreshing balances:", error);
      }
   }, [session?.user, fetchDebts, refreshBalances]);

   // Combined function to fetch all initial data
   const fetchInitialData = useCallback(async () => {
      if (!session?.user?.id || initialDataFetchedRef.current) return;

      try {
         setLoading(true);
         setLoadingBalances(true);

         // Calculate initial date range
         const today = new Date();
         // Use end of current month for proper month view
         const initialDateRange = {
            start: startOfMonth(today),
            end: endOfDay(
               new Date(today.getFullYear(), today.getMonth() + 1, 0)
            ), // Last day of current month
            viewType: "month",
         };

         // Set date range early so projected expenses can be generated
         setDateRange(initialDateRange);

         // Fetch user data, accounts, and debts in parallel
         const [userResponse, accountsData, debtsResponse] = await Promise.all([
            fetch("/api/user"),
            fetchAccounts(),
            fetch("/api/user/debts"),
         ]);

         let userInfo = {
            recurringExpenses: [],
            recurringIncomes: [],
            preferences: { showFutureExpenses: true },
            totalIncome: 0,
            totalAssigned: 0,
            createdAt: new Date(),
         };

         let debtsData = { debts: [] };

         if (userResponse.ok) {
            userInfo = await userResponse.json();
         } else {
            console.warn(
               "Failed to fetch user data, using defaults:",
               userResponse.status
            );
         }

         if (debtsResponse.ok) {
            debtsData = await debtsResponse.json();
         } else {
            console.warn(
               "Failed to fetch debts data, using empty array:",
               debtsResponse.status
            );
         }

         // Get pay period settings from main recurring income
         let paySettings = {
            payPeriod: "monthly",
            payDay: "1",
            payDayOfWeek: "1",
            payAmount: 0,
            recurringExpenses: userInfo.recurringExpenses || [],
            recurringIncomes: userInfo.recurringIncomes || [],
            preferences: userInfo.preferences || { showFutureExpenses: true },
            viewType: "month", // Add default view type
         };

         // If user has a main income, use its pay period settings
         if (
            userInfo.mainIncomeId &&
            userInfo.recurringIncomes &&
            userInfo.recurringIncomes.length > 0
         ) {
            const mainIncome = userInfo.recurringIncomes.find(
               (income) =>
                  income._id === userInfo.mainIncomeId ||
                  income._id.toString() === userInfo.mainIncomeId.toString()
            );
            if (mainIncome) {
               paySettings = {
                  ...paySettings,
                  payPeriod: mainIncome.payPeriod,
                  payDay: mainIncome.payDay,
                  payDayOfWeek: mainIncome.payDayOfWeek,
                  payAmount: mainIncome.payAmount,
               };
            }
         }

         // Set user pay settings early so projected expenses can be generated
         setUserPaySettings(paySettings);

         // Get extended date range based on pay settings
         const extendedRange = getExtendedDateRange(
            initialDateRange,
            paySettings
         );

         // Fetch expenses and incomes for extended range
         const params = new URLSearchParams({
            start: format(extendedRange.start, "yyyy-MM-dd"),
            end: format(extendedRange.end, "yyyy-MM-dd"),
         });

         const [expensesResponse, incomesResponse] = await Promise.all([
            fetch(`/api/expenses?${params}`),
            fetch(`/api/incomes?${params}`),
         ]);

         // Handle empty or error responses more gracefully
         let expensesData = [];
         let incomesData = [];

         if (expensesResponse.ok) {
            expensesData = await expensesResponse.json();
         } else {
            console.warn(
               "Failed to fetch expenses, using empty array:",
               expensesResponse.status
            );
         }

         if (incomesResponse.ok) {
            incomesData = await incomesResponse.json();
         } else {
            console.warn(
               "Failed to fetch incomes, using empty array:",
               incomesResponse.status
            );
         }

         // No need to throw error for missing expenses/incomes - just use empty arrays

         // Filter raw API results to match the exact date range
         const filterRawExpenses = (rawExpenses) => {
            if (!initialDateRange?.start || !initialDateRange?.end)
               return rawExpenses;

            const rangeStartStr = format(initialDateRange.start, "yyyy-MM-dd");
            const rangeEndStr = format(initialDateRange.end, "yyyy-MM-dd");

            return rawExpenses.filter((expense) => {
               // Include if expense date is within range (string comparison)
               if (
                  expense.date &&
                  expense.date >= rangeStartStr &&
                  expense.date <= rangeEndStr
               ) {
                  return true;
               }
               // Handle spread expenses (weekly, biweekly, etc)
               if (expense.startDate && expense.endDate) {
                  return (
                     expense.startDate <= rangeEndStr &&
                     expense.endDate >= rangeStartStr
                  );
               }
               return false;
            });
         };

         const filterRawIncomes = (rawIncomes) => {
            if (!initialDateRange?.start || !initialDateRange?.end)
               return rawIncomes;

            const rangeStartStr = format(initialDateRange.start, "yyyy-MM-dd");
            const rangeEndStr = format(initialDateRange.end, "yyyy-MM-dd");

            return rawIncomes.filter((income) => {
               return (
                  income.date &&
                  income.date >= rangeStartStr &&
                  income.date <= rangeEndStr
               );
            });
         };

         // Apply filters to the data before setting state
         setExpenses(filterRawExpenses(expensesData));
         setIncomes(filterRawIncomes(incomesData));

         // Batch all state updates in a single update cycle to reduce renders
         initialDataFetchedRef.current = true;
         setUserData(userInfo);
         setDebts(debtsData.debts || []);
         setUserCreatedAt(new Date(userInfo.createdAt));
         setBalances({
            currentBalance:
               Number(userInfo.totalIncome || 0) -
               Number(userInfo.totalAssigned || 0),
            totalIncome: Number(userInfo.totalIncome || 0),
            totalAssigned: Number(userInfo.totalAssigned || 0),
            readyToAssign: Number(userInfo.readyToAssign || 0),
            accounts: accountsData || [],
         });
         setError("");
      } catch (err) {
         console.error("Error fetching initial data:", err);
         setError("Failed to load budget data");
      } finally {
         setLoading(false);
         setIsInitialLoad(false);
      }
   }, [session?.user, getExtendedDateRange, fetchAccounts]);

   // Single effect for initial data load - prevent double loading
   useEffect(() => {
      if (!initialDataFetchedRef.current) {
         fetchInitialData();
      }
   }, [fetchInitialData]);

   // Refresh balances only once on initial load
   useEffect(() => {
      if (session?.user?.id && !hasInitialBalanceLoad && !isInitialLoad) {
         handleRefreshBalances().then(() => {
            setHasInitialBalanceLoad(true);
         });
      }
   }, [
      session?.user?.id,
      hasInitialBalanceLoad,
      isInitialLoad,
      handleRefreshBalances,
   ]);

   const handleFetchPeriodData = async () => {
      try {
         // Fetch expenses and incomes for extended range
         const { expenses: expensesData = [], incomes: incomesData = [] } =
            await fetchPeriodData(dateRange, userPaySettings);

         // Filter raw API results to match the exact date range
         const filterRawExpenses = (rawExpenses) => {
            if (!dateRange?.start || !dateRange?.end) return rawExpenses;

            const rangeStartStr = format(dateRange.start, "yyyy-MM-dd");
            const rangeEndStr = format(dateRange.end, "yyyy-MM-dd");

            return rawExpenses.filter((expense) => {
               // Include if expense date is within range (string comparison)
               if (
                  expense.date &&
                  expense.date >= rangeStartStr &&
                  expense.date <= rangeEndStr
               ) {
                  return true;
               }
               // Handle spread expenses (weekly, biweekly, etc)
               if (expense.startDate && expense.endDate) {
                  return (
                     expense.startDate <= rangeEndStr &&
                     expense.endDate >= rangeStartStr
                  );
               }
               return false;
            });
         };

         const filterRawIncomes = (rawIncomes) => {
            if (!dateRange?.start || !dateRange?.end) return rawIncomes;

            const rangeStartStr = format(dateRange.start, "yyyy-MM-dd");
            const rangeEndStr = format(dateRange.end, "yyyy-MM-dd");

            return rawIncomes.filter((income) => {
               return (
                  income.date &&
                  income.date >= rangeStartStr &&
                  income.date <= rangeEndStr
               );
            });
         };

         // Apply filters to the data before setting state
         setExpenses(filterRawExpenses(expensesData));
         setIncomes(filterRawIncomes(incomesData));
         setError(""); // Clear any previous errors
      } catch (error) {
         console.error("Error fetching period data:", error);
         // Use empty arrays instead of showing error
         setExpenses([]);
         setIncomes([]);
         // Only set error for severe issues, not just missing data
         if (error.message !== "Failed to load period data") {
            setError(
               "There was a problem loading your budget data. You can continue using the app."
            );
         }
      }
   };

   // Only fetch period data when date range or settings change AND not on initial load
   useEffect(() => {
      if (
         isInitialLoad ||
         !dateRange ||
         !userPaySettings ||
         initialDataFetchedRef.current === false
      )
         return;
      handleFetchPeriodData();
   }, [dateRange, userPaySettings, isInitialLoad]);

   // Effect to calculate pay periods with projected transactions
   useEffect(() => {
      if (dateRange && userPaySettings && expenses && incomes) {
         const userId = session?.user?.id;
         let hiddenExpenseProjections = [];
         let hiddenIncomeProjections = [];

         if (userId) {
            try {
               const storedExpenses =
                  localStorage.getItem(
                     `skipped-expense-projections-${userId}`
                  ) ||
                  localStorage.getItem(`hidden-expense-projections-${userId}`);
               const storedIncomes =
                  localStorage.getItem(`skipped-projections-${userId}`) ||
                  localStorage.getItem(`hidden-projections-${userId}`);

               if (storedExpenses) {
                  hiddenExpenseProjections = JSON.parse(storedExpenses);
               }
               if (storedIncomes) {
                  hiddenIncomeProjections = JSON.parse(storedIncomes);
               }
            } catch (error) {
               console.error("Error parsing hidden projections:", error);
            }
         }

         const isProjectionHidden = (item, hiddenList) => {
            if (item.status?.toLowerCase() !== "projected") return false;

            const itemDate = format(
               item.date instanceof Date ? item.date : parseISO(item.date),
               "yyyy-MM-dd"
            );

            return hiddenList.some(
               (hidden) =>
                  hidden.description.toLowerCase() ===
                     item.description.toLowerCase() && hidden.date === itemDate
            );
         };

         const filteredProjectedExpenses = projectedExpenses.filter(
            (exp) => !isProjectionHidden(exp, hiddenExpenseProjections)
         );
         const filteredProjectedIncomes = projectedIncomes.filter(
            (inc) => !isProjectionHidden(inc, hiddenIncomeProjections)
         );

         const allExpenses = combineAndSortExpenses(
            expenses,
            filteredProjectedExpenses
         );
         const allIncomes = combineAndSortIncomes(
            incomes,
            filteredProjectedIncomes
         );

         const periods = calculatePayPeriodBalances(
            allExpenses,
            allIncomes,
            dateRange,
            userPaySettings
         );
         setPayPeriods(periods);
      }
   }, [
      dateRange,
      userPaySettings,
      expenses,
      incomes,
      projectedExpenses,
      projectedIncomes,
      session?.user?.id,
   ]);

   // Optimize handleDateRangeChange with useCallback
   const handleDateRangeChange = useCallback(
      async (newRange) => {
         if (!newRange || !newRange.start || !newRange.end) return;

         // Only update if something has actually changed
         const hasDateRangeChanged =
            !dateRange ||
            !isSameDay(dateRange.start, newRange.start) ||
            !isSameDay(dateRange.end, newRange.end) ||
            dateRange.viewType !== newRange.viewType;

         if (!hasDateRangeChanged && !newRange.forceRefresh) return;

         setDateRange(newRange);
         setViewType(newRange.viewType);
         setCurrentDate(newRange.start);
         setLoading(true);
         setError("");

         try {
            // Check if we're in the creation month and looking at dates from previous month
            const isCreationMonth =
               userCreatedAt &&
               isSameMonth(newRange.end, new Date(userCreatedAt));
            const includesPreviousMonth =
               isCreationMonth &&
               isBefore(
                  startOfMonth(newRange.start),
                  startOfMonth(newRange.end)
               );

            // Update pay settings with current view type
            const updatedPaySettings = {
               ...userPaySettings,
               viewType: newRange.viewType,
            };

            const extendedRange = getExtendedDateRange(
               newRange,
               updatedPaySettings
            );

            const params = new URLSearchParams({
               start: format(extendedRange.start, "yyyy-MM-dd"),
               end: format(extendedRange.end, "yyyy-MM-dd"),
               weeklyOnly: includesPreviousMonth ? "true" : "false",
            });

            // Fetch expenses and incomes in parallel
            const [expensesResponse, incomesResponse] = await Promise.all([
               fetch(`/api/expenses?${params}`),
               fetch(`/api/incomes?${params}`),
            ]);

            if (!expensesResponse.ok)
               throw new Error("Failed to fetch expenses");
            if (!incomesResponse.ok) throw new Error("Failed to fetch incomes");

            const [expensesData, incomesData] = await Promise.all([
               expensesResponse.json(),
               incomesResponse.json(),
            ]);

            // Filter raw API results to match the exact date range
            const filterRawExpenses = (rawExpenses) => {
               if (!newRange?.start || !newRange?.end) return rawExpenses;

               const rangeStartStr = format(newRange.start, "yyyy-MM-dd");
               const rangeEndStr = format(newRange.end, "yyyy-MM-dd");

               return rawExpenses.filter((expense) => {
                  // Include if expense date is within range (string comparison)
                  if (
                     expense.date &&
                     expense.date >= rangeStartStr &&
                     expense.date <= rangeEndStr
                  ) {
                     return true;
                  }
                  // Handle spread expenses (weekly, biweekly, etc)
                  if (expense.startDate && expense.endDate) {
                     return (
                        expense.startDate <= rangeEndStr &&
                        expense.endDate >= rangeStartStr
                     );
                  }
                  return false;
               });
            };

            const filterRawIncomes = (rawIncomes) => {
               if (!newRange?.start || !newRange?.end) return rawIncomes;

               const rangeStartStr = format(newRange.start, "yyyy-MM-dd");
               const rangeEndStr = format(newRange.end, "yyyy-MM-dd");

               return rawIncomes.filter((income) => {
                  return (
                     income.date &&
                     income.date >= rangeStartStr &&
                     income.date <= rangeEndStr
                  );
               });
            };

            // Apply filters to the data before setting state
            setExpenses(filterRawExpenses(expensesData));
            setIncomes(filterRawIncomes(incomesData));
         } catch (err) {
            setError(err.message);
            console.error("Error fetching period data:", err);
         } finally {
            setLoading(false);
         }
      },
      [dateRange, userPaySettings, userCreatedAt]
   );

   // Mouse handlers
   const handleDividerMouseDown = (e) => {
      e.preventDefault();
      isDraggingRef.current = true;
      setIsDragging(true);
      document.addEventListener("mousemove", mouseMoveHandlerRef.current);
      document.addEventListener("mouseup", handleDividerMouseUp);
   };

   const handleDividerMouseMove = useCallback((e) => {
      if (isDraggingRef.current) {
         // Get container width and calculate percentage
         const container = document.querySelector(".budget-container");
         if (container) {
            const containerRect = container.getBoundingClientRect();
            const newPosition =
               ((e.clientX - containerRect.left) / containerRect.width) * 100;

            // Limit min/max range (15% to 85%)
            const limitedPosition = Math.min(Math.max(newPosition, 15), 85);
            setDividerPosition(limitedPosition);
         }
      }
   }, []); // Empty dependency array is correct since we only access refs and set state

   const handleDividerMouseUp = useCallback(() => {
      isDraggingRef.current = false;
      setIsDragging(false);
      document.removeEventListener("mousemove", mouseMoveHandlerRef.current);
      document.removeEventListener("mouseup", handleDividerMouseUp);
   }, []); // Remove handleDividerMouseMove dependency to prevent recreation

   // Touch handlers
   const handleDividerTouchStart = (e) => {
      e.preventDefault();
      isDraggingRef.current = true;
      setIsDragging(true);
      document.addEventListener("touchmove", touchMoveHandlerRef.current, {
         passive: false,
      });
      document.addEventListener("touchend", handleDividerTouchEnd);
   };

   const handleDividerTouchMove = useCallback((e) => {
      if (isDraggingRef.current) {
         e.preventDefault(); // Prevent scrolling while dragging
         const touch = e.touches[0];
         // Get container width and calculate percentage
         const container = document.querySelector(".budget-container");
         if (container && touch) {
            const containerRect = container.getBoundingClientRect();
            const newPosition =
               ((touch.clientX - containerRect.left) / containerRect.width) *
               100;

            // Limit min/max range (15% to 85%)
            const limitedPosition = Math.min(Math.max(newPosition, 15), 85);
            setDividerPosition(limitedPosition);
         }
      }
   }, []); // Empty dependency array is correct since we only access refs and set state

   const handleDividerTouchEnd = useCallback(() => {
      isDraggingRef.current = false;
      setIsDragging(false);
      document.removeEventListener("touchmove", touchMoveHandlerRef.current);
      document.removeEventListener("touchend", handleDividerTouchEnd);
   }, []); // Remove handleDividerTouchMove dependency to prevent recreation

   // Assign event handlers to refs to ensure stable references
   useEffect(() => {
      mouseMoveHandlerRef.current = handleDividerMouseMove;
      touchMoveHandlerRef.current = handleDividerTouchMove;
   }, [handleDividerMouseMove, handleDividerTouchMove]);

   // Save divider position to localStorage when it changes
   useEffect(() => {
      if (typeof window !== "undefined") {
         localStorage.setItem(
            "budget-divider-position",
            dividerPosition.toString()
         );
      }
   }, [dividerPosition]);

   // Clean up event listeners when component unmounts
   useEffect(() => {
      return () => {
         // Reset dragging state on unmount
         isDraggingRef.current = false;
         setIsDragging(false);
         // Note: Individual event listeners are cleaned up in their respective mouseUp/touchEnd handlers
      };
   }, []); // Empty dependency array to run only on mount/unmount

   return (
      <div className="h-full flex flex-col">
         <div className="flex-1 flex flex-col overflow-hidden">
            <BudgetHeader
               onDateRangeChange={handleDateRangeChange}
               userPaySettings={userPaySettings}
               userCreatedAt={userCreatedAt}
               viewType={viewType}
               currentDate={currentDate}
               accounts={accounts}
               payPeriods={payPeriods}
               isInitialLoad={isInitialLoad}
            />

            {/* Scrollable Content */}
            <div className="flex-1 overflow-auto md:overflow-hidden">
               {!loading ? (
                  <div
                     className={`h-full ${
                        isMobileView
                           ? "flex flex-col"
                           : "flex budget-container select-none"
                     }`}
                  >
                     {/* Income and Debt Section - full width on mobile */}
                     <div
                        className={`${
                           isMobileView ? "w-full border-b" : "h-full border-r"
                        } border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900`}
                        style={
                           isMobileView ? {} : { width: `${dividerPosition}%` }
                        }
                     >
                        <div
                           className={`${
                              isMobileView
                                 ? "flex flex-col"
                                 : "h-full flex flex-col"
                           }`}
                        >
                           <BudgetIncomeSection
                              dateRange={dateRange}
                              incomes={incomes}
                              setIncomes={setIncomes}
                              userPaySettings={userPaySettings}
                              loading={loading}
                              isMobileView={isMobileView}
                              onRefreshBalances={handleRefreshBalances}
                              onFetchPeriodData={handleFetchPeriodData}
                              onFetchInitialData={fetchInitialData}
                           />

                           <BudgetDebtSection
                              debts={debts}
                              setDebts={setDebts}
                              loading={loading}
                              viewType={viewType}
                              dateRange={dateRange}
                              isMobileView={isMobileView}
                           />
                        </div>
                     </div>

                     {/* Vertical Draggable Divider - hidden on mobile */}
                     {!isMobileView && (
                        <div
                           className={`relative w-2 h-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-700 cursor-col-resize ${
                              isDragging ? "bg-gray-400 dark:bg-gray-600" : ""
                           }`}
                           onMouseDown={handleDividerMouseDown}
                           onTouchStart={handleDividerTouchStart}
                           style={{
                              cursor: "col-resize",
                              userSelect: "none",
                              touchAction: "none",
                              transition: isDragging
                                 ? "none"
                                 : "background-color 0.2s ease",
                           }}
                        >
                           {/* Drag handle indicator */}
                           <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center gap-1.5 pointer-events-none">
                              <div className="w-1 h-1 rounded-full bg-gray-400 dark:bg-gray-500"></div>
                              <div className="w-1 h-1 rounded-full bg-gray-400 dark:bg-gray-500"></div>
                              <div className="w-1 h-1 rounded-full bg-gray-400 dark:bg-gray-500"></div>
                              <div className="w-1 h-1 rounded-full bg-gray-400 dark:bg-gray-500"></div>
                              <div className="w-1 h-1 rounded-full bg-gray-400 dark:bg-gray-500"></div>
                           </div>
                        </div>
                     )}

                     {/* Expenses Section - full width on mobile */}
                     <div
                        className={`${isMobileView ? "w-full" : "h-full"}`}
                        style={
                           isMobileView
                              ? {}
                              : { width: `${100 - dividerPosition - 0.25}%` }
                        }
                     >
                        <BudgetExpenseSection
                           dateRange={dateRange}
                           expenses={expenses}
                           setExpenses={setExpenses}
                           userPaySettings={userPaySettings}
                           setUserPaySettings={setUserPaySettings}
                           debts={debts}
                           loading={loading}
                           error={error}
                           isMobileView={isMobileView}
                           refreshKey={refreshKey}
                           setRefreshKey={setRefreshKey}
                           onDateRangeChange={handleDateRangeChange}
                           onRefreshBalances={handleRefreshBalances}
                           onFetchPeriodData={handleFetchPeriodData}
                        />
                     </div>
                  </div>
               ) : (
                  <div className="flex items-center justify-center h-full">
                     <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white"></div>
                  </div>
               )}
            </div>
         </div>
      </div>
   );
}

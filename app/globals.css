@tailwind base;
@tailwind components;
@tailwind utilities;

/* Disable focus rings on mouse clicks while preserving keyboard accessibility */
@layer base {
   /* Hide focus rings on mouse clicks for better UX */
   *:focus:not(:focus-visible) {
      outline: none;
      box-shadow: none;
   }

   /* Ensure focus rings are visible for keyboard navigation (accessibility) */
   *:focus-visible {
      outline: 2px solid hsl(var(--ring));
      outline-offset: 2px;
   }

   /* Specific override for buttons and interactive elements */
   button:focus:not(:focus-visible),
   [role="button"]:focus:not(:focus-visible),
   [tabindex]:focus:not(:focus-visible) {
      outline: none;
      box-shadow: none;
   }
}

/* Global Scrollbar Styles */
* {
   scrollbar-width: thin;
   scrollbar-color: #cbd5e1 transparent;
}

*::-webkit-scrollbar {
   width: 6px;
}

*::-webkit-scrollbar-track {
   background: transparent;
}

*::-webkit-scrollbar-thumb {
   background-color: #cbd5e1;
   border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
   background-color: #94a3b8;
}

/* Dark mode scrollbar */
.dark * {
   scrollbar-color: #475569 transparent;
}

.dark *::-webkit-scrollbar-thumb {
   background-color: #475569;
}

.dark *::-webkit-scrollbar-thumb:hover {
   background-color: #64748b;
}

@keyframes slide-up {
   from {
      transform: translateY(100%);
   }
   to {
      transform: translateY(0);
   }
}

@keyframes fade-in {
   from {
      opacity: 0;
   }
   to {
      opacity: 1;
   }
}

@keyframes fade-out {
   from {
      opacity: 1;
   }
   to {
      opacity: 0;
   }
}

@keyframes slide-down {
   from {
      transform: translateY(0);
   }
   to {
      transform: translateY(100%);
   }
}

.animate-slide-up {
   animation: slide-up 0.2s ease-out;
}

.animate-slide-down {
   animation: slide-down 0.2s ease-out;
}

.animate-fade-in {
   animation: fade-in 0.2s ease-out;
}

.animate-fade-out {
   animation: fade-out 0.2s ease-out;
}

.dark:root {
   --background: #0a0a0a;
   --foreground: #ededed;
}

/* Prevent zoom on focus for mobile devices while maintaining readability */
@layer base {
   input[type="text"],
   input[type="email"],
   input[type="password"],
   input[type="number"],
   input[type="tel"],
   input[type="url"],
   input[type="search"],
   textarea {
      @apply text-base;
   }

   /* Global Input Padding Specification */
   /* 
   * This section ensures all form inputs have consistent padding and sizing
   * Default: px-3 py-2 (12px horizontal, 8px vertical) with 40px min-height
   * 
   * Available utility classes for different sizes:
   * - .input-sm: Smaller inputs (px-2 py-1, 32px min-height)
   * - .input-lg: Larger inputs (px-4 py-3, 48px min-height) 
   * - .input-xl: Extra large inputs (px-5 py-4, 56px min-height)
   * - .input-no-padding: Removes all padding (use for custom styled inputs)
   * 
   * Note: Individual components can override these styles by applying 
   * more specific classes or using !important if necessary
   */
   input[type="text"],
   input[type="email"],
   input[type="password"],
   input[type="number"],
   input[type="tel"],
   input[type="url"],
   input[type="search"],
   input[type="date"],
   input[type="time"],
   input[type="datetime-local"],
   input[type="month"],
   input[type="week"],
   textarea,
   select {
      @apply px-3 py-2;
      min-height: 2.5rem; /* 40px minimum height for better touch targets */
   }

   /* Ensure select elements have consistent styling */
   select {
      @apply pr-8; /* Extra right padding for dropdown arrow */
   }

   /* Ensure proper line height for better text alignment */
   input,
   textarea,
   select {
      @apply leading-5;
   }

   /* Consistent border radius for all form elements */
   input,
   textarea,
   select {
      @apply rounded-md;
   }

   :root {
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;
      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;
      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;
      --primary: 222.2 47.4% 11.2%;
      --primary-foreground: 210 40% 98%;
      --secondary: 210 40% 96%;
      --secondary-foreground: 222.2 84% 4.9%;
      --muted: 210 40% 96%;
      --muted-foreground: 215.4 16.3% 46.9%;
      --accent: 210 40% 96%;
      --accent-foreground: 222.2 84% 4.9%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;
      --border: 214.3 31.8% 91.4%;
      --input: 214.3 31.8% 91.4%;
      --ring: 222.2 84% 4.9%;
      --chart-1: 12 76% 61%;
      --chart-2: 173 58% 39%;
      --chart-3: 197 37% 24%;
      --chart-4: 43 74% 66%;
      --chart-5: 27 87% 67%;
      --radius: 0.5rem;
   }
   .dark {
      --background: 222.2 84% 4.9%;
      --foreground: 210 40% 98%;
      --card: 217.2 32.6% 17.5%;
      --card-foreground: 210 40% 98%;
      --popover: 222.2 84% 4.9%;
      --popover-foreground: 210 40% 98%;
      --primary: 210 40% 98%;
      --primary-foreground: 222.2 47.4% 11.2%;
      --secondary: 217.2 32.6% 17.5%;
      --secondary-foreground: 210 40% 98%;
      --muted: 217.2 32.6% 17.5%;
      --muted-foreground: 215 20.2% 65.1%;
      --accent: 217.2 32.6% 17.5%;
      --accent-foreground: 210 40% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 210 40% 98%;
      --border: 215 20.2% 45.1%;
      --input: 215 20.2% 45.1%;
      --ring: 212.7 26.8% 83.9%;
      --chart-1: 220 70% 50%;
      --chart-2: 160 60% 45%;
      --chart-3: 30 80% 55%;
      --chart-4: 280 65% 60%;
      --chart-5: 340 75% 55%;
   }
}

/* Custom Scrollbar Styles */
@layer utilities {
   .custom-scrollbar {
      scrollbar-width: thin;
      scrollbar-color: #cbd5e1 transparent;
   }

   .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
   }

   .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
   }

   .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: #cbd5e1;
      border-radius: 3px;
   }

   .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: #94a3b8;
   }

   .dark .custom-scrollbar {
      scrollbar-color: #475569 transparent;
   }

   .dark .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: #475569;
   }

   .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: #64748b;
   }

   /* Input Padding Utility Classes */
   .input-sm {
      @apply px-2 py-1 text-sm;
      min-height: 2rem; /* 32px */
   }

   .input-lg {
      @apply px-4 py-3 text-lg;
      min-height: 3rem; /* 48px */
   }

   .input-xl {
      @apply px-5 py-4 text-xl;
      min-height: 3.5rem; /* 56px */
   }

   /* Override utility for cases where global padding shouldn't apply */
   .input-no-padding {
      @apply p-0;
      min-height: auto;
   }
}

@layer base {
   * {
      @apply border-border;
   }
   body {
      @apply bg-background text-foreground;
   }
}

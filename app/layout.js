import "./globals.css";
import { Inter } from "next/font/google";
import { Providers } from "./components/Providers";
import AuthenticatedLayout from "./components/layouts/AuthenticatedLayout";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
   title: "Alto - Your Paycheck's Best Friend",
   description:
      "Alto - Your Paycheck's Best Friend. Personal budgeting made simple.",
   formatDetection: {
      telephone: false,
   },
   icons: {
      icon: [
         { url: "/favicon.ico", sizes: "any" },
         { url: "/logo.ico", sizes: "any" },
         { url: "/shortcut-icon.png" },
      ],
      shortcut: [{ url: "/shortcut-icon.png" }],
   },
};

export const viewport = {
   width: "device-width",
   initialScale: 1,
};

export default function RootLayout({ children }) {
   return (
      <html lang="en" suppressHydrationWarning>
         <body className={inter.className} suppressHydrationWarning>
            <Providers>
               <AuthenticatedLayout>{children}</AuthenticatedLayout>
            </Providers>
         </body>
      </html>
   );
}

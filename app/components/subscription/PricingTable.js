"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2 } from "lucide-react";
import { SUBSCRIPTION_PLANS } from "@/app/lib/stripe/config";

export default function PricingTable({
   currentPlan = "free",
   onPlanSelect,
   resetLoading = false,
}) {
   const [loading, setLoading] = useState(null);

   // Reset loading state when resetLoading prop changes
   useEffect(() => {
      if (resetLoading) {
         setLoading(null);
      }
   }, [resetLoading]);

   const handlePlanSelect = async (planId) => {
      if (planId === currentPlan) return;

      setLoading(planId);
      try {
         if (planId === "free") {
            // Handle downgrade to free
            const response = await fetch("/api/stripe/subscription", {
               method: "PATCH",
               headers: { "Content-Type": "application/json" },
               body: JSON.stringify({ action: "downgrade_to_free" }),
            });

            if (response.ok) {
               // Refresh the page or update the current plan
               window.location.reload();
            } else {
               const errorData = await response.json();
               console.error("Failed to downgrade to free:", errorData);
               alert("Failed to downgrade to free tier. Please try again.");
            }
         } else if (onPlanSelect) {
            await onPlanSelect(planId);
         } else {
            // Default behavior - redirect to Stripe checkout
            const response = await fetch("/api/stripe/create-checkout", {
               method: "POST",
               headers: { "Content-Type": "application/json" },
               body: JSON.stringify({ planId }),
            });

            if (response.ok) {
               const { url } = await response.json();
               if (url) {
                  window.location.href = url;
               } else {
                  console.error("No checkout URL received");
                  // Reset loading state if no URL is received
                  setLoading(null);
               }
            } else {
               console.error("Failed to create checkout session");
               const errorData = await response.json().catch(() => ({}));
               console.error("Error details:", errorData);
               // Reset loading state on error
               setLoading(null);
            }
         }
      } catch (error) {
         console.error("Error selecting plan:", error);
         // Show user-friendly error message
         alert("Failed to process plan change. Please try again.");
      } finally {
         setLoading(null);
      }
   };

   const getPlanButtonText = (planId) => {
      if (planId === currentPlan) return "Current Plan";
      if (planId === "free") {
         return currentPlan === "free" ? "Free Forever" : "Downgrade to Free";
      }
      return "Upgrade";
   };

   const isPlanDisabled = (planId) => {
      return planId === currentPlan;
   };

   return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
         {Object.values(SUBSCRIPTION_PLANS).map((plan) => (
            <Card
               key={plan.id}
               className={`relative ${
                  plan.id === "pro"
                     ? "border-blue-500 shadow-lg scale-105"
                     : "border-gray-200 dark:border-gray-700"
               }`}
            >
               {plan.id === "pro" && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                     <Badge className="bg-blue-500 text-white">
                        Most Popular
                     </Badge>
                  </div>
               )}

               <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <CardDescription>
                     <span className="text-4xl font-bold text-gray-900 dark:text-gray-100">
                        ${plan.price}
                     </span>
                     {plan.price > 0 && (
                        <span className="text-gray-500 dark:text-gray-400">
                           /month
                        </span>
                     )}
                  </CardDescription>
               </CardHeader>

               <CardContent className="space-y-4">
                  <ul className="space-y-3">
                     {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2">
                           <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                           <span className="text-sm text-gray-700 dark:text-gray-300">
                              {feature}
                           </span>
                        </li>
                     ))}
                  </ul>

                  <Button
                     onClick={() => handlePlanSelect(plan.id)}
                     disabled={isPlanDisabled(plan.id) || loading === plan.id}
                     className={`w-full ${
                        plan.id === "pro" ? "bg-blue-600 hover:bg-blue-700" : ""
                     }`}
                     variant={plan.id === currentPlan ? "outline" : "default"}
                  >
                     {loading === plan.id ? (
                        <>
                           <Loader2 className="h-4 w-4 animate-spin mr-2" />
                           Processing...
                        </>
                     ) : (
                        getPlanButtonText(plan.id)
                     )}
                  </Button>

                  {plan.id === currentPlan && (
                     <div className="text-center">
                        <Badge variant="secondary">Active</Badge>
                     </div>
                  )}
               </CardContent>
            </Card>
         ))}
      </div>
   );
}

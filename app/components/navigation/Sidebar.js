"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut } from "next-auth/react";
import { useTheme } from "next-themes";
import {
   PieChart,
   Receipt,
   Settings,
   LogOut,
   Sun,
   Moon,
   ChevronLeft,
   ChevronRight,
   BarChart3,
   Menu,
   Monitor,
} from "lucide-react";
import { useState, useEffect } from "react";
import { Dropdown, DropdownItem } from "../ui/dropdown";

// App version
const APP_VERSION = "0.4.0";

const navItems = [
   { name: "Budget", href: "/budget", icon: PieChart },
   { name: "Transactions", href: "/transactions", icon: Receipt },
   { name: "Reports", href: "/reports", icon: BarChart3 },
];

export function Sidebar({ isMobileMenuOpen, setIsMobileMenuOpen }) {
   const pathname = usePathname();
   const { theme, setTheme, resolvedTheme } = useTheme();
   const [isCollapsed, setIsCollapsed] = useState(true);

   // Load collapsed state from localStorage on mount
   useEffect(() => {
      const savedState = localStorage.getItem("sidebarCollapsed");
      if (savedState !== null) {
         setIsCollapsed(savedState === "true");
      }
   }, []);

   // Update localStorage when collapsed state changes
   const handleCollapse = () => {
      const newState = !isCollapsed;
      setIsCollapsed(newState);
      localStorage.setItem("sidebarCollapsed", newState.toString());
   };

   // Close mobile menu when clicking outside
   useEffect(() => {
      const handleClickOutside = (event) => {
         const sidebar = document.getElementById("sidebar");
         const mobileButton = document.getElementById("mobile-menu-button");
         if (
            isMobileMenuOpen &&
            sidebar &&
            !sidebar.contains(event.target) &&
            mobileButton &&
            !mobileButton.contains(event.target)
         ) {
            setIsMobileMenuOpen(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, [isMobileMenuOpen, setIsMobileMenuOpen]);

   const handleSignOut = () => {
      signOut({ callbackUrl: "/auth/login" });
   };

   // Theme cycling function
   const cycleTheme = () => {
      if (theme === "system") {
         setTheme("light");
      } else if (theme === "light") {
         setTheme("dark");
      } else {
         setTheme("system");
      }
   };

   // Get theme icon and text
   const getThemeIcon = () => {
      if (theme === "system") {
         return <Monitor className="h-5 w-5" />;
      } else if (theme === "light") {
         return <Sun className="h-5 w-5" />;
      } else {
         return <Moon className="h-5 w-5" />;
      }
   };

   const getThemeText = () => {
      if (theme === "system") {
         return "System";
      } else if (theme === "light") {
         return "Light";
      } else {
         return "Dark";
      }
   };

   return (
      <>
         {/* Mobile Menu Button - Floating */}
         <button
            id="mobile-menu-button"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className={`fixed top-4 left-4 z-[60] lg:hidden inline-flex items-center justify-center p-2 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg transition-all duration-200 ${
               isMobileMenuOpen ? "translate-x-64" : "translate-x-0"
            }`}
            aria-label="Toggle mobile menu"
         >
            <Menu className="h-5 w-5" />
         </button>

         {/* Sidebar */}
         <div
            id="sidebar"
            className={`fixed lg:static inset-y-0 left-0 z-50 flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-all duration-300 ease-in-out h-[100vh] lg:h-screen ${
               isCollapsed ? "lg:w-16" : "lg:w-56"
            } w-56 ${
               isMobileMenuOpen
                  ? "translate-x-0"
                  : "-translate-x-full lg:translate-x-0"
            }`}
         >
            {/* Desktop Collapse Button */}
            <button
               onClick={handleCollapse}
               className={`hidden lg:block absolute ${
                  isCollapsed ? "left-1/2 -translate-x-1/2" : "right-3"
               } top-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-1 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-0 z-50 shadow-sm hover:shadow-md transition-all duration-200 active:scale-95 active:bg-gray-200 dark:active:bg-gray-600`}
               aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
               {isCollapsed ? (
                  <ChevronRight className="h-4 w-4 text-gray-600 dark:text-gray-300" />
               ) : (
                  <ChevronLeft className="h-4 w-4 text-gray-600 dark:text-gray-300" />
               )}
            </button>

            {/* Main Content Container */}
            <div className="flex flex-col h-full">
               {/* Logo and Navigation */}
               <div className="flex-shrink-0">
                  <div
                     className={`${isCollapsed ? "lg:px-1" : "lg:px-3"} px-3 
                        lg:pt-6 pt-4 flex flex-col items-start`}
                  >
                     <div className="flex items-center">
                        <img
                           src={
                              resolvedTheme === "dark"
                                 ? "/images/white.png"
                                 : "/images/black.png"
                           }
                           alt="ALTO Budget"
                           className={`h-6 w-auto transition-opacity duration-200 ${
                              isCollapsed
                                 ? "lg:opacity-0 lg:hidden"
                                 : "lg:opacity-100"
                           } opacity-100`}
                        />
                        <h1
                           className={`text-lg font-bold text-gray-900 dark:text-white transition-opacity duration-200 ml-2 ${
                              isCollapsed
                                 ? "lg:opacity-0 lg:hidden"
                                 : "lg:opacity-100"
                           } opacity-100`}
                        >
                           ALTO
                        </h1>
                     </div>
                     <span
                        className={`text-xs text-gray-500 dark:text-gray-400 mt-0.5 transition-opacity duration-200 ${
                           isCollapsed
                              ? "lg:opacity-0 lg:hidden"
                              : "lg:opacity-100"
                        } opacity-100 flex items-center self-start`}
                     >
                        v{APP_VERSION}{" "}
                        <span className="ml-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-[10px] px-1 py-0.5 rounded">
                           BETA
                        </span>
                     </span>
                  </div>
                  <nav
                     className={`${isCollapsed ? "lg:mt-8" : "lg:mt-3"} mt-3 ${
                        isCollapsed ? "lg:px-0.5" : "lg:px-1.5"
                     } px-1.5 space-y-0.5`}
                  >
                     {navItems.map((item) => {
                        const isActive = pathname === item.href;
                        return (
                           <Link
                              key={item.name}
                              href={item.href}
                              onClick={() => {
                                 if (window.innerWidth < 1024) {
                                    setIsMobileMenuOpen(false);
                                 }
                              }}
                              className={`group flex items-center ${
                                 isCollapsed ? "lg:px-0.5" : "lg:px-1.5"
                              } px-1.5 py-2.5 text-sm font-medium rounded-md transition-all duration-100 border ${
                                 isActive
                                    ? "bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400 border-gray-200 dark:border-gray-700"
                                    : "text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white border-transparent hover:border-gray-200 dark:hover:border-gray-700"
                              } active:scale-95 active:bg-gray-200 dark:active:bg-gray-600`}
                              title={item.name}
                           >
                              <item.icon
                                 className={`${
                                    isCollapsed ? "lg:mx-auto" : "lg:mr-2.5"
                                 } mr-2.5 h-5 w-5 ${
                                    isActive
                                       ? "text-blue-600 dark:text-blue-400"
                                       : "text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-300"
                                 }`}
                                 aria-hidden="true"
                              />
                              <span
                                 className={`transition-opacity duration-200 ${
                                    isCollapsed
                                       ? "lg:opacity-0 lg:hidden"
                                       : "lg:opacity-100"
                                 } opacity-100`}
                              >
                                 {item.name}
                              </span>
                           </Link>
                        );
                     })}
                  </nav>
               </div>

               {/* Spacer */}
               <div className="flex-grow"></div>

               {/* Bottom Actions */}
               <div
                  className={`flex-shrink-0 border-t border-gray-200 dark:border-gray-700 ${
                     isCollapsed ? "lg:px-0.5 lg:py-1.5" : "lg:px-1.5 lg:py-3"
                  } px-1.5 py-3 space-y-1.5`}
               >
                  {/* Theme Toggle Button */}
                  <button
                     onClick={cycleTheme}
                     className={`group flex items-center w-full ${
                        isCollapsed ? "lg:px-0.5" : "lg:px-1.5"
                     } px-1.5 py-2.5 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-all duration-100 ${
                        isCollapsed ? "lg:justify-center" : ""
                     } active:scale-95 active:bg-gray-200 dark:active:bg-gray-600`}
                     title={`Theme: ${getThemeText()}`}
                  >
                     <span
                        className={`${
                           isCollapsed ? "lg:mx-auto" : "lg:mr-2.5"
                        } mr-2.5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-300`}
                     >
                        {getThemeIcon()}
                     </span>
                     <span
                        className={`transition-opacity duration-200 ${
                           isCollapsed
                              ? "lg:opacity-0 lg:hidden"
                              : "lg:opacity-100"
                        } opacity-100`}
                     >
                        {getThemeText()}
                     </span>
                  </button>

                  {/* Settings Link */}
                  <Link
                     href="/settings"
                     onClick={() => {
                        if (window.innerWidth < 1024) {
                           setIsMobileMenuOpen(false);
                        }
                     }}
                     className={`group flex items-center w-full ${
                        isCollapsed ? "lg:px-0.5" : "lg:px-1.5"
                     } px-1.5 py-2.5 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-all duration-100 ${
                        isCollapsed ? "lg:justify-center" : ""
                     } active:scale-95 active:bg-gray-200 dark:active:bg-gray-600`}
                     title="Settings"
                  >
                     <Settings
                        className={`${
                           isCollapsed ? "lg:mx-auto" : "lg:mr-2.5"
                        } mr-2.5 h-5 w-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-300`}
                     />
                     <span
                        className={`transition-opacity duration-200 ${
                           isCollapsed
                              ? "lg:opacity-0 lg:hidden"
                              : "lg:opacity-100"
                        } opacity-100`}
                     >
                        Settings
                     </span>
                  </Link>

                  {/* Logout Button */}
                  <button
                     onClick={handleSignOut}
                     className={`group flex items-center w-full ${
                        isCollapsed ? "lg:px-0.5" : "lg:px-1.5"
                     } px-1.5 py-2.5 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-all duration-100 ${
                        isCollapsed ? "lg:justify-center" : ""
                     } active:scale-95 active:bg-gray-200 dark:active:bg-gray-600`}
                     title="Logout"
                  >
                     <LogOut
                        className={`${
                           isCollapsed ? "lg:mx-auto" : "lg:mr-2.5"
                        } mr-2.5 h-5 w-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-300`}
                     />
                     <span
                        className={`transition-opacity duration-200 ${
                           isCollapsed
                              ? "lg:opacity-0 lg:hidden"
                              : "lg:opacity-100"
                        } opacity-100`}
                     >
                        Logout
                     </span>
                  </button>
               </div>
            </div>
         </div>

         {/* Overlay for mobile */}
         {isMobileMenuOpen && (
            <div
               className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
               onClick={() => {
                  setIsMobileMenuOpen(false);
               }}
            />
         )}
      </>
   );
}

export default Sidebar;

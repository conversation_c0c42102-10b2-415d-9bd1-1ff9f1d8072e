"use client";

import { useState, useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
   AlertTriangle,
   TrendingDown,
   TrendingUp,
   ChevronDown,
   ChevronRight,
   Calendar,
   DollarSign,
   RefreshCw,
} from "lucide-react";
import { format } from "date-fns";

export default function CashFlowWarnings({
   periodsAhead = null, // null means use user preference
   autoRefresh = true,
   showDebugInfo = false,
}) {
   const [warning, setWarning] = useState(null);
   const [loading, setLoading] = useState(true);
   const [error, setError] = useState("");
   const [expanded, setExpanded] = useState(false);
   const [warningsEnabled, setWarningsEnabled] = useState(true);
   const [debugInfo, setDebugInfo] = useState(null);

   const fetchWarnings = async () => {
      try {
         setLoading(true);
         setError("");

         // Only include periodsAhead parameter if explicitly provided
         const url =
            periodsAhead !== null
               ? `/api/reports/cash-flow-warnings?periodsAhead=${periodsAhead}`
               : "/api/reports/cash-flow-warnings";

         const response = await fetch(url);

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch warnings");
         }

         const data = await response.json();
         setWarning(data.warning);
         setWarningsEnabled(data.warningsEnabled);
         setDebugInfo(data.debug);
      } catch (err) {
         console.error("Error fetching cash flow warnings:", err);
         setError(err.message);
      } finally {
         setLoading(false);
      }
   };

   useEffect(() => {
      fetchWarnings();
   }, [periodsAhead]);

   const formatCurrency = (amount) => {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
      }).format(amount);
   };

   const getSeverityColor = (severity) => {
      switch (severity) {
         case "critical":
            return "bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";
         case "warning":
            return "bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";
         default:
            return "bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200";
      }
   };

   if (loading) {
      return (
         <Card className="w-full">
            <CardContent className="p-6">
               <div className="flex items-center justify-center space-x-2">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Calculating cash flow warnings...</span>
               </div>
            </CardContent>
         </Card>
      );
   }

   if (error) {
      return (
         <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
               Failed to load cash flow warnings: {error}
               <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={fetchWarnings}
               >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Retry
               </Button>
            </AlertDescription>
         </Alert>
      );
   }

   if (!warningsEnabled) {
      return (
         <Card className="w-full">
            <CardContent className="p-6">
               <div className="text-center text-gray-500 dark:text-gray-400">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p>Cash flow warnings are disabled</p>
                  <p className="text-sm">
                     Enable them in your settings to see potential cash flow
                     issues
                  </p>
               </div>
            </CardContent>
         </Card>
      );
   }

   if (!warning) {
      return (
         <Card className="w-full">
            <CardContent className="p-6">
               <div className="text-center text-green-600 dark:text-green-400">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p className="font-medium">All clear!</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                     No negative cash flow detected in the next {periodsAhead}{" "}
                     pay periods
                  </p>
               </div>
               {showDebugInfo && debugInfo && (
                  <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded text-xs">
                     <p>
                        <strong>Debug Info:</strong>
                     </p>
                     <p>Recurring Incomes: {debugInfo.recurringIncomesCount}</p>
                     <p>
                        Recurring Expenses: {debugInfo.recurringExpensesCount}
                     </p>
                     <p>Scheduled Incomes: {debugInfo.scheduledIncomesCount}</p>
                     <p>
                        Scheduled Expenses: {debugInfo.scheduledExpensesCount}
                     </p>
                     <p>Active Debts: {debugInfo.debtsCount}</p>
                  </div>
               )}
            </CardContent>
         </Card>
      );
   }

   return (
      <div className="w-full space-y-4">
         {/* Main Warning Alert */}
         <Alert className={getSeverityColor(warning.severity)}>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
               <div>
                  <p className="font-medium">{warning.warning}</p>
                  <p className="text-sm mt-1">
                     {warning.periodNumber === 1
                        ? "Your next pay period"
                        : `In ${warning.periodNumber} pay periods`}
                     : {warning.period.label}
                  </p>
               </div>
               <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setExpanded(!expanded)}
                  className="ml-4"
               >
                  {expanded ? (
                     <>
                        <ChevronDown className="h-4 w-4 mr-1" />
                        Hide Details
                     </>
                  ) : (
                     <>
                        <ChevronRight className="h-4 w-4 mr-1" />
                        Show Details
                     </>
                  )}
               </Button>
            </AlertDescription>
         </Alert>

         {/* Detailed Breakdown */}
         {expanded && (
            <Card>
               <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                     <Calendar className="h-5 w-5" />
                     <span>Cash Flow Breakdown</span>
                     <Badge variant="outline">
                        {format(warning.period.startDate, "MMM d")} -{" "}
                        {format(warning.period.endDate, "MMM d, yyyy")}
                     </Badge>
                  </CardTitle>
               </CardHeader>
               <CardContent className="space-y-6">
                  {/* Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                     <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <TrendingUp className="h-6 w-6 text-green-600 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                           Expected Income
                        </p>
                        <p className="text-lg font-bold text-green-600">
                           {formatCurrency(warning.cashFlow.income.total)}
                        </p>
                     </div>

                     <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <TrendingDown className="h-6 w-6 text-red-600 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                           Expected Expenses
                        </p>
                        <p className="text-lg font-bold text-red-600">
                           {formatCurrency(warning.cashFlow.expenses.total)}
                        </p>
                     </div>

                     <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <DollarSign className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                           Already Assigned
                        </p>
                        <p className="text-lg font-bold text-blue-600">
                           {formatCurrency(warning.cashFlow.assigned.total)}
                        </p>
                     </div>

                     {warning.cashFlow.overspend?.total > 0 && (
                        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                           <TrendingDown className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                           <p className="text-sm text-gray-600 dark:text-gray-400">
                              Overspend Amount
                           </p>
                           <p className="text-lg font-bold text-orange-600">
                              {formatCurrency(warning.cashFlow.overspend.total)}
                           </p>
                        </div>
                     )}

                     <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <DollarSign className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                           Net Cash Flow
                        </p>
                        <p
                           className={`text-lg font-bold ${
                              warning.cashFlow.netFlow < 0
                                 ? "text-red-600"
                                 : "text-green-600"
                           }`}
                        >
                           {formatCurrency(warning.cashFlow.netFlow)}
                        </p>
                     </div>
                  </div>

                  {/* Explanation */}
                  <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg">
                     <p className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>Note:</strong> Expenses show base due amounts.
                        Overspend amounts (where spent &gt; due) are shown
                        separately.
                     </p>
                  </div>

                  {/* Income Breakdown */}
                  {warning.cashFlow.details.incomeBreakdown.length > 0 && (
                     <div>
                        <h4 className="text-lg font-semibold mb-3 text-green-700 dark:text-green-400">
                           Expected Income
                        </h4>
                        <div className="space-y-2">
                           {warning.cashFlow.details.incomeBreakdown.map(
                              (item, index) => (
                                 <div
                                    key={index}
                                    className="flex justify-between items-center p-2 bg-green-50 dark:bg-green-900/10 rounded"
                                 >
                                    <div>
                                       <span className="font-medium">
                                          {item.description}
                                       </span>
                                       <Badge
                                          variant="outline"
                                          size="sm"
                                          className="ml-2"
                                       >
                                          {item.type}
                                       </Badge>
                                    </div>
                                    <div className="text-right">
                                       <span className="font-medium text-green-600">
                                          {formatCurrency(item.amount)}
                                       </span>
                                       <p className="text-xs text-gray-500">
                                          {format(new Date(item.date), "MMM d")}
                                       </p>
                                    </div>
                                 </div>
                              )
                           )}
                        </div>
                     </div>
                  )}

                  {/* Expense Breakdown */}
                  {warning.cashFlow.details.expenseBreakdown.length > 0 && (
                     <div>
                        <h4 className="text-lg font-semibold mb-3 text-red-700 dark:text-red-400">
                           Expected Expenses
                        </h4>
                        <div className="space-y-2">
                           {warning.cashFlow.details.expenseBreakdown.map(
                              (item, index) => (
                                 <div
                                    key={index}
                                    className="flex justify-between items-center p-2 bg-red-50 dark:bg-red-900/10 rounded"
                                 >
                                    <div>
                                       <span className="font-medium">
                                          {item.description}
                                       </span>
                                       <Badge
                                          variant="outline"
                                          size="sm"
                                          className="ml-2"
                                       >
                                          {item.type}
                                       </Badge>
                                    </div>
                                    <div className="text-right">
                                       <span className="font-medium text-red-600">
                                          {formatCurrency(item.amount)}
                                       </span>
                                       <p className="text-xs text-gray-500">
                                          {format(new Date(item.date), "MMM d")}
                                       </p>
                                    </div>
                                 </div>
                              )
                           )}
                        </div>
                     </div>
                  )}

                  {/* Overspend Breakdown */}
                  {warning.cashFlow.details.overspendBreakdown &&
                     warning.cashFlow.details.overspendBreakdown.length > 0 && (
                        <div>
                           <h4 className="text-lg font-semibold mb-3 text-orange-700 dark:text-orange-400">
                              Overspend Amount
                           </h4>
                           <div className="space-y-2">
                              {warning.cashFlow.details.overspendBreakdown.map(
                                 (item, index) => (
                                    <div
                                       key={index}
                                       className="flex justify-between items-center p-2 bg-orange-50 dark:bg-orange-900/10 rounded"
                                    >
                                       <div>
                                          <span className="font-medium">
                                             {item.description}
                                          </span>
                                          <Badge
                                             variant="outline"
                                             size="sm"
                                             className="ml-2"
                                          >
                                             {item.type}
                                          </Badge>
                                       </div>
                                       <div className="text-right">
                                          <span className="font-medium text-orange-600">
                                             {formatCurrency(item.amount)}
                                          </span>
                                          <p className="text-xs text-gray-500">
                                             {format(
                                                new Date(item.date),
                                                "MMM d"
                                             )}
                                          </p>
                                       </div>
                                    </div>
                                 )
                              )}
                           </div>
                        </div>
                     )}

                  {/* Assigned Amounts Breakdown */}
                  {warning.cashFlow.details.assignedBreakdown &&
                     warning.cashFlow.details.assignedBreakdown.length > 0 && (
                        <div>
                           <h4 className="text-lg font-semibold mb-3 text-blue-700 dark:text-blue-400">
                              Already Assigned (Money Saved)
                           </h4>
                           <div className="space-y-2">
                              {warning.cashFlow.details.assignedBreakdown.map(
                                 (item, index) => (
                                    <div
                                       key={index}
                                       className="flex justify-between items-center p-2 bg-blue-50 dark:bg-blue-900/10 rounded"
                                    >
                                       <div>
                                          <span className="font-medium">
                                             {item.description}
                                          </span>
                                          <Badge
                                             variant="outline"
                                             size="sm"
                                             className="ml-2"
                                          >
                                             {item.type}
                                          </Badge>
                                       </div>
                                       <div className="text-right">
                                          <span className="font-medium text-blue-600">
                                             {formatCurrency(item.amount)}
                                          </span>
                                          <p className="text-xs text-gray-500">
                                             {format(
                                                new Date(item.date),
                                                "MMM d"
                                             )}
                                          </p>
                                       </div>
                                    </div>
                                 )
                              )}
                           </div>
                        </div>
                     )}

                  {/* Suggestions */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                     <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                        💡 Suggestions to improve cash flow:
                     </h4>
                     <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                        <li>
                           • Schedule additional income or move existing income
                           to an earlier date
                        </li>
                        <li>
                           • Reduce or delay non-essential expenses in this
                           period
                        </li>
                        <li>
                           • Assign more money to expenses that already have
                           partial funding
                        </li>
                        <li>
                           • Consider moving debt payments to a different period
                           if possible
                        </li>
                     </ul>
                  </div>

                  {/* Refresh Button */}
                  <div className="pt-4 border-t">
                     <Button
                        variant="outline"
                        onClick={fetchWarnings}
                        className="w-full"
                     >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh Warnings
                     </Button>
                  </div>
               </CardContent>
            </Card>
         )}

         {/* Debug Info */}
         {showDebugInfo && debugInfo && (
            <Card>
               <CardHeader>
                  <CardTitle className="text-sm">Debug Information</CardTitle>
               </CardHeader>
               <CardContent className="text-xs space-y-1">
                  <p>
                     User has main income:{" "}
                     {debugInfo.userHasMainIncome ? "Yes" : "No"}
                  </p>
                  <p>Recurring incomes: {debugInfo.recurringIncomesCount}</p>
                  <p>Recurring expenses: {debugInfo.recurringExpensesCount}</p>
                  <p>Scheduled incomes: {debugInfo.scheduledIncomesCount}</p>
                  <p>Scheduled expenses: {debugInfo.scheduledExpensesCount}</p>
                  <p>Active debts: {debugInfo.debtsCount}</p>
                  <p>Periods checked: {periodsAhead}</p>
               </CardContent>
            </Card>
         )}
      </div>
   );
}

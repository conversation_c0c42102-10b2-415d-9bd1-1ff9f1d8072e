"use client";

import { useMemo, useState, useEffect } from "react";
import { format, parseISO } from "date-fns";

export default function FutureProjectionsList({
   data,
   selectedMonths,
   onProjectionChange,
}) {
   const [customAmounts, setCustomAmounts] = useState({});

   const projectionItems = useMemo(() => {
      if (!data || !Array.isArray(data))
         return { income: [], expenses: [], debts: [] };

      // Filter only future projection data
      const futureData = data.filter((item) => item.isFuture);

      if (futureData.length === 0)
         return { income: [], expenses: [], debts: [] };

      // Collect unique items across all future data
      const itemsMap = {
         income: new Map(),
         expenses: new Map(),
         debts: new Map(),
      };

      futureData.forEach((item) => {
         // Process income details
         if (item.incomeDetails && Array.isArray(item.incomeDetails)) {
            item.incomeDetails.forEach((income) => {
               const key = `${income.name}-${income.frequency}`;
               if (!itemsMap.income.has(key)) {
                  itemsMap.income.set(key, {
                     id: key,
                     name: income.name,
                     originalAmount: income.amount,
                     frequency: income.frequency,
                     type: "income",
                  });
               }
            });
         }

         // Process expense details (excluding debts)
         if (item.expenseDetails && Array.isArray(item.expenseDetails)) {
            item.expenseDetails.forEach((expense) => {
               if (expense.type === "debt") {
                  const key = `${expense.name}-${expense.frequency}`;
                  if (!itemsMap.debts.has(key)) {
                     itemsMap.debts.set(key, {
                        id: key,
                        name: expense.name,
                        originalAmount: expense.amount,
                        frequency: expense.frequency,
                        type: "debt",
                     });
                  }
               } else {
                  const key = `${expense.name}-${expense.frequency}`;
                  if (!itemsMap.expenses.has(key)) {
                     itemsMap.expenses.set(key, {
                        id: key,
                        name: expense.name,
                        originalAmount: expense.amount,
                        frequency: expense.frequency,
                        type: "expense",
                     });
                  }
               }
            });
         }
      });

      // Convert maps to arrays (remove prorated calculations - we'll use actual occurrences)
      const income = Array.from(itemsMap.income.values());
      const expenses = Array.from(itemsMap.expenses.values());
      const debts = Array.from(itemsMap.debts.values());

      return { income, expenses, debts };
   }, [data, selectedMonths]);

   const formatCurrency = (value) => {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
         minimumFractionDigits: 2,
      }).format(value);
   };

   const formatFrequency = (frequency) => {
      const frequencyMap = {
         weekly: "Weekly",
         biweekly: "Bi-weekly",
         monthly: "Monthly",
         quarterly: "Quarterly",
         yearly: "Yearly",
      };
      return frequencyMap[frequency] || frequency;
   };

   const handleAmountChange = (itemId, value) => {
      const numericValue = parseFloat(value) || 0;
      setCustomAmounts((prev) => ({
         ...prev,
         [itemId]: numericValue,
      }));
   };

   const handleReset = (itemId) => {
      setCustomAmounts((prev) => {
         const newAmounts = { ...prev };
         delete newAmounts[itemId];
         return newAmounts;
      });
   };

   const getEffectiveAmount = (item) => {
      return customAmounts[item.id] !== undefined
         ? customAmounts[item.id]
         : item.originalAmount;
   };

   const getEffectiveTotal = (item) => {
      const effectiveAmount = getEffectiveAmount(item);

      // Count actual occurrences from the future data instead of using prorated amounts
      if (!data || !Array.isArray(data)) return effectiveAmount;

      const futureData = data.filter((dataPoint) => dataPoint.isFuture);
      let occurrenceCount = 0;

      futureData.forEach((dataPoint) => {
         // Check income details
         if (dataPoint.incomeDetails) {
            dataPoint.incomeDetails.forEach((income) => {
               if (
                  income.name === item.name &&
                  income.frequency === item.frequency
               ) {
                  occurrenceCount++;
               }
            });
         }

         // Check expense details
         if (dataPoint.expenseDetails) {
            dataPoint.expenseDetails.forEach((expense) => {
               if (
                  expense.name === item.name &&
                  expense.frequency === item.frequency
               ) {
                  occurrenceCount++;
               }
            });
         }
      });

      return effectiveAmount * occurrenceCount;
   };

   // Calculate totals with custom amounts
   const totals = useMemo(() => {
      const totalIncome = projectionItems.income.reduce(
         (sum, item) => sum + getEffectiveTotal(item),
         0
      );
      const totalExpenses = projectionItems.expenses.reduce(
         (sum, item) => sum + getEffectiveTotal(item),
         0
      );
      const totalDebts = projectionItems.debts.reduce(
         (sum, item) => sum + getEffectiveTotal(item),
         0
      );

      return {
         income: totalIncome,
         expenses: totalExpenses,
         debts: totalDebts,
         net: totalIncome - totalExpenses - totalDebts,
      };
   }, [projectionItems, customAmounts, selectedMonths]);

   // Notify parent when projections change
   useEffect(() => {
      if (onProjectionChange) {
         onProjectionChange({
            customAmounts,
            projectionItems,
            totals,
         });
      }
   }, [customAmounts, projectionItems, totals, onProjectionChange]);

   if (
      !projectionItems.income.length &&
      !projectionItems.expenses.length &&
      !projectionItems.debts.length
   ) {
      return (
         <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
               No future projection data available.
            </p>
         </div>
      );
   }

   const renderItemRow = (item, bgColor, textColor) => (
      <tr
         key={item.id}
         className="border-b border-gray-200 dark:border-gray-700"
      >
         <td className="px-4 py-3">
            <div className="flex flex-col">
               <span className={`font-medium ${textColor}`}>{item.name}</span>
               <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFrequency(item.frequency)}
               </span>
            </div>
         </td>
         <td className="px-4 py-3 text-right">
            <span className="text-sm text-gray-600 dark:text-gray-400">
               {formatCurrency(item.originalAmount)}
            </span>
         </td>
         <td className="px-4 py-3">
            <div className="flex items-center space-x-2">
               <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={
                     customAmounts[item.id] !== undefined
                        ? customAmounts[item.id]
                        : item.originalAmount
                  }
                  onChange={(e) => handleAmountChange(item.id, e.target.value)}
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 focus:border-gray-500 dark:bg-gray-700 dark:text-white"
               />
               <button
                  onClick={() => handleReset(item.id)}
                  disabled={customAmounts[item.id] === undefined}
                  className={`px-2 py-1 text-xs rounded-md transition-colors ${
                     customAmounts[item.id] !== undefined
                        ? "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                  }`}
                  title="Reset to original amount"
               >
                  ↺
               </button>
            </div>
         </td>
         <td className="px-4 py-3 text-right">
            <span className={`font-medium ${textColor}`}>
               {formatCurrency(getEffectiveTotal(item))}
            </span>
         </td>
      </tr>
   );

   return (
      <div className="space-y-6">
         <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
               Future Projections ({selectedMonths} month
               {selectedMonths !== 1 ? "s" : ""})
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-400">
               Net:{" "}
               <span
                  className={`font-medium ${
                     totals.net >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                  }`}
               >
                  {formatCurrency(totals.net)}
               </span>
            </div>
         </div>

         <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
               <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                     <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Item
                     </th>
                     <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Original Amount
                     </th>
                     <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Custom Amount
                     </th>
                     <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Total ({selectedMonths}mo)
                     </th>
                  </tr>
               </thead>
               <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {/* Income Section */}
                  {projectionItems.income.length > 0 && (
                     <>
                        <tr className="bg-green-50 dark:bg-green-900/20">
                           <td colSpan="4" className="px-4 py-2">
                              <div className="flex items-center justify-between">
                                 <span className="text-sm font-medium text-green-800 dark:text-green-300">
                                    Income
                                 </span>
                                 <span className="text-sm font-semibold text-green-900 dark:text-green-200">
                                    {formatCurrency(totals.income)}
                                 </span>
                              </div>
                           </td>
                        </tr>
                        {projectionItems.income.map((item) =>
                           renderItemRow(
                              item,
                              "bg-green-50 dark:bg-green-900/20",
                              "text-green-800 dark:text-green-300"
                           )
                        )}
                     </>
                  )}

                  {/* Expenses Section */}
                  {projectionItems.expenses.length > 0 && (
                     <>
                        <tr className="bg-red-50 dark:bg-red-900/20">
                           <td colSpan="4" className="px-4 py-2">
                              <div className="flex items-center justify-between">
                                 <span className="text-sm font-medium text-red-800 dark:text-red-300">
                                    Expenses
                                 </span>
                                 <span className="text-sm font-semibold text-red-900 dark:text-red-200">
                                    {formatCurrency(totals.expenses)}
                                 </span>
                              </div>
                           </td>
                        </tr>
                        {projectionItems.expenses.map((item) =>
                           renderItemRow(
                              item,
                              "bg-red-50 dark:bg-red-900/20",
                              "text-red-800 dark:text-red-300"
                           )
                        )}
                     </>
                  )}

                  {/* Debts Section */}
                  {projectionItems.debts.length > 0 && (
                     <>
                        <tr className="bg-orange-50 dark:bg-orange-900/20">
                           <td colSpan="4" className="px-4 py-2">
                              <div className="flex items-center justify-between">
                                 <span className="text-sm font-medium text-orange-800 dark:text-orange-300">
                                    Debt Payments
                                 </span>
                                 <span className="text-sm font-semibold text-orange-900 dark:text-orange-200">
                                    {formatCurrency(totals.debts)}
                                 </span>
                              </div>
                           </td>
                        </tr>
                        {projectionItems.debts.map((item) =>
                           renderItemRow(
                              item,
                              "bg-orange-50 dark:bg-orange-900/20",
                              "text-orange-800 dark:text-orange-300"
                           )
                        )}
                     </>
                  )}
               </tbody>
            </table>
         </div>
      </div>
   );
}

"use client";

import { useState, useEffect } from "react";
import {
   <PERSON><PERSON><PERSON>,
   Line,
   XAxis,
   <PERSON>A<PERSON>s,
   CartesianGrid,
   Tooltip,
   ResponsiveContainer,
   ReferenceLine,
} from "recharts";
import { format, parseISO } from "date-fns";
import FutureProjectionsList from "./FutureProjectionsList";

export default function RunningBalanceChart() {
   const [data, setData] = useState([]);
   const [summary, setSummary] = useState(null);
   const [loading, setLoading] = useState(true);
   const [error, setError] = useState(null);
   const [selectedMonths, setSelectedMonths] = useState(3);
   const [selectedPastMonths, setSelectedPastMonths] = useState(1);
   const [customProjections, setCustomProjections] = useState(null);
   const [originalData, setOriginalData] = useState([]);
   const [adjustedData, setAdjustedData] = useState([]);

   useEffect(() => {
      fetchRunningBalanceData();
   }, [selectedMonths, selectedPastMonths]);

   // Recalculate chart data when custom projections change
   useEffect(() => {
      if (originalData.length > 0 && customProjections) {
         const newData = recalculateWithCustomAmounts(
            originalData,
            customProjections
         );
         setAdjustedData(newData);
      } else {
         setAdjustedData(originalData);
      }
   }, [originalData, customProjections]);

   const recalculateWithCustomAmounts = (
      originalDataArray,
      customProjections
   ) => {
      if (!customProjections || !customProjections.customAmounts) {
         return originalDataArray;
      }

      const { customAmounts, projectionItems } = customProjections;

      // Find the split point between historical/today and future data
      const todayIndex = originalDataArray.findIndex((item) => item.isToday);
      const splitIndex =
         todayIndex >= 0
            ? todayIndex + 1
            : originalDataArray.findIndex((item) => item.isFuture);

      if (splitIndex === -1) {
         return originalDataArray; // No future data to modify
      }

      // Keep historical and today data unchanged
      const unchangedData = originalDataArray.slice(0, splitIndex);
      const startingBalance =
         unchangedData.length > 0
            ? unchangedData[unchangedData.length - 1].balance
            : 0;

      // Helper function to get effective amount for an item
      const getEffectiveAmount = (itemName, frequency, originalAmount) => {
         const key = `${itemName}-${frequency}`;
         return customAmounts[key] !== undefined
            ? customAmounts[key]
            : originalAmount;
      };

      // Recalculate future data points
      const newFutureData = [];
      let runningBalance = startingBalance;

      // Get unique dates from future data
      const futureDates = originalDataArray
         .slice(splitIndex)
         .filter((item) => item.isFuture)
         .map((item) => item.date);

      futureDates.forEach((dateString) => {
         const originalDataPoint = originalDataArray.find(
            (item) => item.date === dateString
         );
         if (!originalDataPoint) return;

         let dailyIncome = 0;
         let dailyExpenses = 0;
         let incomeDetails = [];
         let expenseDetails = [];

         // Recalculate income with custom amounts
         if (originalDataPoint.incomeDetails) {
            originalDataPoint.incomeDetails.forEach((income) => {
               const effectiveAmount = getEffectiveAmount(
                  income.name,
                  income.frequency,
                  income.amount
               );
               dailyIncome += effectiveAmount;
               incomeDetails.push({
                  ...income,
                  amount: effectiveAmount,
               });
            });
         }

         // Recalculate expenses with custom amounts
         if (originalDataPoint.expenseDetails) {
            originalDataPoint.expenseDetails.forEach((expense) => {
               const effectiveAmount = getEffectiveAmount(
                  expense.name,
                  expense.frequency,
                  expense.amount
               );
               dailyExpenses += effectiveAmount;
               expenseDetails.push({
                  ...expense,
                  amount: effectiveAmount,
               });
            });
         }

         // Update running balance
         runningBalance += dailyIncome - dailyExpenses;

         // Create new data point
         newFutureData.push({
            ...originalDataPoint,
            balance: Number(runningBalance.toFixed(2)),
            income: dailyIncome,
            expenses: dailyExpenses,
            incomeDetails: incomeDetails,
            expenseDetails: expenseDetails,
         });
      });

      // Combine unchanged data with recalculated future data
      const recalculatedData = [...unchangedData, ...newFutureData];

      // Update the main data state
      setData(recalculatedData);

      // Update summary with new totals
      const newSummary = {
         ...summary,
         endingBalance:
            recalculatedData.length > 0
               ? recalculatedData[recalculatedData.length - 1].balance
               : startingBalance,
         totalProjectedIncome: customProjections.totals.income,
         totalProjectedExpenses:
            customProjections.totals.expenses + customProjections.totals.debts,
      };
      setSummary(newSummary);

      return recalculatedData;
   };

   const fetchRunningBalanceData = async () => {
      try {
         setLoading(true);
         const response = await fetch(
            `/api/reports/running-balance?months=${selectedMonths}&pastMonths=${selectedPastMonths}`
         );

         if (!response.ok) {
            throw new Error("Failed to fetch running balance data");
         }

         const result = await response.json();
         setOriginalData(result.data);
         setData(result.data);
         setSummary(result.summary);
      } catch (err) {
         console.error("Error fetching running balance data:", err);
         setError(err.message);
      } finally {
         setLoading(false);
      }
   };

   const formatCurrency = (value) => {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
         minimumFractionDigits: 2,
      }).format(value);
   };

   const formatDate = (dateString) => {
      try {
         return format(parseISO(dateString), "MMM dd");
      } catch {
         return dateString;
      }
   };

   const CustomTooltip = ({ active, payload, label }) => {
      if (active && payload && payload.length) {
         const data = payload[0].payload;
         return (
            <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded shadow-lg max-w-xs">
               <div className="flex items-center justify-between mb-2">
                  <p className="font-medium text-gray-900 dark:text-white">
                     {formatDate(label)}
                  </p>
                  {data.isHistorical && (
                     <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded">
                        Historical
                     </span>
                  )}
                  {data.isFuture && (
                     <span className="text-xs bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 px-2 py-1 rounded">
                        Projected
                     </span>
                  )}
                  {data.isToday && (
                     <span className="text-xs bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 px-2 py-1 rounded">
                        Today
                     </span>
                  )}
               </div>
               <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Balance: {formatCurrency(data.balance)}
               </p>

               {data.income > 0 && (
                  <div className="mb-2">
                     <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                        Income: +{formatCurrency(data.income)}
                     </p>
                     {data.incomeDetails && data.incomeDetails.length > 0 && (
                        <div className="ml-2 space-y-1">
                           {data.incomeDetails.map((item, index) => (
                              <p
                                 key={index}
                                 className="text-xs text-green-700 dark:text-green-300"
                              >
                                 • {item.name}: {formatCurrency(item.amount)} (
                                 {item.frequency})
                              </p>
                           ))}
                        </div>
                     )}
                  </div>
               )}

               {data.expenses > 0 && (
                  <div>
                     <p className="text-sm text-red-600 dark:text-red-400 font-medium">
                        Expenses: -{formatCurrency(data.expenses)}
                     </p>
                     {data.expenseDetails && data.expenseDetails.length > 0 && (
                        <div className="ml-2 space-y-1">
                           {data.expenseDetails.map((item, index) => (
                              <p
                                 key={index}
                                 className={`text-xs ${
                                    item.type === "debt"
                                       ? "text-orange-700 dark:text-orange-300"
                                       : "text-red-700 dark:text-red-300"
                                 }`}
                              >
                                 • {item.name}: -{formatCurrency(item.amount)} (
                                 {item.frequency})
                                 {item.type === "debt" && (
                                    <span className="ml-1 text-xs opacity-75">
                                       (debt)
                                    </span>
                                 )}
                              </p>
                           ))}
                        </div>
                     )}
                  </div>
               )}
            </div>
         );
      }
      return null;
   };

   if (loading) {
      return (
         <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
         </div>
      );
   }

   if (error) {
      return (
         <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
               <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800 dark:text-red-400">
                     Error loading chart data
                  </h3>
                  <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                     <p>{error}</p>
                  </div>
               </div>
            </div>
         </div>
      );
   }

   // Use adjusted data if available, otherwise use original data
   const chartData = adjustedData.length > 0 ? adjustedData : data;

   if (!chartData || chartData.length === 0) {
      return (
         <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
               No recurring income or expenses found to project balance.
            </p>
         </div>
      );
   }

   const minBalance = Math.min(...chartData.map((d) => d.balance));
   const maxBalance = Math.max(...chartData.map((d) => d.balance));
   const balanceRange = maxBalance - minBalance;
   const yAxisPadding = Math.max(balanceRange * 0.1, 100); // At least $100 padding

   return (
      <div className="space-y-6">
         {/* Controls */}
         <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
               Running Balance Analysis
            </h2>
            <div className="flex items-center space-x-6">
               <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                     Past Period:
                  </label>
                  <select
                     value={selectedPastMonths}
                     onChange={(e) =>
                        setSelectedPastMonths(parseInt(e.target.value))
                     }
                     className="rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                  >
                     <option value={0}>None</option>
                     <option value={1}>1 Month</option>
                     <option value={2}>2 Months</option>
                     <option value={3}>3 Months</option>
                     <option value={6}>6 Months</option>
                     <option value={12}>12 Months</option>
                  </select>
               </div>
               <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                     Future Period:
                  </label>
                  <select
                     value={selectedMonths}
                     onChange={(e) =>
                        setSelectedMonths(parseInt(e.target.value))
                     }
                     className="rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:text-white"
                  >
                     <option value={1}>1 Month</option>
                     <option value={2}>2 Months</option>
                     <option value={3}>3 Months</option>
                     <option value={6}>6 Months</option>
                     <option value={12}>12 Months</option>
                     <option value={24}>24 Months</option>
                  </select>
               </div>
            </div>
         </div>

         {/* Historical Data Info */}
         {summary &&
            summary.actualHistoricalStartDate &&
            selectedPastMonths > 0 && (
               <div className="bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md p-3 mb-4">
                  <div className="flex items-center">
                     <div className="flex-shrink-0">
                        <svg
                           className="h-5 w-5 text-gray-400"
                           viewBox="0 0 20 20"
                           fill="currentColor"
                        >
                           <path
                              fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clipRule="evenodd"
                           />
                        </svg>
                     </div>
                     <div className="ml-3">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                           Historical data starts from{" "}
                           {formatDate(summary.actualHistoricalStartDate)}
                           {summary.earliestTransactionDate &&
                           summary.actualHistoricalStartDate ===
                              summary.earliestTransactionDate
                              ? " (your first transaction)"
                              : ""}
                        </p>
                     </div>
                  </div>
               </div>
            )}

         {/* Summary Cards */}
         {summary && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
               <div className="bg-gray-50 dark:bg-gray-900/20 rounded-lg p-4">
                  <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                     Current Cash Balance
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-300">
                     {formatCurrency(summary.startingBalance)}
                  </div>
               </div>
               <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <div className="text-sm font-medium text-green-600 dark:text-green-400">
                     Projected Income
                  </div>
                  <div className="text-lg font-semibold text-green-900 dark:text-green-300">
                     {formatCurrency(summary.totalProjectedIncome)}
                  </div>
               </div>
               <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                  <div className="text-sm font-medium text-red-600 dark:text-red-400">
                     Projected Expenses
                  </div>
                  <div className="text-lg font-semibold text-red-900 dark:text-red-300">
                     {formatCurrency(summary.totalProjectedExpenses)}
                  </div>
               </div>
               <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                  <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                     Projected Balance
                  </div>
                  <div className="text-lg font-semibold text-purple-900 dark:text-purple-300">
                     {formatCurrency(summary.endingBalance)}
                  </div>
               </div>
            </div>
         )}

         {/* Chart */}
         <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
               <LineChart
                  data={chartData}
                  margin={{
                     top: 5,
                     right: 30,
                     left: 20,
                     bottom: 5,
                  }}
               >
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis
                     dataKey="date"
                     tickFormatter={formatDate}
                     interval="preserveStartEnd"
                     className="text-sm"
                  />
                  <YAxis
                     tickFormatter={formatCurrency}
                     domain={[
                        minBalance - yAxisPadding,
                        maxBalance + yAxisPadding,
                     ]}
                     className="text-sm"
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <ReferenceLine y={0} stroke="#ef4444" strokeDasharray="5 5" />
                  {/* Add a vertical line for "today" if we have historical data */}
                  {chartData.some((d) => d.isToday) && (
                     <ReferenceLine
                        x={chartData.find((d) => d.isToday)?.date}
                        stroke="#059669"
                        strokeWidth={2}
                        label={{
                           value: "Today",
                           position: "top",
                           style: {
                              fill: "#059669",
                              fontWeight: "bold",
                              fontSize: "12px",
                           },
                        }}
                     />
                  )}
                  <Line
                     type="monotone"
                     dataKey="balance"
                     stroke="#6b7280"
                     strokeWidth={2}
                     dot={false}
                     activeDot={{ r: 6, fill: "#6b7280" }}
                  />
               </LineChart>
            </ResponsiveContainer>
         </div>

         {/* Legend */}
         <div className="flex items-center justify-center space-x-6 text-sm flex-wrap gap-2">
            <div className="flex items-center">
               <div className="w-4 h-0.5 bg-gray-500 mr-2"></div>
               <span className="text-gray-600 dark:text-gray-400">
                  Running Balance
               </span>
            </div>
            <div className="flex items-center">
               <div className="w-4 h-0.5 bg-red-500 border-dashed border-t mr-2"></div>
               <span className="text-gray-600 dark:text-gray-400">
                  Zero Line
               </span>
            </div>
            {chartData.some((d) => d.isToday) && (
               <div className="flex items-center">
                  <div className="w-4 h-0.5 bg-emerald-600 mr-2"></div>
                  <span className="text-emerald-600 dark:text-emerald-400 font-medium">
                     Today
                  </span>
               </div>
            )}
            {chartData.some((d) => d.isHistorical) && (
               <div className="flex items-center">
                  <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded mr-2">
                     Historical
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                     Actual Transactions
                  </span>
               </div>
            )}
            {chartData.some((d) => d.isFuture) && (
               <div className="flex items-center">
                  <span className="text-xs bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 px-2 py-1 rounded mr-2">
                     Projected
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                     {customProjections
                        ? "Custom Projections"
                        : "Recurring Items"}
                  </span>
               </div>
            )}
         </div>

         {/* Future Projections List */}
         <FutureProjectionsList
            data={originalData}
            selectedMonths={selectedMonths}
            onProjectionChange={setCustomProjections}
         />
      </div>
   );
}

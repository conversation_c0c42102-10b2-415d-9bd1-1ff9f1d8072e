"use client";

import { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";

export function Tooltip({ children, content, className = "" }) {
   const [isVisible, setIsVisible] = useState(false);
   const [position, setPosition] = useState({ top: 0, left: 0 });
   const triggerRef = useRef(null);
   const tooltipRef = useRef(null);

   useEffect(() => {
      if (isVisible && triggerRef.current) {
         const rect = triggerRef.current.getBoundingClientRect();
         const tooltipRect = tooltipRef.current?.getBoundingClientRect();
         const tooltipWidth = tooltipRect?.width || 0;

         setPosition({
            top: rect.bottom + window.scrollY + 8,
            left:
               rect.left + window.scrollX + rect.width / 2 - tooltipWidth / 2,
         });
      }
   }, [isVisible]);

   const showTooltip = () => setIsVisible(true);
   const hideTooltip = () => setIsVisible(false);

   return (
      <div
         ref={triggerRef}
         onMouseEnter={showTooltip}
         onMouseLeave={hideTooltip}
         onFocus={showTooltip}
         onBlur={hideTooltip}
         className={`inline-block ${className}`}
      >
         {children}
         {isVisible &&
            createPortal(
               <div
                  ref={tooltipRef}
                  className="fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 dark:bg-gray-700 rounded shadow-lg whitespace-nowrap"
                  style={{
                     top: `${position.top}px`,
                     left: `${position.left}px`,
                  }}
                  role="tooltip"
               >
                  {content}
               </div>,
               document.body
            )}
      </div>
   );
}

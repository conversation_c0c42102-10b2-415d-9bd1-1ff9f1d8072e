"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { ChevronUpDownIcon } from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from "@/components/ui/command";
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from "@/components/ui/popover";

const Combobox = ({
   options,
   value,
   onChange,
   placeholder = "Search...",
   className = "",
   displayValue = (option) => option?.label || "",
   filterFunction = (option, query) =>
      option.label.toLowerCase().includes(query.toLowerCase()),
   autoFocus = false,
   onEscape = () => {},
   noResultsAction,
   position: forcedPosition,
}) => {
   const [isOpen, setIsOpen] = useState(autoFocus);
   const [query, setQuery] = useState("");
   const [highlightedIndex, setHighlightedIndex] = useState(-1);
   const comboboxRef = useRef(null);
   const inputRef = useRef(null);
   const listRef = useRef(null);
   const [position, setPosition] = useState(forcedPosition || "bottom");

   // Filter options using custom filter function
   const filteredOptions = React.useMemo(() => {
      return query === ""
         ? options
         : options.filter((option) => filterFunction(option, query));
   }, [options, query, filterFunction]);

   useEffect(() => {
      if (autoFocus) {
         // Delay to ensure component is mounted
         const timeoutId = setTimeout(() => {
            setIsOpen(true);
         }, 100);
         return () => clearTimeout(timeoutId);
      }
   }, [autoFocus]);

   useEffect(() => {
      if (isOpen) {
         const timeoutId = setTimeout(() => {
            inputRef.current?.focus();
         }, 100);
         return () => clearTimeout(timeoutId);
      }
   }, [isOpen]);

   useEffect(() => {
      if (isOpen && comboboxRef.current && !forcedPosition) {
         const rect = comboboxRef.current.getBoundingClientRect();
         const spaceBelow = window.innerHeight - rect.bottom;
         const spaceAbove = rect.top;
         const dropdownHeight = 250;

         setPosition(
            spaceBelow < dropdownHeight && spaceAbove > spaceBelow
               ? "top"
               : "bottom"
         );
      } else if (forcedPosition) {
         setPosition(forcedPosition);
      }
   }, [isOpen, forcedPosition]);

   // Only set initial selection when the dropdown first opens
   useEffect(() => {
      if (isOpen && highlightedIndex === -1 && filteredOptions.length > 0) {
         setHighlightedIndex(0);
      }
   }, [isOpen, filteredOptions.length, highlightedIndex]);

   const handleOptionSelect = useCallback(
      (option) => {
         // Close dropdown immediately to prevent reset effect
         setIsOpen(false);
         setHighlightedIndex(-1);

         if (option.value === "") {
            onChange({ value: null, label: "Unassigned" });
         } else {
            onChange(option);
         }

         // Don't reset query here - let handleOpenChange handle it
      },
      [onChange]
   );

   const handleKeyDown = useCallback(
      (e) => {
         if (!isOpen) {
            if (e.key === "ArrowDown" || e.key === "Enter" || e.key === " ") {
               e.preventDefault();
               setIsOpen(true);
               return;
            }
         }

         switch (e.key) {
            case "ArrowDown":
               e.preventDefault();
               setHighlightedIndex((prevIndex) => {
                  const newIndex =
                     prevIndex + 1 >= filteredOptions.length
                        ? 0
                        : prevIndex + 1;
                  // Scroll into view
                  setTimeout(() => {
                     const element = listRef.current?.querySelector(
                        `[data-index="${newIndex}"]`
                     );
                     element?.scrollIntoView({ block: "nearest" });
                  }, 0);
                  return newIndex;
               });
               break;

            case "ArrowUp":
               e.preventDefault();
               setHighlightedIndex((prevIndex) => {
                  const newIndex =
                     prevIndex <= 0
                        ? filteredOptions.length - 1
                        : prevIndex - 1;
                  // Scroll into view
                  setTimeout(() => {
                     const element = listRef.current?.querySelector(
                        `[data-index="${newIndex}"]`
                     );
                     element?.scrollIntoView({ block: "nearest" });
                  }, 0);
                  return newIndex;
               });
               break;

            case "Enter":
               e.preventDefault();
               if (filteredOptions.length > 0) {
                  const indexToUse =
                     highlightedIndex >= 0 ? highlightedIndex : 0;
                  handleOptionSelect(filteredOptions[indexToUse]);
               }
               break;

            case "Escape":
               e.preventDefault();
               setIsOpen(false);
               setHighlightedIndex(-1);
               // Don't reset query here - let handleOpenChange handle it
               onEscape();
               break;

            case "Tab":
               if (isOpen) {
                  e.preventDefault();
                  if (e.shiftKey) {
                     setHighlightedIndex((prevIndex) => {
                        const newIndex =
                           prevIndex <= 0
                              ? filteredOptions.length - 1
                              : prevIndex - 1;
                        // Scroll into view
                        setTimeout(() => {
                           const element = listRef.current?.querySelector(
                              `[data-index="${newIndex}"]`
                           );
                           element?.scrollIntoView({ block: "nearest" });
                        }, 0);
                        return newIndex;
                     });
                  } else {
                     setHighlightedIndex((prevIndex) => {
                        const newIndex =
                           prevIndex + 1 >= filteredOptions.length
                              ? 0
                              : prevIndex + 1;
                        // Scroll into view
                        setTimeout(() => {
                           const element = listRef.current?.querySelector(
                              `[data-index="${newIndex}"]`
                           );
                           element?.scrollIntoView({ block: "nearest" });
                        }, 0);
                        return newIndex;
                     });
                  }
               }
               break;
         }
      },
      [isOpen, filteredOptions, highlightedIndex, handleOptionSelect, onEscape]
   );

   const handleOpenChange = useCallback((open) => {
      setIsOpen(open);
      if (!open) {
         setHighlightedIndex(-1);
         // Reset query after dropdown is closed to prevent visible reset effect
         setTimeout(() => {
            setQuery("");
         }, 100); // Small delay to ensure dropdown is fully closed
      }
   }, []);

   return (
      <div className={cn("relative", className)} ref={comboboxRef}>
         <Popover open={isOpen} onOpenChange={handleOpenChange}>
            <PopoverTrigger asChild>
               <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isOpen}
                  className="h-10 w-full justify-between px-2 py-1 text-left font-normal bg-transparent border-transparent text-gray-900 dark:text-gray-100 hover:bg-gray-50/50 dark:hover:bg-gray-700/50"
                  onClick={() => {
                     if (!isOpen) {
                        setIsOpen(true);
                     }
                  }}
               >
                  <span className="truncate">
                     {value ? displayValue(value) : placeholder}
                  </span>
                  <ChevronUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
               </Button>
            </PopoverTrigger>
            <PopoverContent
               className="w-[var(--radix-popover-trigger-width)] p-0 bg-transparent border-transparent z-[60]"
               side={position === "top" ? "top" : "bottom"}
               align="start"
            >
               <Command
                  shouldFilter={false}
                  className="bg-white dark:bg-gray-800"
                  onWheel={(e) => {
                     // Prevent wheel events from bubbling up and closing the dropdown
                     e.stopPropagation();
                  }}
               >
                  <CommandInput
                     ref={inputRef}
                     placeholder={placeholder}
                     value={query}
                     onValueChange={setQuery}
                     onKeyDown={handleKeyDown}
                     className="h-9 border-0 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                  />
                  <CommandList
                     ref={listRef}
                     className="max-h-[200px] overflow-auto bg-white dark:bg-gray-800"
                     onWheel={(e) => {
                        // Prevent wheel events from bubbling up and closing the dropdown
                        e.stopPropagation();
                     }}
                  >
                     {filteredOptions.length === 0 ? (
                        <div className="space-y-1 p-2">
                           <CommandEmpty className="text-gray-500 dark:text-gray-400">
                              No results found
                           </CommandEmpty>
                           {noResultsAction && (
                              <button
                                 onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    noResultsAction.onClick(query);
                                 }}
                                 className="w-full text-left px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50 rounded-md transition-colors duration-200"
                              >
                                 + Create "{query}"
                              </button>
                           )}
                        </div>
                     ) : (
                        <CommandGroup>
                           {filteredOptions.map((option, index) => (
                              <CommandItem
                                 key={option.value}
                                 value={option.value?.toString() || ""}
                                 onSelect={() => handleOptionSelect(option)}
                                 data-index={index}
                                 className={cn(
                                    "flex justify-between items-center cursor-pointer text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700",
                                    option.value === value?.value ||
                                       option.value?.toString() ===
                                          value?.value?.toString()
                                       ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                       : "",
                                    index === highlightedIndex
                                       ? "bg-gray-100 dark:bg-gray-700"
                                       : ""
                                 )}
                              >
                                 <span className="block truncate font-medium">
                                    {displayValue(option)}
                                 </span>
                                 {option.description && (
                                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 whitespace-pre-line">
                                       {option.description}
                                    </span>
                                 )}
                              </CommandItem>
                           ))}
                        </CommandGroup>
                     )}
                  </CommandList>
               </Command>
            </PopoverContent>
         </Popover>
      </div>
   );
};

export { Combobox };

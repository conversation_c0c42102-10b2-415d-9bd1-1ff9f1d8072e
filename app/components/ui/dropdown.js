import { useState, useRef, useEffect } from "react";

export function Dropdown({ trigger, children, className = "" }) {
   const [isOpen, setIsOpen] = useState(false);
   const dropdownRef = useRef(null);

   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            dropdownRef.current &&
            !dropdownRef.current.contains(event.target)
         ) {
            setIsOpen(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, []);

   return (
      <div className={`relative ${className}`} ref={dropdownRef}>
         <div onClick={() => setIsOpen(!isOpen)}>{trigger}</div>

         {isOpen && (
            <>
               {/* Backdrop for mobile */}
               <div
                  className="fixed inset-0 z-40 lg:hidden"
                  onClick={() => setIsOpen(false)}
               />

               {/* Dropdown Menu */}
               <div
                  className="absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50 border border-gray-200 dark:border-gray-700"
                  role="dialog"
               >
                  <div className="py-1">{children}</div>
               </div>
            </>
         )}
      </div>
   );
}

export function DropdownItem({ onClick, children, className = "" }) {
   return (
      <button
         onClick={onClick}
         className={`block w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${className}`}
      >
         {children}
      </button>
   );
}

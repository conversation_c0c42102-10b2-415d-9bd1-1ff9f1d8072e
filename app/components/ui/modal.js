import React from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

const Modal = ({ isOpen, onClose, title, children }) => {
   if (!isOpen) return null;

   return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
         <div className="flex min-h-screen items-center justify-center p-4 text-center sm:p-0">
            <div
               className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
               onClick={onClose}
            ></div>

            <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 w-1/3 min-w-[400px] mx-auto">
               <div className="absolute right-0 top-0 pr-4 pt-4">
                  <button
                     type="button"
                     className="rounded-md bg-transparent text-gray-400 dark:text-gray-300 hover:text-gray-300 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800"
                     onClick={onClose}
                  >
                     <span className="sr-only">Close</span>
                     <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
               </div>

               <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                     <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                        {title}
                     </h3>
                     <div className="mt-4">{children}</div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
};

export { Modal };

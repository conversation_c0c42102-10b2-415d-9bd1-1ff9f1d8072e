"use client";

import { Dialog } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { format, addMonths, parseISO } from "date-fns";
import { formatCurrency } from "../../lib/utils/budgetUtils";
import {
   LineChart,
   Line,
   XAxis,
   YAxis,
   CartesianGrid,
   Tooltip,
   ResponsiveContainer,
   Legend,
} from "recharts";
import { useMemo, useState, useEffect } from "react";
import { useTheme } from "next-themes";

// Calculate and format the payoff status based on projection data
const calculatePayoffStatus = (chartData) => {
   // Find the start point - either the last actual balance entry or the current balance entry
   const startPoint = chartData.find(
      (d) => d.balance !== null && d.projectedBalance !== undefined
   );

   if (!startPoint) return "unknown due to missing data";

   const projectionData = chartData.filter(
      (d) => d.isProjection && d.date !== startPoint.date
   );

   // Check if debt is paid off within projection period
   const paidOffPoint = projectionData.find((d) => d.projectedBalance <= 0);

   if (paidOffPoint) {
      // Find the index to know how many months it takes
      const paidOffIndex = chartData.findIndex(
         (d) => d.date === paidOffPoint.date
      );
      const startIndex = chartData.findIndex((d) => d.date === startPoint.date);
      const monthsToPayoff = paidOffIndex - startIndex;

      // Calculate total interest paid
      let totalInterestPaid = 0;
      let runningBalance = startPoint.balance || startPoint.projectedBalance;
      const monthlyInterestRate = startPoint.apr / 100 / 12;
      const minimumPayment = startPoint.minimumPayment;

      for (let i = 0; i < monthsToPayoff; i++) {
         const monthlyInterest = runningBalance * monthlyInterestRate;
         totalInterestPaid += monthlyInterest;
         runningBalance = runningBalance + monthlyInterest - minimumPayment;
         if (runningBalance <= 0) break;
      }

      const years = Math.floor(monthsToPayoff / 12);
      const months = monthsToPayoff % 12;
      const timeString =
         years > 0
            ? `${years} year${years > 1 ? "s" : ""}${
                 months > 0
                    ? ` and ${months} month${months > 1 ? "s" : ""}`
                    : ""
              }`
            : `${months} month${months > 1 ? "s" : ""}`;

      return `paid off in approximately ${timeString} (by ${
         paidOffPoint.date
      }) with about ${formatCurrency(totalInterestPaid)} in interest paid.`;
   } else {
      // Not paid off within maximum projection period (60 years)
      return `not projected to be paid off with current minimum payments. Consider increasing your payment amount.`;
   }
};

// Helper function to get day suffix
const getDaySuffix = (day) => {
   if (!day) return "";

   const num = parseInt(day);
   if (isNaN(num)) return "";

   if (num >= 11 && num <= 13) {
      return "th";
   }

   switch (num % 10) {
      case 1:
         return "st";
      case 2:
         return "nd";
      case 3:
         return "rd";
      default:
         return "th";
   }
};

// Function to render debt type badge
const renderDebtTypeBadge = (debtType) => {
   let bgColor;
   let formattedText;

   switch (debtType.toLowerCase()) {
      case "credit card":
         bgColor = "bg-gray-600 dark:bg-gray-700";
         formattedText = "Credit Card";
         break;
      case "personal loan":
         bgColor = "bg-purple-600 dark:bg-purple-700";
         formattedText = "Personal Loan";
         break;
      case "student loan":
         bgColor = "bg-green-600 dark:bg-green-700";
         formattedText = "Student Loan";
         break;
      case "mortgage":
         bgColor = "bg-yellow-600 dark:bg-yellow-700";
         formattedText = "Mortgage";
         break;
      case "auto loan":
         bgColor = "bg-gray-600 dark:bg-gray-700";
         formattedText = "Auto Loan";
         break;
      case "medical debt":
         bgColor = "bg-red-600 dark:bg-red-700";
         formattedText = "Medical Debt";
         break;
      default:
         bgColor = "bg-gray-600 dark:bg-gray-700";
         formattedText = "Other";
   }

   return (
      <span
         className={`inline-flex items-center justify-center px-3 py-1 rounded-full ${bgColor} text-white font-medium text-xs`}
      >
         {formattedText}
      </span>
   );
};

export default function DebtHistoryModal({ debt, onClose }) {
   const [isEditMode, setIsEditMode] = useState(false);
   const [formData, setFormData] = useState({
      debtType: debt.debtType || "credit card",
      lender: debt.lender || "",
      balance: debt.balance !== undefined ? debt.balance.toString() : "",
      apr: debt.apr !== undefined ? debt.apr.toString() : "",
      minimumPayment:
         debt.minimumPayment !== undefined
            ? debt.minimumPayment.toString()
            : "",
      dueDate: debt.dueDate || "1",
      note: "",
   });
   const [error, setError] = useState("");
   const [loading, setLoading] = useState(false);
   const [viewMode, setViewMode] = useState("12months");
   const { resolvedTheme } = useTheme();
   const sortedHistory = debt.history
      ? [...debt.history].sort((a, b) => new Date(b.date) - new Date(a.date))
      : [];

   // Function to generate projection data until the debt is paid off
   const generateProjectionData = (
      lastDataPoint,
      currentBalance,
      minimumPayment,
      apr,
      dueDate
   ) => {
      const projectionData = [];
      let previousBalance = lastDataPoint?.balance || currentBalance;

      // If lastDataPoint is null (no history), determine starting projection date from due date
      let projectionDate;
      if (lastDataPoint) {
         projectionDate = new Date(lastDataPoint.date);
      } else {
         // Start from the next due date if no history
         const today = new Date();
         const currentDay = today.getDate();
         const currentMonth = today.getMonth();
         const currentYear = today.getFullYear();

         let nextDueDate = new Date(currentYear, currentMonth, dueDate || 1);

         // If due date has passed this month, move to next month
         if (dueDate < currentDay) {
            nextDueDate = new Date(currentYear, currentMonth + 1, dueDate || 1);
         }

         projectionDate = nextDueDate;
      }

      // Maximum number of months to project (60 years = 720 months) as a safety limit
      const MAX_PROJECTION_MONTHS = 720;

      // Generate projections until the debt is paid off or we hit the safety limit
      for (let i = 0; i <= MAX_PROJECTION_MONTHS; i++) {
         // Only advance the date for subsequent months (not the first projection point)
         if (i > 0) {
            projectionDate = addMonths(projectionDate, 1);
         }

         // Calculate monthly interest
         const monthlyInterestRate = apr / 100 / 12;
         const monthlyInterest = previousBalance * monthlyInterestRate;

         // Calculate new balance (previous balance + interest - minimum payment)
         let newBalance = previousBalance + monthlyInterest - minimumPayment;

         // Don't go below zero
         newBalance = Math.max(0, newBalance);

         projectionData.push({
            date: new Date(projectionDate),
            balance: null,
            projectedBalance: newBalance,
            isProjection: true,
            minimumPayment: minimumPayment,
            apr: apr,
         });

         previousBalance = newBalance;

         // Stop if the debt is paid off
         if (newBalance <= 0) break;
      }

      return projectionData;
   };

   // Prepare chart data - sort chronologically (oldest first) for the chart
   const chartData = useMemo(() => {
      if (!debt) return [];

      // Group history entries by date (day only)
      const entriesByDay = {};

      // Process history entries if they exist
      if (sortedHistory && sortedHistory.length > 0) {
         sortedHistory.forEach((entry) => {
            const date = new Date(entry.date);
            const dateKey = format(date, "yyyy-MM-dd"); // Use date string as key

            // If this date doesn't exist or entry is more recent, use this entry
            if (
               !entriesByDay[dateKey] ||
               new Date(entry.date) > new Date(entriesByDay[dateKey].date)
            ) {
               entriesByDay[dateKey] = entry;
            }
         });
      }

      // Convert the grouped entries to an array
      const uniqueDayEntries = Object.values(entriesByDay);

      // Create chart data points from the unique day entries
      const dataPoints = uniqueDayEntries.map((entry) => ({
         date: new Date(entry.date),
         balance: entry.newBalance,
         minimumPayment: entry.newMinimumPayment,
         apr: entry.newAPR,
         isProjection: false,
      }));

      // Sort chronologically (oldest to newest) for the chart
      const sortedData = dataPoints.sort((a, b) => a.date - b.date);

      // Add projection data
      let lastActualPoint = null;

      if (sortedData.length > 0) {
         // Get the last actual data point
         lastActualPoint = sortedData[sortedData.length - 1];

         // Modify the last actual point to also include projected value
         // This creates a seamless transition between actual and projected lines
         lastActualPoint.projectedBalance = lastActualPoint.balance;
      }

      // Generate projection data
      const projectionData = generateProjectionData(
         lastActualPoint,
         debt.balance,
         debt.minimumPayment,
         debt.apr,
         debt.dueDate
      );

      // If we have actual data points already, add the projections
      if (sortedData.length > 0) {
         // For projection points, only set projectedBalance, not balance
         // This ensures the actual balance line ends at the last real data point
         projectionData.forEach((point) => {
            point.balance = null; // Set to null so the actual line doesn't continue
         });

         // Add the projection data
         sortedData.push(...projectionData);
      } else {
         // If no history data, we need to create a starting point for the chart with current balance
         // and then add the projections
         const today = new Date();
         sortedData.push({
            date: today,
            balance: debt.balance,
            projectedBalance: debt.balance,
            minimumPayment: debt.minimumPayment,
            apr: debt.apr,
            isProjection: false,
         });

         // Add projection data
         sortedData.push(...projectionData);
      }

      // Format dates for display
      const allData = sortedData.map((item) => ({
         ...item,
         date: format(item.date, "MMM d, yyyy"),
      }));

      // Apply view mode filter - only show 12 months if in 12months mode
      if (viewMode === "12months") {
         // If allData contains more than 12 months of data
         if (allData.length > 12) {
            return allData.slice(0, 13); // First point plus 12 months (13 total data points)
         }
      }

      return allData;
   }, [sortedHistory, debt, viewMode]);

   // Custom tooltip for the chart
   const CustomTooltip = ({ active, payload, label }) => {
      if (active && payload && payload.length) {
         const dataPoint = payload[0].payload;

         // Determine what to display based on the data point
         // For the last actual point (which has both balance and projectedBalance),
         // prioritize showing the actual balance
         const isLastActualPoint =
            dataPoint.balance !== null &&
            dataPoint.projectedBalance !== undefined;

         return (
            <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded shadow-lg">
               <p className="font-medium text-gray-900 dark:text-gray-100">
                  {label}
               </p>
               {isLastActualPoint ? (
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                     Actual Balance: {formatCurrency(dataPoint.balance)}
                  </p>
               ) : dataPoint.isProjection ||
                 dataPoint.projectedBalance !== undefined ? (
                  <p className="text-sm text-green-600 dark:text-green-400">
                     Projected Balance:{" "}
                     {formatCurrency(dataPoint.projectedBalance)}
                  </p>
               ) : (
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                     Balance: {formatCurrency(dataPoint.balance)}
                  </p>
               )}
            </div>
         );
      }
      return null;
   };

   const handleInputChange = (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));
      if (error) setError("");
   };

   const handleNumberChange = (e) => {
      const { name, value } = e.target;
      // Allow empty values, numbers, and decimal points
      setFormData((prev) => ({ ...prev, [name]: value }));
      if (error) setError("");
   };

   const validateForm = () => {
      if (!formData.lender.trim()) {
         setError("Lender name is required");
         return false;
      }

      // Allow any input for balance, but validate before submission
      const balance = parseFloat(formData.balance.replace(/[^\d.]/g, ""));
      if (isNaN(balance) || balance < 0) {
         setError("Please enter a valid balance");
         return false;
      }

      // Allow any input for APR, but validate before submission
      const apr = parseFloat(formData.apr.replace(/[^\d.]/g, ""));
      if (isNaN(apr) || apr < 0) {
         setError("Please enter a valid APR percentage");
         return false;
      }

      // Allow any input for minimum payment, but validate before submission
      const minimumPayment = parseFloat(
         formData.minimumPayment.replace(/[^\d.]/g, "")
      );
      if (isNaN(minimumPayment) || minimumPayment <= 0) {
         setError("Please enter a valid minimum payment amount");
         return false;
      }

      if (
         !formData.dueDate ||
         isNaN(parseInt(formData.dueDate)) ||
         parseInt(formData.dueDate) < 1 ||
         parseInt(formData.dueDate) > 31
      ) {
         setError("Please enter a valid due date (1-31)");
         return false;
      }
      return true;
   };

   const handleSave = async () => {
      if (!validateForm()) {
         return;
      }

      setLoading(true);
      try {
         const updatedDebt = {
            _id: debt._id,
            ...formData,
            balance: parseFloat(formData.balance.replace(/[^\d.]/g, "")),
            apr: parseFloat(formData.apr.replace(/[^\d.]/g, "")),
            minimumPayment: parseFloat(
               formData.minimumPayment.replace(/[^\d.]/g, "")
            ),
         };

         const response = await fetch("/api/user/debts", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt: updatedDebt }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update debt");
         }

         window.location.reload();
      } catch (err) {
         console.error("Error updating debt:", err);
         setError(err.message || "Something went wrong. Please try again.");
      } finally {
         setLoading(false);
      }
   };

   const resetForm = () => {
      setFormData({
         debtType: debt.debtType || "credit card",
         lender: debt.lender || "",
         balance: debt.balance !== undefined ? debt.balance.toString() : "",
         apr: debt.apr !== undefined ? debt.apr.toString() : "",
         minimumPayment:
            debt.minimumPayment !== undefined
               ? debt.minimumPayment.toString()
               : "",
         dueDate: debt.dueDate || "1",
         note: "",
      });
      setError("");
   };

   const handleCancelEdit = () => {
      setIsEditMode(false);
      resetForm();
   };

   return (
      <Dialog as="div" className="relative z-50" open={true} onClose={onClose}>
         <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

         <div className="fixed inset-0 flex w-screen items-center justify-center p-6">
            <Dialog.Panel className="mx-auto w-[70%] max-w-6xl transform rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-xl transition-all max-h-[90vh] flex flex-col">
               <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                  <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white">
                     {debt.lender} - Debt Details
                  </Dialog.Title>
                  <button
                     type="button"
                     className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                     onClick={onClose}
                  >
                     <span className="sr-only">Close</span>
                     <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
               </div>

               <div className="overflow-y-auto p-6 flex-1">
                  {error && (
                     <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded">
                        {error}
                     </div>
                  )}

                  <div
                     className={`flex flex-wrap items-start mb-4 gap-3 ${
                        isEditMode
                           ? "border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50 shadow-sm"
                           : ""
                     }`}
                  >
                     {isEditMode ? (
                        <>
                           <div className="w-full mb-3">
                              <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                                 Edit Debt Information
                              </h3>
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 Debt Type
                              </label>
                              <select
                                 name="debtType"
                                 value={formData.debtType}
                                 onChange={handleInputChange}
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 required
                              >
                                 <option value="credit card">
                                    Credit Card
                                 </option>
                                 <option value="personal loan">
                                    Personal Loan
                                 </option>
                                 <option value="student loan">
                                    Student Loan
                                 </option>
                                 <option value="mortgage">Mortgage</option>
                                 <option value="auto loan">Auto Loan</option>
                                 <option value="medical debt">
                                    Medical Debt
                                 </option>
                                 <option value="other">Other</option>
                              </select>
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 Lender Name
                              </label>
                              <input
                                 type="text"
                                 name="lender"
                                 value={formData.lender}
                                 onChange={handleInputChange}
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 placeholder="Chase, Bank of America, etc."
                                 required
                              />
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 Current Balance ($)
                              </label>
                              <input
                                 type="text"
                                 name="balance"
                                 value={formData.balance}
                                 onChange={handleNumberChange}
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 placeholder="5000.00"
                                 required
                              />
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 Minimum Payment ($)
                              </label>
                              <input
                                 type="text"
                                 name="minimumPayment"
                                 value={formData.minimumPayment}
                                 onChange={handleNumberChange}
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 placeholder="100.00"
                                 required
                              />
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 APR (%)
                              </label>
                              <input
                                 type="text"
                                 name="apr"
                                 value={formData.apr}
                                 onChange={handleNumberChange}
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 placeholder="19.99"
                                 required
                              />
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 Due Date (day of month, 1-31)
                              </label>
                              <input
                                 type="number"
                                 name="dueDate"
                                 value={formData.dueDate}
                                 onChange={handleInputChange}
                                 min="1"
                                 max="31"
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 placeholder="15"
                                 required
                              />
                           </div>
                           <div className="flex-1 min-w-[250px] mb-6">
                              <label className="block text-sm text-gray-900 dark:text-gray-100 mb-1">
                                 Note (for this change)
                              </label>
                              <input
                                 type="text"
                                 name="note"
                                 value={formData.note}
                                 onChange={handleInputChange}
                                 className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                                 placeholder="e.g., Made a payment, Interest rate change, etc."
                              />
                           </div>
                        </>
                     ) : (
                        <>
                           <div className="flex-1 min-w-[180px] mb-4">
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                                 Debt Type
                              </div>
                              <div className="font-medium text-sm">
                                 {renderDebtTypeBadge(debt.debtType)}
                              </div>
                           </div>
                           <div className="flex-1 min-w-[180px] mb-4">
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                                 Current Balance
                              </div>
                              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                 {formatCurrency(debt.balance)}
                              </div>
                           </div>
                           <div className="flex-1 min-w-[180px] mb-4">
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                                 Current Minimum Payment
                              </div>
                              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                 {formatCurrency(debt.minimumPayment)}
                              </div>
                           </div>
                           <div className="flex-1 min-w-[130px] mb-4">
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                                 Current APR
                              </div>
                              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                 {debt.apr}%
                              </div>
                           </div>
                           <div className="flex-1 min-w-[180px] mb-4">
                              <div className="text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                                 Due Date
                              </div>
                              <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                 {debt.dueDate
                                    ? `${debt.dueDate}${getDaySuffix(
                                         debt.dueDate
                                      )}`
                                    : "-"}
                              </div>
                           </div>
                        </>
                     )}
                  </div>

                  {!isEditMode && (
                     <div className="flex justify-end mb-4">
                        <button
                           type="button"
                           onClick={() => setIsEditMode(true)}
                           className="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        >
                           Update
                        </button>
                     </div>
                  )}

                  {isEditMode && (
                     <div className="flex justify-end mb-6 space-x-3">
                        <button
                           type="button"
                           onClick={handleCancelEdit}
                           className="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                           disabled={loading}
                        >
                           Cancel
                        </button>
                        <button
                           type="button"
                           onClick={handleSave}
                           disabled={loading}
                           className="inline-flex justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:bg-primary-400 disabled:cursor-not-allowed"
                        >
                           {loading ? "Saving..." : "Save"}
                        </button>
                     </div>
                  )}

                  {/* Payoff Projection Summary */}
                  {chartData.length > 1 && (
                     <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg mb-6">
                        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">
                           Payoff Projection
                        </h4>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                           If you continue making minimum payments of{" "}
                           {formatCurrency(debt.minimumPayment)} per month with
                           the current APR of {debt.apr}%, your debt will be{" "}
                           {calculatePayoffStatus(chartData)}
                        </p>
                     </div>
                  )}

                  {/* Balance Over Time Chart */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mb-6">
                     <div className="flex justify-between items-center mb-4">
                        <h3 className="text-md font-medium text-gray-900 dark:text-gray-100">
                           Balance Over Time
                        </h3>

                        {/* View Mode Toggle */}
                        <div className="flex items-center space-x-2">
                           <span className="text-sm text-gray-500 dark:text-gray-400">
                              View:
                           </span>
                           <div
                              className="flex rounded-md shadow-sm"
                              role="group"
                           >
                              <button
                                 onClick={() => setViewMode("12months")}
                                 className={`px-3 py-1 text-sm font-medium rounded-l-md border transition-colors ${
                                    viewMode === "12months"
                                       ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                                       : "bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
                                 }`}
                              >
                                 12 Months
                              </button>
                              <button
                                 onClick={() => setViewMode("full")}
                                 className={`px-3 py-1 text-sm font-medium rounded-r-md border transition-colors ${
                                    viewMode === "full"
                                       ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                                       : "bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
                                 }`}
                              >
                                 Full Projection
                              </button>
                           </div>
                        </div>
                     </div>

                     {chartData.length > 1 ? (
                        <>
                           {sortedHistory.length === 0 && (
                              <div className="mb-2 text-sm text-gray-600 dark:text-gray-400">
                                 No history data available yet. Showing
                                 projection starting from the next payment due
                                 date.
                              </div>
                           )}
                           <div className="h-64 w-full">
                              <ResponsiveContainer width="100%" height="100%">
                                 <LineChart
                                    data={chartData}
                                    margin={{
                                       top: 5,
                                       right: 20,
                                       left: 0,
                                       bottom: 5,
                                    }}
                                    connectNulls={false}
                                 >
                                    <CartesianGrid
                                       strokeDasharray="3 3"
                                       stroke={
                                          resolvedTheme === "dark"
                                             ? "#374151"
                                             : "#E5E7EB"
                                       }
                                    />
                                    <XAxis
                                       dataKey="date"
                                       tick={{
                                          fontSize: 12,
                                          fill:
                                             resolvedTheme === "dark"
                                                ? "#D1D5DB"
                                                : "#374151",
                                       }}
                                       axisLine={{
                                          stroke:
                                             resolvedTheme === "dark"
                                                ? "#6B7280"
                                                : "#9CA3AF",
                                       }}
                                       tickLine={{
                                          stroke:
                                             resolvedTheme === "dark"
                                                ? "#6B7280"
                                                : "#9CA3AF",
                                       }}
                                       padding={{ left: 30, right: 30 }}
                                    />
                                    <YAxis
                                       tickFormatter={(value) =>
                                          formatCurrency(value)
                                       }
                                       width={80}
                                       tick={{
                                          fontSize: 12,
                                          fill:
                                             resolvedTheme === "dark"
                                                ? "#D1D5DB"
                                                : "#374151",
                                       }}
                                       axisLine={{
                                          stroke:
                                             resolvedTheme === "dark"
                                                ? "#6B7280"
                                                : "#9CA3AF",
                                       }}
                                       tickLine={{
                                          stroke:
                                             resolvedTheme === "dark"
                                                ? "#6B7280"
                                                : "#9CA3AF",
                                       }}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend
                                       wrapperStyle={{
                                          color:
                                             resolvedTheme === "dark"
                                                ? "#D1D5DB"
                                                : "#374151",
                                       }}
                                    />
                                    <Line
                                       type="monotone"
                                       dataKey="balance"
                                       name="Actual Balance"
                                       stroke={
                                          resolvedTheme === "dark"
                                             ? "#9CA3AF"
                                             : "#6B7280"
                                       }
                                       strokeWidth={2}
                                       dot={{ r: 4 }}
                                       activeDot={{ r: 6 }}
                                       isAnimationActive={false}
                                       connectNulls={false}
                                    />
                                    <Line
                                       type="monotone"
                                       dataKey="projectedBalance"
                                       name="Projected Balance"
                                       stroke={
                                          resolvedTheme === "dark"
                                             ? "#34D399"
                                             : "#10B981"
                                       }
                                       strokeWidth={2}
                                       strokeDasharray="5 5"
                                       dot={{ r: 3 }}
                                       activeDot={{ r: 5 }}
                                       isAnimationActive={false}
                                       connectNulls={false}
                                    />
                                 </LineChart>
                              </ResponsiveContainer>
                           </div>
                           <div className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
                              {viewMode === "12months"
                                 ? "Showing next 12 months of projection"
                                 : "Showing full debt payoff projection"}
                           </div>
                        </>
                     ) : (
                        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                           Not enough data to display a chart.
                        </div>
                     )}
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                     <h3 className="text-md font-medium mb-4 text-gray-900 dark:text-gray-100">
                        History
                     </h3>

                     {sortedHistory.length === 0 ? (
                        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                           No history records found for this debt.
                        </div>
                     ) : (
                        <div className="overflow-x-auto">
                           <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                              <thead className="bg-gray-50 dark:bg-gray-800">
                                 <tr>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                       Date
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                       Balance Change
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                       Min Payment Change
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                       APR Change
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                       Due Date Change
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                       Note
                                    </th>
                                 </tr>
                              </thead>
                              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                                 {sortedHistory.map((entry, index) => (
                                    <tr
                                       key={index}
                                       className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                                    >
                                       <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                          {format(
                                             new Date(entry.date),
                                             "MMM d, yyyy"
                                          )}
                                       </td>
                                       <td className="px-4 py-2 whitespace-nowrap text-sm">
                                          <div className="flex flex-col">
                                             <span
                                                className={`font-medium ${
                                                   entry.newBalance <
                                                   entry.oldBalance
                                                      ? "text-green-600 dark:text-green-400"
                                                      : entry.newBalance >
                                                        entry.oldBalance
                                                      ? "text-red-600 dark:text-red-400"
                                                      : "text-gray-600 dark:text-gray-400"
                                                }`}
                                             >
                                                {entry.newBalance <
                                                entry.oldBalance
                                                   ? "▼"
                                                   : entry.newBalance >
                                                     entry.oldBalance
                                                   ? "▲"
                                                   : "■"}
                                                {formatCurrency(
                                                   Math.abs(
                                                      entry.newBalance -
                                                         entry.oldBalance
                                                   )
                                                )}
                                             </span>
                                             <span className="text-xs text-gray-500 dark:text-gray-400">
                                                From{" "}
                                                {formatCurrency(
                                                   entry.oldBalance
                                                )}{" "}
                                                to{" "}
                                                {formatCurrency(
                                                   entry.newBalance
                                                )}
                                             </span>
                                          </div>
                                       </td>
                                       <td className="px-4 py-2 whitespace-nowrap text-sm">
                                          <div className="flex flex-col">
                                             <span
                                                className={`font-medium ${
                                                   entry.newMinimumPayment <
                                                   entry.oldMinimumPayment
                                                      ? "text-green-600 dark:text-green-400"
                                                      : entry.newMinimumPayment >
                                                        entry.oldMinimumPayment
                                                      ? "text-red-600 dark:text-red-400"
                                                      : "text-gray-600 dark:text-gray-400"
                                                }`}
                                             >
                                                {entry.newMinimumPayment <
                                                entry.oldMinimumPayment
                                                   ? "▼"
                                                   : entry.newMinimumPayment >
                                                     entry.oldMinimumPayment
                                                   ? "▲"
                                                   : "■"}
                                                {formatCurrency(
                                                   Math.abs(
                                                      entry.newMinimumPayment -
                                                         entry.oldMinimumPayment
                                                   )
                                                )}
                                             </span>
                                             <span className="text-xs text-gray-500 dark:text-gray-400">
                                                From{" "}
                                                {formatCurrency(
                                                   entry.oldMinimumPayment
                                                )}{" "}
                                                to{" "}
                                                {formatCurrency(
                                                   entry.newMinimumPayment
                                                )}
                                             </span>
                                          </div>
                                       </td>
                                       <td className="px-4 py-2 whitespace-nowrap text-sm">
                                          <div className="flex flex-col">
                                             <span
                                                className={`font-medium ${
                                                   entry.newAPR < entry.oldAPR
                                                      ? "text-green-600 dark:text-green-400"
                                                      : entry.newAPR >
                                                        entry.oldAPR
                                                      ? "text-red-600 dark:text-red-400"
                                                      : "text-gray-600 dark:text-gray-400"
                                                }`}
                                             >
                                                {entry.newAPR < entry.oldAPR
                                                   ? "▼"
                                                   : entry.newAPR > entry.oldAPR
                                                   ? "▲"
                                                   : "■"}
                                                {Math.abs(
                                                   entry.newAPR - entry.oldAPR
                                                ).toFixed(2)}
                                                %
                                             </span>
                                             <span className="text-xs text-gray-500 dark:text-gray-400">
                                                From {entry.oldAPR}% to{" "}
                                                {entry.newAPR}%
                                             </span>
                                          </div>
                                       </td>
                                       <td className="px-4 py-2 whitespace-nowrap text-sm">
                                          {entry.oldDueDate &&
                                          entry.newDueDate ? (
                                             <div className="flex flex-col">
                                                <span
                                                   className={`font-medium ${
                                                      entry.newDueDate !==
                                                      entry.oldDueDate
                                                         ? "text-yellow-600 dark:text-yellow-400"
                                                         : "text-gray-600 dark:text-gray-400"
                                                   }`}
                                                >
                                                   {entry.newDueDate !==
                                                   entry.oldDueDate
                                                      ? "▲"
                                                      : "■"}
                                                   {entry.newDueDate !==
                                                   entry.oldDueDate
                                                      ? "Changed"
                                                      : "No Change"}
                                                </span>
                                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                                   From {entry.oldDueDate}
                                                   {getDaySuffix(
                                                      entry.oldDueDate
                                                   )}{" "}
                                                   to {entry.newDueDate}
                                                   {getDaySuffix(
                                                      entry.newDueDate
                                                   )}
                                                </span>
                                             </div>
                                          ) : (
                                             <span className="text-gray-500 dark:text-gray-400">
                                                -
                                             </span>
                                          )}
                                       </td>
                                       <td className="px-4 py-2 text-sm text-gray-900 dark:text-gray-100">
                                          {entry.note || "Balance update"}
                                       </td>
                                    </tr>
                                 ))}
                              </tbody>
                           </table>
                        </div>
                     )}
                  </div>
               </div>

               <div className="p-6 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex justify-end">
                     {isEditMode ? (
                        <>
                           <button
                              type="button"
                              onClick={handleCancelEdit}
                              className="mr-2 inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                              disabled={loading}
                           >
                              Cancel
                           </button>
                           <button
                              type="button"
                              onClick={handleSave}
                              disabled={loading}
                              className="inline-flex justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:bg-primary-400 disabled:cursor-not-allowed"
                           >
                              {loading ? "Saving..." : "Save"}
                           </button>
                        </>
                     ) : (
                        <button
                           type="button"
                           onClick={onClose}
                           className="inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        >
                           Close
                        </button>
                     )}
                  </div>
               </div>
            </Dialog.Panel>
         </div>
      </Dialog>
   );
}

"use client";

import { useState } from "react";
import { Dialog } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";

export default function AddDebtModal({ onClose, onSave }) {
   const [formData, setFormData] = useState({
      debtType: "credit card",
      lender: "",
      balance: "",
      apr: "",
      minimumPayment: "",
      dueDate: "1",
   });
   const [error, setError] = useState("");
   const [loading, setLoading] = useState(false);

   const handleInputChange = (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));

      // Clear error when user starts typing
      if (error) setError("");
   };

   const handleNumberChange = (e) => {
      const { name, value } = e.target;
      // Allow only numbers and decimal point
      if (value === "" || /^\d*\.?\d*$/.test(value)) {
         setFormData((prev) => ({ ...prev, [name]: value }));
      }

      // Clear error when user starts typing
      if (error) setError("");
   };

   const validateForm = () => {
      if (!formData.lender.trim()) {
         setError("Lender name is required");
         return false;
      }

      if (
         !formData.balance ||
         isNaN(parseFloat(formData.balance)) ||
         parseFloat(formData.balance) <= 0
      ) {
         setError("Please enter a valid balance");
         return false;
      }

      if (
         !formData.apr ||
         isNaN(parseFloat(formData.apr)) ||
         parseFloat(formData.apr) < 0
      ) {
         setError("Please enter a valid APR percentage");
         return false;
      }

      if (
         !formData.minimumPayment ||
         isNaN(parseFloat(formData.minimumPayment)) ||
         parseFloat(formData.minimumPayment) <= 0
      ) {
         setError("Please enter a valid minimum payment amount");
         return false;
      }

      if (
         !formData.dueDate ||
         isNaN(parseInt(formData.dueDate)) ||
         parseInt(formData.dueDate) < 1 ||
         parseInt(formData.dueDate) > 31
      ) {
         setError("Please enter a valid due date (1-31)");
         return false;
      }

      return true;
   };

   const handleSubmit = async (e) => {
      e.preventDefault();

      if (!validateForm() || loading) {
         return;
      }

      setLoading(true);

      try {
         const debt = {
            ...formData,
            balance: parseFloat(formData.balance),
            apr: parseFloat(formData.apr),
            minimumPayment: parseFloat(formData.minimumPayment),
         };

         // Call API to add debt
         const response = await fetch("/api/user/debts", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to add debt");
         }

         const result = await response.json();

         // Validate that we received a debt with an _id
         if (!result.debt || !result.debt._id) {
            throw new Error("Server did not return a valid debt object");
         }

         // Reset form data after successful submission
         setFormData({
            debtType: "credit card",
            lender: "",
            balance: "",
            apr: "",
            minimumPayment: "",
            dueDate: "1",
         });

         onSave(result.debt);
      } catch (err) {
         console.error("Error adding debt:", err);
         setError(err.message || "Something went wrong. Please try again.");
      } finally {
         setLoading(false);
      }
   };

   return (
      <Dialog as="div" className="relative z-50" open={true} onClose={onClose}>
         <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

         <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
            <Dialog.Panel className="mx-auto w-full max-w-md transform rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 p-6 shadow-xl transition-all">
               <div className="flex items-center justify-between mb-4">
                  <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white">
                     Add New Debt
                  </Dialog.Title>
                  <button
                     type="button"
                     className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                     onClick={onClose}
                  >
                     <span className="sr-only">Close</span>
                     <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
               </div>

               {error && (
                  <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded">
                     {error}
                  </div>
               )}

               <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Debt Type
                     </label>
                     <select
                        name="debtType"
                        value={formData.debtType}
                        onChange={handleInputChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                     >
                        <option value="credit card">Credit Card</option>
                        <option value="personal loan">Personal Loan</option>
                        <option value="student loan">Student Loan</option>
                        <option value="mortgage">Mortgage</option>
                        <option value="auto loan">Auto Loan</option>
                        <option value="medical debt">Medical Debt</option>
                        <option value="other">Other</option>
                     </select>
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Lender Name
                     </label>
                     <input
                        type="text"
                        name="lender"
                        value={formData.lender}
                        onChange={handleInputChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="Chase, Bank of America, etc."
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Current Balance ($)
                     </label>
                     <input
                        type="text"
                        name="balance"
                        value={formData.balance}
                        onChange={handleNumberChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="5000.00"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        APR (%)
                     </label>
                     <input
                        type="text"
                        name="apr"
                        value={formData.apr}
                        onChange={handleNumberChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="19.99"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Minimum Payment ($)
                     </label>
                     <input
                        type="text"
                        name="minimumPayment"
                        value={formData.minimumPayment}
                        onChange={handleNumberChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="100.00"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Due Date (day of month, 1-31)
                     </label>
                     <input
                        type="number"
                        name="dueDate"
                        value={formData.dueDate}
                        onChange={handleInputChange}
                        min="1"
                        max="31"
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="15"
                        required
                     />
                  </div>

                  <div className="flex justify-end pt-4">
                     <button
                        type="button"
                        onClick={onClose}
                        className="mr-2 inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                     >
                        Cancel
                     </button>
                     <button
                        type="submit"
                        disabled={loading}
                        className="inline-flex justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:bg-primary-400 disabled:cursor-not-allowed"
                     >
                        {loading ? "Saving..." : "Save"}
                     </button>
                  </div>
               </form>
            </Dialog.Panel>
         </div>
      </Dialog>
   );
}

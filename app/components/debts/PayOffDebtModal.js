"use client";

import { useState } from "react";
import { Dialog } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { formatCurrency } from "../../lib/utils/budgetUtils";

export default function PayOffDebtModal({ debt, onClose, onSave }) {
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState("");
   const [showConfirmation, setShowConfirmation] = useState(false);
   const [closeAccount, setCloseAccount] = useState(true);

   const handlePayOff = () => {
      setShowConfirmation(true);
   };

   const handleConfirm = async () => {
      setLoading(true);
      setError("");

      try {
         const updatedDebt = {
            ...debt,
            balance: 0,
            active: closeAccount ? false : true,
            note: closeAccount
               ? "Account closed - debt paid off"
               : "Debt paid off - account kept active",
         };

         const response = await fetch("/api/user/debts", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt: updatedDebt }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to pay off debt");
         }

         const result = await response.json();
         onSave(result.debt);
      } catch (err) {
         console.error("Error paying off debt:", err);
         setError(err.message || "Something went wrong. Please try again.");
      } finally {
         setLoading(false);
      }
   };

   if (showConfirmation) {
      return (
         <Dialog
            as="div"
            className="relative z-50"
            open={true}
            onClose={() => {}}
         >
            <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

            <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
               <Dialog.Panel className="mx-auto w-full max-w-md transform rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 p-6 shadow-xl transition-all">
                  <div className="mb-6">
                     <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        🎉 Congratulations! Debt Paid Off!
                     </Dialog.Title>
                     <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        You're about to mark <strong>{debt.lender}</strong> as
                        fully paid off. The balance of{" "}
                        <strong>{formatCurrency(debt.balance)}</strong> will be
                        set to $0.00.
                     </p>

                     <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
                        <label className="flex items-start space-x-3">
                           <input
                              type="checkbox"
                              checked={closeAccount}
                              onChange={(e) =>
                                 setCloseAccount(e.target.checked)
                              }
                              className="mt-0.5 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                           />
                           <div>
                              <span className="text-sm font-medium text-gray-900 dark:text-white">
                                 Close this account
                              </span>
                              <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                                 Mark the account as inactive. You can
                                 reactivate it later if needed.
                              </p>
                           </div>
                        </label>
                     </div>

                     {error && (
                        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded text-sm">
                           {error}
                        </div>
                     )}
                  </div>

                  <div className="flex space-x-3">
                     <button
                        type="button"
                        className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        onClick={() => setShowConfirmation(false)}
                        disabled={loading}
                     >
                        Cancel
                     </button>
                     <button
                        type="button"
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:opacity-50"
                        onClick={handleConfirm}
                        disabled={loading}
                     >
                        {loading ? "Processing..." : "Confirm Pay Off"}
                     </button>
                  </div>
               </Dialog.Panel>
            </div>
         </Dialog>
      );
   }

   return (
      <Dialog as="div" className="relative z-50" open={true} onClose={onClose}>
         <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

         <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
            <Dialog.Panel className="mx-auto w-full max-w-md transform rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 p-6 shadow-xl transition-all">
               <div className="flex items-center justify-between mb-4">
                  <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white">
                     Pay Off Debt
                  </Dialog.Title>
                  <button
                     type="button"
                     className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                     onClick={onClose}
                  >
                     <span className="sr-only">Close</span>
                     <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
               </div>

               <div className="mb-6">
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-4">
                     <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
                        {debt.lender}
                     </h3>
                     <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                           <span className="text-green-700 dark:text-green-300">
                              Current Balance:
                           </span>
                           <span className="font-semibold text-green-800 dark:text-green-200">
                              {formatCurrency(debt.balance)}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-green-700 dark:text-green-300">
                              Debt Type:
                           </span>
                           <span className="font-medium text-green-800 dark:text-green-200 capitalize">
                              {debt.debtType}
                           </span>
                        </div>
                        <div className="flex justify-between">
                           <span className="text-green-700 dark:text-green-300">
                              APR:
                           </span>
                           <span className="font-medium text-green-800 dark:text-green-200">
                              {debt.apr}%
                           </span>
                        </div>
                     </div>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                     Are you ready to pay off this debt? This will set the
                     balance to $0.00 and you'll have the option to close the
                     account.
                  </p>
               </div>

               <div className="flex space-x-3">
                  <button
                     type="button"
                     className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                     onClick={onClose}
                  >
                     Cancel
                  </button>
                  <button
                     type="button"
                     className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                     onClick={handlePayOff}
                  >
                     💳 Pay Off Debt
                  </button>
               </div>
            </Dialog.Panel>
         </div>
      </Dialog>
   );
}

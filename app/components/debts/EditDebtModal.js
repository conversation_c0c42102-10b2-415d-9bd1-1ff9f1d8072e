"use client";

import { useState, useEffect } from "react";
import { Dialog } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";

export default function EditDebtModal({ debt, onClose, onSave }) {
   const [formData, setFormData] = useState({
      _id: debt._id,
      debtType: debt.debtType || "credit card",
      lender: debt.lender || "",
      balance: debt.balance !== undefined ? debt.balance.toString() : "",
      apr: debt.apr !== undefined ? debt.apr.toString() : "",
      minimumPayment:
         debt.minimumPayment !== undefined
            ? debt.minimumPayment.toString()
            : "",
      dueDate: debt.dueDate || "1",
      note: "",
   });
   const [error, setError] = useState("");
   const [loading, setLoading] = useState(false);
   const [showCloseAccountDialog, setShowCloseAccountDialog] = useState(false);
   const [pendingDebtUpdate, setPendingDebtUpdate] = useState(null);

   // Update form data if debt prop changes
   useEffect(() => {
      setFormData({
         _id: debt._id,
         debtType: debt.debtType || "credit card",
         lender: debt.lender || "",
         balance: debt.balance !== undefined ? debt.balance.toString() : "",
         apr: debt.apr !== undefined ? debt.apr.toString() : "",
         minimumPayment:
            debt.minimumPayment !== undefined
               ? debt.minimumPayment.toString()
               : "",
         dueDate: debt.dueDate || "1",
         note: "",
      });
   }, [debt]);

   const handleInputChange = (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));

      // Clear error when user starts typing
      if (error) setError("");
   };

   const handleNumberChange = (e) => {
      const { name, value } = e.target;
      // Allow only numbers and decimal point
      if (value === "" || /^\d*\.?\d*$/.test(value)) {
         setFormData((prev) => ({ ...prev, [name]: value }));
      }

      // Clear error when user starts typing
      if (error) setError("");
   };

   const handlePayOffDebt = () => {
      setFormData((prev) => ({ ...prev, balance: "0" }));
   };

   const validateForm = () => {
      if (!formData.lender.trim()) {
         setError("Lender name is required");
         return false;
      }

      if (
         !formData.balance ||
         isNaN(parseFloat(formData.balance)) ||
         parseFloat(formData.balance) < 0
      ) {
         setError("Please enter a valid balance");
         return false;
      }

      if (
         !formData.apr ||
         isNaN(parseFloat(formData.apr)) ||
         parseFloat(formData.apr) < 0
      ) {
         setError("Please enter a valid APR percentage");
         return false;
      }

      if (
         !formData.minimumPayment ||
         isNaN(parseFloat(formData.minimumPayment)) ||
         parseFloat(formData.minimumPayment) <= 0
      ) {
         setError("Please enter a valid minimum payment amount");
         return false;
      }

      if (
         !formData.dueDate ||
         isNaN(parseInt(formData.dueDate)) ||
         parseInt(formData.dueDate) < 1 ||
         parseInt(formData.dueDate) > 31
      ) {
         setError("Please enter a valid due date (1-31)");
         return false;
      }

      return true;
   };

   const handleSubmit = async (e) => {
      e.preventDefault();

      if (!validateForm()) {
         return;
      }

      const balanceValue = parseFloat(formData.balance);
      const originalBalance = parseFloat(debt.balance);

      // Check if balance is being set to 0 and it wasn't 0 before
      if (balanceValue === 0 && originalBalance > 0) {
         const updatedDebt = {
            ...formData,
            balance: balanceValue,
            apr: parseFloat(formData.apr),
            minimumPayment: parseFloat(formData.minimumPayment),
         };
         setPendingDebtUpdate(updatedDebt);
         setShowCloseAccountDialog(true);
         return;
      }

      // Normal update flow
      await updateDebt(false);
   };

   const updateDebt = async (closeAccount = false) => {
      setLoading(true);

      try {
         const updatedDebt = pendingDebtUpdate || {
            ...formData,
            balance: parseFloat(formData.balance),
            apr: parseFloat(formData.apr),
            minimumPayment: parseFloat(formData.minimumPayment),
         };

         if (closeAccount) {
            updatedDebt.active = false;
            updatedDebt.note = "Account closed - debt paid off";
         }

         // Call API to update debt
         const response = await fetch("/api/user/debts", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt: updatedDebt }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update debt");
         }

         const result = await response.json();
         onSave(result.debt);
      } catch (err) {
         console.error("Error updating debt:", err);
         setError(err.message || "Something went wrong. Please try again.");
      } finally {
         setLoading(false);
         setShowCloseAccountDialog(false);
         setPendingDebtUpdate(null);
      }
   };

   const handleCloseAccountConfirm = async () => {
      await updateDebt(true);
   };

   const handleCloseAccountCancel = async () => {
      await updateDebt(false);
   };

   if (showCloseAccountDialog) {
      return (
         <Dialog
            as="div"
            className="relative z-50"
            open={true}
            onClose={() => {}}
         >
            <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

            <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
               <Dialog.Panel className="mx-auto w-full max-w-md transform rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 p-6 shadow-xl transition-all">
                  <div className="mb-4">
                     <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        🎉 Congratulations! Debt Paid Off!
                     </Dialog.Title>
                     <p className="text-sm text-gray-600 dark:text-gray-400">
                        You've successfully paid off your {debt.lender} debt!
                        Would you like to close this account and mark it as
                        inactive?
                     </p>
                     <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                        Closing the account will keep it in your history but
                        mark it as inactive. You can always reactivate it later
                        if needed.
                     </p>
                  </div>

                  <div className="flex space-x-3">
                     <button
                        type="button"
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        onClick={handleCloseAccountConfirm}
                        disabled={loading}
                     >
                        {loading ? "Processing..." : "Close Account"}
                     </button>
                     <button
                        type="button"
                        className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        onClick={handleCloseAccountCancel}
                        disabled={loading}
                     >
                        {loading ? "Processing..." : "Keep Active"}
                     </button>
                  </div>
               </Dialog.Panel>
            </div>
         </Dialog>
      );
   }

   return (
      <Dialog as="div" className="relative z-50" open={true} onClose={onClose}>
         <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

         <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
            <Dialog.Panel className="mx-auto w-full max-w-md transform rounded-xl bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 p-6 shadow-xl transition-all">
               <div className="flex items-center justify-between mb-4">
                  <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white">
                     Edit Debt
                  </Dialog.Title>
                  <button
                     type="button"
                     className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                     onClick={onClose}
                  >
                     <span className="sr-only">Close</span>
                     <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
               </div>

               {error && (
                  <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded">
                     {error}
                  </div>
               )}

               <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Debt Type
                     </label>
                     <select
                        name="debtType"
                        value={formData.debtType}
                        onChange={handleInputChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                     >
                        <option value="credit card">Credit Card</option>
                        <option value="personal loan">Personal Loan</option>
                        <option value="student loan">Student Loan</option>
                        <option value="mortgage">Mortgage</option>
                        <option value="auto loan">Auto Loan</option>
                        <option value="medical debt">Medical Debt</option>
                        <option value="other">Other</option>
                     </select>
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Lender Name
                     </label>
                     <input
                        type="text"
                        name="lender"
                        value={formData.lender}
                        onChange={handleInputChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="Enter lender name"
                        required
                     />
                  </div>

                  <div>
                     <div className="flex items-center justify-between">
                        <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                           Current Balance ($)
                        </label>
                        {parseFloat(formData.balance || 0) > 0 && (
                           <button
                              type="button"
                              onClick={handlePayOffDebt}
                              className="text-xs bg-green-100 hover:bg-green-200 dark:bg-green-900/30 dark:hover:bg-green-900/50 text-green-700 dark:text-green-400 px-2 py-1 rounded transition-colors border border-green-200 dark:border-green-700"
                           >
                              Pay Off Debt
                           </button>
                        )}
                     </div>
                     <input
                        type="text"
                        name="balance"
                        value={formData.balance}
                        onChange={handleNumberChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="0.00"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        APR (%)
                     </label>
                     <input
                        type="text"
                        name="apr"
                        value={formData.apr}
                        onChange={handleNumberChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="0.00"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Minimum Payment ($)
                     </label>
                     <input
                        type="text"
                        name="minimumPayment"
                        value={formData.minimumPayment}
                        onChange={handleNumberChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        placeholder="0.00"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-900 dark:text-gray-100">
                        Due Date (Day of Month)
                     </label>
                     <select
                        name="dueDate"
                        value={formData.dueDate}
                        onChange={handleInputChange}
                        className="mt-1 block w-full h-10 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                        required
                     >
                        {Array.from({ length: 31 }, (_, i) => i + 1).map(
                           (day) => (
                              <option key={day} value={day.toString()}>
                                 {day}
                              </option>
                           )
                        )}
                     </select>
                  </div>

                  <div className="flex space-x-3 pt-4">
                     <button
                        type="button"
                        className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        onClick={onClose}
                     >
                        Cancel
                     </button>
                     <button
                        type="submit"
                        className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:opacity-50"
                        disabled={loading}
                     >
                        {loading ? "Saving..." : "Update Debt"}
                     </button>
                  </div>
               </form>
            </Dialog.Panel>
         </div>
      </Dialog>
   );
}

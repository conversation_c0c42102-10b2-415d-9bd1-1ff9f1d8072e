"use client";

import { useState, useEffect, Fragment, useRef, useMemo } from "react";
import { formatCurrency } from "../../lib/utils/budgetUtils";
import ConfirmDialog from "../settings/ConfirmDialog";
import AddDebtModal from "./AddDebtModal";
import EditDebtModal from "./EditDebtModal";
import DebtHistoryModal from "./DebtHistoryModal";
import PayOffDebtModal from "./PayOffDebtModal";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "../../../components/ui/dropdown-menu";

export default function DebtList({
   debts: propDebts,
   onDebtDelete,
   onDebtEdit,
   onDebtAdd,
   loading: propLoading,
   viewType,
   dateRange,
   isMobileView = false,
   isCollapsed = false,
}) {
   const [debts, setDebts] = useState(propDebts || []);
   const [loading, setLoading] = useState(propLoading);
   const [error, setError] = useState("");
   const [selectedDebt, setSelectedDebt] = useState(null);
   const [isEditModalOpen, setIsEditModalOpen] = useState(false);
   const [isAddModalOpen, setIsAddModalOpen] = useState(false);
   const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
   const [isPayOffModalOpen, setIsPayOffModalOpen] = useState(false);
   const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
   const [debtToDelete, setDebtToDelete] = useState(null);
   const [isExpanded, setIsExpanded] = useState(true);
   const [sortField, setSortField] = useState("balance");
   const [sortDirection, setSortDirection] = useState("desc"); // 'asc' or 'desc'
   const [hideInactiveDebts, setHideInactiveDebts] = useState(true); // Default to hiding inactive debts
   const [preferencesLoading, setPreferencesLoading] = useState(true);

   // Update internal state when props change
   useEffect(() => {
      setDebts(propDebts || []);
   }, [propDebts]);

   useEffect(() => {
      setLoading(propLoading);
   }, [propLoading]);

   // Load user preferences
   useEffect(() => {
      const loadPreferences = async () => {
         try {
            const response = await fetch("/api/user");
            if (response.ok) {
               const userData = await response.json();
               setHideInactiveDebts(
                  userData.preferences?.hideInactiveDebts ?? true
               );
            }
         } catch (error) {
            console.error("Error loading user preferences:", error);
         } finally {
            setPreferencesLoading(false);
         }
      };

      loadPreferences();
   }, []);

   // Save preference to server
   const saveHideInactiveDebtsPreference = async (hide) => {
      try {
         const response = await fetch("/api/user/preferences", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               hideInactiveDebts: hide,
            }),
         });

         if (!response.ok) {
            console.error("Failed to save preference");
         }
      } catch (error) {
         console.error("Error saving preference:", error);
      }
   };

   // Toggle the hideInactiveDebts preference
   const toggleHideInactiveDebts = () => {
      const newValue = !hideInactiveDebts;
      setHideInactiveDebts(newValue);
      saveHideInactiveDebtsPreference(newValue);
   };

   const handleDelete = async (id, e) => {
      if (e) {
         e.preventDefault();
         e.stopPropagation();
      }
      if (!id) {
         return;
      }
      setDebtToDelete(id);
      setDeleteConfirmOpen(true);
   };

   const handleConfirmDelete = async () => {
      setDeleteConfirmOpen(false);
      if (debtToDelete) {
         try {
            const response = await fetch(`/api/user/debts/${debtToDelete}`, {
               method: "DELETE",
            });

            const result = await response.json();

            if (!response.ok) {
               if (result.hasAssociatedExpenses) {
                  setError(
                     result.message ||
                        "Cannot delete debt with associated expenses"
                  );
               } else {
                  throw new Error(result.message || "Failed to delete debt");
               }
               return;
            }

            // Handle different deletion actions
            if (result.action === "deactivated") {
               // Debt was marked as inactive
               setDebts(
                  debts.map((debt) =>
                     debt._id === debtToDelete
                        ? { ...debt, active: false }
                        : debt
                  )
               );
            } else if (result.action === "deleted") {
               // Debt was permanently deleted - remove from list
               setDebts(debts.filter((debt) => debt._id !== debtToDelete));
            }

            // Also call the parent callback if provided
            if (onDebtDelete) {
               onDebtDelete(debtToDelete);
            }
         } catch (error) {
            console.error("Error deleting debt:", error);
            setError(error.message || "Failed to delete debt");
         }
      }
   };

   const toggleActive = async (debt, e) => {
      if (e) {
         e.stopPropagation();
      }
      if (!debt || !debt._id) return;

      const updatedDebt = {
         ...debt,
         active: !debt.active,
      };

      try {
         const response = await fetch(`/api/user/debts`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt: updatedDebt }),
         });

         if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || "Failed to update debt status");
         }

         // Get the response data to ensure we have the server's version
         const responseData = await response.json();
         const serverUpdatedDebt = responseData.debt;

         // Update the debt in local state with server data
         setDebts(
            debts.map((d) => (d._id === debt._id ? serverUpdatedDebt : d))
         );

         // Also call the parent callback if provided
         if (onDebtEdit) {
            onDebtEdit(serverUpdatedDebt);
         }

         console.log(
            `Debt ${debt.lender} active status changed to: ${serverUpdatedDebt.active}`
         );
      } catch (error) {
         console.error("Error updating debt status:", error);
         setError(error.message || "Failed to update debt status");
      }
   };

   const handleEditModalSave = (updatedDebt) => {
      setIsEditModalOpen(false);
      if (onDebtEdit) {
         onDebtEdit(updatedDebt);
      }
   };

   const handleAddDebtSave = (newDebt) => {
      setIsAddModalOpen(false);
      if (onDebtAdd && newDebt && newDebt._id) {
         // Check if debt already exists to prevent duplicates
         const existingDebt = debts.find((debt) => debt._id === newDebt._id);
         if (!existingDebt) {
            onDebtAdd(newDebt);
         }
      }
   };

   const handleRowClick = (e, debt) => {
      // Do not handle row click if a dropdown menu was clicked
      if (
         e.target.closest(".dropdown-menu") ||
         e.target.closest(".menu-button") ||
         e.target.closest("[data-radix-collection-item]") ||
         e.target.closest("[role='menu']") ||
         e.target.closest("[role='menuitem']")
      ) {
         return;
      }

      setSelectedDebt(debt);
      setIsHistoryModalOpen(true);
   };

   const handleEditModalClose = () => {
      setIsEditModalOpen(false);
      setSelectedDebt(null);
   };

   const handleHistoryModalClose = () => {
      setIsHistoryModalOpen(false);
      setSelectedDebt(null);
   };

   const handlePayOffModalClose = () => {
      setIsPayOffModalOpen(false);
      setSelectedDebt(null);
   };

   const handlePayOffModalSave = (updatedDebt) => {
      setDebts(
         debts.map((debt) =>
            debt._id === updatedDebt._id ? updatedDebt : debt
         )
      );
      setSelectedDebt(null);
      setIsPayOffModalOpen(false);

      // Also call the parent callback if provided
      if (onDebtEdit) {
         onDebtEdit(updatedDebt);
      }
   };

   // Sort debts based on sortField and sortDirection
   const sortedDebts = useMemo(() => {
      if (!sortField) {
         // Fall back to unsorted if no sort field
         return [...debts];
      }

      return [...debts].sort((a, b) => {
         // First sort by active status
         if (a.active !== b.active) {
            return a.active ? -1 : 1; // Active debts come first
         }

         // Then handle different sort fields
         let valueA, valueB;

         if (sortField === "balance") {
            valueA = a.balance || 0;
            valueB = b.balance || 0;
         } else if (sortField === "minimumPayment") {
            valueA = a.minimumPayment || 0;
            valueB = b.minimumPayment || 0;
         } else if (sortField === "apr") {
            valueA = a.apr || 0;
            valueB = b.apr || 0;
         } else {
            return 0;
         }

         // Apply sort direction
         return sortDirection === "asc" ? valueA - valueB : valueB - valueA;
      });
   }, [debts, sortField, sortDirection]);

   // Filter debts based on view type and preferences
   const filteredDebts = useMemo(() => {
      // First apply hideInactiveDebts preference
      let filtered = hideInactiveDebts
         ? sortedDebts.filter((debt) => debt.active !== false)
         : sortedDebts;

      // Then apply pay period filtering if needed
      if (viewType === "payPeriod" && dateRange) {
         filtered = filtered.filter((debt) => {
            // Skip filtering for inactive debts if they should still be visible
            // in pay period view, we might want to still filter them
            if (!debt.active) return true;

            if (!debt.dueDate) return true;

            const dueDateNum = parseInt(debt.dueDate);
            if (isNaN(dueDateNum)) return true;

            // Get the start and end dates of the pay period
            const startDay = dateRange.start.getDate();
            const endDay = dateRange.end
               ? dateRange.end.getDate()
               : new Date(
                    dateRange.start.getFullYear(),
                    dateRange.start.getMonth() + 1,
                    0
                 ).getDate();

            // Check if the due date falls within the pay period
            if (startDay <= endDay) {
               // Normal case: start day is before end day in the same month
               return dueDateNum >= startDay && dueDateNum <= endDay;
            } else {
               // Edge case: pay period crosses month boundary (e.g., 25th to 9th)
               // In this case, show debts due between 1st and end day OR between start day and end of month
               const lastDayOfMonth = new Date(
                  dateRange.start.getFullYear(),
                  dateRange.start.getMonth() + 1,
                  0
               ).getDate();
               return (
                  (dueDateNum >= 1 && dueDateNum <= endDay) ||
                  (dueDateNum >= startDay && dueDateNum <= lastDayOfMonth)
               );
            }
         });
      }

      return filtered;
   }, [sortedDebts, viewType, dateRange, hideInactiveDebts]);

   // Calculate total minimum payments for filtered debts
   const totalMinimumPayment = filteredDebts
      .filter((debt) => debt.active !== false)
      .reduce((sum, debt) => sum + (debt.minimumPayment || 0), 0);

   // Calculate total balance for filtered debts
   const totalBalance = filteredDebts
      .filter((debt) => debt.active !== false)
      .reduce((sum, debt) => sum + (debt.balance || 0), 0);

   // Handle sorting
   const handleSort = (field) => {
      if (sortField === field) {
         // If already sorting by this field, toggle direction
         setSortDirection(sortDirection === "asc" ? "desc" : "asc");
      } else {
         // New field, default to descending for balance and min payment
         setSortField(field);
         setSortDirection("desc");
      }
   };

   // Add a helper function for adding day suffix
   const getDaySuffix = (day) => {
      if (!day) return "";

      const num = parseInt(day);
      if (isNaN(num)) return "";

      if (num >= 11 && num <= 13) {
         return "th";
      }

      switch (num % 10) {
         case 1:
            return "st";
         case 2:
            return "nd";
         case 3:
            return "rd";
         default:
            return "th";
      }
   };

   if (error) {
      return <div className="text-red-500">{error}</div>;
   }

   // Don't render at all if viewing a pay period with no active debts
   if (
      viewType === "payPeriod" &&
      filteredDebts.filter((debt) => debt.active !== false).length === 0
   ) {
      return null;
   }

   // Instead of returning null, use CSS to animate the height
   return (
      <div
         className={`space-y-4 overflow-hidden transition-all duration-300 ease-in-out ${
            isMobileView && isCollapsed ? "max-h-0" : "max-h-[2000px]"
         }`}
      >
         <div className="bg-white dark:bg-gray-900">
            <table className="w-full">
               <thead>
                  {/* Header Row */}
                  <tr className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
                     <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Debts
                     </th>
                     <th
                        className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-36 cursor-pointer whitespace-nowrap"
                        onClick={() => handleSort("minimumPayment")}
                     >
                        Min Payment
                        {sortField === "minimumPayment" && (
                           <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                           </span>
                        )}
                     </th>
                     <th
                        className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32 cursor-pointer"
                        onClick={() => handleSort("balance")}
                     >
                        Balance
                        {sortField === "balance" && (
                           <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                           </span>
                        )}
                     </th>
                     <th className="px-4 py-2 w-10">
                        <div className="relative float-right">
                           <DropdownMenu>
                              <DropdownMenuTrigger className="inline-flex items-center p-1 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900">
                                 <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                 >
                                    <path
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                       strokeWidth={2}
                                       d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"
                                    />
                                 </svg>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                 <DropdownMenuItem
                                    onClick={() => setIsAddModalOpen(true)}
                                 >
                                    <svg
                                       className="w-4 h-4 mr-2"
                                       fill="none"
                                       stroke="currentColor"
                                       viewBox="0 0 24 24"
                                    >
                                       <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 4v16m8-8H4"
                                       />
                                    </svg>
                                    Add Debt
                                 </DropdownMenuItem>
                                 <DropdownMenuItem
                                    onClick={toggleHideInactiveDebts}
                                 >
                                    <svg
                                       className="w-4 h-4 mr-2"
                                       fill="none"
                                       stroke="currentColor"
                                       viewBox="0 0 24 24"
                                    >
                                       <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d={
                                             hideInactiveDebts
                                                ? "M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
                                                : "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                          }
                                       />
                                    </svg>
                                    {hideInactiveDebts ? "Show" : "Hide"}{" "}
                                    Inactive Debts
                                 </DropdownMenuItem>
                              </DropdownMenuContent>
                           </DropdownMenu>
                        </div>
                     </th>
                  </tr>
               </thead>

               <tbody className={`${isExpanded ? "" : "hidden"}`}>
                  {filteredDebts.length === 0 ? (
                     <tr key="empty-debts-row">
                        <td
                           colSpan="4"
                           className="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400"
                        >
                           {loading
                              ? "Loading..."
                              : viewType === "payPeriod"
                              ? "No debts due in this pay period."
                              : "No debts found. Add one to get started!"}
                        </td>
                     </tr>
                  ) : (
                     <>
                        {/* Active Debts */}
                        {filteredDebts
                           .filter((debt) => debt.active !== false)
                           .map((debt, index) => (
                              <tr
                                 key={
                                    debt._id ||
                                    `debt_${index}_${debt.lender}_${debt.balance}`
                                 }
                                 onClick={(e) => handleRowClick(e, debt)}
                                 className={`border-b bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50`}
                              >
                                 <td className="px-4 py-1">
                                    <div className="flex items-center">
                                       <div className="flex flex-col">
                                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                                             {debt.lender}
                                          </div>
                                          <div className="text-sm text-gray-500 dark:text-gray-400">
                                             {debt.dueDate
                                                ? `${
                                                     debt.dueDate
                                                  }${getDaySuffix(
                                                     debt.dueDate
                                                  )}`
                                                : "-"}
                                          </div>
                                       </div>
                                    </div>
                                 </td>
                                 <td className="px-4 py-1 text-right text-sm font-medium text-gray-700 dark:text-gray-300 tabular-nums">
                                    {formatCurrency(debt.minimumPayment)}
                                 </td>
                                 <td className="px-4 py-1 text-right text-sm font-medium text-gray-700 dark:text-gray-300 tabular-nums">
                                    {formatCurrency(debt.balance)}
                                 </td>
                                 <td className="px-4 py-1 text-right">
                                    <DropdownMenu>
                                       <DropdownMenuTrigger className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                                          <svg
                                             className="w-5 h-5"
                                             fill="none"
                                             stroke="currentColor"
                                             viewBox="0 0 24 24"
                                          >
                                             <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                                             />
                                          </svg>
                                       </DropdownMenuTrigger>
                                       <DropdownMenuContent align="end">
                                          <DropdownMenuItem
                                             onClick={() => {
                                                setSelectedDebt(debt);
                                                setIsEditModalOpen(true);
                                             }}
                                          >
                                             Edit
                                          </DropdownMenuItem>
                                          {debt.balance > 0 && debt.active && (
                                             <DropdownMenuItem
                                                onClick={() => {
                                                   setSelectedDebt(debt);
                                                   setIsPayOffModalOpen(true);
                                                }}
                                                className="text-green-600 dark:text-green-400 focus:text-green-600 dark:focus:text-green-400"
                                             >
                                                💳 Pay Off Debt
                                             </DropdownMenuItem>
                                          )}
                                          <DropdownMenuItem
                                             onClick={(e) =>
                                                toggleActive(debt, e)
                                             }
                                          >
                                             {debt.active
                                                ? "Mark as Inactive"
                                                : "Mark as Active"}
                                          </DropdownMenuItem>
                                          <DropdownMenuItem
                                             onClick={() =>
                                                handleDelete(debt._id)
                                             }
                                             className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                                          >
                                             {debt.active
                                                ? "Delete (Mark Inactive)"
                                                : "Delete Permanently"}
                                          </DropdownMenuItem>
                                       </DropdownMenuContent>
                                    </DropdownMenu>
                                 </td>
                              </tr>
                           ))}

                        {/* Totals Row - Only show when there are multiple active debts */}
                        {filteredDebts.filter((debt) => debt.active !== false)
                           .length > 1 && (
                           <tr className="bg-gray-50 dark:bg-gray-800 border-y border-gray-200 dark:border-gray-700">
                              <td className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                                 Totals
                              </td>
                              <td className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 tabular-nums">
                                 {formatCurrency(totalMinimumPayment)}
                              </td>
                              <td className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 tabular-nums">
                                 {formatCurrency(totalBalance)}
                              </td>
                              <td className="px-4 py-2"></td>
                           </tr>
                        )}

                        {/* Inactive Debts - shown at bottom grayed out */}
                        {!hideInactiveDebts &&
                           filteredDebts
                              .filter((debt) => debt.active === false)
                              .map((debt, index) => (
                                 <tr
                                    key={`inactive-${
                                       debt._id ||
                                       `debt_${index}_${debt.lender}_${debt.balance}`
                                    }`}
                                    onClick={(e) => handleRowClick(e, debt)}
                                    className="bg-gray-100 dark:bg-gray-800/50 text-gray-400 dark:text-gray-500 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/70"
                                 >
                                    <td className="px-4 py-1">
                                       <div className="flex items-center">
                                          <div className="flex flex-col">
                                             <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                                {debt.lender}
                                             </div>
                                             <div className="text-sm text-gray-400 dark:text-gray-500">
                                                {debt.dueDate
                                                   ? `${
                                                        debt.dueDate
                                                     }${getDaySuffix(
                                                        debt.dueDate
                                                     )}`
                                                   : "-"}
                                             </div>
                                          </div>
                                       </div>
                                    </td>
                                    <td className="px-4 py-1 text-right text-sm font-medium text-gray-500 dark:text-gray-500 tabular-nums">
                                       {formatCurrency(debt.minimumPayment)}
                                    </td>
                                    <td className="px-4 py-1 text-right text-sm font-medium text-gray-500 dark:text-gray-500 tabular-nums">
                                       {formatCurrency(debt.balance)}
                                    </td>
                                    <td className="px-4 py-1 text-right">
                                       <DropdownMenu>
                                          <DropdownMenuTrigger className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                                             <svg
                                                className="w-5 h-5"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                             >
                                                <path
                                                   strokeLinecap="round"
                                                   strokeLinejoin="round"
                                                   strokeWidth={2}
                                                   d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                                                />
                                             </svg>
                                          </DropdownMenuTrigger>
                                          <DropdownMenuContent align="end">
                                             <DropdownMenuItem
                                                onClick={() => {
                                                   setSelectedDebt(debt);
                                                   setIsEditModalOpen(true);
                                                }}
                                             >
                                                Edit
                                             </DropdownMenuItem>
                                             {debt.balance > 0 &&
                                                debt.active && (
                                                   <DropdownMenuItem
                                                      onClick={() => {
                                                         setSelectedDebt(debt);
                                                         setIsPayOffModalOpen(
                                                            true
                                                         );
                                                      }}
                                                      className="text-green-600 dark:text-green-400 focus:text-green-600 dark:focus:text-green-400"
                                                   >
                                                      💳 Pay Off Debt
                                                   </DropdownMenuItem>
                                                )}
                                             <DropdownMenuItem
                                                onClick={(e) =>
                                                   toggleActive(debt, e)
                                                }
                                             >
                                                {debt.active
                                                   ? "Mark as Inactive"
                                                   : "Mark as Active"}
                                             </DropdownMenuItem>
                                             <DropdownMenuItem
                                                onClick={() =>
                                                   handleDelete(debt._id)
                                                }
                                                className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                                             >
                                                {debt.active
                                                   ? "Delete (Mark Inactive)"
                                                   : "Delete Permanently"}
                                             </DropdownMenuItem>
                                          </DropdownMenuContent>
                                       </DropdownMenu>
                                    </td>
                                 </tr>
                              ))}
                     </>
                  )}
               </tbody>
            </table>
         </div>

         {/* Edit Modal */}
         {isEditModalOpen && selectedDebt && (
            <EditDebtModal
               debt={selectedDebt}
               onClose={handleEditModalClose}
               onSave={handleEditModalSave}
            />
         )}

         {/* History Modal */}
         {isHistoryModalOpen && selectedDebt && (
            <DebtHistoryModal
               debt={selectedDebt}
               onClose={handleHistoryModalClose}
            />
         )}

         {/* Add Modal */}
         {isAddModalOpen && (
            <AddDebtModal
               onClose={() => setIsAddModalOpen(false)}
               onSave={handleAddDebtSave}
            />
         )}

         {/* Pay Off Modal */}
         {isPayOffModalOpen && selectedDebt && (
            <PayOffDebtModal
               debt={selectedDebt}
               onClose={handlePayOffModalClose}
               onSave={handlePayOffModalSave}
            />
         )}

         {/* Delete Confirmation Dialog */}
         <ConfirmDialog
            isOpen={deleteConfirmOpen}
            onClose={() => setDeleteConfirmOpen(false)}
            onConfirm={handleConfirmDelete}
            title="Delete Debt"
            message={
               debtToDelete && debts.find((d) => d._id === debtToDelete)?.active
                  ? "Are you sure you want to delete this debt? It will be marked as inactive and can be reactivated later."
                  : "Are you sure you want to permanently delete this debt? This action cannot be undone and will remove all debt history."
            }
            confirmText={
               debtToDelete && debts.find((d) => d._id === debtToDelete)?.active
                  ? "Mark Inactive"
                  : "Delete Permanently"
            }
            cancelText="Cancel"
         />
      </div>
   );
}

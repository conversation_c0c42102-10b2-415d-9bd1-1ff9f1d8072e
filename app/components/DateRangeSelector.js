import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
   format,
   startOfMonth,
   endOfMonth,
   addMonths,
   subMonths,
   startOfDay,
   endOfDay,
   addDays,
   subDays,
   isBefore,
   isAfter,
   startOfDay as getStartOfDay,
   addWeeks,
   parseISO,
   isSameDay,
   isSameMonth,
   setDate,
   isAfter as dateIsAfter,
} from "date-fns";
import { useRouter } from "next/navigation";
import { findMostRecentPayDate } from "../lib/utils/payPeriodUtils";
import {
   CalendarIcon,
   Sliders,
   ArrowLeft,
   ArrowRight,
   AlertTriangle,
   CheckCircle,
} from "lucide-react";
import { twMerge } from "tailwind-merge";
import {
   isCashAccount,
   parseTimezoneDate,
} from "@/app/lib/utils/transactionsUtils";
import useBalanceStore from "../lib/stores/balanceStore";

const VIEW_TYPES = [
   { value: "month", label: "Monthly" },
   { value: "payPeriod", label: "Pay Period" },
];

export default function DateRangeSelector({
   onRangeChange,
   userPaySettings,
   userCreatedAt,
   externalViewType,
   externalCurrentDate,
   payPeriods,
}) {
   // Get balances from Zustand store
   const { balances, isLoadingBalances } = useBalanceStore();
   const router = useRouter();
   const initialRenderRef = useRef(true);
   const [viewType, setViewType] = useState(externalViewType || "month");
   const [currentDate, setCurrentDate] = useState(() => {
      // If we have a user creation date, ensure we start in their creation month
      if (userCreatedAt) {
         const creationDate = new Date(userCreatedAt);
         // If external date is provided and it's not before creation month, use it
         if (
            externalCurrentDate &&
            !isBefore(
               startOfMonth(externalCurrentDate),
               startOfMonth(creationDate)
            )
         ) {
            return externalCurrentDate;
         }
         // Otherwise, start at the creation month
         return creationDate;
      }
      // If no creation date, use external date or today
      return externalCurrentDate || new Date();
   });
   const [periodsAhead, setPeriodsAhead] = useState(0);
   const onRangeChangeRef = useRef(onRangeChange);
   const basePayPeriodRef = useRef(null);
   const [showProjections, setShowProjections] = useState(false);
   const projectionsRef = useRef(null);
   const dropdownButtonRef = useRef(null);
   const dateRangeSetRef = useRef(false);
   const [currentValue, setCurrentValue] = useState(
      userPaySettings?.payPeriod === "biweekly" ? 2 : 1
   );
   const [view, setView] = useState(null);
   const [useSettings, setUseSettings] = useState(true);
   const calendarRef = useRef(null);
   const settingsRef = useRef(null);
   const dateRef = useRef(null);
   const [showCalendar, setShowCalendar] = useState(false);
   const [showSettings, setShowSettings] = useState(false);
   // Remove isGeneratingPdf state
   // Add state to store the current date range
   const [currentDateRange, setCurrentDateRange] = useState(null);
   // Add state for cash flow warnings
   const [cashFlowWarning, setCashFlowWarning] = useState(null);
   const [warningData, setWarningData] = useState(null); // Store complete warning response
   const [isLoadingWarnings, setIsLoadingWarnings] = useState(false);
   // Calculate projected balance based on payPeriods data
   const projectedBalance = useMemo(() => {
      if (
         !payPeriods ||
         !Array.isArray(payPeriods) ||
         payPeriods.length === 0
      ) {
         return {
            projectedIncome: 0,
            projectedExpenses: 0,
            projectedOverspend: 0,
            assignedInRange: 0,
            isViewingFuturePeriod: false,
            netBalance: 0,
         };
      }

      // Sum up income and expenses from all relevant pay periods
      const periodTotals = payPeriods.reduce(
         (totals, period) => {
            // Use the pre-calculated assigned value from each period
            // The payPeriods array already has this calculated from the calculatePayPeriodBalances function
            return {
               projectedIncome: totals.projectedIncome + (period.income || 0),
               projectedExpenses:
                  totals.projectedExpenses + (period.expenses || 0),
               projectedOverspend:
                  totals.projectedOverspend + (period.overspend || 0),
               assignedInRange: totals.assignedInRange + (period.assigned || 0),
            };
         },
         {
            projectedIncome: 0,
            projectedExpenses: 0,
            projectedOverspend: 0,
            assignedInRange: 0,
         }
      );

      // Check if viewing a future period
      const today = new Date();
      const isViewingFuturePeriod =
         currentDateRange &&
         currentDateRange.start &&
         dateIsAfter(startOfDay(currentDateRange.start), startOfDay(today));

      // Calculate net balance with proper defaults
      const projectedIncome = periodTotals.projectedIncome || 0;
      const projectedExpenses = periodTotals.projectedExpenses || 0;
      const projectedOverspend = periodTotals.projectedOverspend || 0;
      const assignedInRange = periodTotals.assignedInRange || 0;

      // Calculate the adjusted net balance (handle the adjustment for future periods)
      let netBalance = 0;
      if (isViewingFuturePeriod && assignedInRange > 0) {
         netBalance =
            projectedIncome -
            (projectedExpenses - assignedInRange) -
            projectedOverspend;
      } else {
         netBalance = projectedIncome - projectedExpenses - projectedOverspend;
      }

      // Store the raw values for display in tooltip
      return {
         projectedIncome,
         projectedExpenses,
         projectedOverspend,
         assignedInRange,
         isViewingFuturePeriod,
         netBalance,
      };
   }, [payPeriods, currentDateRange]);

   // Function to fetch cash flow warnings
   const fetchCashFlowWarnings = useCallback(async () => {
      if (!userPaySettings) return;

      try {
         setIsLoadingWarnings(true);
         const response = await fetch("/api/reports/cash-flow-warnings");

         if (response.ok) {
            const data = await response.json();
            setCashFlowWarning(data.warning);
            setWarningData(data); // Store complete response
         } else {
            setCashFlowWarning(null);
            setWarningData(null);
         }
      } catch (error) {
         console.error("Error fetching cash flow warnings:", error);
         setCashFlowWarning(null);
         setWarningData(null);
      } finally {
         setIsLoadingWarnings(false);
      }
   }, [userPaySettings]);

   // Fetch warnings when component mounts or when view changes
   useEffect(() => {
      fetchCashFlowWarnings();
   }, [fetchCashFlowWarnings, viewType, currentDate]);

   // Helper functions for warning badge
   const formatCurrency = (amount) => {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
      }).format(amount);
   };

   const getSeverityColor = (severity) => {
      switch (severity) {
         case "critical":
            return "bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";
         case "warning":
            return "bg-yellow-100 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";
         default:
            return "bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200";
      }
   };

   const getPositiveColor = () => {
      return "bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200";
   };

   // Handle click on warning badge to navigate to the referenced pay period
   const handleWarningClick = () => {
      if (cashFlowWarning?.period && onRangeChange) {
         // Extract the pay period dates from the warning using timezone-safe parsing
         const periodStart = parseTimezoneDate(
            cashFlowWarning.period.startDate
         );
         const periodEnd = parseTimezoneDate(cashFlowWarning.period.endDate);

         // Navigate to the specific pay period in the budget page
         onRangeChange({
            start: periodStart,
            end: periodEnd,
            viewType: "payPeriod",
            forceRefresh: true,
         });
      } else {
         // Fallback to reports if warning data is incomplete
         router.push("/reports");
      }
   };

   // Handle click outside to close projections dropdown
   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            projectionsRef.current &&
            dropdownButtonRef.current &&
            !projectionsRef.current.contains(event.target) &&
            !dropdownButtonRef.current.contains(event.target)
         ) {
            setShowProjections(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
         document.removeEventListener("mousedown", handleClickOutside);
      };
   }, []);

   // Handle click outside to close calendar
   useEffect(() => {
      if (showCalendar) {
         const handleClickOutside = (event) => {
            if (
               calendarRef.current &&
               !calendarRef.current.contains(event.target)
            ) {
               setShowCalendar(false);
            }
         };
         document.addEventListener("mousedown", handleClickOutside);
         return () => {
            document.removeEventListener("mousedown", handleClickOutside);
         };
      }
   }, [showCalendar]);

   // Handle click outside to close settings
   useEffect(() => {
      if (showSettings) {
         const handleClickOutside = (event) => {
            if (
               settingsRef.current &&
               !settingsRef.current.contains(event.target)
            ) {
               setShowSettings(false);
            }
         };
         document.addEventListener("mousedown", handleClickOutside);
         return () => {
            document.removeEventListener("mousedown", handleClickOutside);
         };
      }
   }, [showSettings]);

   // Function to navigate to a specific pay period
   const navigateToPayPeriod = useCallback(
      (periodStartDate) => {
         if (!userPaySettings || !periodStartDate) return;

         setViewType("payPeriod");
         setCurrentDate(periodStartDate);
         basePayPeriodRef.current = periodStartDate;
         setPeriodsAhead(0);
      },
      [userPaySettings]
   );

   // Expose the navigation function through a ref
   useEffect(() => {
      if (window) {
         window.navigateToPayPeriod = navigateToPayPeriod;
      }
   }, [navigateToPayPeriod]);

   // Keep the callback ref up to date
   useEffect(() => {
      onRangeChangeRef.current = onRangeChange;
   }, [onRangeChange]);

   // Update date range only when parameters change, not on every render
   const updateDateRange = useCallback(() => {
      // Skip the first render to prevent double fetching
      if (initialRenderRef.current) {
         initialRenderRef.current = false;
         return;
      }

      let start, end;

      if (viewType === "month") {
         start = startOfMonth(currentDate);
         if (periodsAhead === 0) {
            end = endOfMonth(currentDate);
         } else {
            end = endOfMonth(currentDate);
         }
      } else if (userPaySettings) {
         const { payPeriod } = userPaySettings;

         // Use currentDate directly as the start date
         start = startOfDay(currentDate);

         // Calculate end date based on periods ahead
         let endDate = start;
         for (let i = 0; i < periodsAhead + 1; i++) {
            if (i < periodsAhead) {
               if (payPeriod === "biweekly") {
                  endDate = addDays(endDate, 14);
               } else if (payPeriod === "weekly") {
                  endDate = addDays(endDate, 7);
               } else if (payPeriod === "semimonthly") {
                  // For semimonthly, if we're on the 1st, next date is the 14th
                  // If we're on the 15th, next date is end of month
                  if (endDate.getDate() === 1) {
                     endDate = addDays(endDate, 13); // End on the 14th
                  } else {
                     endDate = endOfMonth(endDate);
                  }
               } else {
                  endDate = addMonths(endDate, 1);
               }
            }
         }

         // Calculate the final end date based on the last period
         if (payPeriod === "weekly") {
            end = startOfDay(addDays(endDate, 6));
         } else if (payPeriod === "biweekly") {
            end = startOfDay(addDays(endDate, 13));
         } else if (payPeriod === "semimonthly") {
            if (endDate.getDate() === 1) {
               end = startOfDay(addDays(endDate, 13)); // End on the 14th
            } else {
               end = startOfDay(endOfMonth(endDate));
            }
         } else {
            const nextPayDate = getNextPayDate(
               endDate,
               payPeriod,
               userPaySettings.payDay,
               userPaySettings.payDayOfWeek
            );
            end = startOfDay(subDays(nextPayDate, 1));
         }
      }

      // Only trigger if we have valid dates
      if (start && end) {
         // Only include forceRefresh for initial load or when view parameters change
         const isFirstRender = !dateRangeSetRef.current;

         // Store the date range in component state
         const newDateRange = {
            start: startOfDay(start),
            end: endOfDay(end),
         };

         // Only update if the date range has actually changed
         if (
            !currentDateRange ||
            !isSameDay(newDateRange.start, currentDateRange.start) ||
            !isSameDay(newDateRange.end, currentDateRange.end)
         ) {
            setCurrentDateRange(newDateRange);

            onRangeChangeRef.current({
               start: newDateRange.start,
               end: newDateRange.end,
               viewType,
               periodsAhead,
               userPaySettings:
                  viewType === "payPeriod" ? userPaySettings : null,
               forceRefresh: isFirstRender, // Only force refresh on first render
            });
         }

         // Mark that we've set the date range at least once
         dateRangeSetRef.current = true;
      }
   }, [viewType, currentDate, periodsAhead, userPaySettings, currentDateRange]);

   // Update internal state when external props change
   useEffect(() => {
      if (externalViewType && externalViewType !== viewType) {
         setViewType(externalViewType);
         // Reset date range set ref for view type changes
         dateRangeSetRef.current = false;
      }
   }, [externalViewType]);

   useEffect(() => {
      if (externalCurrentDate && !isSameDay(externalCurrentDate, currentDate)) {
         // Only update if the new date isn't before creation month
         if (
            userCreatedAt &&
            isBefore(
               startOfMonth(externalCurrentDate),
               startOfMonth(new Date(userCreatedAt))
            )
         ) {
            return;
         }
         // Reset date range set ref for date changes
         dateRangeSetRef.current = false;
         setCurrentDate(externalCurrentDate);
         setPeriodsAhead(0);
      }
   }, [externalCurrentDate, userCreatedAt]);

   // Calculate projection start date
   const calculateProjectionStartDate = (
      viewDate,
      viewType,
      payPeriod = null
   ) => {
      if (!userCreatedAt) return viewDate;

      const userCreationDate = new Date(userCreatedAt);
      const isCreationMonth = isSameMonth(viewDate, userCreationDate);

      // For pay period view, we need to look back one full period
      if (viewType === "payPeriod" && userPaySettings) {
         const { payPeriod } = userPaySettings;
         let periodLength;

         switch (payPeriod) {
            case "weekly":
               periodLength = 7;
               break;
            case "biweekly":
               periodLength = 14;
               break;
            case "semimonthly":
               periodLength = 15;
               break;
            case "monthly":
               periodLength = 30;
               break;
            default:
               periodLength = 7;
         }

         // Look back one full period from the creation date
         return startOfDay(subDays(userCreationDate, periodLength));
      }

      // For month view, only look back if we're in the creation month
      if (viewType === "month" && isCreationMonth) {
         // Look back 7 days from the start of the month to catch any weekly expenses
         return startOfDay(subDays(startOfMonth(userCreationDate), 7));
      }

      return viewDate;
   };

   const canNavigatePrevious = () => {
      if (!userCreatedAt) return true;
      if (viewType === "payPeriod" && !userPaySettings) return false;

      const userCreationStart = startOfDay(new Date(userCreatedAt));

      if (viewType === "month") {
         const prevMonth = startOfMonth(subMonths(currentDate, 1));
         return !isBefore(prevMonth, startOfMonth(userCreationStart));
      } else if (viewType === "payPeriod") {
         const { payPeriod } = userPaySettings;
         let periodLength;

         switch (payPeriod) {
            case "weekly":
               periodLength = 7;
               break;
            case "biweekly":
               periodLength = 14;
               break;
            case "semimonthly":
               periodLength = 15;
               break;
            case "monthly":
               periodLength = 30;
               break;
            default:
               periodLength = 7;
         }

         // Get the current pay period's start date
         const currentPeriodStart = findMostRecentPayDate(
            currentDate,
            payPeriod,
            userPaySettings.payDay,
            userPaySettings.payDayOfWeek,
            payPeriod === "biweekly" ? userPaySettings.lastPaymentDate : null
         );

         // Get the previous pay period's potential start date
         let previousPeriodStart;
         if (payPeriod === "semimonthly") {
            previousPeriodStart =
               currentPeriodStart.getDate() === 1
                  ? new Date(subMonths(currentPeriodStart, 1).setDate(15))
                  : new Date(currentPeriodStart.setDate(1));
         } else if (payPeriod === "monthly") {
            previousPeriodStart = subMonths(currentPeriodStart, 1);
         } else {
            previousPeriodStart = subDays(currentPeriodStart, periodLength);
         }

         // Calculate the end date of the previous period
         let previousPeriodEnd;
         if (payPeriod === "weekly") {
            previousPeriodEnd = addDays(previousPeriodStart, 6);
         } else if (payPeriod === "biweekly") {
            previousPeriodEnd = addDays(previousPeriodStart, 13);
         } else if (payPeriod === "semimonthly") {
            previousPeriodEnd =
               previousPeriodStart.getDate() === 1
                  ? addDays(previousPeriodStart, 13)
                  : endOfMonth(previousPeriodStart);
         } else {
            previousPeriodEnd = endOfMonth(previousPeriodStart);
         }

         // Allow navigation if either:
         // 1. The previous period starts in or after the creation month, or
         // 2. The previous period ends in or after the creation month
         return (
            !isBefore(
               startOfMonth(previousPeriodStart),
               startOfMonth(userCreationStart)
            ) ||
            !isBefore(
               startOfMonth(previousPeriodEnd),
               startOfMonth(userCreationStart)
            )
         );
      }

      return true;
   };

   const handleNext = () => {
      let newDate;
      if (viewType === "month") {
         newDate = addMonths(currentDate, 1);
         basePayPeriodRef.current = null; // Reset base pay period in month view
      } else if (userPaySettings) {
         const { payPeriod, payDay, payDayOfWeek } = userPaySettings;

         // Get or update the base pay period
         if (!basePayPeriodRef.current) {
            basePayPeriodRef.current = findMostRecentPayDate(
               currentDate,
               payPeriod,
               payDay,
               payDayOfWeek,
               payPeriod === "biweekly" ? userPaySettings.lastPaymentDate : null
            );
         }

         if (payPeriod === "biweekly") {
            // For biweekly, add 14 days
            newDate = startOfDay(addDays(basePayPeriodRef.current, 14));
         } else if (payPeriod === "weekly") {
            // For weekly, add 7 days
            newDate = startOfDay(addDays(basePayPeriodRef.current, 7));
         } else if (payPeriod === "semimonthly") {
            // For semimonthly, if we're on the 1st go to 15th, if on 15th go to 1st of next month
            const currentDate = basePayPeriodRef.current;
            if (currentDate.getDate() <= 1) {
               // We're on the 1st, go to the 15th
               newDate = startOfDay(new Date(currentDate.setDate(15)));
            } else {
               // We're on the 15th, go to 1st of next month
               newDate = startOfDay(
                  new Date(addMonths(currentDate, 1).setDate(1))
               );
            }
         } else {
            // For monthly, add one month
            newDate = startOfDay(addMonths(basePayPeriodRef.current, 1));
         }

         // Update the base pay period reference
         basePayPeriodRef.current = newDate;
      }

      // Reset date range set ref so we force a refresh on navigation
      dateRangeSetRef.current = false;

      setCurrentDate(newDate);
      setPeriodsAhead(0); // Reset periods ahead when navigating
   };

   const handlePrevious = () => {
      if (!canNavigatePrevious()) return;

      let newDate;
      if (viewType === "month") {
         newDate = subMonths(currentDate, 1);
         basePayPeriodRef.current = null; // Reset base pay period in month view
      } else if (userPaySettings) {
         const { payPeriod, payDay, payDayOfWeek } = userPaySettings;

         // Get or update the base pay period
         if (!basePayPeriodRef.current) {
            basePayPeriodRef.current = findMostRecentPayDate(
               currentDate,
               payPeriod,
               payDay,
               payDayOfWeek,
               payPeriod === "biweekly" ? userPaySettings.lastPaymentDate : null
            );
         }

         if (payPeriod === "biweekly") {
            // For biweekly, subtract 14 days
            newDate = startOfDay(subDays(basePayPeriodRef.current, 14));
         } else if (payPeriod === "weekly") {
            // For weekly, subtract 7 days
            newDate = startOfDay(subDays(basePayPeriodRef.current, 7));
         } else if (payPeriod === "semimonthly") {
            // For semimonthly, if we're on the 15th go to 1st, if on 1st go to 15th of previous month
            const currentDate = basePayPeriodRef.current;
            if (currentDate.getDate() === 15) {
               newDate = startOfDay(new Date(currentDate.setDate(1)));
            } else {
               // We're on the 1st, go to 15th of previous month
               newDate = startOfDay(
                  new Date(subMonths(currentDate, 1).setDate(15))
               );
            }
         } else {
            // For monthly, subtract one month
            newDate = startOfDay(subMonths(basePayPeriodRef.current, 1));
         }

         // Update the base pay period reference
         basePayPeriodRef.current = newDate;
      }

      // Reset date range set ref so we force a refresh on navigation
      dateRangeSetRef.current = false;

      setCurrentDate(newDate);
      setPeriodsAhead(0); // Reset periods ahead when navigating
   };

   const handlePeriodsAheadChange = (e) => {
      const newPeriodsAhead = Number(e.target.value);
      setPeriodsAhead(newPeriodsAhead);
   };

   // Watch for changes and update date range
   useEffect(() => {
      if (viewType === "payPeriod" && !userPaySettings) {
         setViewType("month");
         return;
      }
      updateDateRange();
   }, [viewType, currentDate, periodsAhead, userPaySettings, updateDateRange]);

   const getPeriodsAheadOptions = () => {
      if (viewType === "month") {
         const startMonth = startOfMonth(currentDate);
         return Array.from({ length: 7 }, (_, i) => {
            const endMonth = endOfMonth(addMonths(startMonth, i));
            return {
               value: i,
               label:
                  i === 0
                     ? format(startMonth, "MMMM yyyy")
                     : `${format(startMonth, "MMM yyyy")} - ${format(
                          endMonth,
                          "MMM yyyy"
                       )}`,
            };
         });
      } else if (userPaySettings) {
         const { payPeriod } = userPaySettings;
         const periodStart = startOfDay(currentDate);

         return Array.from({ length: 21 }, (_, i) => {
            let end;

            // Calculate end date based on the number of periods ahead
            if (payPeriod === "weekly") {
               end = addDays(periodStart, (i + 1) * 7 - 1);
            } else if (payPeriod === "biweekly") {
               end = addDays(periodStart, (i + 1) * 14 - 1);
            } else if (payPeriod === "semimonthly") {
               let tempEnd = periodStart;
               for (let j = 0; j <= i; j++) {
                  if (tempEnd.getDate() === 1) {
                     tempEnd = addDays(tempEnd, 14);
                  } else {
                     tempEnd = endOfMonth(tempEnd);
                     if (j < i) {
                        tempEnd = startOfDay(
                           addMonths(new Date(tempEnd).setDate(1), 1)
                        );
                     }
                  }
               }
               end = tempEnd;
            } else {
               end = subDays(
                  getNextPayDate(
                     addMonths(periodStart, i + 1),
                     payPeriod,
                     userPaySettings.payDay,
                     userPaySettings.payDayOfWeek
                  ),
                  1
               );
            }

            return {
               value: i,
               label:
                  i === 0
                     ? `${format(periodStart, "MMM d")} - ${format(
                          end,
                          "MMM d, yyyy"
                       )}`
                     : `${format(periodStart, "MMM d")} - ${format(
                          end,
                          "MMM d, yyyy"
                       )}`,
            };
         });
      }
      return [];
   };

   const getNextPayDate = (fromDate, payPeriod, payDay, payDayOfWeek) => {
      let nextDate = startOfDay(new Date(fromDate));

      if (payPeriod === "semimonthly") {
         const currentDay = nextDate.getDate();
         if (currentDay < 15) {
            // If we're in the first half of the month, next date is the 15th
            nextDate.setDate(15);
         } else {
            // If we're in the second half, next date is 1st of next month
            nextDate = startOfDay(addMonths(nextDate, 1));
            nextDate.setDate(1);
         }
      } else if (payPeriod === "monthly") {
         // For monthly, move to the next month's pay day
         nextDate = addMonths(nextDate, 1);
         nextDate.setDate(parseInt(payDay));
      } else if (payPeriod === "biweekly") {
         // For biweekly, simply add 14 days - no day-of-week alignment
         nextDate = startOfDay(addDays(nextDate, 14));
      } else {
         // For weekly, find the next occurrence of the pay day
         nextDate = addDays(nextDate, 7);
         // Align to the correct day of week for weekly only
         const targetDay = parseInt(payDayOfWeek) % 7;
         let currentDay = nextDate.getDay();
         let daysToAdd = targetDay - currentDay;
         if (daysToAdd < 0) daysToAdd += 7;
         nextDate = addDays(nextDate, daysToAdd);
      }

      // Always return a new date object and ensure consistent timezone
      return startOfDay(new Date(nextDate));
   };

   const handleViewTypeChange = (e) => {
      const newViewType = e.target.value;
      if (newViewType === "payPeriod" && !userPaySettings) {
         return;
      }
      setViewType(newViewType);
      setPeriodsAhead(0);

      // Reset date range set ref so we force a refresh when changing view types
      dateRangeSetRef.current = false;

      // Always use today's date when switching views
      const today = new Date();
      let start;

      if (newViewType === "month") {
         // When switching to month view, use current month
         start = startOfMonth(today);
         basePayPeriodRef.current = null; // Reset base pay period
      } else if (userPaySettings) {
         // When switching to pay period view, find the current pay period
         const { payPeriod, payDay, payDayOfWeek } = userPaySettings;
         start = findMostRecentPayDate(
            today,
            payPeriod,
            payDay,
            payDayOfWeek,
            payPeriod === "biweekly" ? userPaySettings.lastPaymentDate : null
         );
         basePayPeriodRef.current = start; // Set the base pay period
      }

      setCurrentDate(start || today);

      if (start) {
         onRangeChangeRef.current({
            start,
            end: newViewType === "month" ? endOfMonth(start) : null, // end will be calculated in updateDateRange
            viewType: newViewType,
            periodsAhead: 0,
         });
      }
   };

   const getAvailableViewTypes = () => {
      // Only show Pay Period option if user has non-monthly pay period settings
      return VIEW_TYPES.filter((type) => {
         if (type.value === "payPeriod") {
            return userPaySettings && userPaySettings.payPeriod !== "monthly";
         }
         return true;
      });
   };

   const isCurrentPeriod = () => {
      // If we're projecting ahead, we're not in the current period
      if (periodsAhead > 0) return false;

      const today = new Date();

      if (viewType === "month") {
         return isSameMonth(currentDate, today);
      } else if (viewType === "payPeriod" && userPaySettings) {
         const currentPeriodStart = findMostRecentPayDate(
            today,
            userPaySettings.payPeriod,
            userPaySettings.payDay,
            userPaySettings.payDayOfWeek,
            userPaySettings.payPeriod === "biweekly"
               ? userPaySettings.lastPaymentDate
               : null
         );
         return isSameDay(currentDate, currentPeriodStart);
      }
      return true;
   };

   const handleTodayClick = () => {
      const today = new Date();
      let newDate;

      if (viewType === "month") {
         newDate = startOfMonth(today);
         basePayPeriodRef.current = null;
      } else if (userPaySettings) {
         const { payPeriod, payDay, payDayOfWeek } = userPaySettings;
         newDate = findMostRecentPayDate(
            today,
            payPeriod,
            payDay,
            payDayOfWeek,
            payPeriod === "biweekly" ? userPaySettings.lastPaymentDate : null
         );
         basePayPeriodRef.current = newDate;
      } else {
         newDate = today;
      }

      // Reset date range set ref so we force a refresh when going to today
      dateRangeSetRef.current = false;

      setCurrentDate(newDate);
      setPeriodsAhead(0);
   };

   return (
      <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center justify-between py-2">
         <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 w-full">
            <div className="flex items-stretch gap-2 w-full sm:w-auto justify-between sm:justify-start px-1 sm:px-0">
               <button
                  onClick={handlePrevious}
                  disabled={!canNavigatePrevious() || periodsAhead > 0}
                  className={`inline-flex items-center justify-center px-2 py-1 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 shadow-sm ${
                     (!canNavigatePrevious() || periodsAhead > 0) &&
                     "opacity-50 cursor-not-allowed"
                  }`}
                  aria-label="Previous period"
               >
                  <svg
                     className="h-5 w-5"
                     xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 20 20"
                     fill="currentColor"
                  >
                     <path
                        fillRule="evenodd"
                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clipRule="evenodd"
                     />
                  </svg>
               </button>

               <div className="relative flex-1 sm:flex-none">
                  <button
                     ref={dropdownButtonRef}
                     onClick={() => setShowProjections(!showProjections)}
                     className="w-full sm:w-auto text-base sm:text-lg font-medium text-gray-900 dark:text-white flex-shrink-0 min-w-[140px] text-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center gap-1"
                  >
                     <span className="truncate">
                        {viewType === "month"
                           ? periodsAhead === 0
                              ? format(currentDate, "MMMM")
                              : `${format(
                                   startOfMonth(currentDate),
                                   "MMM"
                                )} - ${format(
                                   endOfMonth(
                                      addMonths(currentDate, periodsAhead)
                                   ),
                                   "MMM"
                                )}`
                           : userPaySettings
                           ? (() => {
                                const { payPeriod } = userPaySettings;
                                const start = startOfDay(currentDate);
                                let end;

                                // Calculate the end date based on the current period
                                if (payPeriod === "weekly") {
                                   end = startOfDay(addDays(start, 6));
                                } else if (payPeriod === "biweekly") {
                                   end = startOfDay(addDays(start, 13));
                                } else if (payPeriod === "semimonthly") {
                                   if (start.getDate() === 1) {
                                      end = startOfDay(addDays(start, 13)); // End on the 14th
                                   } else {
                                      end = startOfDay(endOfMonth(start));
                                   }
                                } else {
                                   const nextPayDate = getNextPayDate(
                                      start,
                                      payPeriod,
                                      userPaySettings.payDay,
                                      userPaySettings.payDayOfWeek
                                   );
                                   end = startOfDay(subDays(nextPayDate, 1));
                                }

                                // If we're projecting ahead, calculate the final end date
                                if (periodsAhead > 0) {
                                   let finalEnd = end;
                                   let currentStart = start;

                                   for (let i = 0; i < periodsAhead; i++) {
                                      if (payPeriod === "weekly") {
                                         currentStart = addDays(
                                            currentStart,
                                            7
                                         );
                                         finalEnd = startOfDay(
                                            addDays(currentStart, 6)
                                         );
                                      } else if (payPeriod === "biweekly") {
                                         currentStart = addDays(
                                            currentStart,
                                            14
                                         );
                                         finalEnd = startOfDay(
                                            addDays(currentStart, 13)
                                         );
                                      } else if (payPeriod === "semimonthly") {
                                         if (currentStart.getDate() === 1) {
                                            currentStart = new Date(
                                               currentStart.setDate(15)
                                            );
                                            finalEnd = startOfDay(
                                               endOfMonth(currentStart)
                                            );
                                         } else {
                                            const nextMonthStart = addMonths(
                                               new Date(currentStart),
                                               1
                                            );
                                            nextMonthStart.setDate(1);
                                            currentStart =
                                               startOfDay(nextMonthStart);
                                            finalEnd = startOfDay(
                                               addDays(currentStart, 13)
                                            );
                                         }
                                      } else {
                                         currentStart = addMonths(
                                            currentStart,
                                            1
                                         );
                                         const nextPayDate = getNextPayDate(
                                            currentStart,
                                            payPeriod,
                                            userPaySettings.payDay,
                                            userPaySettings.payDayOfWeek
                                         );
                                         finalEnd = startOfDay(
                                            subDays(nextPayDate, 1)
                                         );
                                      }
                                   }
                                   end = finalEnd;
                                }

                                // Only show year if the dates span different years
                                const showYear =
                                   start.getFullYear() !== end.getFullYear();
                                return `${format(start, "MMM d")} - ${format(
                                   end,
                                   showYear ? "MMM d, yyyy" : "MMM d"
                                )}`;
                             })()
                           : format(currentDate, "MMMM")}
                     </span>
                     <svg
                        className={`h-4 w-4 transition-transform ${
                           showProjections ? "rotate-180" : ""
                        }`}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                     >
                        <path
                           fillRule="evenodd"
                           d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                           clipRule="evenodd"
                        />
                     </svg>
                  </button>

                  {showProjections && (
                     <div
                        ref={projectionsRef}
                        className="absolute z-[100] mt-1 w-full sm:w-64 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 overflow-hidden"
                        style={{
                           top: "100%",
                           left: 0,
                           maxHeight: "80vh",
                        }}
                     >
                        <div className="py-2">
                           <div className="mb-3 border-b border-gray-200 dark:border-gray-700 pb-2">
                              <button
                                 onClick={() => {
                                    const newViewType =
                                       viewType === "month"
                                          ? "payPeriod"
                                          : "month";
                                    if (
                                       newViewType === "payPeriod" &&
                                       !userPaySettings
                                    )
                                       return;
                                    handleViewTypeChange({
                                       target: { value: newViewType },
                                    });
                                    setShowProjections(false);
                                 }}
                                 disabled={
                                    viewType === "month" && !userPaySettings
                                 }
                                 className={`w-full flex items-center justify-between px-4 py-2 text-sm ${
                                    viewType === "month" && !userPaySettings
                                       ? "opacity-50 cursor-not-allowed"
                                       : "hover:bg-gray-50 dark:hover:bg-gray-700"
                                 }`}
                              >
                                 <span className="flex items-center gap-2">
                                    {viewType === "month" ? (
                                       <>
                                          <svg
                                             xmlns="http://www.w3.org/2000/svg"
                                             className="h-4 w-4 text-gray-500"
                                             viewBox="0 0 20 20"
                                             fill="currentColor"
                                          >
                                             <path
                                                fillRule="evenodd"
                                                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                                clipRule="evenodd"
                                             />
                                          </svg>
                                          <span className="font-medium text-gray-700 dark:text-gray-300">
                                             Switch to Pay Period View
                                          </span>
                                       </>
                                    ) : (
                                       <>
                                          <svg
                                             xmlns="http://www.w3.org/2000/svg"
                                             className="h-4 w-4 text-gray-500"
                                             viewBox="0 0 20 20"
                                             fill="currentColor"
                                          >
                                             <path
                                                fillRule="evenodd"
                                                d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                                                clipRule="evenodd"
                                             />
                                          </svg>
                                          <span className="font-medium text-gray-700 dark:text-gray-300">
                                             Switch to Monthly View
                                          </span>
                                       </>
                                    )}
                                 </span>
                              </button>
                           </div>

                           <div className="px-4 text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Project Ahead
                           </div>
                           <div className="max-h-[220px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                              {getPeriodsAheadOptions().map((option) => (
                                 <button
                                    key={option.value}
                                    onClick={() => {
                                       setPeriodsAhead(option.value);
                                       setShowProjections(false);
                                    }}
                                    className={`w-full text-left px-8 py-2.5 text-sm ${
                                       periodsAhead === option.value
                                          ? "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
                                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                                    }`}
                                 >
                                    {option.label}
                                 </button>
                              ))}
                           </div>
                        </div>
                     </div>
                  )}
               </div>

               <button
                  onClick={handleNext}
                  disabled={
                     (viewType === "payPeriod" && !userPaySettings) ||
                     periodsAhead > 0
                  }
                  className={`inline-flex items-center justify-center px-2 py-1 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 shadow-sm ${
                     ((viewType === "payPeriod" && !userPaySettings) ||
                        periodsAhead > 0) &&
                     "opacity-50 cursor-not-allowed"
                  }`}
                  aria-label="Next period"
               >
                  <svg
                     className="h-5 w-5"
                     xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 20 20"
                     fill="currentColor"
                  >
                     <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                     />
                  </svg>
               </button>

               {!isCurrentPeriod() && (
                  <button
                     onClick={handleTodayClick}
                     className="text-xs px-3 py-1.5 text-white bg-[#2463eb] hover:bg-[#2463eb]/90 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2463eb] focus:ring-offset-2 flex items-center justify-center"
                     aria-label="Go to today"
                  >
                     Today
                  </button>
               )}
            </div>

            {balances && (
               <div className="flex flex-nowrap items-center gap-2 sm:gap-4 sm:divide-x sm:divide-gray-200 sm:dark:divide-gray-700 w-full sm:w-auto ml-auto">
                  {/* Cash Flow Warning/Status Badge */}
                  {((cashFlowWarning &&
                     // Don't show warning badge if we're already viewing the warning period
                     !(
                        currentDateRange &&
                        isSameDay(
                           currentDateRange.start instanceof Date
                              ? currentDateRange.start
                              : parseTimezoneDate(currentDateRange.start),
                           parseTimezoneDate(cashFlowWarning.period.startDate)
                        ) &&
                        isSameDay(
                           currentDateRange.end instanceof Date
                              ? currentDateRange.end
                              : parseTimezoneDate(currentDateRange.end),
                           parseTimezoneDate(cashFlowWarning.period.endDate)
                        )
                     )) ||
                     // Show positive badge when warnings are enabled but no warning exists
                     (warningData?.warningsEnabled &&
                        !cashFlowWarning &&
                        !isLoadingWarnings)) && (
                     <div className="flex-shrink-0 px-2">
                        <div className="group relative">
                           <button
                              onClick={
                                 cashFlowWarning
                                    ? handleWarningClick
                                    : undefined
                              }
                              className={`flex items-center justify-center text-xs font-medium border transition-opacity ${
                                 cashFlowWarning
                                    ? "gap-1 px-2 py-1 cursor-pointer hover:opacity-80"
                                    : "w-6 h-6 cursor-default"
                              } rounded-full ${
                                 cashFlowWarning
                                    ? getSeverityColor(cashFlowWarning.severity)
                                    : getPositiveColor()
                              }`}
                              title={
                                 cashFlowWarning
                                    ? "Click to view this pay period in budget"
                                    : undefined
                              }
                           >
                              {cashFlowWarning ? (
                                 <>
                                    <AlertTriangle className="h-3 w-3" />
                                    <span className="text-xs">
                                       {/* Use the pre-formatted label to avoid timezone issues */}
                                       {
                                          cashFlowWarning.period.label.split(
                                             " - "
                                          )[0]
                                       }
                                       -
                                       {
                                          cashFlowWarning.period.label
                                             .split(" - ")[1]
                                             .split(",")[0]
                                             .split(" ")[1]
                                       }
                                    </span>
                                 </>
                              ) : (
                                 <CheckCircle className="h-3 w-3" />
                              )}
                           </button>

                           {/* Tooltip */}
                           <div className="fixed sm:absolute hidden group-hover:block z-50 w-72 p-4 inset-x-0 mx-auto sm:mx-0 top-1/2 -translate-y-1/2 sm:translate-y-0 sm:top-full sm:mt-2 sm:right-0 sm:left-auto bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700">
                              <div className="space-y-2">
                                 {cashFlowWarning ? (
                                    // Warning tooltip content
                                    <>
                                       <div className="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                                          <AlertTriangle className="h-4 w-4 inline mr-2 text-yellow-600" />
                                          Cash Flow Warning
                                       </div>

                                       <div className="text-sm text-gray-700 dark:text-gray-300">
                                          <strong>
                                             {cashFlowWarning.periodNumber === 1
                                                ? "Your next pay period"
                                                : `In ${cashFlowWarning.periodNumber} pay periods`}
                                             :
                                          </strong>
                                          <br />
                                          {cashFlowWarning.period.label}
                                       </div>

                                       <div className="text-sm text-red-600 dark:text-red-400 font-medium">
                                          Negative cash flow of{" "}
                                          {formatCurrency(
                                             Math.abs(
                                                cashFlowWarning.cashFlow.netFlow
                                             )
                                          )}
                                       </div>

                                       <div className="space-y-1 text-xs">
                                          <div className="flex justify-between">
                                             <span className="text-gray-600 dark:text-gray-400">
                                                Income:
                                             </span>
                                             <span className="font-mono text-green-600 dark:text-green-400">
                                                {formatCurrency(
                                                   cashFlowWarning.cashFlow
                                                      .income.total
                                                )}
                                             </span>
                                          </div>
                                          <div className="flex justify-between">
                                             <span className="text-gray-600 dark:text-gray-400">
                                                Expenses:
                                             </span>
                                             <span className="font-mono text-red-600 dark:text-red-400">
                                                {formatCurrency(
                                                   cashFlowWarning.cashFlow
                                                      .expenses.total
                                                )}
                                             </span>
                                          </div>
                                          <div className="flex justify-between">
                                             <span className="text-gray-600 dark:text-gray-400">
                                                Assigned:
                                             </span>
                                             <span className="font-mono text-blue-600 dark:text-blue-400">
                                                {formatCurrency(
                                                   cashFlowWarning.cashFlow
                                                      .assigned.total
                                                )}
                                             </span>
                                          </div>
                                          {cashFlowWarning.cashFlow.overspend
                                             ?.total > 0 && (
                                             <div className="flex justify-between">
                                                <span className="text-gray-600 dark:text-gray-400">
                                                   Overspend:
                                                </span>
                                                <span className="font-mono text-orange-600 dark:text-orange-400">
                                                   {formatCurrency(
                                                      cashFlowWarning.cashFlow
                                                         .overspend.total
                                                   )}
                                                </span>
                                             </div>
                                          )}
                                       </div>

                                       <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                          <strong>Note:</strong> Expenses show
                                          base due amounts. Overspend amounts
                                          (where spent &gt; due) are shown
                                          separately.
                                       </div>

                                       <div className="text-xs text-gray-500 italic mt-2">
                                          Click the warning icon to view this
                                          pay period in the budget page
                                       </div>
                                    </>
                                 ) : (
                                    // Positive "all clear" tooltip content
                                    <>
                                       <div className="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                                          <CheckCircle className="h-4 w-4 inline mr-2 text-green-600" />
                                          Cash Flow Status
                                       </div>

                                       <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                                          All clear!
                                       </div>

                                       <div className="text-sm text-gray-700 dark:text-gray-300">
                                          The next{" "}
                                          <strong>
                                             {warningData?.periodsChecked || 4}
                                          </strong>{" "}
                                          pay periods show positive cash flow
                                          with no warnings.
                                       </div>

                                       <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                          <strong>Note:</strong> This indicates
                                          your scheduled income will cover all
                                          planned expenses and debt payments in
                                          the upcoming periods.
                                       </div>
                                    </>
                                 )}
                              </div>
                           </div>
                        </div>
                     </div>
                  )}

                  {/* Net Projected */}
                  {projectedBalance && (
                     <div className="flex-1 sm:flex-none sm:pr-4 pl-3">
                        <div className="flex flex-col items-center sm:items-end group relative">
                           <div className="flex items-center gap-2">
                              <span className="text-[10px] sm:text-sm font-medium text-gray-500 dark:text-gray-400">
                                 Net Projected
                              </span>
                           </div>
                           {isLoadingBalances ? (
                              <div className="h-6 sm:h-8 w-20 sm:w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                           ) : (
                              <span
                                 className={`text-base sm:text-2xl font-bold font-mono ${
                                    (projectedBalance?.netBalance || 0) >= 0
                                       ? "text-green-600 dark:text-green-400"
                                       : "text-red-600 dark:text-red-400"
                                 }`}
                              >
                                 $
                                 {(
                                    projectedBalance?.netBalance || 0
                                 ).toLocaleString("en-US", {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                 })}
                              </span>
                           )}

                           {/* Net Projected Tooltip */}
                           <div className="fixed sm:absolute hidden group-hover:block z-50 w-72 p-4 inset-x-0 mx-auto sm:mx-0 top-1/2 -translate-y-1/2 sm:translate-y-0 sm:top-full sm:mt-2 sm:right-0 sm:left-auto bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700">
                              <div className="space-y-2">
                                 <div className="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                                    Projection Breakdown
                                 </div>
                                 {/* Projected Income */}
                                 <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                       Projected Income
                                    </span>
                                    <span className="text-sm font-mono text-green-600 dark:text-green-400">
                                       $
                                       {(
                                          projectedBalance?.projectedIncome || 0
                                       ).toLocaleString("en-US", {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2,
                                       })}
                                    </span>
                                 </div>
                                 {/* Projected Expenses */}
                                 <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                       Projected Expenses
                                    </span>
                                    <span className="text-sm font-mono text-red-600 dark:text-red-400">
                                       -$
                                       {(
                                          projectedBalance?.projectedExpenses ||
                                          0
                                       ).toLocaleString("en-US", {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2,
                                       })}
                                    </span>
                                 </div>

                                 {/* Projected Overspend */}
                                 {(projectedBalance?.projectedOverspend || 0) >
                                    0 && (
                                    <div className="flex justify-between items-center">
                                       <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                          Overspend Amount
                                       </span>
                                       <span className="text-sm font-mono text-orange-600 dark:text-orange-400">
                                          -$
                                          {(
                                             projectedBalance?.projectedOverspend ||
                                             0
                                          ).toLocaleString("en-US", {
                                             minimumFractionDigits: 2,
                                             maximumFractionDigits: 2,
                                          })}
                                       </span>
                                    </div>
                                 )}

                                 {/* Add adjustment note for future periods */}
                                 {projectedBalance?.isViewingFuturePeriod &&
                                    (projectedBalance?.assignedInRange || 0) >
                                       0 && (
                                       <div className="flex justify-between items-center">
                                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                             Already Assigned in Range
                                          </span>
                                          <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                                             +$
                                             {(
                                                projectedBalance?.assignedInRange ||
                                                0
                                             ).toLocaleString("en-US", {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2,
                                             })}
                                          </span>
                                       </div>
                                    )}

                                 {/* Pay Period Breakdown */}
                                 {Array.isArray(payPeriods) &&
                                    payPeriods.length > 1 &&
                                    viewType !== "month" && (
                                       <div className="mt-4">
                                          <div className="text-xs font-medium text-gray-700 dark:text-gray-400 mb-2">
                                             Period Breakdown
                                          </div>
                                          <div className="max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                                             {payPeriods.map((period, idx) => (
                                                <div
                                                   key={idx}
                                                   className="text-xs mb-2 pb-2 border-b border-gray-100 dark:border-gray-700 last:border-0"
                                                >
                                                   <div className="flex justify-between mb-1">
                                                      <span className="text-gray-600 dark:text-gray-400">
                                                         {format(
                                                            new Date(
                                                               period.startDate
                                                            ),
                                                            "MMM d"
                                                         )}{" "}
                                                         -{" "}
                                                         {format(
                                                            new Date(
                                                               period.endDate
                                                            ),
                                                            "MMM d"
                                                         )}
                                                      </span>
                                                      <span
                                                         className={`font-mono ${
                                                            period.balance >= 0
                                                               ? "text-green-600 dark:text-green-400"
                                                               : "text-red-600 dark:text-red-400"
                                                         }`}
                                                      >
                                                         $
                                                         {period.balance.toLocaleString(
                                                            "en-US",
                                                            {
                                                               minimumFractionDigits: 2,
                                                               maximumFractionDigits: 2,
                                                            }
                                                         )}
                                                      </span>
                                                   </div>
                                                   <div className="flex justify-between text-[10px]">
                                                      <span className="flex gap-1">
                                                         <span className="text-gray-600 dark:text-gray-400">
                                                            Income:
                                                         </span>
                                                         <span className="font-mono text-green-500 dark:text-green-400">
                                                            $
                                                            {period.income.toLocaleString(
                                                               "en-US",
                                                               {
                                                                  minimumFractionDigits: 2,
                                                                  maximumFractionDigits: 2,
                                                               }
                                                            )}
                                                         </span>
                                                      </span>
                                                      <span className="flex gap-1">
                                                         <span className="text-gray-600 dark:text-gray-400">
                                                            Expenses:
                                                         </span>
                                                         <span className="font-mono text-red-500 dark:text-red-400">
                                                            $
                                                            {period.expenses.toLocaleString(
                                                               "en-US",
                                                               {
                                                                  minimumFractionDigits: 2,
                                                                  maximumFractionDigits: 2,
                                                               }
                                                            )}
                                                         </span>
                                                      </span>
                                                   </div>
                                                </div>
                                             ))}
                                          </div>
                                       </div>
                                    )}

                                 {/* Net Difference */}
                                 <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                                    <div className="flex justify-between items-center">
                                       <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                          Net Difference
                                       </span>
                                       <span
                                          className={`text-sm font-mono ${
                                             (projectedBalance?.netBalance ||
                                                0) >= 0
                                                ? "text-green-600 dark:text-green-400"
                                                : "text-red-600 dark:text-red-400"
                                          }`}
                                       >
                                          $
                                          {(
                                             projectedBalance?.netBalance || 0
                                          ).toLocaleString("en-US", {
                                             minimumFractionDigits: 2,
                                             maximumFractionDigits: 2,
                                          })}
                                       </span>
                                    </div>
                                 </div>
                                 <div className="text-xs text-gray-500 italic mt-2">
                                    Based on{" "}
                                    {viewType === "month"
                                       ? "monthly"
                                       : "pay period"}{" "}
                                    view (
                                    {format(
                                       currentDateRange?.start || new Date(),
                                       "MMM d"
                                    )}{" "}
                                    -
                                    {format(
                                       currentDateRange?.end || new Date(),
                                       "MMM d"
                                    )}
                                    )
                                 </div>
                                 <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                    <strong>Note:</strong> Expenses show base
                                    due amounts. Overspend amounts (where spent
                                    &gt; due) are shown separately.
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  )}

                  {/* Ready to Assign */}
                  <div className="flex-1 sm:flex-none sm:pr-4 pl-3">
                     <div className="flex flex-col items-center sm:items-end">
                        <div className="flex items-center gap-2">
                           <span className="text-[10px] sm:text-sm font-medium text-gray-500 dark:text-gray-400">
                              Ready to Assign
                           </span>
                        </div>
                        {isLoadingBalances ? (
                           <div className="h-6 sm:h-8 w-20 sm:w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                        ) : (
                           <span
                              className={`text-base sm:text-2xl font-bold font-mono ${
                                 balances.readyToAssign >= 0
                                    ? "text-green-600 dark:text-green-400"
                                    : "text-red-600 dark:text-red-400"
                              }`}
                           >
                              $
                              {(balances.readyToAssign || 0).toLocaleString(
                                 "en-US",
                                 {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                 }
                              )}
                           </span>
                        )}
                     </div>
                  </div>

                  {/* Total Cash */}
                  <div className="flex-1 sm:flex-none sm:pl-4">
                     <div className="flex flex-col items-center sm:items-end group relative">
                        <span className="text-[10px] sm:text-sm font-medium text-gray-500 dark:text-gray-400">
                           Total Cash
                        </span>
                        {isLoadingBalances ? (
                           <div className="h-6 sm:h-8 w-20 sm:w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                        ) : (
                           <span
                              className={`text-base sm:text-2xl font-bold font-mono ${
                                 balances?.accounts
                                    ?.filter((account) =>
                                       isCashAccount(account)
                                    )
                                    ?.reduce(
                                       (sum, account) => sum + account.balance,
                                       0
                                    ) >= 0
                                    ? "text-green-600 dark:text-green-400"
                                    : "text-red-600 dark:text-red-400"
                              }`}
                           >
                              $
                              {(
                                 balances?.accounts
                                    ?.filter((account) =>
                                       isCashAccount(account)
                                    )
                                    ?.reduce(
                                       (sum, account) => sum + account.balance,
                                       0
                                    ) || 0
                              ).toLocaleString("en-US", {
                                 minimumFractionDigits: 2,
                                 maximumFractionDigits: 2,
                              })}
                           </span>
                        )}

                        {/* Cash Tooltip */}
                        <div className="fixed sm:absolute hidden group-hover:block z-50 w-72 p-4 inset-x-0 mx-auto sm:mx-0 top-1/2 -translate-y-1/2 sm:translate-y-0 sm:top-full sm:mt-2 sm:right-0 sm:left-auto bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700">
                           <div className="space-y-2">
                              <div className="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                                 Cash Breakdown
                              </div>
                              {/* Cash Accounts Section */}
                              {balances?.accounts?.filter((account) =>
                                 isCashAccount(account)
                              )?.length > 0 && (
                                 <>
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                       Cash Accounts
                                    </div>
                                    {balances?.accounts
                                       ?.filter((account) =>
                                          isCashAccount(account)
                                       )
                                       ?.map((account) => (
                                          <div
                                             key={account._id}
                                             className="flex justify-between items-center"
                                          >
                                             <span className="text-sm text-gray-600 dark:text-gray-300">
                                                {account.name}
                                             </span>
                                             <span className="text-sm font-mono text-green-600 dark:text-green-400">
                                                $
                                                {Math.abs(
                                                   account.balance
                                                ).toLocaleString("en-US", {
                                                   minimumFractionDigits: 2,
                                                   maximumFractionDigits: 2,
                                                })}
                                             </span>
                                          </div>
                                       ))}
                                    <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                                       <div className="flex justify-between items-center">
                                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                             Cash Accounts Total
                                          </span>
                                          <span className="text-sm font-mono text-green-600 dark:text-green-400">
                                             $
                                             {Math.abs(
                                                balances?.accounts
                                                   ?.filter((account) =>
                                                      isCashAccount(account)
                                                   )
                                                   ?.reduce(
                                                      (sum, account) =>
                                                         sum + account.balance,
                                                      0
                                                   ) || 0
                                             ).toLocaleString("en-US", {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2,
                                             })}
                                          </span>
                                       </div>
                                    </div>
                                 </>
                              )}
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            )}
         </div>
      </div>
   );
}

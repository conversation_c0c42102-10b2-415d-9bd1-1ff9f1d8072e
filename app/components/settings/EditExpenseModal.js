"use client";

import { useState, useEffect } from "react";
import { Switch } from "@headlessui/react";

const EXPENSE_FREQUENCIES = [
   { value: "weekly", label: "Weekly" },
   { value: "biweekly", label: "Bi-weekly" },
   { value: "monthly", label: "Monthly" },
   { value: "quarterly", label: "Quarterly" },
   { value: "annually", label: "Annually" },
];

const DAYS_OF_WEEK = [
   { value: "1", label: "Monday" },
   { value: "2", label: "Tuesday" },
   { value: "3", label: "Wednesday" },
   { value: "4", label: "Thursday" },
   { value: "5", label: "Friday" },
   { value: "6", label: "Saturday" },
   { value: "7", label: "Sunday" },
];

export default function EditExpenseModal({ expense, onClose, onSave }) {
   const [formData, setFormData] = useState({
      name: "",
      amount: "",
      frequency: "monthly",
      dueDay: "1",
      dueMonth: "1",
      enabled: true,
      weeklyChargeType: "one-time",
   });

   useEffect(() => {
      if (expense) {
         setFormData({
            name: expense.name || "",
            amount: expense.amount || "",
            frequency: expense.frequency || "monthly",
            dueDay: expense.dueDay || "1",
            dueMonth: expense.dueMonth || "1",
            enabled: expense.enabled ?? true,
            weeklyChargeType: expense.weeklyChargeType || "one-time",
         });
      }
   }, [expense]);

   const handleChange = (e) => {
      const { name, value } = e.target;
      setFormData((prev) => {
         const updated = {
            ...prev,
            [name]: value,
         };

         // Reset fields when changing frequency
         if (name === "frequency") {
            if (value === "annually") {
               updated.dueDay = "1"; // Reset to January
               updated.dueMonth = "1"; // Reset to 1st day
            } else if (value === "weekly") {
               updated.dueDay = "1"; // Reset to Monday
            } else {
               updated.dueDay = "1"; // Reset to 1st day for monthly/quarterly
            }
         }

         return updated;
      });
   };

   const handleSubmit = async (e) => {
      e.preventDefault();
      onSave({ ...expense, ...formData });
   };

   return (
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-50">
         <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
               <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                  <div className="absolute right-0 top-0 pr-4 pt-4">
                     <button
                        type="button"
                        onClick={onClose}
                        className="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                     >
                        <span className="sr-only">Close</span>
                        <svg
                           className="h-6 w-6"
                           fill="none"
                           viewBox="0 0 24 24"
                           strokeWidth="1.5"
                           stroke="currentColor"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M6 18L18 6M6 6l12 12"
                           />
                        </svg>
                     </button>
                  </div>

                  <div className="sm:flex sm:items-start">
                     <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 className="text-lg font-semibold leading-6 text-gray-900 dark:text-gray-100">
                           Edit Recurring Expense
                        </h3>

                        <form
                           onSubmit={handleSubmit}
                           className="mt-4 space-y-4"
                        >
                           <div>
                              <label
                                 htmlFor="name"
                                 className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                              >
                                 Name
                              </label>
                              <input
                                 type="text"
                                 id="name"
                                 name="name"
                                 value={formData.name}
                                 onChange={handleChange}
                                 required
                                 className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                              />
                           </div>

                           <div>
                              <label
                                 htmlFor="amount"
                                 className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                              >
                                 Amount
                              </label>
                              <div className="mt-1 relative rounded-md shadow-sm">
                                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                                       $
                                    </span>
                                 </div>
                                 <input
                                    type="number"
                                    id="amount"
                                    name="amount"
                                    value={formData.amount}
                                    onChange={handleChange}
                                    required
                                    min="0"
                                    step="0.01"
                                    className="mt-1 block w-full pl-7 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                 />
                              </div>
                           </div>

                           <div>
                              <label
                                 htmlFor="frequency"
                                 className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                              >
                                 Frequency
                              </label>
                              <select
                                 id="frequency"
                                 name="frequency"
                                 value={formData.frequency}
                                 onChange={handleChange}
                                 className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                              >
                                 {EXPENSE_FREQUENCIES.map((freq) => (
                                    <option key={freq.value} value={freq.value}>
                                       {freq.label}
                                    </option>
                                 ))}
                              </select>
                           </div>

                           <div>
                              <label
                                 htmlFor="dueDay"
                                 className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                              >
                                 {formData.frequency === "annually"
                                    ? "Due Month"
                                    : "Due Day"}
                              </label>
                              <select
                                 id="dueDay"
                                 name="dueDay"
                                 value={formData.dueDay}
                                 onChange={handleChange}
                                 className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                              >
                                 {formData.frequency === "annually"
                                    ? Array.from({ length: 12 }, (_, i) => {
                                         const monthNames = [
                                            "January",
                                            "February",
                                            "March",
                                            "April",
                                            "May",
                                            "June",
                                            "July",
                                            "August",
                                            "September",
                                            "October",
                                            "November",
                                            "December",
                                         ];
                                         return (
                                            <option
                                               key={i + 1}
                                               value={(i + 1).toString()}
                                            >
                                               {monthNames[i]}
                                            </option>
                                         );
                                      })
                                    : formData.frequency === "monthly" ||
                                      formData.frequency === "quarterly"
                                    ? Array.from({ length: 31 }, (_, i) => (
                                         <option
                                            key={i + 1}
                                            value={(i + 1).toString()}
                                         >
                                            {i + 1}
                                         </option>
                                      ))
                                    : DAYS_OF_WEEK.map((day) => (
                                         <option
                                            key={day.value}
                                            value={day.value}
                                         >
                                            {day.label}
                                         </option>
                                      ))}
                              </select>
                           </div>

                           {formData.frequency === "annually" && (
                              <div>
                                 <label
                                    htmlFor="dueMonth"
                                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                                 >
                                    Due Day
                                 </label>
                                 <select
                                    id="dueMonth"
                                    name="dueMonth"
                                    value={formData.dueMonth}
                                    onChange={handleChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                 >
                                    {Array.from({ length: 31 }, (_, i) => (
                                       <option
                                          key={i + 1}
                                          value={(i + 1).toString()}
                                       >
                                          {i + 1}
                                       </option>
                                    ))}
                                 </select>
                              </div>
                           )}

                           <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-700 dark:text-gray-300">
                                 Enabled
                              </span>
                              <Switch
                                 checked={formData.enabled}
                                 onChange={(checked) =>
                                    setFormData((prev) => ({
                                       ...prev,
                                       enabled: checked,
                                    }))
                                 }
                                 className={`${
                                    formData.enabled
                                       ? "bg-gray-600"
                                       : "bg-gray-200 dark:bg-gray-700"
                                 } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900`}
                              >
                                 <span className="sr-only">Enable expense</span>
                                 <span
                                    className={`${
                                       formData.enabled
                                          ? "translate-x-6"
                                          : "translate-x-1"
                                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                                 />
                              </Switch>
                           </div>

                           {formData.frequency === "weekly" && (
                              <div>
                                 <label
                                    htmlFor="weeklyChargeType"
                                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                                 >
                                    Charge Type
                                 </label>
                                 <select
                                    id="weeklyChargeType"
                                    name="weeklyChargeType"
                                    value={formData.weeklyChargeType}
                                    onChange={handleChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                 >
                                    <option value="one-time">
                                       One-time charge on the due day
                                    </option>
                                    <option value="spread">
                                       Spread over the week
                                    </option>
                                 </select>
                                 <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {formData.weeklyChargeType === "one-time"
                                       ? "The entire amount will be charged on the specified day"
                                       : "The amount will be spread across the entire week"}
                                 </p>
                              </div>
                           )}

                           <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                              <button
                                 type="submit"
                                 className="inline-flex w-full justify-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 sm:ml-3 sm:w-auto"
                              >
                                 Save Changes
                              </button>
                              <button
                                 type="button"
                                 onClick={onClose}
                                 className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                              >
                                 Cancel
                              </button>
                           </div>
                        </form>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
}

"use client";

import { useState } from "react";

const PAY_PERIODS = [
   { value: "weekly", label: "Weekly" },
   { value: "biweekly", label: "Bi-weekly" },
   { value: "semimonthly", label: "Semi-monthly (1st & 15th)" },
   { value: "monthly", label: "Monthly" },
   { value: "quarterly", label: "Quarterly" },
   { value: "annually", label: "Annually" },
];

const DAYS_OF_WEEK = [
   { value: "1", label: "Monday" },
   { value: "2", label: "Tuesday" },
   { value: "3", label: "Wednesday" },
   { value: "4", label: "Thursday" },
   { value: "5", label: "Friday" },
   { value: "6", label: "Saturday" },
   { value: "7", label: "Sunday" },
];

export default function AddIncomeModal({
   onClose,
   onSave,
   existingIncomes = [],
}) {
   const [income, setIncome] = useState({
      description: "",
      payAmount: "",
      payPeriod: "monthly",
      payDay: "1",
      payDayOfWeek: "5", // Default to Friday
      payWeekDay: "1", // First occurrence
      enabled: true,
      isMainIncome: existingIncomes.length === 0, // First income is automatically main
   });

   const [addAnother, setAddAnother] = useState(false);

   const handleSubmit = (e) => {
      e.preventDefault();
      onSave(income, addAnother);
      if (addAnother) {
         // Reset form but keep some defaults
         setIncome({
            description: "",
            payAmount: "",
            payPeriod: "monthly",
            payDay: "1",
            payDayOfWeek: "5",
            payWeekDay: "1",
            enabled: true,
            isMainIncome: false,
         });
      }
   };

   const handleChange = (e) => {
      const { name, value, type, checked } = e.target;
      setIncome((prev) => ({
         ...prev,
         [name]: type === "checkbox" ? checked : value,
      }));
   };

   return (
      <div className="space-y-6">
         <div className="text-center mb-6">
            <h2 className="text-2xl font-bold">Add Your Income</h2>
            <p className="text-gray-600 dark:text-gray-400">
               Tell us about your regular income to help with budgeting
            </p>
         </div>

         <form onSubmit={handleSubmit} className="space-y-4">
            <div>
               <label
                  htmlFor="description"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
               >
                  Income Description
               </label>
               <input
                  type="text"
                  id="description"
                  name="description"
                  value={income.description}
                  onChange={handleChange}
                  placeholder="e.g., Main Job Salary"
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
               />
            </div>

            <div>
               <label
                  htmlFor="payAmount"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
               >
                  Amount
               </label>
               <input
                  type="number"
                  id="payAmount"
                  name="payAmount"
                  value={income.payAmount}
                  onChange={handleChange}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
               />
            </div>

            <div>
               <label
                  htmlFor="payPeriod"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
               >
                  Pay Period
               </label>
               <select
                  id="payPeriod"
                  name="payPeriod"
                  value={income.payPeriod}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
               >
                  {PAY_PERIODS.map((period) => (
                     <option key={period.value} value={period.value}>
                        {period.label}
                     </option>
                  ))}
               </select>
            </div>

            {["monthly"].includes(income.payPeriod) && (
               <div>
                  <label
                     htmlFor="payDay"
                     className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                     Pay Day of Month
                  </label>
                  <select
                     id="payDay"
                     name="payDay"
                     value={income.payDay}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  >
                     {[...Array(31)].map((_, i) => (
                        <option key={i + 1} value={String(i + 1)}>
                           {i + 1}
                        </option>
                     ))}
                  </select>
               </div>
            )}

            {["weekly", "biweekly"].includes(income.payPeriod) && (
               <div>
                  <label
                     htmlFor="payDayOfWeek"
                     className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                     Pay Day of Week
                  </label>
                  <select
                     id="payDayOfWeek"
                     name="payDayOfWeek"
                     value={income.payDayOfWeek}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  >
                     {DAYS_OF_WEEK.map((day) => (
                        <option key={day.value} value={day.value}>
                           {day.label}
                        </option>
                     ))}
                  </select>
               </div>
            )}

            <div className="flex items-center">
               <input
                  type="checkbox"
                  name="isMainIncome"
                  checked={income.isMainIncome}
                  onChange={handleChange}
                  className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
                  disabled={existingIncomes.length === 0} // First income must be main
               />
               <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Set as main income (determines pay period for budgeting)
               </label>
            </div>

            <div className="flex items-center">
               <input
                  type="checkbox"
                  name="addAnother"
                  checked={addAnother}
                  onChange={(e) => setAddAnother(e.target.checked)}
                  className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
               />
               <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Add another income after this one
               </label>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
               <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
               >
                  Cancel
               </button>
               <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
               >
                  {addAnother ? "Save & Add Another" : "Save"}
               </button>
            </div>
         </form>
      </div>
   );
}

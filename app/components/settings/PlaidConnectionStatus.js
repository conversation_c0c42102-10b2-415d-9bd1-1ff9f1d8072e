"use client";

import { useState, useEffect, useRef } from "react";
import { usePlaidLink } from "react-plaid-link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tooltip } from "@/app/components/ui/tooltip";
import {
   CheckCircle,
   AlertTriangle,
   XCircle,
   RefreshCw,
   Wifi,
   WifiOff,
} from "lucide-react";

export default function PlaidConnectionStatus({
   account,
   onStatusUpdate,
   inline = false,
}) {
   const [connectionStatus, setConnectionStatus] = useState(null);
   const [loading, setLoading] = useState(false);
   const [linkToken, setLinkToken] = useState(null);
   const [error, setError] = useState(null);
   const [debugInfo, setDebugInfo] = useState(null);

   // Reference to store the current abort controller
   const abortControllerRef = useRef(null);

   // Debounce timer reference
   const debounceTimerRef = useRef(null);

   // Configure Plaid Link for update mode
   const config = {
      token: linkToken,
      onSuccess: (public_token, metadata) => {
         handleUpdateSuccess(public_token, metadata);
      },
      onExit: (err, metadata) => {
         setLinkToken(null);
         if (err) {
            console.error("Plaid Link error:", err);
            setError("Failed to re-authenticate. Please try again.");
         }
      },
   };

   const { open, ready } = usePlaidLink(config);

   // Open Plaid Link when token is ready
   useEffect(() => {
      if (linkToken && ready) {
         open();
      }
   }, [linkToken, ready, open]);

   // Check connection status on mount (only once)
   useEffect(() => {
      if (account.plaidItemId) {
         checkConnectionStatus();
      }

      // Cleanup function to abort any pending requests when component unmounts
      return () => {
         if (abortControllerRef.current) {
            abortControllerRef.current.abort();
         }

         // Clear any existing debounce timer
         if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
         }
      };
   }, []); // Empty dependency array to ensure it only runs once on mount

   const checkConnectionStatus = async () => {
      if (!account.plaidItemId) return;

      // Clear any existing debounce timer
      if (debounceTimerRef.current) {
         clearTimeout(debounceTimerRef.current);
      }

      // Set a new debounce timer (300ms)
      debounceTimerRef.current = setTimeout(async () => {
         try {
            // Cancel any existing request
            if (abortControllerRef.current) {
               abortControllerRef.current.abort();
            }

            // Create a new abort controller for this request
            abortControllerRef.current = new AbortController();
            const signal = abortControllerRef.current.signal;

            setLoading(true);
            setError(null);

            const response = await fetch("/api/plaid/connection-status", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  itemId: account.plaidItemId,
                  action: "check_status",
               }),
               signal, // Add the abort signal to the fetch request
            });

            if (!response.ok) {
               // Handle subscription errors specifically
               if (response.status === 403) {
                  const errorData = await response.json().catch(() => ({}));
                  if (errorData.requiresUpgrade) {
                     // Set a special status for subscription required
                     setConnectionStatus({
                        status: "subscription_required",
                        message: "Subscription required for bank connections",
                        needsAction: false,
                        requiresUpgrade: true,
                     });
                     return;
                  }
               }

               // Try to get error details from response
               const errorData = await response.json().catch(() => ({}));
               const errorMessage =
                  errorData.error ||
                  `Failed to check connection status (${response.status})`;
               throw new Error(errorMessage);
            }

            const data = await response.json();
            setConnectionStatus(data);

            // Notify parent component of status update
            if (onStatusUpdate) {
               onStatusUpdate(account._id, data);
            }
         } catch (error) {
            // Only set error if it's not an abort error
            if (error.name !== "AbortError") {
               console.error("Error checking connection status:", error);
               setError("Failed to check connection status");
            }
         } finally {
            // Only update loading state if the component is still mounted
            // and the request wasn't aborted
            if (abortControllerRef.current) {
               setLoading(false);
            }
         }
      }, 300); // 300ms debounce time
   };

   const handleReAuthenticate = async () => {
      if (!account.plaidItemId) return;

      // Clear any existing debounce timer
      if (debounceTimerRef.current) {
         clearTimeout(debounceTimerRef.current);
      }

      // Set a new debounce timer (300ms)
      debounceTimerRef.current = setTimeout(async () => {
         try {
            // Cancel any existing request
            if (abortControllerRef.current) {
               abortControllerRef.current.abort();
            }

            // Create a new abort controller for this request
            abortControllerRef.current = new AbortController();
            const signal = abortControllerRef.current.signal;

            setLoading(true);
            setError(null);

            const response = await fetch("/api/plaid/connection-status", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  itemId: account.plaidItemId,
                  action: "create_update_token",
               }),
               signal, // Add the abort signal to the fetch request
            });

            if (!response.ok) {
               // Try to get error details from response
               const errorData = await response.json().catch(() => ({}));
               const errorMessage =
                  errorData.error ||
                  `Failed to create update token (${response.status})`;
               throw new Error(errorMessage);
            }

            const data = await response.json();
            setLinkToken(data.link_token);
         } catch (error) {
            // Only set error if it's not an abort error
            if (error.name !== "AbortError") {
               console.error("Error creating update token:", error);
               setError("Failed to start re-authentication");
            }
         } finally {
            // Only update loading state if the component is still mounted
            // and the request wasn't aborted
            if (abortControllerRef.current) {
               setLoading(false);
            }
         }
      }, 300); // 300ms debounce time
   };

   const handleUpdateSuccess = async (public_token, metadata) => {
      try {
         setError(null);

         // The access token doesn't change in update mode, so we just need to refresh status
         await checkConnectionStatus();

         if (onStatusUpdate) {
            onStatusUpdate(account._id, {
               status: "good",
               message: "Connection restored",
               needsAction: false,
            });
         }
      } catch (error) {
         console.error("Error handling update success:", error);
         setError("Re-authentication completed but failed to update status");
      }
   };

   const handleDebugConnection = async () => {
      if (!account.plaidItemId) return;

      // Clear any existing debounce timer
      if (debounceTimerRef.current) {
         clearTimeout(debounceTimerRef.current);
      }

      // Set a new debounce timer (300ms)
      debounceTimerRef.current = setTimeout(async () => {
         try {
            // Cancel any existing request
            if (abortControllerRef.current) {
               abortControllerRef.current.abort();
            }

            // Create a new abort controller for this request
            abortControllerRef.current = new AbortController();
            const signal = abortControllerRef.current.signal;

            setLoading(true);
            setError(null);

            const response = await fetch("/api/plaid/debug-connection", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  itemId: account.plaidItemId,
               }),
               signal, // Add the abort signal to the fetch request
            });

            if (!response.ok) {
               // Try to get error details from response
               const errorData = await response.json().catch(() => ({}));
               const errorMessage =
                  errorData.error ||
                  `Failed to debug connection (${response.status})`;
               throw new Error(errorMessage);
            }

            const data = await response.json();
            setDebugInfo(data);

            // Log debug info to console for troubleshooting
            console.log("Connection Debug Info:", data);

            if (data.needsReauth) {
               setError(
                  "Connection needs re-authentication. Please use the Re-authenticate button."
               );
            }
         } catch (error) {
            // Only set error if it's not an abort error
            if (error.name !== "AbortError") {
               console.error("Error debugging connection:", error);
               setError("Failed to debug connection");
            }
         } finally {
            // Only update loading state if the component is still mounted
            // and the request wasn't aborted
            if (abortControllerRef.current) {
               setLoading(false);
            }
         }
      }, 300); // 300ms debounce time
   };

   const getStatusBadge = () => {
      const badge = (() => {
         if (!account.plaidItemId) {
            return (
               <Badge
                  variant="secondary"
                  className="inline-flex items-center gap-1"
               >
                  <WifiOff className="h-3 w-3" />
                  Manual Account
               </Badge>
            );
         }

         if (loading) {
            return (
               <Badge
                  variant="secondary"
                  className="inline-flex items-center gap-1"
               >
                  <RefreshCw className="h-3 w-3 animate-spin" />
                  Checking...
               </Badge>
            );
         }

         if (!connectionStatus) {
            return (
               <Badge
                  variant="secondary"
                  className="inline-flex items-center gap-1"
               >
                  <Wifi className="h-3 w-3" />
                  Connected
               </Badge>
            );
         }

         switch (connectionStatus.status) {
            case "good":
            case "connected":
               return (
                  <Badge
                     variant="success"
                     className="inline-flex items-center gap-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                  >
                     <CheckCircle className="h-3 w-3" />
                     Connected
                  </Badge>
               );

            case "subscription_required":
               return (
                  <Badge
                     variant="secondary"
                     className="inline-flex items-center gap-1 bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
                  >
                     <WifiOff className="h-3 w-3" />
                     Manual
                  </Badge>
               );

            case "login_required":
               return (
                  <Badge
                     variant="destructive"
                     className="inline-flex items-center gap-1"
                  >
                     <XCircle className="h-3 w-3" />
                     Login Required
                  </Badge>
               );

            case "pending_expiration":
            case "pending_disconnect":
               return (
                  <Badge
                     variant="warning"
                     className="inline-flex items-center gap-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                  >
                     <AlertTriangle className="h-3 w-3" />
                     Needs Attention
                  </Badge>
               );

            case "error":
               return (
                  <Badge
                     variant="destructive"
                     className="inline-flex items-center gap-1"
                  >
                     <XCircle className="h-3 w-3" />
                     Connection Error
                  </Badge>
               );

            default:
               return (
                  <Badge
                     variant="secondary"
                     className="inline-flex items-center gap-1"
                  >
                     <AlertTriangle className="h-3 w-3" />
                     Unknown Status
                  </Badge>
               );
         }
      })();

      // Add tooltip for detailed status information
      if (
         connectionStatus &&
         (connectionStatus.itemStatus || connectionStatus.accountStatus)
      ) {
         const tooltipContent = (
            <div className="text-xs space-y-1">
               {connectionStatus.itemStatus && (
                  <div>
                     <span className="font-medium">Item:</span>{" "}
                     {connectionStatus.itemMessage}
                  </div>
               )}
               {connectionStatus.accountStatus && (
                  <div>
                     <span className="font-medium">Accounts:</span>{" "}
                     {connectionStatus.accountMessage}
                  </div>
               )}
            </div>
         );

         return <Tooltip content={tooltipContent}>{badge}</Tooltip>;
      }

      return badge;
   };

   const showAlert = () => {
      if (!connectionStatus || !connectionStatus.needsAction) return null;

      return (
         <div className="mt-2 space-y-2">
            <Alert className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
               <AlertTriangle className="h-4 w-4" />
               <AlertDescription className="text-red-800 dark:text-red-200">
                  {connectionStatus.message}
               </AlertDescription>
            </Alert>

            {/* Show detailed item and account status if available */}
            {(connectionStatus.itemStatus ||
               connectionStatus.accountStatus) && (
               <div className="text-xs space-y-1">
                  {connectionStatus.itemStatus && (
                     <div className="flex items-center gap-2">
                        <span className="font-medium">Item:</span>
                        <Badge
                           variant={
                              connectionStatus.itemStatus === "good"
                                 ? "success"
                                 : "destructive"
                           }
                           className="text-xs"
                        >
                           {connectionStatus.itemStatus === "good" ? "✓" : "✗"}{" "}
                           {connectionStatus.itemMessage}
                        </Badge>
                     </div>
                  )}
                  {connectionStatus.accountStatus && (
                     <div className="flex items-center gap-2">
                        <span className="font-medium">Accounts:</span>
                        <Badge
                           variant={
                              connectionStatus.accountStatus === "good"
                                 ? "success"
                                 : "destructive"
                           }
                           className="text-xs"
                        >
                           {connectionStatus.accountStatus === "good"
                              ? "✓"
                              : "✗"}{" "}
                           {connectionStatus.accountMessage}
                        </Badge>
                     </div>
                  )}
               </div>
            )}
         </div>
      );
   };

   // If inline mode, just return the badge
   if (inline) {
      return getStatusBadge();
   }

   return (
      <div className="space-y-2">
         <div className="flex items-center justify-between">
            {getStatusBadge()}

            {account.plaidItemId && connectionStatus?.needsAction && (
               <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReAuthenticate}
                  disabled={loading}
                  className="h-8 px-3"
               >
                  Re-authenticate
               </Button>
            )}
         </div>

         {showAlert()}

         {error && (
            <Alert className="mt-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
               <XCircle className="h-4 w-4" />
               <AlertDescription className="text-red-800 dark:text-red-200">
                  {error}
               </AlertDescription>
            </Alert>
         )}
      </div>
   );
}

"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
   <PERSON>alog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from "@/components/ui/dialog";
import { AlertTriangle, Trash2 } from "lucide-react";

export default function DeleteAccountModal({ isOpen, onClose }) {
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState("");
   const [confirmationText, setConfirmationText] = useState("");
   const router = useRouter();

   const handleDeleteAccount = async () => {
      if (confirmationText !== "DELETE") {
         setError("Please type 'DELETE' to confirm account deletion");
         return;
      }

      setLoading(true);
      setError("");

      try {
         const response = await fetch("/api/user/delete-account", {
            method: "DELETE",
            headers: {
               "Content-Type": "application/json",
            },
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to delete account");
         }

         const result = await response.json();
         console.log("Account deleted successfully:", result);

         // Sign out the user and redirect to register page
         await signOut({ callbackUrl: "/auth/register" });
      } catch (err) {
         console.error("Error deleting account:", err);
         setError(err.message);
      } finally {
         setLoading(false);
      }
   };

   const handleClose = () => {
      if (!loading) {
         setConfirmationText("");
         setError("");
         onClose();
      }
   };

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 sm:max-w-[500px]">
            <DialogHeader>
               <DialogTitle className="text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Delete Account
               </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
               <Alert className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <AlertDescription className="text-red-700 dark:text-red-300">
                     <strong>Warning:</strong> This action cannot be undone.
                     This will permanently delete your account and all
                     associated data.
                  </AlertDescription>
               </Alert>

               <div className="space-y-3">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                     <p className="font-medium mb-2">
                        The following data will be permanently deleted:
                     </p>
                     <ul className="list-disc list-inside space-y-1 ml-4">
                        <li>All transactions and financial records</li>
                        <li>All income and expense tracking data</li>
                        <li>All debt information and payment history</li>
                        <li>
                           All connected bank accounts and Plaid connections
                        </li>
                        <li>All user accounts and balances</li>
                        <li>Profile information and preferences</li>
                        <li>Your authentication account</li>
                     </ul>
                  </div>

                  <div className="space-y-2">
                     <Label
                        htmlFor="confirmation"
                        className="text-gray-900 dark:text-gray-100"
                     >
                        To confirm deletion, type <strong>DELETE</strong> in the
                        box below:
                     </Label>
                     <Input
                        id="confirmation"
                        type="text"
                        value={confirmationText}
                        onChange={(e) => setConfirmationText(e.target.value)}
                        placeholder="Type DELETE to confirm"
                        disabled={loading}
                        className="h-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                     />
                  </div>

                  {error && (
                     <Alert className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                        <AlertDescription className="text-red-700 dark:text-red-300">
                           {error}
                        </AlertDescription>
                     </Alert>
                  )}
               </div>
            </div>

            <DialogFooter className="gap-2">
               <Button
                  variant="outline"
                  onClick={handleClose}
                  disabled={loading}
                  className="h-10"
               >
                  Cancel
               </Button>
               <Button
                  variant="destructive"
                  onClick={handleDeleteAccount}
                  disabled={loading || confirmationText !== "DELETE"}
                  className="h-10"
               >
                  {loading ? (
                     <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Deleting...
                     </>
                  ) : (
                     <>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Account
                     </>
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

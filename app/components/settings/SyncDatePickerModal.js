"use client";

import { useState, useEffect } from "react";
import {
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>ontent,
   <PERSON><PERSON><PERSON>eader,
   <PERSON>alogTitle,
   <PERSON>alogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, AlertTriangle, Info } from "lucide-react";

const SYNC_OPTIONS = [
   {
      value: "today",
      label: "From Today Forward",
      description:
         "Only sync transactions from today onward. Your existing account balance should reflect all previous transactions.",
      recommendation: "Recommended for most users",
      icon: "📅",
   },
   {
      value: "current_month",
      label: "From Start of Current Month",
      description:
         "Sync transactions from the beginning of this month. Good for mid-month connections.",
      recommendation: "Good choice if connecting mid-month",
      icon: "📊",
   },
   {
      value: "1_month",
      label: "Past 1 Month",
      description:
         "Sync the last 30 days of transactions. Useful for reconciling recent activity.",
      recommendation: "",
      icon: "📆",
   },
   {
      value: "3_months",
      label: "Past 3 Months",
      description:
         "Sync the last 90 days of transactions. Provides good historical context.",
      recommendation: "",
      icon: "📋",
   },
   {
      value: "6_months",
      label: "Past 6 Months",
      description:
         "Sync the last 180 days of transactions. Good for comprehensive tracking.",
      recommendation: "",
      icon: "📈",
   },
   {
      value: "custom",
      label: "Custom Date",
      description: "Choose a specific start date for syncing transactions.",
      recommendation: "",
      icon: "🗓️",
   },
];

export default function SyncDatePickerModal({
   isOpen,
   onClose,
   onConfirm,
   institutionName = "this bank",
   accountCreationDate = null,
}) {
   const [selectedOption, setSelectedOption] = useState("today");
   const [customDate, setCustomDate] = useState("");
   const [calculatedDate, setCalculatedDate] = useState(null);
   const [error, setError] = useState("");

   // Calculate the minimum allowed date (account creation date or 2 years ago, whichever is more recent)
   const getMinDate = () => {
      const twoYearsAgo = new Date();
      twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);

      if (accountCreationDate) {
         const creationDate = new Date(accountCreationDate);
         return creationDate > twoYearsAgo ? creationDate : twoYearsAgo;
      }

      return twoYearsAgo;
   };

   // Calculate the actual sync date based on selected option
   useEffect(() => {
      const today = new Date();
      let date = null;

      switch (selectedOption) {
         case "today":
            date = today;
            break;
         case "current_month":
            date = new Date(today.getFullYear(), today.getMonth(), 1);
            break;
         case "1_month":
            date = new Date(today);
            date.setDate(date.getDate() - 30);
            break;
         case "3_months":
            date = new Date(today);
            date.setDate(date.getDate() - 90);
            break;
         case "6_months":
            date = new Date(today);
            date.setDate(date.getDate() - 180);
            break;
         case "custom":
            if (customDate) {
               // Parse custom date in local timezone to avoid date shifts
               const [year, month, day] = customDate.split("-").map(Number);
               date = new Date(year, month - 1, day);
            }
            break;
      }

      setCalculatedDate(date);
      setError("");

      // Validate the date
      if (date) {
         const minDate = getMinDate();
         if (date < minDate) {
            setError(
               `Date cannot be earlier than ${minDate.toLocaleDateString()}`
            );
         } else if (date > today) {
            setError("Date cannot be in the future");
         }
      }
   }, [selectedOption, customDate, accountCreationDate]);

   const handleConfirm = () => {
      if (!calculatedDate || error) {
         return;
      }

      // Format the date for the API - use local timezone to avoid date shifts
      const year = calculatedDate.getFullYear();
      const month = String(calculatedDate.getMonth() + 1).padStart(2, "0");
      const day = String(calculatedDate.getDate()).padStart(2, "0");
      const syncStartDate = `${year}-${month}-${day}`;

      onConfirm({
         option: selectedOption,
         date: syncStartDate,
         calculatedDate: calculatedDate,
      });
   };

   const formatDate = (date) => {
      return date
         ? date.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
           })
         : "";
   };

   const getFormattedCustomDateInput = () => {
      const minDate = getMinDate();
      const today = new Date();

      return (
         <input
            type="date"
            value={customDate}
            onChange={(e) => setCustomDate(e.target.value)}
            min={minDate.toISOString().split("T")[0]}
            max={today.toISOString().split("T")[0]}
            className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
         />
      );
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 max-w-2xl">
            <DialogHeader>
               <DialogTitle className="text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Choose Transaction Sync Start Date
               </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
               <Alert className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                  <Info className="h-4 w-4" />
                  <AlertDescription className="text-blue-800 dark:text-blue-200">
                     Select when to start syncing transactions from{" "}
                     {institutionName}. Only transactions from your chosen date
                     onward will be imported.
                  </AlertDescription>
               </Alert>

               <div className="space-y-3">
                  <Label className="text-gray-900 dark:text-gray-100 text-base font-medium">
                     Sync Start Date Options
                  </Label>

                  <div className="grid gap-3">
                     {SYNC_OPTIONS.map((option) => (
                        <Card
                           key={option.value}
                           className={`cursor-pointer transition-all ${
                              selectedOption === option.value
                                 ? "ring-2 ring-blue-500 border-blue-200 dark:border-blue-600"
                                 : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                           }`}
                           onClick={() => setSelectedOption(option.value)}
                        >
                           <CardContent className="p-4">
                              <div className="flex items-start gap-3">
                                 <div className="flex-shrink-0">
                                    <span className="text-xl">
                                       {option.icon}
                                    </span>
                                 </div>
                                 <div className="flex-grow">
                                    <div className="flex items-center gap-2">
                                       <span className="font-medium text-gray-900 dark:text-gray-100">
                                          {option.label}
                                       </span>
                                       {option.recommendation && (
                                          <span className="text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                                             {option.recommendation}
                                          </span>
                                       )}
                                    </div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                       {option.description}
                                    </p>
                                    {selectedOption === option.value &&
                                       option.value === "custom" && (
                                          <div className="mt-3">
                                             <Label className="text-sm text-gray-700 dark:text-gray-300">
                                                Select Date
                                             </Label>
                                             <div className="mt-1">
                                                {getFormattedCustomDateInput()}
                                             </div>
                                          </div>
                                       )}
                                 </div>
                                 <div className="flex-shrink-0">
                                    <div
                                       className={`w-4 h-4 rounded-full border-2 ${
                                          selectedOption === option.value
                                             ? "border-blue-500 bg-blue-500"
                                             : "border-gray-300 dark:border-gray-600"
                                       }`}
                                    >
                                       {selectedOption === option.value && (
                                          <div className="w-2 h-2 bg-white rounded-full m-auto mt-0.5"></div>
                                       )}
                                    </div>
                                 </div>
                              </div>
                           </CardContent>
                        </Card>
                     ))}
                  </div>
               </div>

               {calculatedDate && !error && (
                  <Card className="bg-gray-50 dark:bg-gray-800">
                     <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                           <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Transactions will be synced from:
                           </span>
                           <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                              {formatDate(calculatedDate)}
                           </span>
                        </div>
                     </CardContent>
                  </Card>
               )}

               {error && (
                  <Alert className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                     <AlertTriangle className="h-4 w-4" />
                     <AlertDescription className="text-red-800 dark:text-red-200">
                        {error}
                     </AlertDescription>
                  </Alert>
               )}
            </div>

            <DialogFooter>
               <Button
                  variant="outline"
                  onClick={onClose}
                  className="text-gray-700 dark:text-gray-300"
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleConfirm}
                  disabled={!calculatedDate || !!error}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
               >
                  Continue with Bank Connection
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

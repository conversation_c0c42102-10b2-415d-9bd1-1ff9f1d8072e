"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import ConfirmDialog from "./ConfirmDialog";
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, XCircle, Lock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import SyncDatePickerModal from "./SyncDatePickerModal";

const ACCOUNT_TYPES = [
   {
      value: "cash",
      label: "Cash",
      description:
         "For checking, savings, and cash accounts that will be included in your budget calculations.",
   },
];

const formatNumber = (number) => {
   return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
   }).format(Math.abs(number));
};

export default function AddAccountModal({
   onClose,
   onSave,
   onLinkBank,
   plaidConnection,
}) {
   const { data: session } = useSession();
   const [loading, setLoading] = useState(false);
   const [showStartOverConfirm, setShowStartOverConfirm] = useState(false);
   const [isConnectingPlaid, setIsConnectingPlaid] = useState(false);
   const [error, setError] = useState(null);
   const [hasBasicSubscription, setHasBasicSubscription] = useState(false);
   const [subscriptionLoading, setSubscriptionLoading] = useState(true);
   const [newAccount, setNewAccount] = useState({
      name: "",
      bank: plaidConnection ? plaidConnection.institutionName : "",
      accountType: "cash",
      plaidItemId: plaidConnection?.itemId || null,
   });

   // Add new state for sync start date and date picker modal
   const [syncStartDate, setSyncStartDate] = useState("today");
   const [showSyncDatePicker, setShowSyncDatePicker] = useState(false);
   const [selectedSyncDate, setSelectedSyncDate] = useState(null);

   // Check subscription status
   useEffect(() => {
      const checkSubscription = async () => {
         try {
            // For now, assume all authenticated users have subscription access
            // This should be properly implemented with your subscription system
            setHasBasicSubscription(!!session?.user);
         } catch (error) {
            console.error("Error checking subscription:", error);
            setHasBasicSubscription(false);
         } finally {
            setSubscriptionLoading(false);
         }
      };

      checkSubscription();
   }, [session?.user]);

   // Update bank name when Plaid connection is established
   useEffect(() => {
      if (plaidConnection) {
         setNewAccount((prev) => ({
            ...prev,
            bank: plaidConnection.institutionName,
            plaidItemId: plaidConnection.itemId,
         }));
         setIsConnectingPlaid(false);
      }
   }, [plaidConnection]);

   const handleChange = (e) => {
      const { name, value } = e.target;
      if (name === "bank" && plaidConnection) {
         // Prevent manual changes to bank name when connected to Plaid
         return;
      }

      // Handle numeric fields
      if (
         name === "balance" ||
         name === "minimumPayment" ||
         name === "interestRate"
      ) {
         setNewAccount((prev) => ({
            ...prev,
            [name]: value === "" ? "" : Number(value),
         }));
      } else {
         setNewAccount((prev) => ({
            ...prev,
            [name]: value,
         }));
      }
   };

   const handleSubmit = async (e) => {
      e.preventDefault();
      try {
         setLoading(true);
         setError(null); // Clear any previous errors

         // Process the account data
         const accountToSave = {
            ...newAccount,
            plaidItemId: newAccount.plaidItemId || null,
            syncStartDate: syncStartDate,
            accountType: newAccount.accountType || "cash", // Default to cash if not specified
         };

         // Create the account
         const response = await fetch("/api/user/accounts", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ account: accountToSave }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to create account");
         }

         const result = await response.json();
         const createdAccount = result.account;

         // If this account has a Plaid connection, update the pairing
         if (plaidConnection && plaidConnection.itemId) {
            const pairResponse = await fetch("/api/plaid/pair-account", {
               method: "POST",
               headers: { "Content-Type": "application/json" },
               body: JSON.stringify({
                  itemId: plaidConnection.itemId,
                  accountId: createdAccount._id,
                  syncStartDate: selectedSyncDate?.date || syncStartDate,
               }),
            });

            if (!pairResponse.ok) {
               const errorData = await pairResponse.json();
               throw new Error(
                  errorData.error || "Failed to pair Plaid account"
               );
            }
         }

         onSave(createdAccount);
      } catch (error) {
         console.error("Error saving account:", error);
         setError(error.message || "Failed to create account");
      } finally {
         setLoading(false);
      }
   };

   const handleStartOver = () => {
      onClose();
      onLinkBank();
      setShowStartOverConfirm(false);
   };

   const handleConnectBank = (e) => {
      e.preventDefault();
      if (!hasBasicSubscription) {
         setError(
            "Bank connections require a Basic or Pro subscription. Please upgrade to connect your bank account."
         );
         return;
      }

      // Show date picker modal first
      setShowSyncDatePicker(true);
   };

   const handleDatePickerConfirm = (dateSelection) => {
      setSelectedSyncDate(dateSelection);
      setSyncStartDate(dateSelection.date);
      setShowSyncDatePicker(false);
      setIsConnectingPlaid(true);

      // Now initiate Plaid Link with the selected date
      onLinkBank({ syncStartDate: dateSelection.date });
   };

   const handlePlaidSuccess = async (publicToken, metadata) => {
      try {
         // Exchange public token for access token
         const response = await fetch("/api/plaid/exchange-token", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               public_token: publicToken,
               institution: metadata.institution,
               syncStartDate: syncStartDate, // Add sync start date preference
            }),
         });

         if (!response.ok) {
            throw new Error("Failed to exchange token");
         }

         // ... existing code ...
      } catch (error) {
         console.error("Error handling Plaid success:", error);
      }
   };

   return (
      <>
         <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
               <DialogHeader>
                  <DialogTitle>Add New Account</DialogTitle>
               </DialogHeader>

               {error && (
                  <Alert
                     variant="destructive"
                     className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"
                  >
                     <AlertDescription>{error}</AlertDescription>
                  </Alert>
               )}

               <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                     <Label htmlFor="name">Account Name</Label>
                     <Input
                        type="text"
                        id="name"
                        name="name"
                        value={newAccount.name}
                        onChange={handleChange}
                        required
                        className="h-10"
                     />
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="bank">Bank/Institution</Label>
                     <div className="flex items-center gap-3">
                        <Input
                           type="text"
                           id="bank"
                           name="bank"
                           value={newAccount.bank}
                           onChange={handleChange}
                           required
                           className={`h-10 ${
                              plaidConnection
                                 ? "bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600"
                                 : ""
                           }`}
                           placeholder={
                              plaidConnection ? "" : "Enter bank name manually"
                           }
                           readOnly={!!plaidConnection}
                           disabled={!!plaidConnection}
                        />
                        {!plaidConnection && (
                           <Button
                              type="button"
                              onClick={handleConnectBank}
                              disabled={
                                 isConnectingPlaid || subscriptionLoading
                              }
                              className="h-10"
                           >
                              {subscriptionLoading ? (
                                 <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Loading...
                                 </>
                              ) : isConnectingPlaid ? (
                                 <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Connecting...
                                 </>
                              ) : !hasBasicSubscription ? (
                                 <>
                                    <Lock className="mr-2 h-4 w-4" />
                                    Upgrade Required
                                 </>
                              ) : (
                                 "Connect Bank"
                              )}
                           </Button>
                        )}
                     </div>

                     {!hasBasicSubscription &&
                        !plaidConnection &&
                        !subscriptionLoading && (
                           <div className="mt-2 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                              <div className="flex items-center space-x-2">
                                 <Lock className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                                 <p className="text-sm text-amber-800 dark:text-amber-200">
                                    <strong>
                                       Bank connections require a subscription.
                                    </strong>
                                 </p>
                              </div>
                              <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                                 Upgrade to Basic or Pro to automatically sync
                                 transactions from your bank account.
                              </p>
                           </div>
                        )}

                     {plaidConnection && (
                        <div className="mt-2 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                           <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                 <CheckCircle className="h-5 w-5 text-green-500" />
                                 <div>
                                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                                       Successfully Connected to Plaid
                                    </p>
                                    <p className="text-sm text-green-700 dark:text-green-300">
                                       {plaidConnection.institutionName}
                                    </p>
                                 </div>
                              </div>
                              <Button
                                 type="button"
                                 variant="outline"
                                 onClick={() => setShowStartOverConfirm(true)}
                                 className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                              >
                                 <XCircle className="mr-2 h-4 w-4" />
                                 Start Over
                              </Button>
                           </div>

                           <div className="mt-4">
                              <Label className="text-green-800 dark:text-green-200">
                                 When would you like to start syncing
                                 transactions?
                              </Label>
                              <RadioGroup
                                 value={syncStartDate}
                                 onValueChange={(value) =>
                                    setSyncStartDate(value)
                                 }
                                 className="mt-2 space-y-2"
                              >
                                 <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                       value="creation"
                                       id="syncStartCreation"
                                    />
                                    <Label
                                       htmlFor="syncStartCreation"
                                       className="text-green-800 dark:text-green-200"
                                    >
                                       From account creation date
                                    </Label>
                                 </div>
                                 <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                       value="all"
                                       id="syncStartAll"
                                    />
                                    <Label
                                       htmlFor="syncStartAll"
                                       className="text-green-800 dark:text-green-200"
                                    >
                                       All available transactions
                                    </Label>
                                 </div>
                              </RadioGroup>
                           </div>
                        </div>
                     )}
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="accountType">Account Type</Label>
                     <Select
                        value={newAccount.accountType}
                        onValueChange={(value) =>
                           handleChange({
                              target: { name: "accountType", value },
                           })
                        }
                     >
                        <SelectTrigger className="h-10">
                           <SelectValue placeholder="Select account type" />
                        </SelectTrigger>
                        <SelectContent>
                           {ACCOUNT_TYPES.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                 {type.label}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                     <p className="text-xs text-muted-foreground">
                        {ACCOUNT_TYPES.find(
                           (type) => type.value === newAccount.accountType
                        )?.description || ""}
                     </p>
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="balance">Starting Balance</Label>
                     <div className="relative">
                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500 dark:text-gray-400">
                           $
                        </span>
                        <Input
                           type="number"
                           id="balance"
                           name="balance"
                           value={newAccount.balance || ""}
                           onChange={handleChange}
                           required
                           step="0.01"
                           min="0"
                           className="h-10 !pl-7 pr-3 py-2"
                        />
                     </div>
                     <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Note: The starting balance will create a transaction in
                        this account.
                     </p>
                  </div>

                  <DialogFooter>
                     <Button
                        type="button"
                        variant="outline"
                        onClick={onClose}
                        className="h-10"
                     >
                        Cancel
                     </Button>
                     <Button type="submit" disabled={loading} className="h-10">
                        {loading ? (
                           <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                           </>
                        ) : (
                           "Save Account"
                        )}
                     </Button>
                  </DialogFooter>
               </form>
            </DialogContent>
         </Dialog>

         <SyncDatePickerModal
            isOpen={showSyncDatePicker}
            onClose={() => setShowSyncDatePicker(false)}
            onConfirm={handleDatePickerConfirm}
            institutionName="your bank"
         />

         {/* Confirmation Dialog */}
         {showStartOverConfirm && (
            <ConfirmDialog
               isOpen={showStartOverConfirm}
               onClose={() => setShowStartOverConfirm(false)}
               onConfirm={handleStartOver}
               title="Start Over?"
               message="Are you sure you want to start over? This will clear the current bank connection and allow you to connect a different bank."
            />
         )}
      </>
   );
}

"use client";

import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow,
} from "@/components/ui/table";
import EditIncomeModal from "./EditIncomeModal";
import { format } from "date-fns";

const PAY_PERIODS = [
   { value: "weekly", label: "Weekly" },
   { value: "biweekly", label: "Bi-weekly" },
   { value: "semimonthly", label: "Semi-monthly (1st & 15th)" },
   { value: "monthly", label: "Monthly" },
];

const DAYS_OF_WEEK = [
   { value: "1", label: "Monday" },
   { value: "2", label: "Tuesday" },
   { value: "3", label: "Wednesday" },
   { value: "4", label: "Thursday" },
   { value: "5", label: "Friday" },
   { value: "6", label: "Saturday" },
   { value: "7", label: "Sunday" },
];

export default function RecurringIncomes({
   recurringIncomes,
   onIncomesChange,
   onError,
   onSuccess,
}) {
   const [saving, setSaving] = useState(false);
   const [showIncomeForm, setShowIncomeForm] = useState(false);
   const [editingIncome, setEditingIncome] = useState(null);
   const [newIncome, setNewIncome] = useState({
      description: "",
      payAmount: "",
      payPeriod: "monthly",
      payDay: "1",
      payDayOfWeek: "1",
      payWeekDay: "Monday",
      enabled: true,
   });

   const handleIncomeChange = (name, value) => {
      setNewIncome((prev) => {
         const updates = { [name]: value };

         // Update related fields based on pay period
         if (name === "payPeriod") {
            if (value === "monthly") {
               updates.payDay = "1";
               updates.payDayOfWeek = undefined;
               updates.payWeekDay = undefined;
               updates.lastPaymentDate = undefined;
            } else if (value === "semimonthly") {
               updates.payDay = "1"; // Default to 1st
               updates.payDayOfWeek = undefined;
               updates.payWeekDay = undefined;
               updates.lastPaymentDate = undefined;
            } else {
               updates.payDay = undefined;
               updates.payDayOfWeek = "1";
               updates.payWeekDay = DAYS_OF_WEEK.find(
                  (day) => day.value === "1"
               )?.label;
               if (value === "biweekly") {
                  // Keep existing lastPaymentDate if it exists
                  updates.lastPaymentDate =
                     prev.lastPaymentDate ||
                     new Date().toISOString().split("T")[0];
               }
            }
         } else if (name === "payDayOfWeek") {
            // Update payWeekDay when payDayOfWeek changes
            const selectedDay = DAYS_OF_WEEK.find((day) => day.value === value);
            if (selectedDay) {
               updates.payWeekDay = selectedDay.label;
            }
         }

         return { ...prev, ...updates };
      });
   };

   const handleInputChange = (e) => {
      const { name, value } = e.target;
      handleIncomeChange(name, value);
   };

   const handleIncomeSubmit = async () => {
      try {
         setSaving(true);
         const incomeData = { ...newIncome };

         // For bi-weekly incomes, ensure lastPaymentDate exists but don't modify it
         if (incomeData.payPeriod === "biweekly") {
            if (!incomeData.lastPaymentDate) {
               onError("Last payment date is required for bi-weekly incomes");
               return;
            }
         } else {
            // Remove lastPaymentDate for non-biweekly incomes
            delete incomeData.lastPaymentDate;
         }

         const response = await fetch("/api/user/recurring-incomes", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(incomeData),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to add income");
         }

         const data = await response.json();
         onIncomesChange(data.currentIncomes);
         onSuccess("Income added successfully");
         setShowIncomeForm(false);
         setNewIncome({
            description: "",
            payAmount: "",
            payPeriod: "monthly",
            payDay: "1",
            payDayOfWeek: "1",
            payWeekDay: "Monday",
            enabled: true,
         });
      } catch (err) {
         onError(err.message);
      } finally {
         setSaving(false);
      }
   };

   const toggleIncome = async (_id) => {
      try {
         const income = recurringIncomes.find((i) => i._id === _id);
         const response = await fetch("/api/user/recurring-incomes", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               _id,
               enabled: !income.enabled,
            }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to update income");
         }

         const data = await response.json();
         onIncomesChange(data.currentIncomes);
         onSuccess("Income updated successfully");
      } catch (err) {
         onError(err.message);
      }
   };

   const deleteIncome = async (_id) => {
      try {
         const response = await fetch("/api/user/recurring-incomes", {
            method: "DELETE",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ _id }),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to delete income");
         }

         const data = await response.json();
         onIncomesChange(data.currentIncomes);
         onSuccess("Income deleted successfully");
      } catch (err) {
         onError(err.message);
      }
   };

   const handleEditIncome = (income) => {
      setEditingIncome(income);
   };

   const handleSaveEditedIncome = async (updatedIncome) => {
      try {
         setSaving(true);

         // For bi-weekly incomes, ensure lastPaymentDate exists but don't modify it
         if (updatedIncome.payPeriod === "biweekly") {
            if (!updatedIncome.lastPaymentDate) {
               onError("Last payment date is required for bi-weekly incomes");
               return;
            }
         } else {
            // Remove lastPaymentDate for non-biweekly incomes
            delete updatedIncome.lastPaymentDate;
         }

         const response = await fetch("/api/user/recurring-incomes", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(updatedIncome),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to update income");
         }

         const data = await response.json();
         onIncomesChange(data.currentIncomes);
         onSuccess("Income updated successfully");
         setEditingIncome(null);
      } catch (err) {
         onError(err.message);
      } finally {
         setSaving(false);
      }
   };

   return (
      <div className="p-6 pt-0 space-y-6">
         <div className="flex justify-between items-center">
            <Button
               variant={showIncomeForm ? "outline" : "default"}
               onClick={() => setShowIncomeForm(!showIncomeForm)}
            >
               {showIncomeForm ? "Cancel" : "Add Income"}
            </Button>
         </div>

         {showIncomeForm && (
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
               <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-2">
                     <Label htmlFor="incomeDescription">Description</Label>
                     <Input
                        type="text"
                        id="incomeDescription"
                        name="description"
                        value={newIncome.description}
                        onChange={handleInputChange}
                        required
                     />
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="incomePayAmount">Pay Amount</Label>
                     <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                           <span className="text-muted-foreground text-sm">
                              $
                           </span>
                        </div>
                        <Input
                           type="number"
                           id="incomePayAmount"
                           name="payAmount"
                           value={newIncome.payAmount}
                           onChange={handleInputChange}
                           required
                           min="0"
                           step="0.01"
                           className="h-10 !pl-7 pr-3 py-2"
                        />
                     </div>
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="incomePayPeriod">Pay Period</Label>
                     <Select
                        value={newIncome.payPeriod}
                        onValueChange={(value) =>
                           handleIncomeChange("payPeriod", value)
                        }
                     >
                        <SelectTrigger>
                           <SelectValue placeholder="Select pay period" />
                        </SelectTrigger>
                        <SelectContent>
                           {PAY_PERIODS.map((period) => (
                              <SelectItem
                                 key={period.value}
                                 value={period.value}
                              >
                                 {period.label}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  </div>

                  {newIncome.payPeriod === "semimonthly" ? (
                     <div className="space-y-2">
                        <Label htmlFor="incomePayDay">Pay Days</Label>
                        <div className="text-sm text-muted-foreground">
                           Fixed on 1st and 15th of each month
                        </div>
                     </div>
                  ) : newIncome.payPeriod === "monthly" ? (
                     <div className="space-y-2">
                        <Label htmlFor="incomePayDay">Pay Day of Month</Label>
                        <Select
                           value={newIncome.payDay}
                           onValueChange={(value) =>
                              handleIncomeChange("payDay", value)
                           }
                        >
                           <SelectTrigger>
                              <SelectValue placeholder="Select day" />
                           </SelectTrigger>
                           <SelectContent>
                              {Array.from({ length: 31 }, (_, i) => (
                                 <SelectItem
                                    key={i + 1}
                                    value={(i + 1).toString()}
                                 >
                                    {i + 1}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     </div>
                  ) : (
                     <>
                        <div className="space-y-2">
                           <Label htmlFor="incomePayDayOfWeek">
                              Pay Day of Week
                           </Label>
                           <Select
                              value={newIncome.payDayOfWeek}
                              onValueChange={(value) =>
                                 handleIncomeChange("payDayOfWeek", value)
                              }
                           >
                              <SelectTrigger>
                                 <SelectValue placeholder="Select day" />
                              </SelectTrigger>
                              <SelectContent>
                                 {DAYS_OF_WEEK.map((day) => (
                                    <SelectItem
                                       key={day.value}
                                       value={day.value}
                                    >
                                       {day.label}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                        </div>
                        {newIncome.payPeriod === "biweekly" && (
                           <div className="space-y-2">
                              <Label htmlFor="lastPaymentDate">
                                 Last Payment Date
                              </Label>
                              <Input
                                 type="date"
                                 id="lastPaymentDate"
                                 name="lastPaymentDate"
                                 value={newIncome.lastPaymentDate}
                                 onChange={handleInputChange}
                                 required
                              />
                           </div>
                        )}
                     </>
                  )}

                  <div className="sm:col-span-2 lg:col-span-4 flex justify-end">
                     <Button onClick={handleIncomeSubmit} disabled={saving}>
                        {saving ? "Adding..." : "Add Income"}
                     </Button>
                  </div>
               </div>
            </div>
         )}

         <div className="mt-4">
            {recurringIncomes.length === 0 ? (
               <p className="text-muted-foreground text-center py-6 px-6">
                  No recurring incomes added yet.
               </p>
            ) : (
               <div className="rounded-md border">
                  <Table>
                     <TableHeader>
                        <TableRow>
                           <TableHead>Description</TableHead>
                           <TableHead>Pay Amount</TableHead>
                           <TableHead>Pay Period</TableHead>
                           <TableHead>Pay Day</TableHead>
                           <TableHead>Status</TableHead>
                           <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                     </TableHeader>
                     <TableBody>
                        {recurringIncomes.map((income) => (
                           <TableRow key={income._id}>
                              <TableCell>{income.description}</TableCell>
                              <TableCell>
                                 ${parseFloat(income.payAmount).toFixed(2)}
                              </TableCell>
                              <TableCell>
                                 {
                                    PAY_PERIODS.find(
                                       (p) => p.value === income.payPeriod
                                    )?.label
                                 }
                              </TableCell>
                              <TableCell>
                                 {income.payPeriod === "semimonthly"
                                    ? "1st and 15th"
                                    : income.payPeriod === "monthly"
                                    ? `Day ${income.payDay}`
                                    : income.payWeekDay}
                                 {income.payPeriod === "biweekly" &&
                                    income.lastPaymentDate && (
                                       <span className="ml-2 text-muted-foreground text-sm">
                                          (Last paid:{" "}
                                          {format(
                                             new Date(
                                                new Date(income.lastPaymentDate)
                                                   .toISOString()
                                                   .split("T")[0] +
                                                   "T12:00:00.000Z"
                                             ),
                                             "MM/dd/yyyy"
                                          )}
                                          )
                                       </span>
                                    )}
                              </TableCell>
                              <TableCell>
                                 <Switch
                                    checked={income.enabled}
                                    onCheckedChange={() =>
                                       toggleIncome(income._id)
                                    }
                                 />
                              </TableCell>
                              <TableCell className="text-right">
                                 <div className="flex gap-2 justify-end">
                                    <Button
                                       variant="ghost"
                                       size="sm"
                                       onClick={() => handleEditIncome(income)}
                                    >
                                       Edit
                                    </Button>
                                    <Button
                                       variant="destructive"
                                       size="sm"
                                       onClick={() => deleteIncome(income._id)}
                                    >
                                       Delete
                                    </Button>
                                 </div>
                              </TableCell>
                           </TableRow>
                        ))}
                     </TableBody>
                  </Table>
               </div>
            )}
         </div>

         {/* Edit Income Modal */}
         {editingIncome && (
            <EditIncomeModal
               income={editingIncome}
               onClose={() => setEditingIncome(null)}
               onSave={handleSaveEditedIncome}
            />
         )}
      </div>
   );
}

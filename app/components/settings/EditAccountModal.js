"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import ConfirmDialog from "./ConfirmDialog";
import { ACCOUNT_TYPES } from "@/app/lib/constants";
import {
   <PERSON><PERSON>,
   DialogContent,
   <PERSON><PERSON>Header,
   <PERSON><PERSON>T<PERSON>le,
   <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, XCircle, Lock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import PlaidConnectionStatus from "./PlaidConnectionStatus";
import SyncDatePickerModal from "./SyncDatePickerModal";

const formatNumber = (number) => {
   return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
   }).format(Math.abs(number));
};

export default function EditAccountModal({
   account,
   onClose,
   onLinkBank,
   plaidConnection,
}) {
   const { data: session } = useSession();
   const [loading, setLoading] = useState(false);
   const [plaidItems, setPlaidItems] = useState([]);
   const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false);
   const [isConnectingPlaid, setIsConnectingPlaid] = useState(false);
   const [isPairingAccount, setIsPairingAccount] = useState(false);
   const [hasBasicSubscription, setHasBasicSubscription] = useState(false);
   const [subscriptionLoading, setSubscriptionLoading] = useState(true);
   const [subscriptionError, setSubscriptionError] = useState(null);
   const [connectionStatus, setConnectionStatus] = useState(null);
   const [showSyncDatePicker, setShowSyncDatePicker] = useState(false);
   const [selectedSyncDate, setSelectedSyncDate] = useState(null);
   const [editedAccount, setEditedAccount] = useState({
      ...account,
      name: account.name,
      bank: account.bank,
      accountType: account.accountType || "cash",
      balance: account.balance || 0,
      minimumPayment: account.minimumPayment || 0,
      interestRate: account.interestRate || 0,
      dueDate: account.dueDate || "1",
      plaidItemId: account.plaidItemId,
   });
   const processedConnectionsRef = useRef(new Set());

   // Check subscription status
   useEffect(() => {
      const checkSubscription = async () => {
         try {
            // For now, assume all authenticated users have subscription access
            // This should be properly implemented with your subscription system
            setHasBasicSubscription(!!session?.user);
         } catch (error) {
            console.error("Error checking subscription:", error);
            setHasBasicSubscription(false);
         } finally {
            setSubscriptionLoading(false);
         }
      };

      checkSubscription();
   }, [session?.user]);

   // Sync options are now handled automatically by the backend

   // Date calculation logic removed since sync options are handled automatically

   useEffect(() => {
      const fetchPlaidItems = async () => {
         try {
            setLoading(true);
            const response = await fetch("/api/user");
            if (!response.ok) throw new Error("Failed to fetch user data");
            const data = await response.json();
            setPlaidItems(data.plaidItems || []);
         } catch (error) {
            console.error("Error fetching plaid items:", error);
         } finally {
            setLoading(false);
         }
      };

      fetchPlaidItems();
   }, []);

   useEffect(() => {
      if (plaidConnection) {
         // With automatic backend linking, we just need to update the UI
         setEditedAccount((prev) => ({
            ...prev,
            bank: plaidConnection.institutionName,
            plaidItemId: plaidConnection.itemId,
         }));
         setIsConnectingPlaid(false);
         setIsPairingAccount(false);
      }
   }, [plaidConnection]);

   // handlePlaidConnection is no longer needed as linking is automatic

   const handleChange = (e) => {
      const { name, value } = e.target;
      if (
         name === "bank" &&
         (editedAccount.plaidItemId || plaidConnection) &&
         hasBasicSubscription
      ) {
         // Prevent manual changes to bank name when connected to Plaid
         return;
      }

      // Handle numeric fields
      if (
         name === "balance" ||
         name === "minimumPayment" ||
         name === "interestRate"
      ) {
         setEditedAccount((prev) => ({
            ...prev,
            [name]: value === "" ? "" : Number(value),
         }));
      } else {
         setEditedAccount((prev) => ({
            ...prev,
            [name]: value,
         }));
      }
   };

   const handleSubmit = async (e) => {
      e.preventDefault();
      try {
         setLoading(true);

         // Process account data
         const updatedAccount = {
            ...editedAccount,
            plaidItemId: editedAccount.plaidItemId,
         };

         console.log("Submitting account with data:", updatedAccount);

         // Update the account
         const accountResponse = await fetch("/api/user/accounts", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ account: updatedAccount }),
         });

         if (!accountResponse.ok) {
            const errorData = await accountResponse.json();
            throw new Error(errorData.error || "Failed to update account");
         }

         // Get the updated account data
         const accountData = await accountResponse.json();
         console.log("Account update response:", accountData);

         // Close the modal - account linking is handled automatically by the backend
         onClose();
      } catch (error) {
         console.error("Error saving account:", error);
         throw error;
      } finally {
         setLoading(false);
      }
   };

   const handleDisconnect = async () => {
      try {
         const response = await fetch("/api/plaid/disconnect", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               itemId: editedAccount.plaidItemId,
            }),
         });

         if (!response.ok) throw new Error("Failed to disconnect");

         setEditedAccount((prev) => ({
            ...prev,
            plaidItemId: null,
         }));
         setShowDisconnectConfirm(false);
      } catch (error) {
         console.error("Error disconnecting Plaid:", error);
      }
   };

   const handleConnectBank = async (e) => {
      e.preventDefault();
      if (!hasBasicSubscription) {
         setSubscriptionError(
            "Bank connections require a Basic or Pro subscription. Please upgrade to connect your bank account."
         );
         return;
      }

      // Show date picker modal first
      setShowSyncDatePicker(true);
   };

   const handleDatePickerConfirm = async (dateSelection) => {
      setSelectedSyncDate(dateSelection);
      setShowSyncDatePicker(false);

      try {
         setIsConnectingPlaid(true);
         setSubscriptionError(null);

         const response = await fetch("/api/plaid/create-link-token", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               accountId: String(account._id),
               syncStartDate: dateSelection.date,
            }),
         });

         if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));

            if (response.status === 403 && errorData.requiresUpgrade) {
               setSubscriptionError(
                  "Bank connections require a Basic or Pro subscription plan. Please upgrade your subscription to connect your bank account."
               );
               return;
            }

            throw new Error(errorData.message || "Failed to create link token");
         }

         const data = await response.json();

         // Call the parent's onLinkBank with the link token data (now includes syncStartDate)
         onLinkBank(data);
      } catch (error) {
         console.error("Error initiating bank link:", error);
         setSubscriptionError(
            "Failed to initiate bank connection. Please try again."
         );
      } finally {
         setIsConnectingPlaid(false);
      }
   };

   const handleConnectionStatusUpdate = (accountId, statusInfo) => {
      setConnectionStatus(statusInfo);
   };

   return (
      <>
         <Dialog open={true} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
               <DialogHeader>
                  <DialogTitle>Edit Account</DialogTitle>
               </DialogHeader>

               {subscriptionError && (
                  <Alert
                     variant="destructive"
                     className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"
                  >
                     <AlertDescription>{subscriptionError}</AlertDescription>
                  </Alert>
               )}

               <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                     <Label htmlFor="name">Account Name</Label>
                     <Input
                        type="text"
                        id="name"
                        name="name"
                        value={editedAccount.name}
                        onChange={handleChange}
                        required
                        className="h-10"
                     />
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="bank">Bank/Institution</Label>
                     <div className="flex items-center gap-3">
                        <Input
                           type="text"
                           id="bank"
                           name="bank"
                           value={editedAccount.bank}
                           onChange={handleChange}
                           required
                           className={`h-10 ${
                              (editedAccount.plaidItemId || plaidConnection) &&
                              hasBasicSubscription
                                 ? "bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600"
                                 : ""
                           }`}
                           placeholder={
                              (editedAccount.plaidItemId || plaidConnection) &&
                              hasBasicSubscription
                                 ? ""
                                 : "Enter bank name manually"
                           }
                           readOnly={
                              !!(
                                 (editedAccount.plaidItemId ||
                                    plaidConnection) &&
                                 hasBasicSubscription
                              )
                           }
                           disabled={
                              !!(
                                 (editedAccount.plaidItemId ||
                                    plaidConnection) &&
                                 hasBasicSubscription
                              )
                           }
                        />
                        {!editedAccount.plaidItemId && !plaidConnection && (
                           <Button
                              type="button"
                              onClick={handleConnectBank}
                              disabled={
                                 isConnectingPlaid || subscriptionLoading
                              }
                              className="h-10"
                           >
                              {subscriptionLoading ? (
                                 <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Loading...
                                 </>
                              ) : isConnectingPlaid ? (
                                 <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Connecting...
                                 </>
                              ) : !hasBasicSubscription ? (
                                 <>
                                    <Lock className="mr-2 h-4 w-4" />
                                    Upgrade Required
                                 </>
                              ) : (
                                 "Connect Bank"
                              )}
                           </Button>
                        )}
                     </div>

                     {!hasBasicSubscription &&
                        !editedAccount.plaidItemId &&
                        !plaidConnection &&
                        !subscriptionLoading && (
                           <div className="mt-2 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                              <div className="flex items-center space-x-2">
                                 <Lock className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                                 <p className="text-sm text-amber-800 dark:text-amber-200">
                                    <strong>
                                       Bank connections require a subscription.
                                    </strong>
                                 </p>
                              </div>
                              <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                                 Upgrade to Basic or Pro to automatically sync
                                 transactions from your bank account.
                              </p>
                           </div>
                        )}

                     {(editedAccount.plaidItemId || plaidConnection) && (
                        <div className="mt-2 space-y-4">
                           {/* Connection Status */}
                           <div className="p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-md">
                              <div className="flex items-center justify-between mb-3">
                                 <div className="flex items-center space-x-3">
                                    <div>
                                       <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                          Bank Connection Status
                                       </p>
                                       <p className="text-sm text-gray-600 dark:text-gray-400">
                                          {plaidConnection
                                             ? plaidConnection.institutionName
                                             : editedAccount.bank}
                                       </p>
                                    </div>
                                 </div>
                                 {editedAccount.plaidItemId &&
                                    hasBasicSubscription && (
                                       <Button
                                          type="button"
                                          variant="outline"
                                          onClick={() =>
                                             setShowDisconnectConfirm(true)
                                          }
                                          className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                                       >
                                          <XCircle className="mr-2 h-4 w-4" />
                                          Disconnect
                                       </Button>
                                    )}
                              </div>

                              {/* PlaidConnectionStatus Component */}
                              {editedAccount.plaidItemId &&
                                 hasBasicSubscription && (
                                    <PlaidConnectionStatus
                                       account={editedAccount}
                                       onStatusUpdate={
                                          handleConnectionStatusUpdate
                                       }
                                       inline={false}
                                    />
                                 )}

                              {/* Show pairing status */}
                              {isPairingAccount && (
                                 <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                                    <div className="flex items-center space-x-2">
                                       <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                                       <span className="text-sm text-blue-800 dark:text-blue-200">
                                          Pairing account with bank
                                          connection...
                                       </span>
                                    </div>
                                 </div>
                              )}

                              {/* New connection setup message */}
                              {plaidConnection &&
                                 !editedAccount.plaidItemId && (
                                    <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                                       <div className="flex items-center space-x-2">
                                          <CheckCircle className="h-4 w-4 text-green-600" />
                                          <span className="text-sm text-green-800 dark:text-green-200">
                                             Successfully connected to Plaid
                                          </span>
                                       </div>
                                    </div>
                                 )}

                              {/* Sync info */}
                              <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
                                 This account will automatically sync
                                 transactions and balances from your bank
                              </p>
                           </div>
                        </div>
                     )}
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="accountType">Account Type</Label>
                     <Select
                        name="accountType"
                        value={editedAccount.accountType}
                        onValueChange={(value) =>
                           setEditedAccount((prev) => ({
                              ...prev,
                              accountType: value,
                           }))
                        }
                     >
                        <SelectTrigger className="h-10">
                           <SelectValue placeholder="Select account type" />
                        </SelectTrigger>
                        <SelectContent>
                           {ACCOUNT_TYPES.filter(
                              (type) => type.value === "cash"
                           ).map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                 {type.label}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                     <p className="text-xs text-muted-foreground">
                        {ACCOUNT_TYPES.filter(
                           (type) => type.value === "cash"
                        ).find(
                           (type) => type.value === editedAccount.accountType
                        )?.description || ""}
                     </p>
                  </div>

                  <div className="space-y-2">
                     <Label htmlFor="balance">Current Balance</Label>
                     <div className="relative">
                        <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500 dark:text-gray-400">
                           $
                        </span>
                        <Input
                           type="number"
                           id="balance"
                           name="balance"
                           value={editedAccount.balance}
                           onChange={handleChange}
                           step="0.01"
                           className="h-10 !pl-7 pr-3 py-2"
                        />
                     </div>
                     <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Note: Changing the balance will create an adjustment
                        transaction to reconcile the difference.
                     </p>
                  </div>

                  {editedAccount.accountType === "credit" && (
                     <>
                        <div className="space-y-2">
                           <Label htmlFor="dueDate">
                              Due Date (Day of Month)
                           </Label>
                           <Input
                              type="number"
                              id="dueDate"
                              name="dueDate"
                              min="1"
                              max="31"
                              value={editedAccount.dueDate}
                              onChange={handleChange}
                              required
                              className="h-10"
                           />
                        </div>
                        <div className="space-y-2">
                           <Label htmlFor="minimumPayment">
                              Minimum Payment
                           </Label>
                           <div className="relative">
                              <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500 dark:text-gray-400">
                                 $
                              </span>
                              <Input
                                 type="number"
                                 id="minimumPayment"
                                 name="minimumPayment"
                                 value={editedAccount.minimumPayment}
                                 onChange={handleChange}
                                 step="0.01"
                                 required
                                 className="h-10 !pl-7"
                              />
                           </div>
                        </div>
                        <div className="space-y-2">
                           <Label htmlFor="interestRate">
                              Interest Rate (%)
                           </Label>
                           <Input
                              type="number"
                              id="interestRate"
                              name="interestRate"
                              value={editedAccount.interestRate}
                              onChange={handleChange}
                              step="0.01"
                              required
                              className="h-10"
                           />
                        </div>
                     </>
                  )}

                  <DialogFooter>
                     <Button
                        type="button"
                        variant="outline"
                        onClick={onClose}
                        disabled={isPairingAccount}
                        className="h-10"
                     >
                        Cancel
                     </Button>
                     <Button
                        type="submit"
                        disabled={loading || isPairingAccount}
                        className="h-10"
                     >
                        {loading ? (
                           <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                           </>
                        ) : (
                           "Save Account"
                        )}
                     </Button>
                  </DialogFooter>
               </form>
            </DialogContent>
         </Dialog>

         <SyncDatePickerModal
            isOpen={showSyncDatePicker}
            onClose={() => setShowSyncDatePicker(false)}
            onConfirm={handleDatePickerConfirm}
            institutionName={editedAccount.bank || "your bank"}
         />

         {/* Disconnect Confirmation Dialog */}
         {showDisconnectConfirm && (
            <ConfirmDialog
               isOpen={showDisconnectConfirm}
               onClose={() => setShowDisconnectConfirm(false)}
               onConfirm={handleDisconnect}
               title="Disconnect Bank Account?"
               message="Are you sure you want to disconnect this bank account? You will no longer receive automatic transaction updates for this account."
            />
         )}
      </>
   );
}

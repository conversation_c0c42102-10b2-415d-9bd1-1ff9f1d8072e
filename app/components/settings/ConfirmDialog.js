import React from "react";
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

export default function ConfirmDialog({
   isOpen,
   onClose,
   onConfirm,
   title,
   message,
}) {
   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
                     <AlertTriangle className="h-5 w-5 text-destructive" />
                  </div>
                  <div>
                     <DialogTitle className="text-left">{title}</DialogTitle>
                     <DialogDescription className="text-left">
                        {message}
                     </DialogDescription>
                  </div>
               </div>
            </DialogHeader>
            <div className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end">
               <Button variant="outline" onClick={onClose}>
                  Cancel
               </Button>
               <Button variant="destructive" onClick={onConfirm}>
                  Delete
               </Button>
            </div>
         </DialogContent>
      </Dialog>
   );
}

import { format, parseISO } from "date-fns";
import { FormatAmountDisplay } from "./ExpenseUIComponents";
import { StatusBadge, AvailablePill } from "./ExpenseStatusComponents";

export const ExpenseCard = ({
   expense,
   editingDue,
   editingDueValue,
   editingAssigned,
   editingValue,
   showAvailableDropdown,
   isProcessing,
   onDueClick,
   onDueChange,
   onDueBlur,
   onDueKeyDown,
   onAssignedClick,
   onAssignedBlur,
   onAssignedKeyDown,
   onPillClick,
   onCollectAmount,
   onAssignAmount,
   onSchedule,
   onHide,
   onUnhide,
   onDelete,
   onEdit,
}) => {
   return (
      <div
         className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors ${
            expense.status === "projected"
               ? "bg-gray-50/50 dark:bg-gray-800/50"
               : ""
         }`}
      >
         <div className="p-2 py-1">
            <div className="flex justify-between items-center">
               <div className="flex-1">
                  <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-1">
                     <span>
                        {expense.startDate && expense.endDate ? (
                           <>
                              {format(
                                 expense.startDate instanceof Date
                                    ? expense.startDate
                                    : parseISO(expense.startDate),
                                 "MMM d"
                              )}
                              {" - "}
                              {format(
                                 expense.endDate instanceof Date
                                    ? expense.endDate
                                    : parseISO(expense.endDate),
                                 "MMM d, yyyy"
                              )}
                           </>
                        ) : (
                           format(
                              expense.date instanceof Date
                                 ? expense.date
                                 : parseISO(expense.date),
                              "MMM d, yyyy"
                           )
                        )}
                     </span>
                     <div className="flex items-center space-x-2">
                        <StatusBadge status={expense.status} />
                     </div>
                  </div>
                  <div className="flex items-center space-x-2">
                     <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {expense.description}
                        {expense.isDebtPayment && (
                           <span className="ml-2 px-1.5 py-0.5 text-xs font-semibold rounded-md bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                              Debt
                           </span>
                        )}
                     </div>
                  </div>
               </div>
               <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-8">
                     {/* Due Amount */}
                     <div className="w-32">
                        <div className="text-sm text-gray-900 dark:text-white text-right">
                           {editingDue === expense._id ? (
                              <input
                                 type="number"
                                 value={editingDueValue}
                                 onChange={onDueChange}
                                 onBlur={() => onDueBlur(expense)}
                                 onKeyDown={(e) => onDueKeyDown(e, expense)}
                                 className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                                 autoFocus
                                 step="0.01"
                                 min="0"
                              />
                           ) : (
                              <button
                                 onClick={() => onDueClick(expense)}
                                 className={`w-full text-right inline-flex items-center justify-end px-2 py-1 border border-transparent rounded ${
                                    expense.status === "scheduled" ||
                                    expense.status === "funded" ||
                                    expense.status === "paid" ||
                                    expense.status === "late" ||
                                    expense.status === "overpaid" ||
                                    expense.status === "underbudget"
                                       ? "hover:border-gray-200 dark:hover:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                                       : "cursor-not-allowed opacity-75"
                                 }`}
                              >
                                 <FormatAmountDisplay
                                    value={expense.amountDue}
                                 />
                              </button>
                           )}
                        </div>
                     </div>

                     {/* Assigned Amount */}
                     <div className="w-32">
                        <div className="text-sm text-gray-900 dark:text-white text-right">
                           {editingAssigned === expense._id ? (
                              <input
                                 type="text"
                                 value={editingValue}
                                 onChange={() => {}} // Legacy component - needs proper implementation
                                 onBlur={() => onAssignedBlur(expense)}
                                 onKeyDown={(e) =>
                                    onAssignedKeyDown(e, expense)
                                 }
                                 className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                                 autoFocus
                              />
                           ) : (
                              <button
                                 onClick={() => onAssignedClick(expense)}
                                 className={`w-full text-right inline-flex items-center justify-end px-2 py-1 border border-transparent rounded ${
                                    expense.status === "scheduled" ||
                                    expense.status === "funded" ||
                                    expense.status === "paid" ||
                                    expense.status === "late" ||
                                    expense.status === "overpaid" ||
                                    expense.status === "underbudget"
                                       ? "hover:border-gray-200 dark:hover:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                                       : "cursor-not-allowed opacity-75"
                                 }`}
                              >
                                 <FormatAmountDisplay
                                    value={expense.amountAssigned}
                                 />
                              </button>
                           )}
                        </div>
                     </div>

                     {/* Spent Amount */}
                     <div className="w-32">
                        <div className="text-sm text-gray-900 dark:text-white text-right">
                           <FormatAmountDisplay value={expense.amountSpent} />
                        </div>
                     </div>

                     {/* Available Amount */}
                     <div className="w-32">
                        <div className="text-right">
                           <AvailablePill
                              expense={expense}
                              showDropdown={showAvailableDropdown}
                              onPillClick={onPillClick}
                              onCollectAmount={onCollectAmount}
                              onAssignAmount={onAssignAmount}
                              isProcessing={isProcessing}
                           />
                        </div>
                     </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4">
                     {expense.status === "projected" ? (
                        <>
                           {!expense.isHidden && (
                              <button
                                 onClick={() => onSchedule(expense)}
                                 className="text-sm px-2 py-1 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                                 title="Schedule"
                              >
                                 Schedule
                              </button>
                           )}
                           <button
                              onClick={(e) =>
                                 expense.isHidden
                                    ? onUnhide(expense, e)
                                    : onHide(expense, e)
                              }
                              className="text-sm px-2 py-1 text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 font-medium"
                              title={expense.isHidden ? "Unskip" : "Skip"}
                           >
                              {expense.isHidden ? "Unskip" : "Skip"}
                           </button>
                           <button
                              onClick={() => onEdit(expense)}
                              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                              title="Edit"
                           >
                              <svg
                                 xmlns="http://www.w3.org/2000/svg"
                                 className="h-5 w-5"
                                 fill="none"
                                 viewBox="0 0 24 24"
                                 stroke="currentColor"
                              >
                                 <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                 />
                              </svg>
                           </button>
                        </>
                     ) : (
                        <button
                           onClick={() => onDelete(expense._id)}
                           className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                           <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-5 w-5"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                           >
                              <path d="M3 6h18" />
                              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                           </svg>
                        </button>
                     )}
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
};

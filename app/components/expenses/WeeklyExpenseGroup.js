"use client";

import { Fragment, useState } from "react";
import { format, parseISO } from "date-fns";
import { ExpenseRow } from "./ExpenseRow";
import {
   formatAmount,
   getAvailablePillStyle,
} from "../../lib/utils/expenseUtils";

export default function WeeklyExpenseGroup({
   weeklyExpenses,
   onSchedule,
   onHide,
   onUnhide,
   onDelete,
   onEdit,
   dateRange,
   isMobileView,
   selectedMobileColumn,
}) {
   const [expandedGroups, setExpandedGroups] = useState(new Set());

   if (!weeklyExpenses || weeklyExpenses.length === 0) {
      return null;
   }

   // Group weekly expenses by description
   const groupedExpenses = weeklyExpenses.reduce((groups, expense) => {
      const key = expense.description?.toLowerCase().trim() || "unnamed";
      if (!groups[key]) {
         groups[key] = {
            description: expense.description,
            category: expense.category,
            expenses: [],
            totalDue: 0,
            totalAssigned: 0,
            totalSpent: 0,
            totalAvailable: 0,
         };
      }

      groups[key].expenses.push(expense);
      groups[key].totalDue += expense.amountDue || 0;
      groups[key].totalAssigned += expense.amountAssigned || 0;
      groups[key].totalSpent += expense.amountSpent || 0;
      groups[key].totalAvailable += expense.amountAvailable || 0;

      return groups;
   }, {});

   const handleToggleGroup = (groupKey) => {
      setExpandedGroups((prev) => {
         const newSet = new Set(prev);
         if (newSet.has(groupKey)) {
            newSet.delete(groupKey);
         } else {
            newSet.add(groupKey);
         }
         return newSet;
      });
   };

   // Function to get dot color for status
   const getStatusDotColor = (status, isFutureFunded = false) => {
      if (status === "future" && isFutureFunded) {
         return "bg-green-500";
      }

      const colors = {
         projected: "bg-blue-500",
         scheduled: "bg-yellow-500",
         funded: "bg-green-500",
         paid: "bg-green-500",
         received: "bg-green-500",
         late: "bg-red-500",
         overpaid: "bg-orange-500",
         underpaid: "bg-green-500",
         underbudget: "bg-purple-500",
         hidden: "bg-gray-500",
         future: "bg-indigo-500",
      };
      return colors[status] || "bg-yellow-500";
   };

   return (
      <Fragment>
         {Object.entries(groupedExpenses).map(([groupKey, group]) => {
            const isExpanded = expandedGroups.has(groupKey);
            const expenseCount = group.expenses.length;

            // If there's only one expense in this group, render it as a regular ExpenseRow
            if (expenseCount === 1) {
               return (
                  <ExpenseRow
                     key={group.expenses[0]._id || `single-weekly-${groupKey}`}
                     expense={group.expenses[0]}
                     onSchedule={onSchedule}
                     onHide={onHide}
                     onUnhide={onUnhide}
                     onDelete={onDelete}
                     onEdit={onEdit}
                     dateRange={dateRange}
                     isMobileView={isMobileView}
                     selectedMobileColumn={selectedMobileColumn}
                  />
               );
            }

            // For multiple expenses, use the original grouped format
            return (
               <Fragment key={`weekly-group-${groupKey}`}>
                  {/* Group Header Row */}
                  <tr
                     className="bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800/50 border-l-2 border-gray-300 dark:border-gray-600 cursor-pointer"
                     onClick={() => handleToggleGroup(groupKey)}
                  >
                     <td className="px-4 py-2 whitespace-normal break-words text-sm">
                        <div className="flex flex-col">
                           <span className="font-semibold text-gray-900 dark:text-gray-100">
                              {group.description}
                           </span>
                           <span className="text-xs text-gray-500 dark:text-gray-400">
                              {expenseCount} week{expenseCount !== 1 ? "s" : ""}
                           </span>
                        </div>
                     </td>

                     {/* Status Dots Column */}
                     <td className="px-2 py-2 whitespace-nowrap text-sm text-center">
                        <div className="flex items-center justify-center gap-1 flex-wrap max-w-[80px]">
                           {group.expenses
                              .sort(
                                 (a, b) =>
                                    new Date(a.date || a.startDate) -
                                    new Date(b.date || b.startDate)
                              )
                              .map((expense, index) => (
                                 <div
                                    key={
                                       expense._id || `${groupKey}-dot-${index}`
                                    }
                                    className={`w-2 h-2 rounded-full ${getStatusDotColor(
                                       expense.status,
                                       expense.isFutureFunded
                                    )}`}
                                    title={`${format(
                                       parseISO(
                                          expense.date || expense.startDate
                                       ),
                                       "MMM d"
                                    )} - ${expense.status}`}
                                 />
                              ))}
                        </div>
                     </td>

                     {isMobileView ? (
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                           <div className="flex items-center justify-between">
                              <div className="flex flex-col space-y-1">
                                 <div className="text-xs font-medium">
                                    Total: $
                                    {
                                       formatAmount(group.totalDue)
                                          .formattedValue
                                    }
                                 </div>
                                 <div className="text-xs">
                                    Assigned: $
                                    {
                                       formatAmount(group.totalAssigned)
                                          .formattedValue
                                    }
                                 </div>
                                 <div className="text-xs">
                                    Available:
                                    <span
                                       className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-sm font-medium ml-1 ${getAvailablePillStyle(
                                          group.totalAvailable
                                       )}`}
                                    >
                                       $
                                       {
                                          formatAmount(group.totalAvailable)
                                             .formattedValue
                                       }
                                    </span>
                                 </div>
                              </div>
                              <svg
                                 className={`w-4 h-4 transform transition-transform text-gray-500 dark:text-gray-400 ml-2 ${
                                    isExpanded ? "rotate-90" : ""
                                 }`}
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24"
                              >
                                 <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5l7 7-7 7"
                                 />
                              </svg>
                           </div>
                        </td>
                     ) : (
                        <>
                           {/* Due Amount */}
                           <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right font-medium">
                              ${formatAmount(group.totalDue).formattedValue}
                           </td>

                           {/* Assigned Amount */}
                           <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right">
                              $
                              {formatAmount(group.totalAssigned).formattedValue}
                           </td>

                           {/* Spent Amount */}
                           <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right">
                              ${formatAmount(group.totalSpent).formattedValue}
                           </td>

                           {/* Available Amount */}
                           <td className="px-4 py-2 whitespace-nowrap text-sm text-right">
                              <span
                                 className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${getAvailablePillStyle(
                                    group.totalAvailable
                                 )}`}
                              >
                                 $
                                 {
                                    formatAmount(group.totalAvailable)
                                       .formattedValue
                                 }
                              </span>
                           </td>

                           {/* Actions Column with Caret */}
                           <td className="px-4 py-2 whitespace-nowrap text-sm">
                              <div className="flex justify-end">
                                 <svg
                                    className={`w-4 h-4 transform transition-transform text-gray-500 dark:text-gray-400 ${
                                       isExpanded ? "rotate-90" : ""
                                    }`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                 >
                                    <path
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                       strokeWidth={2}
                                       d="M9 5l7 7-7 7"
                                    />
                                 </svg>
                              </div>
                           </td>
                        </>
                     )}
                  </tr>

                  {/* Individual Weekly Expenses */}
                  {isExpanded &&
                     group.expenses
                        .sort(
                           (a, b) =>
                              new Date(a.date || a.startDate) -
                              new Date(b.date || b.startDate)
                        )
                        .map((expense) => (
                           <ExpenseRow
                              key={expense._id || `${groupKey}-${expense.date}`}
                              expense={expense}
                              onSchedule={onSchedule}
                              onHide={onHide}
                              onUnhide={onUnhide}
                              onDelete={onDelete}
                              onEdit={onEdit}
                              dateRange={dateRange}
                              isMobileView={isMobileView}
                              selectedMobileColumn={selectedMobileColumn}
                           />
                        ))}
               </Fragment>
            );
         })}
      </Fragment>
   );
}

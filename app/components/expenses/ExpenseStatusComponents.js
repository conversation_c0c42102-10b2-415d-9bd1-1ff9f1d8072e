import {
   getStatusBadgeStyle,
   getAvailablePillStyle,
} from "../../lib/utils/expenseUtils";
import { FormatAmountDisplay } from "./ExpenseUIComponents";

// Status Badge Component
export const StatusBadge = ({ status, isFutureFunded = false }) => {
   // Default to 'scheduled' if status is undefined
   const currentStatus = status || "scheduled";

   // Handle combined statuses
   if (currentStatus.includes("-")) {
      const statuses = currentStatus
         .split("-")
         .filter((s) => s !== "overspent");
      return (
         <div className="inline-flex items-center space-x-1">
            {statuses.map((s) => (
               <span
                  key={s}
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeStyle(
                     s,
                     s === "future" && isFutureFunded
                  )}`}
               >
                  {s.charAt(0).toUpperCase() + s.slice(1)}
               </span>
            ))}
         </div>
      );
   }

   return (
      <span
         className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeStyle(
            currentStatus,
            currentStatus === "future" && isFutureFunded
         )}`}
      >
         {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
      </span>
   );
};

// Available Pill Component - Controlled component that reports actions to parent
export const AvailablePill = ({
   expense,
   showDropdown,
   onPillClick,
   onCollectAmount, // Callback to report collect action
   onAssignAmount, // Callback to report assign action
   isProcessing = false, // Processing state from parent
   isDisabled = false,
}) => {
   const value = parseFloat(expense.amountAvailable || 0);
   const pillStyle = getAvailablePillStyle(value);

   const handleAction = () => {
      if (isProcessing || isDisabled || value === 0) return;

      if (value > 0) {
         onCollectAmount?.(expense);
      } else {
         onAssignAmount?.(expense);
      }
   };

   return (
      <div className="relative">
         <button
            onClick={() =>
               !isDisabled && value !== 0 && onPillClick(expense._id)
            }
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${pillStyle} ${
               !isDisabled && value !== 0
                  ? "cursor-pointer hover:opacity-80"
                  : "cursor-default"
            }`}
            disabled={isProcessing || isDisabled}
         >
            <FormatAmountDisplay value={value} />
         </button>
         {!isDisabled && showDropdown && value !== 0 && (
            <div className="absolute z-10 mt-1 w-36 -left-12 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
               <button
                  onClick={handleAction}
                  className="w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  disabled={isProcessing}
               >
                  {value > 0
                     ? `Collect $${Math.abs(value).toFixed(2)}`
                     : `Assign $${Math.abs(value).toFixed(2)}`}
               </button>
            </div>
         )}
      </div>
   );
};

// Total Available Pill Component
export const TotalAvailablePill = ({
   totalAvailable,
   showDropdown,
   onPillClick,
   onAction,
   isProcessing,
}) => {
   const pillStyle = getAvailablePillStyle(totalAvailable);

   return (
      <div className="relative">
         <button
            onClick={() =>
               totalAvailable !== 0 && onPillClick("total-available")
            }
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${pillStyle} ${
               totalAvailable !== 0
                  ? "cursor-pointer hover:opacity-80"
                  : "cursor-default"
            }`}
         >
            <FormatAmountDisplay value={Math.abs(totalAvailable)} />
         </button>
         {showDropdown === "total-available" &&
            totalAvailable !== 0 &&
            !isProcessing && (
               <div className="absolute z-10 mt-1 w-36 -left-12 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                  <button
                     onClick={onAction}
                     className="w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                     {totalAvailable > 0
                        ? `Collect $${totalAvailable.toFixed(2)}`
                        : `Assign $${Math.abs(totalAvailable).toFixed(2)}`}
                  </button>
               </div>
            )}
      </div>
   );
};

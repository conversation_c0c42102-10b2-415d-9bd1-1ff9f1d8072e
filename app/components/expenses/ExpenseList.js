"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import {
   parseISO,
   isBefore,
   isAfter,
   startOfDay,
   endOfDay,
   format,
} from "date-fns";
import { useSession } from "next-auth/react";

import {
   LoadingOverlay,
   ErrorMessage,
   LoadingMessage,
} from "./ExpenseUIComponents";
import ExpenseDetailModal from "./ExpenseDetailModal";
import ExpenseListModal from "./ExpenseListModal";
import HiddenExpensesCategory from "./HiddenExpensesCategory";
import TotalsRow from "./TotalsRow";
import { useHiddenProjections } from "../../hooks/useHiddenProjections";
import { useExpenseFiltering } from "../../hooks/useExpenseFiltering";

import { useFutureExpenses } from "../../hooks/useFutureExpenses";
import { useExpectedExpenses } from "../../hooks/useExpectedExpenses";
import ExpenseCategory from "./ExpenseCategory";
import ExpenseListHeader from "./ExpenseListHeader";
import { processExpenseData } from "../../lib/utils/expenseDataProcessing";
import {
   handleDelete as handleDeleteUtil,
   handleScheduleExpense as handleScheduleExpenseUtil,
   handleScheduleAllExpenses as handleScheduleAllExpensesUtil,
} from "../../lib/utils/expenseActions";

export default function ExpenseList({
   dateRange,
   onShowFormChange = () => {},
   expenses = [],
   userExpenseSettings = null,
   loading = false,
   error = "",
   onExpensesChange = () => {},
   onRefreshBalances = () => {},
   onFetchPeriodData = () => {},
   isMobileView = false,
   isCollapsed = false,
}) {
   const { data: session } = useSession();

   // Custom hook for hidden projections
   const {
      hiddenProjections,
      isHiddenSectionExpanded,
      setIsHiddenSectionExpanded,
      isProjectionHidden,
      handleHideProjection,
      handleUnhideProjection,
   } = useHiddenProjections();

   // Custom hook for expense filtering
   const {
      showStatusFilter,
      setShowStatusFilter,
      selectedStatuses,
      setSelectedStatuses,
      statusFilterRef,
      filterExpensesByStatus,
      STATUS_OPTIONS,
   } = useExpenseFiltering();

   // State declarations needed by hooks
   // Removed showAvailableDropdown - ExpenseRow now manages this internally
   const [errorMessage, setErrorMessage] = useState("");

   // Custom hook for expected expenses
   const { expectedExpenses } = useExpectedExpenses({
      dateRange,
      userExpenseSettings,
   });

   // Custom hook for future expenses
   const {
      futureExpenses,
      setFutureExpenses,
      handleToggleFutureExpenses,
      memoizedShowFutureExpenses,
   } = useFutureExpenses({
      dateRange,
      userExpenseSettings,
      onExpensesChange,
      expenses,
   });

   const [expandedRows, setExpandedRows] = useState(new Set());
   const [expandedCategories, setExpandedCategories] = useState(
      new Set(["oneoff", "weekly", "monthly", "annual"])
   );
   const [scheduleAllLoading, setScheduleAllLoading] = useState(false);
   const [isProcessing, setIsProcessing] = useState(false);
   const [selectedMobileColumn, setSelectedMobileColumn] = useState("due");
   const mobileColumnSelectorRef = useRef(null);
   const [showMobileColumnSelector, setShowMobileColumnSelector] =
      useState(false);

   const [showDetailModal, setShowDetailModal] = useState(false);
   const [selectedExpense, setSelectedExpense] = useState(null);
   const [detailModalInitialTab, setDetailModalInitialTab] =
      useState("details");

   const handleDelete = async (id, isGroup = false, groupExpenses = null) => {
      try {
         await handleDeleteUtil(
            id,
            isGroup,
            groupExpenses,
            onExpensesChange,
            onRefreshBalances
         );
      } catch (error) {
         setErrorMessage(error.message || "Failed to delete expense");
      }
   };

   const handleScheduleExpense = async (expense) => {
      try {
         await handleScheduleExpenseUtil(
            expense,
            onExpensesChange,
            onRefreshBalances,
            expenses
         );
      } catch (error) {
         setErrorMessage(error.message || "Failed to schedule expense");
      }
   };

   const handleScheduleAllExpenses = async () => {
      await handleScheduleAllExpensesUtil(
         allExpenses,
         isProjectionHidden,
         onExpensesChange,
         onRefreshBalances,
         onFetchPeriodData,
         setScheduleAllLoading,
         setErrorMessage
      );
   };

   const handleRowClick = (expense) => {
      setExpandedRows((prev) => {
         const newSet = new Set(prev);
         if (newSet.has(expense.description)) {
            newSet.delete(expense.description);
         } else {
            newSet.add(expense.description);
         }
         return newSet;
      });
   };

   const handleCategoryClick = (category) => {
      setExpandedCategories((prev) => {
         const newSet = new Set(prev);
         if (newSet.has(category)) {
            newSet.delete(category);
         } else {
            newSet.add(category);
         }
         return newSet;
      });
   };
   const filterExpensesByDate = useCallback(
      (expensesToFilter) => {
         if (!dateRange?.start || !dateRange?.end) return expensesToFilter;

         return expensesToFilter.filter((expense) => {
            // Skip date filtering for expenses already identified as future
            if (expense.status === "future" || expense.isFutureExpense) {
               return true;
            }

            // For spread expenses (weekly, biweekly, etc), check if any part of the expense duration overlaps with the period
            if (expense.startDate && expense.endDate) {
               const rangeStartStr = format(dateRange.start, "yyyy-MM-dd");
               const rangeEndStr = format(dateRange.end, "yyyy-MM-dd");

               // Check if the expense period overlaps with the target period (string comparison)
               return (
                  expense.startDate <= rangeEndStr &&
                  expense.endDate >= rangeStartStr
               );
            }

            // For non-spread expenses, check if the date falls within the range (string comparison)
            const rangeStartStr = format(dateRange.start, "yyyy-MM-dd");
            const rangeEndStr = format(dateRange.end, "yyyy-MM-dd");
            return expense.date >= rangeStartStr && expense.date <= rangeEndStr;
         });
      },
      [dateRange]
   );
   const filterProjectedExpensesByDate = useCallback(
      (expensesToFilter) => {
         // If no expenses or no date range, return as is
         if (!expensesToFilter.length || !dateRange?.start || !dateRange?.end)
            return expensesToFilter;

         // Get current date as YYYY-MM-DD string
         const currentDateStr = format(new Date(), "yyyy-MM-dd");
         // Get current month start as YYYY-MM-DD string
         const currentMonthStart = new Date();
         currentMonthStart.setDate(1);
         const currentMonthStartStr = format(currentMonthStart, "yyyy-MM-dd");

         return expensesToFilter.filter((expense) => {
            // Don't filter future expenses - always keep them
            if (expense.status === "future" || expense.isFutureExpense) {
               return true;
            }

            // Skip if not a projected expense
            if (expense.status !== "projected") return true;

            // For aggregated expenses, filter children
            if (expense.isAggregated && expense.expenses) {
               // Filter the children expenses to only keep current month forward
               const filteredExpenses = expense.expenses.filter((weekExp) => {
                  // Don't filter future expenses
                  if (weekExp.status === "future" || weekExp.isFutureExpense) {
                     return true;
                  }

                  if (weekExp.status !== "projected") return true;

                  // Keep only if expense date is in current month or future (string comparison)
                  return weekExp.date >= currentMonthStartStr;
               });

               // If we have filtered expenses, update the expense object
               if (filteredExpenses.length > 0) {
                  expense.expenses = filteredExpenses;
                  // Update aggregated values
                  expense.amountDue = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountDue,
                     0
                  );
                  expense.amountAssigned = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountAssigned,
                     0
                  );
                  expense.amountSpent = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountSpent,
                     0
                  );
                  expense.amountAvailable = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountAvailable,
                     0
                  );

                  // Keep this aggregated expense since we have valid children
                  return true;
               }

               return false;
            }

            // For non-aggregated expenses
            // Keep only if expense date is in current month or future (string comparison)
            return expense.date >= currentMonthStartStr;
         });
      },
      [dateRange]
   );

   // Process expense data using utility function - simplified version
   const {
      organizedExpenses,
      allExpenses,
      visibleExpenses,
      hiddenExpenses,
      totals,
   } = useMemo(() => {
      return processExpenseData({
         expenses,
         expectedExpenses,
         futureExpenses,
         showFutureExpenses: memoizedShowFutureExpenses,
         dateRange,
         // Restore filtering functions for skip/hide functionality
         filterExpensesByDate,
         filterExpensesByStatus,
         filterProjectedExpensesByDate,
         isProjectionHidden,
      });
   }, [
      expenses,
      expectedExpenses,
      futureExpenses,
      memoizedShowFutureExpenses,
      dateRange,
      // Restore filtering function dependencies
      filterExpensesByDate,
      filterExpensesByStatus,
      filterProjectedExpensesByDate,
      isProjectionHidden,
   ]);

   // Check if there are any projected expenses that can be scheduled
   const hasProjectedExpenses = allExpenses.some((expense) => {
      // Skip if the expense is hidden
      if (isProjectionHidden(expense)) return false;

      if (expense.isAggregated) {
         // For aggregated expenses, check if any week is projected and not hidden
         return expense.expenses.some(
            (weekExp) =>
               weekExp.status === "projected" && !isProjectionHidden(weekExp)
         );
      }
      // For non-aggregated expenses
      return (
         expense.status === "projected" &&
         (expense.frequency === "weekly" ||
            expense.frequency === "monthly" ||
            (expense.startDate && expense.endDate))
      );
   });

   // Update the onExpensesChange function to ensure it updates future expenses as well
   useEffect(() => {
      // When the expenses array changes from a parent update, ensure our futureExpenses
      // array is kept in sync with any updated future expenses
      if (expenses.length) {
         setFutureExpenses((prevFutureExpenses) => {
            // First create a map of the existing future expenses by ID
            const futureExpenseMap = new Map(
               prevFutureExpenses.map((exp) => [exp._id, exp])
            );

            // Check for updated future expenses in the expenses array
            expenses.forEach((expense) => {
               if (
                  (expense.isFutureExpense || expense.status === "future") &&
                  futureExpenseMap.has(expense._id)
               ) {
                  // Get the current future expense to access its prorated amountDue
                  const currentFutureExp = futureExpenseMap.get(expense._id);
                  const proratedAmountDue = currentFutureExp.amountDue;

                  // Check if expense is properly funded but keep status as "future"
                  const isFutureFunded =
                     expense.amountAssigned >= proratedAmountDue;

                  // Update the future expense with any changes from the expenses array
                  futureExpenseMap.set(expense._id, {
                     ...futureExpenseMap.get(expense._id),
                     ...expense,
                     // Ensure future expense properties are preserved
                     isFutureExpense: true,
                     // Always keep status as "future"
                     status: "future",
                     isFutureFunded: isFutureFunded,
                     amountDue: proratedAmountDue,
                     originalAmountDue: currentFutureExp.originalAmountDue,
                  });
               }
            });

            // Return the updated future expenses array
            return Array.from(futureExpenseMap.values());
         });
      }
   }, [expenses]);

   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            mobileColumnSelectorRef.current &&
            !mobileColumnSelectorRef.current.contains(event.target)
         ) {
            setShowMobileColumnSelector(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, []);

   const handleEdit = (expense) => {
      setSelectedExpense(expense);
      if (expense.status === "projected") {
         setDetailModalInitialTab("rules");
      } else {
         setDetailModalInitialTab("details");
      }
      setShowDetailModal(true);
   };

   // Handle expense update from the modal
   const handleExpenseUpdate = (updatedExpense) => {
      // Update the expense in the list
      if (updatedExpense) {
         const updatedExpenses = expenses.map((exp) =>
            exp._id === updatedExpense._id ? updatedExpense : exp
         );
         onExpensesChange(updatedExpenses);
      }
   };

   if (loading) {
      return <LoadingMessage />;
   }

   if (error) {
      return <ErrorMessage message={error} />;
   }

   return (
      <div
         className={`h-full overflow-hidden transition-all duration-300 ease-in-out ${
            isMobileView && isCollapsed ? "max-h-0" : "max-h-[10000px]"
         }`}
      >
         {/* Desktop View */}
         <div className="h-full">
            <div className="bg-white dark:bg-gray-900 h-full flex flex-col">
               {/* Scrollable Expense List */}
               <div className="flex-1 overflow-y-auto custom-scrollbar">
                  <div className="h-full">
                     {/* Main Expense Table */}
                     <div className="h-full overflow-x-auto">
                        <table
                           className={`w-full divide-y divide-gray-200 dark:divide-gray-700 border-collapse ${
                              !isMobileView ? "min-w-[768px]" : ""
                           }`}
                        >
                           <ExpenseListHeader
                              isMobileView={isMobileView}
                              showStatusFilter={showStatusFilter}
                              setShowStatusFilter={setShowStatusFilter}
                              selectedStatuses={selectedStatuses}
                              setSelectedStatuses={setSelectedStatuses}
                              statusFilterRef={statusFilterRef}
                              STATUS_OPTIONS={STATUS_OPTIONS}
                              selectedMobileColumn={selectedMobileColumn}
                              setSelectedMobileColumn={setSelectedMobileColumn}
                              showMobileColumnSelector={
                                 showMobileColumnSelector
                              }
                              setShowMobileColumnSelector={
                                 setShowMobileColumnSelector
                              }
                              mobileColumnSelectorRef={mobileColumnSelectorRef}
                              showFutureExpenses={memoizedShowFutureExpenses}
                              onToggleFutureExpenses={
                                 handleToggleFutureExpenses
                              }
                              hasProjectedExpenses={hasProjectedExpenses}
                              scheduleAllLoading={scheduleAllLoading}
                              onScheduleAllExpenses={handleScheduleAllExpenses}
                              onShowFormChange={onShowFormChange}
                           />
                           <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                              {/* Skipped Expenses */}
                              <HiddenExpensesCategory
                                 hiddenExpenses={hiddenExpenses}
                                 isExpanded={isHiddenSectionExpanded}
                                 onToggleExpanded={() =>
                                    setIsHiddenSectionExpanded(
                                       !isHiddenSectionExpanded
                                    )
                                 }
                                 onUnhideProjection={handleUnhideProjection}
                                 isMobileView={isMobileView}
                              />

                              {/* One-off Expenses */}
                              <ExpenseCategory
                                 categoryName="oneoff"
                                 categoryLabel="One-off"
                                 expenses={organizedExpenses.oneoff}
                                 isExpanded={expandedCategories.has("oneoff")}
                                 onToggleExpanded={handleCategoryClick}
                                 onSchedule={handleScheduleExpense}
                                 onHide={handleHideProjection}
                                 onUnhide={handleUnhideProjection}
                                 onDelete={handleDelete}
                                 onEdit={handleEdit}
                                 onToggleExpand={handleRowClick}
                                 expandedRows={expandedRows}
                                 dateRange={{
                                    ...dateRange,
                                    userPaySettings: userExpenseSettings,
                                 }}
                                 isMobileView={isMobileView}
                                 selectedMobileColumn={selectedMobileColumn}
                              />

                              {/* Weekly Expenses */}
                              <ExpenseCategory
                                 categoryName="weekly"
                                 categoryLabel="Weekly"
                                 expenses={organizedExpenses.weekly}
                                 isExpanded={expandedCategories.has("weekly")}
                                 onToggleExpanded={handleCategoryClick}
                                 onSchedule={handleScheduleExpense}
                                 onHide={handleHideProjection}
                                 onUnhide={handleUnhideProjection}
                                 onDelete={handleDelete}
                                 onEdit={handleEdit}
                                 onToggleExpand={handleRowClick}
                                 expandedRows={expandedRows}
                                 dateRange={{
                                    ...dateRange,
                                    userPaySettings: userExpenseSettings,
                                 }}
                                 isMobileView={isMobileView}
                                 selectedMobileColumn={selectedMobileColumn}
                              />

                              {/* Monthly Expenses */}
                              <ExpenseCategory
                                 categoryName="monthly"
                                 categoryLabel="Monthly"
                                 expenses={organizedExpenses.monthly}
                                 isExpanded={expandedCategories.has("monthly")}
                                 onToggleExpanded={handleCategoryClick}
                                 onSchedule={handleScheduleExpense}
                                 onHide={handleHideProjection}
                                 onUnhide={handleUnhideProjection}
                                 onDelete={handleDelete}
                                 onEdit={handleEdit}
                                 onToggleExpand={handleRowClick}
                                 expandedRows={expandedRows}
                                 dateRange={{
                                    ...dateRange,
                                    userPaySettings: userExpenseSettings,
                                 }}
                                 isMobileView={isMobileView}
                                 selectedMobileColumn={selectedMobileColumn}
                              />

                              {/* Annual Expenses */}
                              <ExpenseCategory
                                 categoryName="annual"
                                 categoryLabel="Annual"
                                 expenses={organizedExpenses.annual || []}
                                 isExpanded={expandedCategories.has("annual")}
                                 onToggleExpanded={handleCategoryClick}
                                 onSchedule={handleScheduleExpense}
                                 onHide={handleHideProjection}
                                 onUnhide={handleUnhideProjection}
                                 onDelete={handleDelete}
                                 onEdit={handleEdit}
                                 onToggleExpand={handleRowClick}
                                 expandedRows={expandedRows}
                                 dateRange={{
                                    ...dateRange,
                                    userPaySettings: userExpenseSettings,
                                 }}
                                 isMobileView={isMobileView}
                                 selectedMobileColumn={selectedMobileColumn}
                              />

                              {/* Totals Row */}
                              <TotalsRow
                                 totals={totals}
                                 isMobileView={isMobileView}
                              />
                           </tbody>
                        </table>
                     </div>
                  </div>
               </div>
            </div>
         </div>

         {/* Loading Overlay */}
         {isProcessing && <LoadingOverlay />}

         {/* Full screen modal for success/error messages */}
         <ExpenseListModal
            isVisible={!!errorMessage}
            message={errorMessage}
            onClose={() => setErrorMessage("")}
         />

         {/* Detail Modal */}
         {showDetailModal && selectedExpense && (
            <ExpenseDetailModal
               expense={selectedExpense}
               isOpen={showDetailModal}
               onClose={() => {
                  setShowDetailModal(false);
                  setSelectedExpense(null);
               }}
               formatAmount={(amount) => ({
                  formattedValue: `$${parseFloat(amount || 0).toFixed(2)}`,
                  isNegative: amount < 0,
               })}
               onExpenseUpdate={handleExpenseUpdate}
               initialTab={detailModalInitialTab}
            />
         )}
      </div>
   );
}

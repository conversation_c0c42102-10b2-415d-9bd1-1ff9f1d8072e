import { format, parseISO } from "date-fns";
import { FormatAmountDisplay } from "./ExpenseUIComponents";
import { StatusBadge, AvailablePill } from "./ExpenseStatusComponents";
import { Fragment } from "react";

export const WeeklyExpenseCard = ({
   expense,
   weekExpenses,
   editingDue,
   editingDueValue,
   editingAssigned,
   editingValue,
   showAvailableDropdown,
   isProcessing,
   onDueClick,
   onDueChange,
   onDueBlur,
   onDueKeyDown,
   onAssignedClick,
   onAssignedBlur,
   onAssignedKeyDown,
   onPillClick,
   onCollectAmount,
   onAssignAmount,
   onSchedule,
   onHide,
   onUnhide,
   onDelete,
   isOverspent,
   isExpanded,
   onToggleExpand,
}) => {
   const hasMultipleExpenses = weekExpenses.length > 1;

   // If there's only one expense, render it like a regular expense
   if (!hasMultipleExpenses && weekExpenses.length === 1) {
      return (
         <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors">
            <div className="p-2">
               <div className="flex justify-between items-center">
                  <div className="flex-1">
                     <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-1">
                        <span>
                           {weekExpenses[0].weeklyChargeType === "spread" ? (
                              <>
                                 {format(
                                    weekExpenses[0].startDate instanceof Date
                                       ? weekExpenses[0].startDate
                                       : parseISO(weekExpenses[0].startDate),
                                    "MMM d"
                                 )}
                                 {" - "}
                                 {format(
                                    weekExpenses[0].endDate instanceof Date
                                       ? weekExpenses[0].endDate
                                       : parseISO(weekExpenses[0].endDate),
                                    "MMM d, yyyy"
                                 )}
                              </>
                           ) : (
                              format(
                                 weekExpenses[0].date instanceof Date
                                    ? weekExpenses[0].date
                                    : parseISO(weekExpenses[0].date),
                                 "MMM d, yyyy"
                              )
                           )}
                        </span>
                        <div className="flex items-center space-x-2">
                           <StatusBadge status={weekExpenses[0].status} />
                        </div>
                     </div>
                     <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                           {weekExpenses[0].description}
                        </div>
                     </div>
                  </div>
                  <div className="flex items-center space-x-6">
                     <div className="flex items-center space-x-8">
                        {/* Due Amount */}
                        <div className="w-32">
                           <div className="text-sm text-gray-900 dark:text-white text-right">
                              {editingDue === weekExpenses[0]._id ? (
                                 <input
                                    type="number"
                                    value={editingDueValue}
                                    onChange={onDueChange}
                                    onBlur={() => onDueBlur(weekExpenses[0])}
                                    onKeyDown={(e) =>
                                       onDueKeyDown(e, weekExpenses[0])
                                    }
                                    className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                                    autoFocus
                                    step="0.01"
                                    min="0"
                                 />
                              ) : (
                                 <button
                                    onClick={() => onDueClick(weekExpenses[0])}
                                    className={`w-full text-right inline-flex items-center justify-end px-2 py-1 border border-transparent rounded ${
                                       weekExpenses[0].status === "scheduled" ||
                                       weekExpenses[0].status === "funded" ||
                                       weekExpenses[0].status === "paid" ||
                                       weekExpenses[0].status === "late" ||
                                       weekExpenses[0].status === "overpaid" ||
                                       weekExpenses[0].status === "underbudget"
                                          ? "hover:border-gray-200 dark:hover:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                                          : "cursor-not-allowed opacity-75"
                                    }`}
                                    disabled={
                                       weekExpenses[0].status !== "scheduled" &&
                                       weekExpenses[0].status !== "funded" &&
                                       weekExpenses[0].status !== "paid" &&
                                       weekExpenses[0].status !== "late" &&
                                       weekExpenses[0].status !== "overpaid" &&
                                       weekExpenses[0].status !== "underbudget"
                                    }
                                 >
                                    <FormatAmountDisplay
                                       value={weekExpenses[0].amountDue}
                                    />
                                 </button>
                              )}
                           </div>
                        </div>

                        {/* Assigned Amount */}
                        <div className="w-32">
                           <div className="text-sm text-gray-900 dark:text-white text-right">
                              {editingAssigned === weekExpenses[0]._id ? (
                                 <input
                                    type="text"
                                    value={editingValue}
                                    onChange={() => {}} // Legacy component - needs proper implementation
                                    onBlur={() =>
                                       onAssignedBlur(weekExpenses[0])
                                    }
                                    onKeyDown={(e) =>
                                       onAssignedKeyDown(e, weekExpenses[0])
                                    }
                                    className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                                    autoFocus
                                 />
                              ) : (
                                 <button
                                    onClick={() =>
                                       onAssignedClick(weekExpenses[0])
                                    }
                                    className={`w-full text-right inline-flex items-center justify-end px-2 py-1 border border-transparent rounded ${
                                       weekExpenses[0].status === "scheduled" ||
                                       weekExpenses[0].status === "funded" ||
                                       weekExpenses[0].status === "paid" ||
                                       weekExpenses[0].status === "late" ||
                                       weekExpenses[0].status === "overpaid" ||
                                       weekExpenses[0].status === "underbudget"
                                          ? "hover:border-gray-200 dark:hover:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                                          : "cursor-not-allowed opacity-75"
                                    }`}
                                    disabled={
                                       weekExpenses[0].status !== "scheduled" &&
                                       weekExpenses[0].status !== "funded" &&
                                       weekExpenses[0].status !== "paid" &&
                                       weekExpenses[0].status !== "late" &&
                                       weekExpenses[0].status !== "overpaid" &&
                                       weekExpenses[0].status !== "underbudget"
                                    }
                                 >
                                    <FormatAmountDisplay
                                       value={weekExpenses[0].amountAssigned}
                                    />
                                 </button>
                              )}
                           </div>
                        </div>

                        {/* Spent Amount */}
                        <div className="w-32">
                           <div className="text-sm text-gray-900 dark:text-white text-right">
                              <FormatAmountDisplay
                                 value={weekExpenses[0].amountSpent}
                              />
                           </div>
                        </div>

                        {/* Available Amount */}
                        <div className="w-32">
                           <div className="text-right">
                              <AvailablePill
                                 expense={weekExpenses[0]}
                                 showDropdown={
                                    showAvailableDropdown ===
                                    weekExpenses[0]._id
                                 }
                                 onPillClick={() =>
                                    onPillClick(weekExpenses[0]._id)
                                 }
                                 onCollectAmount={onCollectAmount}
                                 onAssignAmount={onAssignAmount}
                                 isProcessing={isProcessing}
                              />
                           </div>
                        </div>
                     </div>

                     {/* Action Buttons */}
                     <div className="flex items-center space-x-2 ml-4">
                        {weekExpenses[0].status === "projected" ? (
                           <>
                              {!weekExpenses[0].isHidden && (
                                 <button
                                    onClick={() => onSchedule(weekExpenses[0])}
                                    className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                 >
                                    Schedule
                                 </button>
                              )}
                              <button
                                 onClick={(e) =>
                                    weekExpenses[0].isHidden
                                       ? onUnhide(weekExpenses[0], e)
                                       : onHide(weekExpenses[0], e)
                                 }
                                 className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                              >
                                 {weekExpenses[0].isHidden ? "Unhide" : "Hide"}
                              </button>
                           </>
                        ) : (
                           <button
                              onClick={() => onDelete(weekExpenses[0]._id)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                           >
                              <svg
                                 xmlns="http://www.w3.org/2000/svg"
                                 className="h-5 w-5"
                                 viewBox="0 0 24 24"
                                 fill="none"
                                 stroke="currentColor"
                                 strokeWidth="2"
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                              >
                                 <path d="M3 6h18" />
                                 <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                 <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                              </svg>
                           </button>
                        )}
                     </div>
                  </div>
               </div>
            </div>
         </div>
      );
   }

   // For multiple weekly expenses
   return (
      <div className="space-y-2">
         {/* Main Card */}
         <div
            className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors cursor-pointer`}
            onClick={onToggleExpand}
         >
            <div className="p-2">
               <div className="flex justify-between items-center">
                  <div className="flex-1">
                     <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-1">
                        <span>
                           {expense.weeklyChargeType === "spread" ? (
                              <>
                                 {format(
                                    expense.startDate instanceof Date
                                       ? expense.startDate
                                       : parseISO(expense.startDate),
                                    "MMM d"
                                 )}
                                 {" - "}
                                 {format(
                                    expense.endDate instanceof Date
                                       ? expense.endDate
                                       : parseISO(expense.endDate),
                                    "MMM d, yyyy"
                                 )}
                              </>
                           ) : (
                              weekExpenses
                                 .map((week) =>
                                    format(
                                       week.date instanceof Date
                                          ? week.date
                                          : parseISO(week.date),
                                       "do"
                                    )
                                 )
                                 .join(", ")
                           )}
                        </span>
                        <div className="flex items-center space-x-2">
                           <div className="flex justify-center items-center gap-1">
                              {weekExpenses
                                 .map((e) => e.status)
                                 .map((status, index) => (
                                    <div
                                       key={`status-dot-${index}`}
                                       className={`w-2.5 h-2.5 rounded-full ${
                                          status?.toLowerCase() === "paid"
                                             ? "bg-green-500 dark:bg-green-400"
                                             : status?.toLowerCase() ===
                                               "scheduled"
                                             ? "bg-yellow-500 dark:bg-yellow-400"
                                             : status?.toLowerCase() ===
                                               "projected"
                                             ? "bg-gray-500 dark:bg-gray-400"
                                             : status?.toLowerCase() ===
                                               "funded"
                                             ? "bg-green-500 dark:bg-green-400"
                                             : status?.toLowerCase() ===
                                               "overpaid"
                                             ? "bg-orange-500 dark:bg-orange-400"
                                             : status?.toLowerCase() === "late"
                                             ? "bg-red-500 dark:bg-red-400"
                                             : status?.toLowerCase() ===
                                               "underbudget"
                                             ? "bg-purple-500 dark:bg-purple-400"
                                             : status?.toLowerCase() ===
                                               "hidden"
                                             ? "bg-gray-500 dark:bg-gray-400"
                                             : "bg-gray-500 dark:bg-gray-400"
                                       }`}
                                    />
                                 ))}
                           </div>
                        </div>
                     </div>
                     <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                           {expense.description}
                        </div>
                     </div>
                  </div>
                  <div className="flex items-center space-x-6">
                     <div className="flex items-center space-x-8">
                        {/* Due Amount */}
                        <div className="w-32">
                           <div className="text-sm text-gray-900 dark:text-white text-right">
                              <FormatAmountDisplay value={expense.amountDue} />
                           </div>
                        </div>

                        {/* Assigned Amount */}
                        <div className="w-32">
                           <div className="text-sm text-gray-900 dark:text-white text-right">
                              <FormatAmountDisplay
                                 value={expense.amountAssigned}
                              />
                           </div>
                        </div>

                        {/* Spent Amount */}
                        <div className="w-32">
                           <div className="text-sm text-gray-900 dark:text-white text-right">
                              <FormatAmountDisplay
                                 value={expense.amountSpent}
                              />
                           </div>
                        </div>

                        {/* Available Amount */}
                        <div className="w-32">
                           <div className="text-right">
                              <AvailablePill
                                 expense={expense}
                                 showDropdown={
                                    showAvailableDropdown === expense._id
                                 }
                                 onPillClick={() => onPillClick(expense._id)}
                                 onCollectAmount={onCollectAmount}
                                 onAssignAmount={onAssignAmount}
                                 isProcessing={isProcessing}
                                 isDisabled={true}
                              />
                           </div>
                        </div>
                     </div>

                     {/* Expand Button */}
                     <button
                        onClick={(e) => {
                           e.stopPropagation();
                           onToggleExpand();
                        }}
                        className="focus:outline-none ml-4"
                     >
                        <svg
                           className={`w-5 h-5 transform transition-transform ${
                              isExpanded ? "rotate-90" : ""
                           } text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300`}
                           fill="none"
                           stroke="currentColor"
                           viewBox="0 0 24 24"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                           />
                        </svg>
                     </button>
                  </div>
               </div>
            </div>
         </div>

         {/* Sub-expenses */}
         {isExpanded && (
            <div className="ml-4 space-y-2">
               {weekExpenses.map((weekExpense, index) => (
                  <div
                     key={weekExpense._id || `week-${index}`}
                     className="bg-gray-50 dark:bg-gray-800/50 rounded-lg py-1.5 px-2"
                  >
                     <div className="flex justify-between items-center">
                        <div>
                           <div className="flex flex-col space-y-1">
                              <span className="text-xs text-gray-900 dark:text-white">
                                 {weekExpense.weeklyChargeType === "spread" ? (
                                    <>
                                       {format(
                                          weekExpense.startDate instanceof Date
                                             ? weekExpense.startDate
                                             : parseISO(weekExpense.startDate),
                                          "MMM d"
                                       )}
                                       {" - "}
                                       {format(
                                          weekExpense.endDate instanceof Date
                                             ? weekExpense.endDate
                                             : parseISO(weekExpense.endDate),
                                          "MMM d, yyyy"
                                       )}
                                    </>
                                 ) : (
                                    format(
                                       weekExpense.date instanceof Date
                                          ? weekExpense.date
                                          : parseISO(weekExpense.date),
                                       "MMM d, yyyy"
                                    )
                                 )}
                              </span>
                              <div className="flex items-center">
                                 <div className="flex items-center space-x-2">
                                    <StatusBadge status={weekExpense.status} />
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div className="flex items-center space-x-6">
                           <div className="flex items-center space-x-8">
                              {/* Due Amount */}
                              <div className="w-32">
                                 <div className="text-sm text-gray-900 dark:text-white text-right">
                                    {editingDue === weekExpense._id ? (
                                       <input
                                          type="number"
                                          value={editingDueValue}
                                          onChange={onDueChange}
                                          onBlur={() => onDueBlur(weekExpense)}
                                          onKeyDown={(e) =>
                                             onDueKeyDown(e, weekExpense)
                                          }
                                          className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                                          autoFocus
                                          step="0.01"
                                          min="0"
                                       />
                                    ) : (
                                       <button
                                          onClick={() =>
                                             onDueClick(weekExpense)
                                          }
                                          className={`w-full text-right inline-flex items-center justify-end px-2 py-1 border border-transparent rounded ${
                                             weekExpense.status ===
                                                "scheduled" ||
                                             weekExpense.status === "funded" ||
                                             weekExpense.status === "paid" ||
                                             weekExpense.status === "late" ||
                                             weekExpense.status ===
                                                "overpaid" ||
                                             weekExpense.status ===
                                                "underbudget"
                                                ? "hover:border-gray-200 dark:hover:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                                                : "cursor-not-allowed opacity-75"
                                          }`}
                                          disabled={
                                             weekExpense.status !==
                                                "scheduled" &&
                                             weekExpense.status !== "funded" &&
                                             weekExpense.status !== "paid" &&
                                             weekExpense.status !== "late" &&
                                             weekExpense.status !==
                                                "overpaid" &&
                                             weekExpense.status !==
                                                "underbudget"
                                          }
                                       >
                                          <FormatAmountDisplay
                                             value={weekExpense.amountDue}
                                          />
                                       </button>
                                    )}
                                 </div>
                              </div>

                              {/* Assigned Amount */}
                              <div className="w-32">
                                 <div className="text-sm text-gray-900 dark:text-white text-right">
                                    {editingAssigned === weekExpense._id ? (
                                       <input
                                          type="text"
                                          value={editingValue}
                                          onChange={() => {}} // Legacy component - needs proper implementation
                                          onBlur={() =>
                                             onAssignedBlur(weekExpense)
                                          }
                                          onKeyDown={(e) =>
                                             onAssignedKeyDown(e, weekExpense)
                                          }
                                          className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                                          autoFocus
                                       />
                                    ) : (
                                       <button
                                          onClick={() =>
                                             onAssignedClick(weekExpense)
                                          }
                                          className={`w-full text-right inline-flex items-center justify-end px-2 py-1 border border-transparent rounded ${
                                             weekExpense.status ===
                                                "scheduled" ||
                                             weekExpense.status === "funded" ||
                                             weekExpense.status === "paid" ||
                                             weekExpense.status === "late" ||
                                             weekExpense.status ===
                                                "overpaid" ||
                                             weekExpense.status ===
                                                "underbudget"
                                                ? "hover:border-gray-200 dark:hover:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                                                : "cursor-not-allowed opacity-75"
                                          }`}
                                          disabled={
                                             weekExpense.status !==
                                                "scheduled" &&
                                             weekExpense.status !== "funded" &&
                                             weekExpense.status !== "paid" &&
                                             weekExpense.status !== "late" &&
                                             weekExpense.status !==
                                                "overpaid" &&
                                             weekExpense.status !==
                                                "underbudget"
                                          }
                                       >
                                          <FormatAmountDisplay
                                             value={weekExpense.amountAssigned}
                                          />
                                       </button>
                                    )}
                                 </div>
                              </div>

                              {/* Spent Amount */}
                              <div className="w-32">
                                 <div className="text-sm text-gray-900 dark:text-white text-right">
                                    <FormatAmountDisplay
                                       value={weekExpense.amountSpent}
                                    />
                                 </div>
                              </div>

                              {/* Available Amount */}
                              <div className="w-32">
                                 <div className="text-right">
                                    <AvailablePill
                                       expense={weekExpense}
                                       showDropdown={
                                          showAvailableDropdown ===
                                          weekExpense._id
                                       }
                                       onPillClick={() =>
                                          onPillClick(weekExpense._id)
                                       }
                                       onCollectAmount={onCollectAmount}
                                       onAssignAmount={onAssignAmount}
                                       isProcessing={isProcessing}
                                    />
                                 </div>
                              </div>
                           </div>

                           {/* Action Buttons */}
                           <div className="flex items-center space-x-2 ml-4">
                              {weekExpense.status === "projected" ? (
                                 <>
                                    {!weekExpense.isHidden && (
                                       <button
                                          onClick={() =>
                                             onSchedule(weekExpense)
                                          }
                                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                       >
                                          Schedule
                                       </button>
                                    )}
                                    <button
                                       onClick={(e) =>
                                          weekExpense.isHidden
                                             ? onUnhide(weekExpense, e)
                                             : onHide(weekExpense, e)
                                       }
                                       className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                    >
                                       {weekExpense.isHidden
                                          ? "Unhide"
                                          : "Hide"}
                                    </button>
                                 </>
                              ) : (
                                 <button
                                    onClick={() => onDelete(weekExpense._id)}
                                    className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                 >
                                    <svg
                                       xmlns="http://www.w3.org/2000/svg"
                                       className="h-5 w-5"
                                       viewBox="0 0 24 24"
                                       fill="none"
                                       stroke="currentColor"
                                       strokeWidth="2"
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                    >
                                       <path d="M3 6h18" />
                                       <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                       <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                                    </svg>
                                 </button>
                              )}
                           </div>
                        </div>
                     </div>
                  </div>
               ))}
            </div>
         )}
      </div>
   );
};

"use client";

import { formatAmount } from "../../lib/utils/expenseUtils";

export default function TotalsRow({ totals, isMobileView }) {
   if (isMobileView) {
      return null; // Don't show totals row in mobile view
   }

   return (
      <tr className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 sticky bottom-0">
         <th
            scope="col"
            className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800"
         >
            Totals
         </th>
         <th
            scope="col"
            className="px-4 py-2 text-center bg-gray-50 dark:bg-gray-800"
         ></th>
         <th
            scope="col"
            className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-800"
         >
            ${formatAmount(totals.due).formattedValue.replace("$", "")}
         </th>
         <th
            scope="col"
            className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-800"
         >
            ${formatAmount(totals.assigned).formattedValue.replace("$", "")}
         </th>
         <th
            scope="col"
            className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-800"
         >
            ${formatAmount(totals.spent).formattedValue.replace("$", "")}
         </th>
         <th
            scope="col"
            className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-800"
         >
            $
            {formatAmount(
               totals.assigned + totals.spent
            ).formattedValue.replace("$", "")}
         </th>
         <th scope="col" className="px-4 py-2 bg-gray-50 dark:bg-gray-800"></th>
      </tr>
   );
}

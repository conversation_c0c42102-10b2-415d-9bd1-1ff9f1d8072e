import { useState, useEffect } from "react";
import { format, parseISO } from "date-fns";
import { FormatAmountDisplay } from "./ExpenseUIComponents";
import { StatusBadge } from "./ExpenseStatusComponents";
import { createPortal } from "react-dom";
import { determineExpenseStatus } from "../../lib/utils/expenseUtils";
import { RecurringExpenseDeletionModal } from "../settings/RecurringExpenseDeletionModal";

export const ExpenseDetailModal = ({
   expense,
   isOpen,
   onClose,
   formatAmount,
   onExpenseUpdate,
   initialTab = "details",
}) => {
   const [transactions, setTransactions] = useState([]);
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState(null);
   const [activeTab, setActiveTab] = useState(initialTab);
   const [recurringExpense, setRecurringExpense] = useState(null);
   const [loadingRecurring, setLoadingRecurring] = useState(false);
   const [recurringError, setRecurringError] = useState(null);

   // Debt payment states
   const [debtInfo, setDebtInfo] = useState(null);
   const [loadingDebt, setLoadingDebt] = useState(false);
   const [debtError, setDebtError] = useState(null);
   const [editedDebt, setEditedDebt] = useState(null);
   const [originalDebt, setOriginalDebt] = useState(null);
   const [debtModified, setDebtModified] = useState(false);

   // Editing state
   const [isEditing, setIsEditing] = useState(false);
   const [editedExpense, setEditedExpense] = useState({
      description: "",
      amountDue: 0,
      date: "",
      startDate: "",
      endDate: "",
      category: "",
   });
   const [isEditingRecurring, setIsEditingRecurring] = useState(false);
   const [editedRecurring, setEditedRecurring] = useState({
      name: "",
      amount: 0,
      frequency: "monthly",
      dueDay: "",
      dueMonth: "",
      weeklyChargeType: "one-time",
      enabled: true,
   });
   const [savingChanges, setSavingChanges] = useState(false);
   const [saveError, setSaveError] = useState(null);
   const [saveSuccess, setSaveSuccess] = useState(null);

   // Track whether expense details or recurring expense has been modified
   const [expenseModified, setExpenseModified] = useState(false);
   const [recurringModified, setRecurringModified] = useState(false);

   // Original expense reference to compare against
   const [originalExpense, setOriginalExpense] = useState(null);
   const [originalRecurring, setOriginalRecurring] = useState(null);

   // State for recurring expense deletion modal
   const [deletionModal, setDeletionModal] = useState({
      isOpen: false,
      recurringExpense: null,
   });

   useEffect(() => {
      if (isOpen && expense) {
         if (activeTab === "details") {
            // Initialize edited expense with the current expense values
            // For future expenses, use originalAmountDue instead of amountDue
            const formattedExpense = {
               ...expense,
               // If it's a future expense, use the original amount due, otherwise use the regular amount due
               amountDue:
                  expense.isFutureExpense && expense.originalAmountDue
                     ? expense.originalAmountDue
                     : expense.amountDue,
               date: expense.date
                  ? expense.date instanceof Date
                     ? expense.date.toISOString().split("T")[0]
                     : expense.date.includes && expense.date.includes("T")
                     ? new Date(expense.date).toISOString().split("T")[0]
                     : expense.date
                  : "",
               startDate: expense.startDate
                  ? expense.startDate instanceof Date
                     ? expense.startDate.toISOString().split("T")[0]
                     : expense.startDate.includes &&
                       expense.startDate.includes("T")
                     ? new Date(expense.startDate).toISOString().split("T")[0]
                     : expense.startDate
                  : "",
               endDate: expense.endDate
                  ? expense.endDate instanceof Date
                     ? expense.endDate.toISOString().split("T")[0]
                     : expense.endDate.includes && expense.endDate.includes("T")
                     ? new Date(expense.endDate).toISOString().split("T")[0]
                     : expense.endDate
                  : "",
            };

            setEditedExpense(formattedExpense);
            // Store the original expense for comparison
            setOriginalExpense(formattedExpense);
         }

         // For projected expenses, always show the rules tab
         if (expense.status === "projected") {
            setActiveTab("rules");
            // Fetch recurring expense data for projected expenses
            if (expense.recurringExpenseId) {
               fetchRecurringExpense();
            }
            if (expense.isDebtPayment && expense.debtId) {
               fetchDebtInfo();
            }
         } else {
            // For other expenses, fetch transactions if viewing transactions tab
            if (activeTab === "transactions") {
               fetchTransactions();
            } else if (activeTab === "rules") {
               if (expense.recurringExpenseId) {
                  fetchRecurringExpense();
               }
               if (expense.isDebtPayment && expense.debtId) {
                  fetchDebtInfo();
               }
            }
         }

         // Reset modification flags
         setExpenseModified(false);
         setRecurringModified(false);
         setDebtModified(false);

         // Reset editing state
         setIsEditing(false);
         setIsEditingRecurring(false);
         // Don't reset saveSuccess here to allow the message to persist when reopening the modal
      }
   }, [isOpen, expense, activeTab]);

   // Add effect to update activeTab when initialTab changes
   useEffect(() => {
      if (initialTab) {
         // For projected expenses, always use "rules" tab regardless of initialTab
         if (expense && expense.status === "projected") {
            setActiveTab("rules");
         } else {
            setActiveTab(initialTab);
         }
      }
   }, [initialTab, expense]);

   // Clear success message after delay
   useEffect(() => {
      if (saveSuccess) {
         const timer = setTimeout(() => {
            setSaveSuccess(null);
         }, 3000);
         return () => clearTimeout(timer);
      }
   }, [saveSuccess]);

   // Check for expense modifications
   useEffect(() => {
      if (originalExpense && editedExpense) {
         const isModified =
            originalExpense.description !== editedExpense.description ||
            originalExpense.amountDue !== editedExpense.amountDue ||
            originalExpense.date !== editedExpense.date ||
            originalExpense.startDate !== editedExpense.startDate ||
            originalExpense.endDate !== editedExpense.endDate ||
            originalExpense.category !== editedExpense.category;

         setExpenseModified(isModified);
      }
   }, [editedExpense, originalExpense]);

   // Check for recurring expense modifications
   useEffect(() => {
      if (originalRecurring && editedRecurring) {
         const isModified =
            originalRecurring.name !== editedRecurring.name ||
            originalRecurring.amount !== editedRecurring.amount ||
            originalRecurring.frequency !== editedRecurring.frequency ||
            originalRecurring.dueDay !== editedRecurring.dueDay ||
            originalRecurring.dueMonth !== editedRecurring.dueMonth ||
            originalRecurring.weeklyChargeType !==
               editedRecurring.weeklyChargeType ||
            originalRecurring.enabled !== editedRecurring.enabled;

         setRecurringModified(isModified);
      }
   }, [editedRecurring, originalRecurring]);

   // Check for debt modifications
   useEffect(() => {
      if (originalDebt && editedDebt) {
         const isModified =
            originalDebt.lender !== editedDebt.lender ||
            originalDebt.balance !== editedDebt.balance ||
            originalDebt.apr !== editedDebt.apr ||
            originalDebt.minimumPayment !== editedDebt.minimumPayment ||
            originalDebt.dueDate !== editedDebt.dueDate ||
            originalDebt.debtType !== editedDebt.debtType;

         setDebtModified(isModified);
      }
   }, [editedDebt, originalDebt]);

   // Fetch debt information for debt payments
   const fetchDebtInfo = async () => {
      if (!expense.debtId) return;

      setLoadingDebt(true);
      setDebtError(null);

      try {
         const response = await fetch(`/api/user/debts/${expense.debtId}`);
         if (!response.ok) {
            throw new Error("Failed to fetch debt details");
         }

         const data = await response.json();
         setDebtInfo(data.debt);

         // Set up edited debt state
         const formattedDebt = {
            ...data.debt,
            balance: data.debt.balance,
            apr: data.debt.apr,
            minimumPayment: data.debt.minimumPayment,
         };

         setEditedDebt(formattedDebt);
         setOriginalDebt(formattedDebt);
         setDebtModified(false);
      } catch (err) {
         console.error("Error fetching debt information:", err);
         setDebtError("Failed to load debt details. Please try again.");
      } finally {
         setLoadingDebt(false);
      }
   };

   const fetchRecurringExpense = async () => {
      setLoadingRecurring(true);
      setRecurringError(null);
      try {
         const response = await fetch(
            `/api/user/recurring-expenses/${expense.recurringExpenseId}`
         );
         if (!response.ok) {
            throw new Error("Failed to fetch recurring expense details");
         }
         const data = await response.json();
         setRecurringExpense(data);
         setEditedRecurring(data);
         // Store original recurring expense for comparison
         setOriginalRecurring(data);
         // Reset modification flag
         setRecurringModified(false);
      } catch (err) {
         console.error("Error fetching recurring expense:", err);
         setRecurringError(
            "Failed to load recurring expense details. Please try again."
         );
      } finally {
         setLoadingRecurring(false);
      }
   };

   const fetchTransactions = async () => {
      setLoading(true);
      setError(null);
      try {
         // Fetch transactions for this expense category
         const response = await fetch(
            `/api/transactions/by-category?id=${expense._id}`
         );
         if (!response.ok) {
            throw new Error("Failed to fetch transactions");
         }
         const data = await response.json();
         setTransactions(data.transactions || []);
      } catch (err) {
         console.error("Error fetching transactions:", err);
         setError("Failed to load transactions. Please try again.");
      } finally {
         setLoading(false);
      }
   };

   const handleInputChange = (e) => {
      const { name, value } = e.target;
      setEditedExpense((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleAmountChange = (e) => {
      const { name, value } = e.target;
      // Ensure amount is a valid number
      const numValue = value === "" ? 0 : parseFloat(value);
      setEditedExpense((prev) => ({
         ...prev,
         [name]: numValue,
      }));
   };

   const handleRecurringInputChange = (e) => {
      const { name, value } = e.target;
      setEditedRecurring((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleDebtInputChange = (e) => {
      const { name, value } = e.target;
      setEditedDebt((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleDebtNumberChange = (e) => {
      const { name, value } = e.target;
      // Ensure amount is a valid number
      const numValue = value === "" ? 0 : parseFloat(value);
      setEditedDebt((prev) => ({
         ...prev,
         [name]: numValue,
      }));
   };

   const toggleEdit = () => {
      setIsEditing(!isEditing);
      setSaveError(null);
   };

   const saveChanges = async () => {
      setSavingChanges(true);
      setSaveError(null);
      setSaveSuccess(null);

      try {
         // Create request body with basic fields
         const requestBody = {
            description: editedExpense.description,
            category: editedExpense.category,
         };

         // For future expenses, save to originalAmountDue but preserve the prorated amountDue
         if (expense.isFutureExpense) {
            // Send the originalAmountDue (what user is editing) with the API request
            requestBody.originalAmountDue = editedExpense.amountDue;
            // Don't update amountDue directly for future expenses - it will be recalculated
         } else {
            // For regular expenses, update amountDue directly
            requestBody.amountDue = editedExpense.amountDue;
         }

         // Only include date fields based on expense type
         if (isWeeklySpreadExpense()) {
            // For weekly spread expense, include start and end dates
            requestBody.startDate = editedExpense.startDate || null;
            requestBody.endDate = editedExpense.endDate || null;
         } else {
            // For other expense types, include regular date
            requestBody.date = editedExpense.date || null;
         }

         const response = await fetch(`/api/expenses/${editedExpense._id}`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
         });

         if (!response.ok) {
            throw new Error("Failed to update expense");
         }

         const updatedExpense = await response.json();

         // Update the modal with the new expense details
         const formattedExpense = {
            ...updatedExpense,
            date: updatedExpense.date
               ? updatedExpense.date instanceof Date
                  ? updatedExpense.date.toISOString().split("T")[0]
                  : updatedExpense.date.includes &&
                    updatedExpense.date.includes("T")
                  ? new Date(updatedExpense.date).toISOString().split("T")[0]
                  : updatedExpense.date
               : "",
            startDate: updatedExpense.startDate
               ? updatedExpense.startDate instanceof Date
                  ? updatedExpense.startDate.toISOString().split("T")[0]
                  : updatedExpense.startDate.includes &&
                    updatedExpense.startDate.includes("T")
                  ? new Date(updatedExpense.startDate)
                       .toISOString()
                       .split("T")[0]
                  : updatedExpense.startDate
               : "",
            endDate: updatedExpense.endDate
               ? updatedExpense.endDate instanceof Date
                  ? updatedExpense.endDate.toISOString().split("T")[0]
                  : updatedExpense.endDate.includes &&
                    updatedExpense.endDate.includes("T")
                  ? new Date(updatedExpense.endDate).toISOString().split("T")[0]
                  : updatedExpense.endDate
               : "",
         };

         // If this is a future expense, ensure we preserve all future expense properties
         if (expense.isFutureExpense) {
            // Apply the original server's prorated amountDue (not the edited value)
            formattedExpense.amountDue = updatedExpense.amountDue;

            // Preserve future expense properties
            formattedExpense.isFutureExpense = true;
            formattedExpense.originalAmountDue = editedExpense.amountDue; // The value user edited
            formattedExpense.isFutureProrated = true;

            // Check if it's funded based on the assigned vs prorated amount
            formattedExpense.isFutureFunded =
               updatedExpense.amountAssigned >= updatedExpense.amountDue;

            // Ensure status remains as "future"
            formattedExpense.status = "future";
         } else {
            // Don't override the status if it's already "paid"
            if (updatedExpense.status !== "paid") {
               // Re-determine status client-side to ensure "late" status is preserved
               // Use the imported determineExpenseStatus function for consistency
               const clientSideStatus = determineExpenseStatus(
                  updatedExpense.amountSpent,
                  updatedExpense.amountAssigned,
                  updatedExpense.amountDue,
                  updatedExpense.date,
                  updatedExpense.startDate,
                  updatedExpense.endDate,
                  updatedExpense.status // Pass current status
               );

               // Only override the status if it should be "late"
               // This preserves other statuses determined by the server
               if (clientSideStatus === "late") {
                  formattedExpense.status = "late";
               }
            }
         }

         setEditedExpense(formattedExpense);
         // Update the original expense reference
         setOriginalExpense(formattedExpense);
         setExpenseModified(false);

         // Update the current expense in the modal
         expense = updatedExpense;

         setIsEditing(false);
         setSaveSuccess("Expense successfully updated!");

         // Notify parent of the change to update the UI immediately
         if (typeof onExpenseUpdate === "function") {
            // Send the formatted expense with the corrected status
            onExpenseUpdate(formattedExpense);
         }
      } catch (err) {
         console.error("Error updating expense:", err);
         setSaveError("Failed to save changes. Please try again.");
      } finally {
         setSavingChanges(false);
      }
   };

   const cancelEdit = () => {
      // Reset to original expense values
      setEditedExpense(originalExpense);
      setExpenseModified(false);
      setIsEditing(false);
      setSaveError(null);
   };

   const saveRecurringChanges = async () => {
      setSavingChanges(true);
      setSaveError(null);
      setSaveSuccess(null);

      try {
         const response = await fetch(
            `/api/user/recurring-expenses/${editedRecurring.id}`,
            {
               method: "PATCH",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  name: editedRecurring.name,
                  amount: editedRecurring.amount,
                  frequency: editedRecurring.frequency,
                  dueDay: editedRecurring.dueDay,
                  weeklyChargeType: editedRecurring.weeklyChargeType,
                  enabled: editedRecurring.enabled,
               }),
            }
         );

         if (!response.ok) {
            throw new Error("Failed to update recurring expense");
         }

         const updatedData = await response.json();
         const updatedRecurring = updatedData.expense || updatedData;

         // Update state with new data
         setRecurringExpense(updatedRecurring);
         setEditedRecurring(updatedRecurring);
         setOriginalRecurring(updatedRecurring);
         setRecurringModified(false);
         setIsEditingRecurring(false);
         setSaveSuccess("Recurring expense rule successfully updated!");

         // If the response includes an updated expense, notify parent component
         if (
            updatedData.updatedExpense &&
            typeof onExpenseUpdate === "function"
         ) {
            // Apply client-side status determination
            const updatedWithCorrectStatus = { ...updatedData.updatedExpense };

            // Don't override the status if it's already "paid"
            if (updatedWithCorrectStatus.status !== "paid") {
               // Use the imported determineExpenseStatus function for consistency
               const clientSideStatus = determineExpenseStatus(
                  updatedWithCorrectStatus.amountSpent,
                  updatedWithCorrectStatus.amountAssigned,
                  updatedWithCorrectStatus.amountDue,
                  updatedWithCorrectStatus.date,
                  updatedWithCorrectStatus.startDate,
                  updatedWithCorrectStatus.endDate,
                  updatedWithCorrectStatus.status // Pass current status
               );

               // Only override the status if it should be "late"
               if (clientSideStatus === "late") {
                  updatedWithCorrectStatus.status = "late";
               }
            }

            onExpenseUpdate(updatedWithCorrectStatus);
         }
      } catch (err) {
         console.error("Error updating recurring expense:", err);
         setSaveError(
            "Failed to save recurring expense changes. Please try again."
         );
      } finally {
         setSavingChanges(false);
      }
   };

   const handleDeleteRecurring = () => {
      setDeletionModal({
         isOpen: true,
         recurringExpense: recurringExpense,
      });
   };

   const handleConfirmRecurringDeletion = async (expenseId, action) => {
      try {
         setSavingChanges(true);
         setSaveError(null);

         const response = await fetch(
            `/api/user/recurring-expenses/${expenseId}`,
            {
               method: "DELETE",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({ action }),
            }
         );

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to delete recurring expense");
         }

         const result = await response.json();

         setDeletionModal({ isOpen: false, recurringExpense: null });

         // Show detailed success message based on action taken
         const message =
            action === "template-only"
               ? `Template deleted. ${
                    result.standaloneExpenses || 0
                 } expenses converted to standalone items.`
               : `Template and ${
                    result.deletedExpenses || 0
                 } expenses deleted. ${
                    result.unassignedTransactions || 0
                 } transactions unassigned.`;

         setSaveSuccess(message);

         // Close the modal after successful deletion since the template no longer exists
         setTimeout(() => {
            onClose();
         }, 2000);
      } catch (err) {
         setSaveError(err.message);
      } finally {
         setSavingChanges(false);
      }
   };

   const saveDebtChanges = async () => {
      setSavingChanges(true);
      setSaveError(null);
      setSaveSuccess(null);

      try {
         // Create a note for the change
         const note = "Updated via expense modal";

         // Prepare the debt update with the edited values
         const updatedDebt = {
            _id: editedDebt._id,
            debtType: editedDebt.debtType,
            lender: editedDebt.lender,
            balance: parseFloat(editedDebt.balance),
            apr: parseFloat(editedDebt.apr),
            minimumPayment: parseFloat(editedDebt.minimumPayment),
            dueDate: editedDebt.dueDate,
            note: note,
         };

         // Call the debt API to update
         const response = await fetch("/api/user/debts", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt: updatedDebt }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update debt");
         }

         const result = await response.json();

         // Update state with the returned debt
         setDebtInfo(result.debt);
         setEditedDebt(result.debt);
         setOriginalDebt(result.debt);
         setDebtModified(false);

         setSaveSuccess("Debt information successfully updated!");

         // If the expense amount needs to be updated to match the new minimum payment
         if (editedExpense.amountDue !== result.debt.minimumPayment) {
            // Update the expense's amount due to match the new minimum payment
            const updatedExpense = {
               ...editedExpense,
               amountDue: result.debt.minimumPayment,
            };

            const expenseResponse = await fetch(
               `/api/expenses/${editedExpense._id}`,
               {
                  method: "PUT",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                     amountDue: result.debt.minimumPayment,
                  }),
               }
            );

            if (expenseResponse.ok) {
               const updatedExpenseData = await expenseResponse.json();
               setEditedExpense(updatedExpenseData);
               setOriginalExpense(updatedExpenseData);

               // Update parent component
               if (typeof onExpenseUpdate === "function") {
                  onExpenseUpdate(updatedExpenseData);
               }
            }
         }
      } catch (err) {
         console.error("Error updating debt:", err);
         setSaveError("Failed to save debt changes. Please try again.");
      } finally {
         setSavingChanges(false);
      }
   };

   if (!isOpen || !expense) return null;

   const formatDate = (date) => {
      if (!date) return "";
      try {
         return format(
            date instanceof Date ? date : parseISO(date),
            "MMM d, yyyy"
         );
      } catch (error) {
         return "";
      }
   };

   const getDateDisplay = () => {
      if (expense.startDate && expense.endDate) {
         return `${formatDate(expense.startDate)} - ${formatDate(
            expense.endDate
         )}`;
      }
      return formatDate(expense.date);
   };

   const isWeeklySpreadExpense = () => {
      return (
         expense.frequency === "weekly" && expense.weeklyChargeType === "spread"
      );
   };

   // Create the modal content
   const modalContent = (
      <div className="fixed inset-0 z-50 overflow-y-auto">
         <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" onClick={onClose}>
               <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">
               &#8203;
            </span>

            <div
               className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"
               onClick={(e) => e.stopPropagation()}
            >
               <div className="bg-white dark:bg-gray-800 px-4 pt-4 pb-3 sm:p-5">
                  <div className="w-full">
                     {/* Header - Now just shows tabs */}
                     <div className="flex justify-between items-center mb-3">
                        <div>
                           <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                              {expense.status === "projected"
                                 ? "Recurring Expense Rules"
                                 : "Expense Details"}
                           </h3>
                           <span className="text-xs text-gray-500 dark:text-gray-400 font-normal">
                              ID:{" "}
                              {expense.status === "projected" &&
                              expense.recurringExpenseId
                                 ? expense.recurringExpenseId
                                 : expense._id}
                           </span>
                        </div>
                        {/* X button to close the modal */}
                        <button
                           onClick={onClose}
                           className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
                           aria-label="Close"
                        >
                           <svg
                              className="h-6 w-6"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                           >
                              <path
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                                 d="M6 18L18 6M6 6l12 12"
                              />
                           </svg>
                        </button>
                     </div>

                     {/* Tabs */}
                     <div className="border-b border-gray-200 dark:border-gray-700 mb-4">
                        <nav className="-mb-px flex space-x-8">
                           {/* Only show Details and Transactions tabs for non-projected expenses */}
                           {expense.status !== "projected" && (
                              <>
                                 <button
                                    onClick={() => {
                                       setActiveTab("details");
                                    }}
                                    className={`${
                                       activeTab === "details"
                                          ? "border-gray-500 text-gray-600 dark:text-gray-400"
                                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
                                 >
                                    Details
                                 </button>
                                 <button
                                    onClick={() => {
                                       setActiveTab("transactions");
                                    }}
                                    className={`${
                                       activeTab === "transactions"
                                          ? "border-gray-500 text-gray-600 dark:text-gray-400"
                                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
                                 >
                                    Transactions
                                 </button>
                              </>
                           )}
                           {/* Always show Rules tab for expenses with recurring rules or debt payments */}
                           {(expense.recurringExpenseId ||
                              expense.isDebtPayment) && (
                              <button
                                 onClick={() => {
                                    setActiveTab("rules");
                                 }}
                                 className={`${
                                    activeTab === "rules"
                                       ? "border-gray-500 text-gray-600 dark:text-gray-400"
                                       : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                                 } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
                              >
                                 Rules
                              </button>
                           )}
                        </nav>
                     </div>

                     {/* Success/Error Messages */}
                     {saveSuccess && (
                        <div className="bg-green-50 border border-green-200 dark:bg-green-900/30 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-md mb-4 flex items-center shadow-sm animate-fadeIn sticky top-0 z-10">
                           <svg
                              className="h-5 w-5 mr-2 text-green-500 dark:text-green-400"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                 clipRule="evenodd"
                              />
                           </svg>
                           <div>
                              <p className="font-medium">{saveSuccess}</p>
                              <p className="text-sm text-green-700 dark:text-green-400">
                                 Your changes have been successfully saved.
                              </p>
                           </div>
                        </div>
                     )}

                     {saveError && (
                        <p className="text-red-500 text-sm mb-2">{saveError}</p>
                     )}

                     {/* Tab Content */}
                     {activeTab === "details" ? (
                        <>
                           {/* Main Info Section */}
                           <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 mb-4">
                              <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                                 {/* Left Column */}
                                 <div>
                                    {/* Description - Moved from header to here */}
                                    <div className="mb-2">
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          Description
                                       </span>
                                       <input
                                          type="text"
                                          name="description"
                                          value={editedExpense.description}
                                          onChange={handleInputChange}
                                          className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white mt-1"
                                       />
                                    </div>

                                    {/* Date */}
                                    <div className="mb-2">
                                       <div className="flex justify-between">
                                          <span className="text-sm text-gray-500 dark:text-gray-400">
                                             {isWeeklySpreadExpense()
                                                ? "Date Range"
                                                : "Date"}
                                          </span>
                                       </div>
                                       {isWeeklySpreadExpense() ? (
                                          <div className="flex space-x-2 mt-1">
                                             <div className="w-1/2">
                                                <input
                                                   type="date"
                                                   name="startDate"
                                                   value={
                                                      editedExpense.startDate
                                                   }
                                                   onChange={handleInputChange}
                                                   className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                   placeholder="Start Date"
                                                />
                                             </div>
                                             <div className="w-1/2">
                                                <input
                                                   type="date"
                                                   name="endDate"
                                                   value={editedExpense.endDate}
                                                   onChange={handleInputChange}
                                                   className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                   placeholder="End Date"
                                                />
                                             </div>
                                          </div>
                                       ) : (
                                          <input
                                             type="date"
                                             name="date"
                                             value={editedExpense.date}
                                             onChange={handleInputChange}
                                             className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white mt-1"
                                          />
                                       )}
                                    </div>

                                    {/* Category */}
                                    {expense.category && (
                                       <div className="mb-2">
                                          <span className="text-sm text-gray-500 dark:text-gray-400">
                                             Category
                                          </span>
                                          <input
                                             type="text"
                                             name="category"
                                             value={editedExpense.category}
                                             onChange={handleInputChange}
                                             className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white mt-1"
                                          />
                                       </div>
                                    )}

                                    {/* Frequency */}
                                    {expense.frequency && (
                                       <div className="mb-2">
                                          <span className="text-sm text-gray-500 dark:text-gray-400">
                                             Frequency
                                          </span>
                                          <p className="font-medium text-gray-900 dark:text-white capitalize">
                                             {expense.frequency}
                                             {expense.frequency === "weekly" &&
                                                expense.weeklyChargeType && (
                                                   <span>
                                                      {" "}
                                                      (
                                                      {expense.weeklyChargeType}
                                                      )
                                                   </span>
                                                )}
                                          </p>
                                       </div>
                                    )}

                                    {/* Status */}
                                    <div>
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          Status
                                       </span>
                                       <div className="mt-1">
                                          <StatusBadge
                                             status={expense.status}
                                          />
                                       </div>
                                    </div>
                                 </div>

                                 {/* Right Column - Amounts */}
                                 <div className="grid grid-cols-1 gap-2">
                                    {/* Amount Due */}
                                    <div>
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          {expense.isFutureExpense
                                             ? "Original Amount Due"
                                             : "Amount Due"}
                                          {expense.isFutureExpense && (
                                             <span className="ml-1 text-xs text-gray-500">
                                                (Prorated:{" "}
                                                {
                                                   formatAmount(
                                                      expense.amountDue
                                                   ).formattedValue
                                                }
                                                )
                                             </span>
                                          )}
                                       </span>
                                       <input
                                          type="number"
                                          name="amountDue"
                                          value={editedExpense.amountDue}
                                          onChange={handleAmountChange}
                                          step="0.01"
                                          min="0"
                                          className="w-full px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white mt-1"
                                       />
                                    </div>

                                    {/* Amount Assigned */}
                                    <div>
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          Amount Assigned
                                       </span>
                                       <p className="font-medium text-gray-900 dark:text-white">
                                          <FormatAmountDisplay
                                             value={expense.amountAssigned}
                                          />
                                       </p>
                                    </div>

                                    {/* Amount Spent */}
                                    <div>
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          Amount Spent
                                       </span>
                                       <p className="font-medium text-gray-900 dark:text-white">
                                          <FormatAmountDisplay
                                             value={expense.amountSpent}
                                          />
                                       </p>
                                    </div>

                                    {/* Available */}
                                    <div>
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          Available
                                       </span>
                                       <p className="font-medium text-gray-900 dark:text-white">
                                          <FormatAmountDisplay
                                             value={
                                                expense.amountAvailable || 0
                                             }
                                          />
                                       </p>
                                    </div>
                                 </div>
                              </div>
                           </div>

                           {/* Save/Cancel Buttons */}
                           <div className="flex gap-2 mb-4">
                              <button
                                 onClick={saveChanges}
                                 disabled={savingChanges || !expenseModified}
                                 className={`px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm ${
                                    savingChanges || !expenseModified
                                       ? "opacity-50 cursor-not-allowed"
                                       : ""
                                 }`}
                              >
                                 {savingChanges ? "Saving..." : "Save Changes"}
                              </button>
                              <button
                                 onClick={() => {
                                    // Reset to original expense values
                                    setEditedExpense(originalExpense);
                                    setExpenseModified(false);
                                    setSaveError(null);
                                 }}
                                 disabled={!expenseModified}
                                 className={`px-3 py-1 bg-gray-300 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 text-sm ${
                                    !expenseModified
                                       ? "opacity-50 cursor-not-allowed"
                                       : ""
                                 }`}
                              >
                                 Reset
                              </button>
                           </div>
                        </>
                     ) : activeTab === "transactions" ? (
                        /* Transactions Tab Content */
                        <div>
                           <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">
                              Related Transactions
                           </h4>

                           {loading ? (
                              <p className="text-center text-gray-500 dark:text-gray-400 py-3">
                                 Loading transactions...
                              </p>
                           ) : error ? (
                              <p className="text-center text-red-500 py-3">
                                 {error}
                              </p>
                           ) : transactions.length === 0 ? (
                              <p className="text-center text-gray-500 dark:text-gray-400 py-3">
                                 No transactions found for this expense.
                              </p>
                           ) : (
                              <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                                 <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead className="bg-gray-50 dark:bg-gray-700">
                                       <tr>
                                          <th
                                             scope="col"
                                             className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                          >
                                             Date
                                          </th>
                                          <th
                                             scope="col"
                                             className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                          >
                                             Payee
                                          </th>
                                          <th
                                             scope="col"
                                             className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                          >
                                             Amount
                                          </th>
                                          <th
                                             scope="col"
                                             className="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                          >
                                             Status
                                          </th>
                                       </tr>
                                    </thead>
                                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                       {transactions.map((transaction) => (
                                          <tr
                                             key={transaction._id}
                                             className="hover:bg-gray-50 dark:hover:bg-gray-700"
                                          >
                                             <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                                                {formatDate(transaction.date)}
                                             </td>
                                             <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200 max-w-[180px]">
                                                <div className="truncate">
                                                   {transaction.payee || "—"}
                                                </div>
                                             </td>
                                             <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-900 dark:text-gray-200">
                                                <FormatAmountDisplay
                                                   value={transaction.amount}
                                                />
                                             </td>
                                             <td className="px-3 py-2 whitespace-nowrap text-sm text-center">
                                                <span
                                                   className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                      transaction.status ===
                                                      "cleared"
                                                         ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                                         : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                                   }`}
                                                >
                                                   {transaction.status ===
                                                   "cleared"
                                                      ? "Cleared"
                                                      : "Pending"}
                                                </span>
                                             </td>
                                          </tr>
                                       ))}

                                       {/* Total Row */}
                                       {transactions.length > 0 && (
                                          <tr className="bg-gray-50 dark:bg-gray-700 font-semibold">
                                             <td
                                                colSpan="2"
                                                className="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200 text-right"
                                             >
                                                Total Transactions:
                                             </td>
                                             <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-900 dark:text-gray-200">
                                                <FormatAmountDisplay
                                                   value={transactions.reduce(
                                                      (sum, transaction) =>
                                                         sum +
                                                         (transaction.amount ||
                                                            0),
                                                      0
                                                   )}
                                                />
                                             </td>
                                             <td className="px-3 py-2 whitespace-nowrap"></td>
                                          </tr>
                                       )}
                                    </tbody>
                                 </table>
                              </div>
                           )}
                        </div>
                     ) : (
                        <>
                           {/* Recurring Expense Rules Content */}
                           {expense.recurringExpenseId && (
                              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 mb-4">
                                 {loadingRecurring ? (
                                    <p className="text-center text-gray-500 dark:text-gray-400 py-3">
                                       Loading recurring expense rules...
                                    </p>
                                 ) : recurringError ? (
                                    <p className="text-center text-red-500 py-3">
                                       {recurringError}
                                    </p>
                                 ) : recurringExpense ? (
                                    <div className="space-y-4">
                                       <div className="flex justify-between items-center">
                                          <h4 className="text-md font-medium text-gray-900 dark:text-white">
                                             {expense.status === "projected"
                                                ? "Edit Recurring Expense Rules"
                                                : "Recurring Expense Rules"}
                                          </h4>
                                       </div>

                                       <div className="space-y-3">
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Name
                                             </label>
                                             <input
                                                type="text"
                                                name="name"
                                                value={editedRecurring.name}
                                                onChange={
                                                   handleRecurringInputChange
                                                }
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Amount
                                             </label>
                                             <input
                                                type="number"
                                                name="amount"
                                                value={editedRecurring.amount}
                                                onChange={
                                                   handleRecurringInputChange
                                                }
                                                step="0.01"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Frequency
                                             </label>
                                             <select
                                                name="frequency"
                                                value={
                                                   editedRecurring.frequency
                                                }
                                                onChange={
                                                   handleRecurringInputChange
                                                }
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             >
                                                <option value="weekly">
                                                   Weekly
                                                </option>
                                                <option value="biweekly">
                                                   Bi-weekly
                                                </option>
                                                <option value="monthly">
                                                   Monthly
                                                </option>
                                                <option value="quarterly">
                                                   Quarterly
                                                </option>
                                                <option value="annually">
                                                   Annually
                                                </option>
                                             </select>
                                          </div>
                                          {editedRecurring.frequency ===
                                             "weekly" && (
                                             <div>
                                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                   Weekly Charge Type
                                                </label>
                                                <select
                                                   name="weeklyChargeType"
                                                   value={
                                                      editedRecurring.weeklyChargeType
                                                   }
                                                   onChange={
                                                      handleRecurringInputChange
                                                   }
                                                   className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                >
                                                   <option value="one-time">
                                                      One Time
                                                   </option>
                                                   <option value="spread">
                                                      Spread
                                                   </option>
                                                </select>
                                             </div>
                                          )}
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                {editedRecurring.frequency ===
                                                "annually"
                                                   ? "Due Month"
                                                   : "Due Day"}
                                             </label>
                                             <select
                                                name="dueDay"
                                                value={editedRecurring.dueDay}
                                                onChange={
                                                   handleRecurringInputChange
                                                }
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             >
                                                {editedRecurring.frequency ===
                                                "annually"
                                                   ? Array.from(
                                                        { length: 12 },
                                                        (_, i) => {
                                                           const monthNames = [
                                                              "January",
                                                              "February",
                                                              "March",
                                                              "April",
                                                              "May",
                                                              "June",
                                                              "July",
                                                              "August",
                                                              "September",
                                                              "October",
                                                              "November",
                                                              "December",
                                                           ];
                                                           return (
                                                              <option
                                                                 key={i + 1}
                                                                 value={(
                                                                    i + 1
                                                                 ).toString()}
                                                              >
                                                                 {monthNames[i]}
                                                              </option>
                                                           );
                                                        }
                                                     )
                                                   : editedRecurring.frequency ===
                                                        "monthly" ||
                                                     editedRecurring.frequency ===
                                                        "quarterly"
                                                   ? Array.from(
                                                        { length: 31 },
                                                        (_, i) => (
                                                           <option
                                                              key={i + 1}
                                                              value={(
                                                                 i + 1
                                                              ).toString()}
                                                           >
                                                              {i + 1}
                                                           </option>
                                                        )
                                                     )
                                                   : Array.from(
                                                        { length: 7 },
                                                        (_, i) => {
                                                           const dayNames = [
                                                              "Monday",
                                                              "Tuesday",
                                                              "Wednesday",
                                                              "Thursday",
                                                              "Friday",
                                                              "Saturday",
                                                              "Sunday",
                                                           ];
                                                           return (
                                                              <option
                                                                 key={i + 1}
                                                                 value={(
                                                                    i + 1
                                                                 ).toString()}
                                                              >
                                                                 {dayNames[i]}
                                                              </option>
                                                           );
                                                        }
                                                     )}
                                             </select>
                                          </div>
                                          {editedRecurring.frequency ===
                                             "annually" && (
                                             <div>
                                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                   Due Day
                                                </label>
                                                <select
                                                   name="dueMonth"
                                                   value={
                                                      editedRecurring.dueMonth ||
                                                      "1"
                                                   }
                                                   onChange={
                                                      handleRecurringInputChange
                                                   }
                                                   className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                >
                                                   {Array.from(
                                                      { length: 31 },
                                                      (_, i) => (
                                                         <option
                                                            key={i + 1}
                                                            value={(
                                                               i + 1
                                                            ).toString()}
                                                         >
                                                            {i + 1}
                                                         </option>
                                                      )
                                                   )}
                                                </select>
                                             </div>
                                          )}
                                          <div className="flex items-center">
                                             <input
                                                type="checkbox"
                                                name="enabled"
                                                checked={
                                                   editedRecurring.enabled
                                                }
                                                onChange={(e) =>
                                                   handleRecurringInputChange({
                                                      target: {
                                                         name: "enabled",
                                                         value: e.target
                                                            .checked,
                                                      },
                                                   })
                                                }
                                                className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
                                             />
                                             <label className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                                Enabled
                                             </label>
                                          </div>
                                          <div className="flex gap-2 mt-4">
                                             <button
                                                onClick={saveRecurringChanges}
                                                disabled={
                                                   savingChanges ||
                                                   !recurringModified
                                                }
                                                className={`px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm ${
                                                   savingChanges ||
                                                   !recurringModified
                                                      ? "opacity-50 cursor-not-allowed"
                                                      : ""
                                                }`}
                                             >
                                                {savingChanges
                                                   ? "Saving..."
                                                   : "Save Changes"}
                                             </button>
                                             <button
                                                onClick={() => {
                                                   setEditedRecurring(
                                                      originalRecurring
                                                   );
                                                   setRecurringModified(false);
                                                   setSaveError(null);
                                                }}
                                                disabled={!recurringModified}
                                                className={`px-3 py-1 bg-gray-300 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 text-sm ${
                                                   !recurringModified
                                                      ? "opacity-50 cursor-not-allowed"
                                                      : ""
                                                }`}
                                             >
                                                Reset
                                             </button>
                                             <button
                                                onClick={handleDeleteRecurring}
                                                disabled={savingChanges}
                                                className={`px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm ${
                                                   savingChanges
                                                      ? "opacity-50 cursor-not-allowed"
                                                      : ""
                                                }`}
                                             >
                                                Delete Template
                                             </button>
                                          </div>
                                       </div>
                                    </div>
                                 ) : (
                                    <p className="text-center text-gray-500 dark:text-gray-400 py-3">
                                       No recurring expense rules found.
                                    </p>
                                 )}
                              </div>
                           )}

                           {/* Debt Payment Rules Section */}
                           {expense.isDebtPayment && expense.debtId && (
                              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 mb-4">
                                 {loadingDebt ? (
                                    <p className="text-center text-gray-500 dark:text-gray-400 py-3">
                                       Loading debt information...
                                    </p>
                                 ) : debtError ? (
                                    <p className="text-center text-red-500 py-3">
                                       {debtError}
                                    </p>
                                 ) : debtInfo ? (
                                    <div className="space-y-4">
                                       <div className="flex justify-between items-center">
                                          <h4 className="text-md font-medium text-gray-900 dark:text-white">
                                             Debt Payment Rules
                                          </h4>
                                       </div>

                                       <div className="space-y-3">
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Debt Type
                                             </label>
                                             <select
                                                name="debtType"
                                                value={editedDebt.debtType}
                                                onChange={handleDebtInputChange}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             >
                                                <option value="credit card">
                                                   Credit Card
                                                </option>
                                                <option value="personal loan">
                                                   Personal Loan
                                                </option>
                                                <option value="student loan">
                                                   Student Loan
                                                </option>
                                                <option value="mortgage">
                                                   Mortgage
                                                </option>
                                                <option value="auto loan">
                                                   Auto Loan
                                                </option>
                                                <option value="medical debt">
                                                   Medical Debt
                                                </option>
                                                <option value="other">
                                                   Other
                                                </option>
                                             </select>
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Lender
                                             </label>
                                             <input
                                                type="text"
                                                name="lender"
                                                value={editedDebt.lender}
                                                onChange={handleDebtInputChange}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Current Balance
                                             </label>
                                             <input
                                                type="number"
                                                name="balance"
                                                value={editedDebt.balance}
                                                onChange={
                                                   handleDebtNumberChange
                                                }
                                                step="0.01"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                APR (%)
                                             </label>
                                             <input
                                                type="number"
                                                name="apr"
                                                value={editedDebt.apr}
                                                onChange={
                                                   handleDebtNumberChange
                                                }
                                                step="0.01"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Minimum Payment
                                             </label>
                                             <input
                                                type="number"
                                                name="minimumPayment"
                                                value={
                                                   editedDebt.minimumPayment
                                                }
                                                onChange={
                                                   handleDebtNumberChange
                                                }
                                                step="0.01"
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>
                                          <div>
                                             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Due Date (day of month)
                                             </label>
                                             <input
                                                type="text"
                                                name="dueDate"
                                                value={editedDebt.dueDate}
                                                onChange={handleDebtInputChange}
                                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                             />
                                          </div>

                                          <div className="flex gap-2 mt-4">
                                             <button
                                                onClick={saveDebtChanges}
                                                disabled={
                                                   savingChanges ||
                                                   !debtModified
                                                }
                                                className={`px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm ${
                                                   savingChanges ||
                                                   !debtModified
                                                      ? "opacity-50 cursor-not-allowed"
                                                      : ""
                                                }`}
                                             >
                                                {savingChanges
                                                   ? "Saving..."
                                                   : "Save Changes"}
                                             </button>
                                             <button
                                                onClick={() => {
                                                   setEditedDebt(originalDebt);
                                                   setDebtModified(false);
                                                   setSaveError(null);
                                                }}
                                                disabled={!debtModified}
                                                className={`px-3 py-1 bg-gray-300 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded hover:bg-gray-400 dark:hover:bg-gray-500 text-sm ${
                                                   !debtModified
                                                      ? "opacity-50 cursor-not-allowed"
                                                      : ""
                                                }`}
                                             >
                                                Reset
                                             </button>
                                          </div>
                                       </div>
                                    </div>
                                 ) : (
                                    <p className="text-center text-gray-500 dark:text-gray-400 py-3">
                                       No debt information found.
                                    </p>
                                 )}
                              </div>
                           )}
                        </>
                     )}
                  </div>
               </div>
            </div>
         </div>
      </div>
   );

   // Create complete modal structure with deletion modal
   const completeModal = (
      <>
         {modalContent}
         {/* Recurring Expense Deletion Modal */}
         {deletionModal.isOpen && (
            <RecurringExpenseDeletionModal
               recurringExpense={deletionModal.recurringExpense}
               isOpen={deletionModal.isOpen}
               onClose={() =>
                  setDeletionModal({ isOpen: false, recurringExpense: null })
               }
               onConfirm={handleConfirmRecurringDeletion}
            />
         )}
      </>
   );

   // Use React createPortal to render the modal outside of the table DOM structure
   // Only attempt to use createPortal if we're in a browser environment
   if (typeof document !== "undefined") {
      return createPortal(completeModal, document.body);
   }

   // Fallback for SSR (server-side rendering)
   return completeModal;
};

export default ExpenseDetailModal;

"use client";

import { useState, useRef, useEffect } from "react";
import { format, startOfMonth, setDate, setDay } from "date-fns";

export default function ExpenseForm({ onSuccess, onCancel }) {
   const nameInputRef = useRef(null);
   const [formData, setFormData] = useState({
      name: "",
      amount: "",
      frequency: "one-time",
      dueDay: "1",
      enabled: true,
      date: format(new Date(), "yyyy-MM-dd"),
   });
   const [error, setError] = useState("");
   const [loading, setLoading] = useState(false);

   const handleSubmit = async (e) => {
      e.preventDefault();
      setError("");
      setLoading(true);

      try {
         if (formData.frequency === "one-time") {
            // Create a one-time expense
            const response = await fetch("/api/expenses", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  description: formData.name,
                  amountDue: parseFloat(formData.amount),
                  amountAssigned: 0,
                  amountSpent: 0,
                  date: formData.date, // Use date string directly (YYYY-MM-DD)
                  status: "scheduled",
               }),
            });

            if (!response.ok) {
               const data = await response.json();
               throw new Error(data.error || "Failed to create expense");
            }
         } else {
            // Calculate the due date for this month
            let dueDate;
            const currentMonth = startOfMonth(new Date());

            if (
               formData.frequency === "weekly" ||
               formData.frequency === "biweekly"
            ) {
               // For weekly/biweekly, set to the next occurrence of the specified day
               dueDate = setDay(currentMonth, parseInt(formData.dueDay));
               // If the date is in the past, add 7 days
               if (dueDate < new Date()) {
                  dueDate.setDate(dueDate.getDate() + 7);
               }
            } else {
               // For monthly/quarterly/annually, set to the specified day of the month
               dueDate = setDate(currentMonth, parseInt(formData.dueDay));
            }

            // Create the scheduled expense for this month
            const expenseResponse = await fetch("/api/expenses", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  description: formData.name,
                  amountDue: parseFloat(formData.amount),
                  amountAssigned: 0,
                  amountSpent: 0,
                  date: format(dueDate, "yyyy-MM-dd"), // Convert to YYYY-MM-DD string
                  status: "scheduled",
               }),
            });

            if (!expenseResponse.ok) {
               throw new Error("Failed to create scheduled expense");
            }

            // Add recurring expense
            const response = await fetch("/api/user/recurring-expenses", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  name: formData.name,
                  amount: formData.amount,
                  frequency: formData.frequency,
                  dueDay: formData.dueDay,
                  enabled: formData.enabled,
               }),
            });

            if (!response.ok) {
               const data = await response.json();
               throw new Error(
                  data.error || "Failed to create recurring expense"
               );
            }
         }

         onSuccess?.();
      } catch (err) {
         setError(err.message);
      } finally {
         setLoading(false);
      }
   };

   const handleChange = (e) => {
      const { name, value, type, checked } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: type === "checkbox" ? checked : value,
      }));
   };

   // Add effect to focus the input when component mounts
   useEffect(() => {
      if (nameInputRef.current) {
         nameInputRef.current.focus();
      }
   }, []);

   return (
      <form onSubmit={handleSubmit} className="space-y-6">
         {error && (
            <div className="text-red-600 dark:text-red-400 text-sm">
               {error}
            </div>
         )}

         <div>
            <label
               htmlFor="name"
               className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
               Description
            </label>
            <input
               ref={nameInputRef}
               type="text"
               id="name"
               name="name"
               value={formData.name}
               onChange={handleChange}
               required
               className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            />
         </div>

         <div>
            <label
               htmlFor="amount"
               className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
               Amount
            </label>
            <div className="mt-1 relative rounded-md shadow-sm">
               <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                     $
                  </span>
               </div>
               <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.01"
                  className="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
               />
            </div>
         </div>

         <div>
            <label
               htmlFor="frequency"
               className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
               Frequency
            </label>
            <select
               id="frequency"
               name="frequency"
               value={formData.frequency}
               onChange={handleChange}
               required
               className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
            >
               <option value="one-time">One-time</option>
               <option value="weekly">Weekly</option>
               <option value="biweekly">Bi-weekly</option>
               <option value="monthly">Monthly</option>
               <option value="quarterly">Quarterly</option>
               <option value="annually">Annually</option>
            </select>
         </div>

         {formData.frequency === "one-time" ? (
            <div>
               <label
                  htmlFor="date"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
               >
                  Due Date
               </label>
               <input
                  type="date"
                  id="date"
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
               />
            </div>
         ) : (
            <>
               <div>
                  <label
                     htmlFor="dueDay"
                     className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                     Due Day
                  </label>
                  <select
                     id="dueDay"
                     name="dueDay"
                     value={formData.dueDay}
                     onChange={handleChange}
                     required
                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                  >
                     {formData.frequency === "weekly" ||
                     formData.frequency === "biweekly" ? (
                        // Days of the week
                        <>
                           <option value="0">Sunday</option>
                           <option value="1">Monday</option>
                           <option value="2">Tuesday</option>
                           <option value="3">Wednesday</option>
                           <option value="4">Thursday</option>
                           <option value="5">Friday</option>
                           <option value="6">Saturday</option>
                        </>
                     ) : (
                        // Days of the month
                        Array.from({ length: 31 }, (_, i) => (
                           <option key={i + 1} value={i + 1}>
                              {i + 1}
                           </option>
                        ))
                     )}
                  </select>
               </div>

               <div className="flex items-center">
                  <input
                     type="checkbox"
                     id="enabled"
                     name="enabled"
                     checked={formData.enabled}
                     onChange={handleChange}
                     className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded"
                  />
                  <label
                     htmlFor="enabled"
                     className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                  >
                     Enable this recurring expense
                  </label>
               </div>
            </>
         )}

         <div className="flex justify-end space-x-3">
            <button
               type="button"
               onClick={onCancel}
               className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900"
            >
               Cancel
            </button>
            <button
               type="submit"
               disabled={loading}
               className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900 disabled:opacity-50"
            >
               {loading ? "Creating..." : "Create Expense"}
            </button>
         </div>
      </form>
   );
}

"use client";

export default function ExpenseListModal({ isVisible, message, onClose }) {
   if (!isVisible || !message) {
      return null;
   }

   const isSuccess = message.toLowerCase().includes("success");

   return (
      <div className="fixed inset-0 flex items-center justify-center z-50">
         <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={onClose}
         ></div>
         <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 md:mx-auto p-6 z-50">
            <div className="flex items-start mb-4">
               {isSuccess ? (
                  <div className="flex-shrink-0 mr-3">
                     <svg
                        className="h-8 w-8 text-green-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                     </svg>
                  </div>
               ) : (
                  <div className="flex-shrink-0 mr-3">
                     <svg
                        className="h-8 w-8 text-red-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                     </svg>
                  </div>
               )}
               <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                     {isSuccess ? "Success" : "Notice"}
                  </h3>
                  <p className="mt-2 text-gray-600 dark:text-gray-300">
                     {message}
                  </p>
               </div>
            </div>
            <div className="mt-5 flex justify-end">
               <button
                  type="button"
                  className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  onClick={onClose}
               >
                  Close
               </button>
            </div>
         </div>
      </div>
   );
}

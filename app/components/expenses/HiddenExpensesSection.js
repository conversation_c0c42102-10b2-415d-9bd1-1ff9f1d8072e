"use client";

import { format, parseISO } from "date-fns";
import { formatAmount } from "../../lib/utils/expenseUtils";
import { StatusBadge } from "./ExpenseStatusComponents";

export default function HiddenExpensesSection({
   hiddenExpenses,
   isExpanded,
   onToggleExpanded,
   onUnhideProjection,
}) {
   if (hiddenExpenses.length === 0) {
      return null;
   }

   return (
      <div className="sticky bottom-0 mt-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-md z-10">
         <div className="px-3 py-1 bg-gray-50 dark:bg-gray-800/50 border-y border-gray-200 dark:border-gray-700">
            <button className="w-full text-left" onClick={onToggleExpanded}>
               <div className="flex justify-between items-center">
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                     Skipped Expenses ({hiddenExpenses.length})
                  </span>
                  <svg
                     className={`w-4 h-4 transform transition-transform text-gray-500 dark:text-gray-400 ${
                        isExpanded ? "rotate-180" : ""
                     }`}
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24"
                  >
                     <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                     />
                  </svg>
               </div>
            </button>
         </div>

         {isExpanded && (
            <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-64 overflow-y-auto">
               {hiddenExpenses.map((expense) => (
                  <div
                     key={`hidden-${
                        expense._id || `${expense.description}-${expense.date}`
                     }`}
                     className="p-2 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  >
                     <div className="flex justify-between items-center">
                        <div className="flex-1">
                           <div className="flex items-center space-x-1">
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                 {format(
                                    expense.date instanceof Date
                                       ? expense.date
                                       : parseISO(expense.date),
                                    "MMM d"
                                 )}
                              </span>
                              {expense.status && (
                                 <StatusBadge status={expense.status} />
                              )}
                           </div>
                           <div className="text-xs font-medium text-gray-900 dark:text-white">
                              {expense.description}
                           </div>
                        </div>
                        <div className="flex items-center space-x-3">
                           <div className="flex gap-4">
                              <div className="w-20 text-right">
                                 <div className="text-xs text-gray-500 dark:text-gray-400 tabular-nums">
                                    $
                                    {
                                       formatAmount(expense.amountDue || 0)
                                          .formattedValue
                                    }
                                 </div>
                              </div>
                              <div className="w-20 text-right">
                                 <div className="text-xs text-gray-500 dark:text-gray-400 tabular-nums">
                                    $
                                    {
                                       formatAmount(expense.amountAssigned || 0)
                                          .formattedValue
                                    }
                                 </div>
                              </div>
                           </div>
                           <div className="w-10 flex justify-end">
                              <button
                                 onClick={(e) => onUnhideProjection(expense, e)}
                                 className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                              >
                                 Unskip
                              </button>
                           </div>
                        </div>
                     </div>
                  </div>
               ))}
            </div>
         )}
      </div>
   );
}

"use client";

import { Fragment } from "react";
import { format, parseISO } from "date-fns";
import { formatAmount } from "../../lib/utils/expenseUtils";
import { StatusBadge } from "./ExpenseStatusComponents";

export default function HiddenExpensesCategory({
   hiddenExpenses,
   isExpanded,
   onToggleExpanded,
   onUnhideProjection,
   isMobileView,
}) {
   if (!hiddenExpenses || hiddenExpenses.length === 0) {
      return null;
   }

   return (
      <Fragment>
         <tr className="bg-gray-50 dark:bg-gray-800/50">
            <td colSpan={isMobileView ? "3" : "8"} className="px-4 py-1">
               <button
                  onClick={onToggleExpanded}
                  className="flex items-center justify-between w-full text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
               >
                  <span>Skipped Expenses ({hiddenExpenses.length})</span>
                  <svg
                     className={`w-4 h-4 transform transition-transform ${
                        isExpanded ? "rotate-90" : ""
                     }`}
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24"
                  >
                     <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                     />
                  </svg>
               </button>
            </td>
         </tr>
         {isExpanded &&
            hiddenExpenses.map((expense) => (
               <tr
                  key={`hidden-${
                     expense._id || `${expense.description}-${expense.date}`
                  }`}
                  className="bg-gray-50/30 hover:bg-gray-100/50 dark:bg-gray-800/30 dark:hover:bg-gray-700/50"
               >
                  <td className="px-4 py-1 whitespace-normal break-words text-sm">
                     <div className="flex flex-col max-w-[180px] sm:max-w-[280px] md:max-w-full">
                        <span className="font-medium text-gray-900 dark:text-gray-100 break-words">
                           {expense.description}
                           {expense.isDebtPayment && (
                              <span className="ml-2 px-1.5 py-0.5 text-xs font-semibold rounded-md bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                                 Debt
                              </span>
                           )}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                           {format(
                              expense.date instanceof Date
                                 ? expense.date
                                 : parseISO(expense.date),
                              "MMM d, yyyy"
                           )}
                        </span>
                     </div>
                  </td>
                  <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-center">
                     <div className="flex items-center justify-center space-x-2">
                        <StatusBadge
                           status={expense.status}
                           isFutureFunded={expense.isFutureFunded}
                        />
                     </div>
                  </td>
                  {isMobileView ? (
                     <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                        <div className="flex flex-col space-y-1">
                           <div className="text-xs">
                              Due: $
                              {
                                 formatAmount(expense.amountDue || 0)
                                    .formattedValue
                              }
                           </div>
                           <div className="text-xs">Assigned: --</div>
                           <button
                              onClick={(e) => onUnhideProjection(expense, e)}
                              className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                           >
                              Unskip
                           </button>
                        </div>
                     </td>
                  ) : (
                     <>
                        <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                           $
                           {formatAmount(expense.amountDue || 0).formattedValue}
                        </td>
                        <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                           --
                        </td>
                        <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                           --
                        </td>
                        <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-right">
                           --
                        </td>
                        <td className="px-4 py-1 whitespace-nowrap text-sm text-center">
                           <button
                              onClick={(e) => onUnhideProjection(expense, e)}
                              className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                           >
                              Unskip
                           </button>
                        </td>
                     </>
                  )}
               </tr>
            ))}
      </Fragment>
   );
}

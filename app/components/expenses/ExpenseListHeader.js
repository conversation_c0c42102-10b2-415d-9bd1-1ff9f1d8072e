"use client";

import { formatAmount } from "../../lib/utils/expenseUtils";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "../../../components/ui/dropdown-menu";

export default function ExpenseListHeader({
   isMobileView = false,
   // Status filter props
   showStatusFilter,
   setShowStatusFilter,
   selectedStatuses,
   setSelectedStatuses,
   statusFilterRef,
   STATUS_OPTIONS,
   // Mobile column selector props
   selectedMobileColumn,
   setSelectedMobileColumn,
   showMobileColumnSelector,
   setShowMobileColumnSelector,
   mobileColumnSelectorRef,
   // Future expenses props
   showFutureExpenses,
   onToggleFutureExpenses,
   // Schedule all props
   hasProjectedExpenses,
   scheduleAllLoading,
   onScheduleAllExpenses,
   // Other props
   onShowFormChange,
}) {
   return (
      <thead
         className="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10 shadow-md"
         style={{ borderSpacing: "0" }}
      >
         {/* Main header row with no bottom border to avoid gap */}
         <tr style={{ borderBottom: "none" }}>
            <th
               scope="col"
               className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-gray-800 w-full min-w-[150px] md:min-w-[200px] lg:min-w-[300px]"
            >
               Expenses
            </th>
            <th
               scope="col"
               className={`px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-gray-800 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 ${
                  isMobileView ? "w-[80px]" : ""
               }`}
               onClick={() => setShowStatusFilter(!showStatusFilter)}
            >
               <div className="flex flex-col items-center relative">
                  <div className="flex items-center">
                     Status
                     {/* Only show status badge if specific filtering is active */}
                     {!(
                        selectedStatuses.includes("all") &&
                        STATUS_OPTIONS.filter((s) => s !== "all").every((s) =>
                           selectedStatuses.includes(s)
                        )
                     ) && selectedStatuses.length > 0 ? (
                        <span className="ml-2 inline-flex">
                           <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                              {selectedStatuses.length === 1
                                 ? selectedStatuses[0].charAt(0).toUpperCase() +
                                   selectedStatuses[0].slice(1)
                                 : `${selectedStatuses.length}`}
                           </span>
                        </span>
                     ) : selectedStatuses.length === 0 ? (
                        <span className="ml-2 inline-flex">
                           <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                              None
                           </span>
                        </span>
                     ) : null}
                     <svg
                        className="w-4 h-4 ml-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M19 9l-7 7-7-7"
                        />
                     </svg>
                  </div>
                  {showStatusFilter && (
                     <div
                        ref={statusFilterRef}
                        className="absolute right-0 mt-8 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-20"
                     >
                        <div className="py-1" role="menu">
                           {STATUS_OPTIONS.map((status) => (
                              <button
                                 key={status}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    if (status === "all") {
                                       // If all is clicked, toggle between all statuses selected and none selected
                                       const allStatusesExceptAll =
                                          STATUS_OPTIONS.filter(
                                             (s) => s !== "all"
                                          );

                                       // Check if currently all statuses are selected (including "all")
                                       const allSelected =
                                          selectedStatuses.includes("all") &&
                                          allStatusesExceptAll.every((s) =>
                                             selectedStatuses.includes(s)
                                          );

                                       if (allSelected) {
                                          // If all are selected, deselect all
                                          setSelectedStatuses([]);
                                       } else {
                                          // If not all are selected, select all
                                          setSelectedStatuses([
                                             "all",
                                             ...allStatusesExceptAll,
                                          ]);
                                       }
                                    } else {
                                       setSelectedStatuses((prev) => {
                                          const isSelected =
                                             prev.includes(status);
                                          let newStatuses;

                                          if (isSelected) {
                                             // Remove the status
                                             newStatuses = prev.filter(
                                                (s) => s !== status
                                             );

                                             // Also remove "all" if it was there
                                             newStatuses = newStatuses.filter(
                                                (s) => s !== "all"
                                             );
                                          } else {
                                             // Add the status
                                             newStatuses = [...prev, status];

                                             // Check if all other statuses are now selected
                                             const allStatusesExceptAll =
                                                STATUS_OPTIONS.filter(
                                                   (s) => s !== "all"
                                                );
                                             const allOthersSelected =
                                                allStatusesExceptAll.every(
                                                   (s) =>
                                                      s === status ||
                                                      newStatuses.includes(s)
                                                );

                                             // If all other statuses are selected, also select "all"
                                             if (
                                                allOthersSelected &&
                                                !newStatuses.includes("all")
                                             ) {
                                                newStatuses.push("all");
                                             }
                                          }

                                          return newStatuses;
                                       });
                                    }
                                 }}
                                 className={`block w-full text-left px-4 py-2 text-sm ${
                                    status === "all"
                                       ? selectedStatuses.includes("all") &&
                                         STATUS_OPTIONS.filter(
                                            (s) => s !== "all"
                                         ).every((s) =>
                                            selectedStatuses.includes(s)
                                         )
                                          ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                       : selectedStatuses.includes(status)
                                       ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                       : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                 }`}
                                 role="menuitem"
                              >
                                 {status === "all"
                                    ? "All Statuses"
                                    : status.charAt(0).toUpperCase() +
                                      status.slice(1)}
                              </button>
                           ))}
                        </div>
                     </div>
                  )}
               </div>
            </th>

            {/* Mobile View Column Selector */}
            {isMobileView ? (
               <th
                  scope="col"
                  className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-gray-800 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 w-[110px]"
                  onClick={() =>
                     setShowMobileColumnSelector(!showMobileColumnSelector)
                  }
               >
                  <div
                     className="flex flex-col items-center relative"
                     ref={mobileColumnSelectorRef}
                  >
                     <div className="flex items-center">
                        {selectedMobileColumn.charAt(0).toUpperCase() +
                           selectedMobileColumn.slice(1)}
                        <svg
                           className="w-4 h-4 ml-1"
                           fill="none"
                           stroke="currentColor"
                           viewBox="0 0 24 24"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                           />
                        </svg>
                     </div>
                     {showMobileColumnSelector && (
                        <div className="absolute right-0 mt-8 w-32 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-20">
                           <div className="py-1" role="menu">
                              {["due", "assigned", "spent", "available"].map(
                                 (column) => (
                                    <button
                                       key={column}
                                       onClick={(e) => {
                                          e.stopPropagation();
                                          setSelectedMobileColumn(column);
                                          setShowMobileColumnSelector(false);
                                       }}
                                       className={`block w-full text-left px-4 py-2 text-sm ${
                                          selectedMobileColumn === column
                                             ? "bg-blue-50 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                             : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                       }`}
                                       role="menuitem"
                                    >
                                       {column.charAt(0).toUpperCase() +
                                          column.slice(1)}
                                    </button>
                                 )
                              )}
                           </div>
                        </div>
                     )}
                  </div>
               </th>
            ) : (
               <>
                  <th
                     scope="col"
                     className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32 bg-gray-50 dark:bg-gray-800"
                  >
                     Due
                  </th>
                  <th
                     scope="col"
                     className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32 bg-gray-50 dark:bg-gray-800"
                  >
                     Assigned
                  </th>
                  <th
                     scope="col"
                     className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32 bg-gray-50 dark:bg-gray-800"
                  >
                     Spent
                  </th>
                  <th
                     scope="col"
                     className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-32 bg-gray-50 dark:bg-gray-800"
                  >
                     Available
                  </th>
               </>
            )}

            {!isMobileView && (
               <th
                  scope="col"
                  className="px-4 py-2 text-right w-24 bg-gray-50 dark:bg-gray-800"
               >
                  <div className="flex items-center justify-end space-x-2">
                     {/* Schedule All button - only show when there are projected expenses */}
                     {hasProjectedExpenses && (
                        <button
                           onClick={onScheduleAllExpenses}
                           disabled={scheduleAllLoading}
                           title="Schedule All Projected Expenses"
                           className="inline-flex items-center p-1 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                           Schedule All
                        </button>
                     )}
                     <div className="relative">
                        <DropdownMenu>
                           <DropdownMenuTrigger className="inline-flex items-center p-1 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900">
                              <svg
                                 className="w-4 h-4"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24"
                              >
                                 <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"
                                 />
                              </svg>
                           </DropdownMenuTrigger>
                           <DropdownMenuContent align="end" className="w-64">
                              <DropdownMenuItem
                                 onClick={() => onShowFormChange(true)}
                              >
                                 <svg
                                    className="w-4 h-4 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                 >
                                    <path
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                       strokeWidth={2}
                                       d="M12 4v16m8-8H4"
                                    />
                                 </svg>
                                 Add Expense
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    onToggleFutureExpenses();
                                 }}
                              >
                                 <div className="w-full flex items-center justify-between">
                                    <span className="whitespace-nowrap mr-4">
                                       Show Future Expenses
                                    </span>
                                    <div
                                       className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 ${
                                          showFutureExpenses
                                             ? "bg-blue-600"
                                             : "bg-gray-600"
                                       }`}
                                       role="switch"
                                       aria-checked={showFutureExpenses}
                                    >
                                       <span
                                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                             showFutureExpenses
                                                ? "translate-x-5"
                                                : "translate-x-1"
                                          }`}
                                       />
                                    </div>
                                 </div>
                              </DropdownMenuItem>
                           </DropdownMenuContent>
                        </DropdownMenu>
                     </div>
                  </div>
               </th>
            )}
         </tr>
      </thead>
   );
}

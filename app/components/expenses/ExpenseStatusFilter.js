import { useState, useRef, useEffect } from "react";

export const ExpenseStatusFilter = ({ selectedStatuses, onStatusChange }) => {
   const [isOpen, setIsOpen] = useState(false);
   const dropdownRef = useRef(null);

   const AVAILABLE_STATUSES = [
      { id: "projected", label: "Projected" },
      { id: "scheduled", label: "Scheduled" },
      { id: "funded", label: "Funded" },
      { id: "paid", label: "Paid" },
      { id: "late", label: "Late" },
      { id: "overpaid", label: "Overpaid" },
      { id: "underpaid", label: "Underpaid" },
      { id: "underbudget", label: "Under Budget" },
   ];

   const handleSelectAll = () => {
      onStatusChange(AVAILABLE_STATUSES.map((status) => status.id));
   };

   const handleDeselectAll = () => {
      onStatusChange([]);
   };

   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            dropdownRef.current &&
            !dropdownRef.current.contains(event.target)
         ) {
            setIsOpen(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, []);

   return (
      <div className="relative" ref={dropdownRef}>
         <button
            onClick={() => setIsOpen(!isOpen)}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none"
         >
            <svg
               className="h-4 w-4 mr-2"
               fill="none"
               stroke="currentColor"
               viewBox="0 0 24 24"
            >
               <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
               />
            </svg>
            Filter
            {selectedStatuses.length !== AVAILABLE_STATUSES.length && (
               <span className="ml-1 text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 px-1.5 rounded-full">
                  {selectedStatuses.length}
               </span>
            )}
         </button>

         {isOpen && (
            <div className="absolute left-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
               <div className="p-2 space-y-1">
                  {AVAILABLE_STATUSES.map((status) => (
                     <label
                        key={status.id}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer"
                     >
                        <input
                           type="checkbox"
                           className="form-checkbox h-4 w-4 text-gray-600 dark:text-gray-400 rounded border-gray-300 dark:border-gray-600"
                           checked={selectedStatuses.includes(status.id)}
                           onChange={() => {
                              const newStatuses = selectedStatuses.includes(
                                 status.id
                              )
                                 ? selectedStatuses.filter(
                                      (s) => s !== status.id
                                   )
                                 : [...selectedStatuses, status.id];
                              onStatusChange(newStatuses);
                           }}
                        />
                        <span className="ml-2">{status.label}</span>
                     </label>
                  ))}
                  <div className="border-t border-gray-200 dark:border-gray-700 mt-2 pt-2 space-y-1">
                     <button
                        onClick={handleSelectAll}
                        className="w-full text-left px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                     >
                        Select All
                     </button>
                     <button
                        onClick={handleDeselectAll}
                        className="w-full text-left px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                     >
                        Deselect All
                     </button>
                  </div>
               </div>
            </div>
         )}
      </div>
   );
};

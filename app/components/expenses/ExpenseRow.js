import { format, parseISO } from "date-fns";
import { FormatAmountDisplay, OverspentIcon } from "./ExpenseUIComponents";
import { StatusBadge, AvailablePill } from "./ExpenseStatusComponents";
import { useState, useEffect, useRef } from "react";
import ExpenseDetailModal from "./ExpenseDetailModal";
import {
   formatAmount,
   determineExpenseStatus,
   createDebouncedExpenseUpdater,
   validateExpenseUpdate,
   evaluateExpression,
   calculateTimeUntilDue,
} from "../../lib/utils/expenseUtils";
import useClickOutside from "../../hooks/useClickOutside";
import useBalanceStore from "../../lib/stores/balanceStore";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "../../../components/ui/dropdown-menu";

export const ExpenseRow = ({
   expense,
   onSchedule,
   onHide,
   onUnhide,
   onDelete,
   dateRange,
   isMobileView = false,
   selectedMobileColumn = "due",
}) => {
   // Create refs for the expense updater and current expense state
   const expenseUpdaterRef = useRef(null);
   const currentExpenseRef = useRef(expense);

   // Local state for editing
   const [isEditingDue, setIsEditingDue] = useState(false);
   const [localDueValue, setLocalDueValue] = useState("");
   const [isEditingAssigned, setIsEditingAssigned] = useState(false);
   const [localAssignedValue, setLocalAssignedValue] = useState("");
   const [isProcessing, setIsProcessing] = useState(false);
   const [isCollectAssignProcessing, setIsCollectAssignProcessing] =
      useState(false);

   // Add a ref for the available pill dropdown
   const availablePillRef = useRef(null);
   // Add local state to control the available pill dropdown visibility
   const [localShowAvailableDropdown, setLocalShowAvailableDropdown] =
      useState(false);

   // State for detail modal
   const [showDetailModal, setShowDetailModal] = useState(false);
   const [detailModalInitialTab, setDetailModalInitialTab] =
      useState("details");
   // Local state to track the current expense data
   const [currentExpense, setCurrentExpense] = useState(expense);

   useEffect(() => {
      // Initialize the debounced updater
      expenseUpdaterRef.current = createDebouncedExpenseUpdater();
      currentExpenseRef.current = expense;
      setCurrentExpense(expense);

      // Cleanup function to cancel any pending updates
      return () => {
         expenseUpdaterRef.current?.cancelPendingUpdates();
      };
   }, []);

   // Calculate if expense is overspent internally
   const isOverspent = (() => {
      if (!currentExpense) return false;

      // For individual expenses
      if (!currentExpense.isAggregated) {
         const amountSpent = Math.abs(currentExpense.amountSpent || 0);
         const amountDue = currentExpense.amountDue || 0;
         return amountSpent > amountDue;
      }

      // For aggregated weekly expenses, check if any child expense is overspent
      if (currentExpense.expenses) {
         return currentExpense.expenses.some((week) => {
            const amountSpent = Math.abs(week.amountSpent || 0);
            const amountDue = week.amountDue || 0;
            return amountSpent > amountDue;
         });
      }

      return false;
   })();

   // Use the useClickOutside hook for the available pill dropdown
   useClickOutside(availablePillRef, () => {
      if (localShowAvailableDropdown) {
         setLocalShowAvailableDropdown(false);
      }
   });

   const handleDueClick = (e) => {
      e.stopPropagation(); // Prevent row click
      if (currentExpense.status === "projected") return;

      setIsEditingDue(true);
      setLocalDueValue(currentExpense.amountDue.toString());
   };

   const handleDueChange = (e) => {
      setLocalDueValue(e.target.value);
   };

   const handleDueKeyDown = async (e) => {
      if (e.key === "Enter") {
         e.preventDefault();
         await handleDueBlur();
      } else if (e.key === "Escape") {
         e.preventDefault();
         setIsEditingDue(false);
         setLocalDueValue("");
      }
   };

   const handleDueBlur = async () => {
      if (!localDueValue || !validateExpenseUpdate(localDueValue)) {
         setIsEditingDue(false);
         setLocalDueValue("");
         return;
      }

      const newAmount = parseFloat(localDueValue);
      const oldAmount = currentExpenseRef.current.amountDue || 0;

      if (newAmount === oldAmount) {
         setIsEditingDue(false);
         setLocalDueValue("");
         return;
      }

      const newStatus = determineExpenseStatus(
         currentExpenseRef.current.amountSpent,
         currentExpenseRef.current.amountAssigned,
         newAmount
      );

      // Create optimistic update
      const updatedExpense = {
         ...currentExpenseRef.current,
         amountDue: Number(newAmount.toFixed(2)),
         status: newStatus,
      };

      // Update local state optimistically
      Object.assign(currentExpenseRef.current, updatedExpense);
      setCurrentExpense(updatedExpense);

      // Queue the update
      expenseUpdaterRef.current.queueUpdate(
         expense._id,
         {
            amountDue: updatedExpense.amountDue,
            status: updatedExpense.status,
         },
         {
            onSuccess: (result) => {
               Object.assign(currentExpenseRef.current, result);
               setCurrentExpense((prev) => ({ ...prev, ...result }));
            },
            onError: () => {
               // Revert on error
               Object.assign(currentExpenseRef.current, {
                  amountDue: oldAmount,
                  status: determineExpenseStatus(
                     currentExpenseRef.current.amountSpent,
                     currentExpenseRef.current.amountAssigned,
                     oldAmount
                  ),
               });
               setCurrentExpense((prev) => ({
                  ...prev,
                  amountDue: oldAmount,
                  status: determineExpenseStatus(
                     prev.amountSpent,
                     prev.amountAssigned,
                     oldAmount
                  ),
               }));
            },
         }
      );

      setIsEditingDue(false);
      setLocalDueValue("");
   };

   const handleAssignedClick = (e) => {
      e.stopPropagation(); // Prevent row click
      if (currentExpense.status === "projected") return;

      setIsEditingAssigned(true);
      setLocalAssignedValue(currentExpense.amountAssigned.toString());
   };

   const handleAssignedChange = (e) => {
      const value = e.target.value.replace(/[^0-9.+-]/g, "");
      setLocalAssignedValue(value);
   };

   const handleAssignedKeyDown = async (e) => {
      if (e.key === "Enter") {
         e.preventDefault();
         await handleAssignedBlur();
      } else if (e.key === "Escape") {
         e.preventDefault();
         setIsEditingAssigned(false);
         setLocalAssignedValue("");
      }
   };

   const handleAssignedBlur = async () => {
      if (!localAssignedValue) {
         setIsEditingAssigned(false);
         setLocalAssignedValue("");
         return;
      }

      const newAmount = evaluateExpression(
         localAssignedValue,
         currentExpenseRef.current.amountAssigned || 0
      );

      if (!validateExpenseUpdate(newAmount)) {
         setIsEditingAssigned(false);
         setLocalAssignedValue("");
         return;
      }

      const oldAmount = currentExpenseRef.current.amountAssigned || 0;
      const difference = Number((newAmount - oldAmount).toFixed(2));
      const newAvailable = Number(
         (newAmount + (currentExpenseRef.current.amountSpent || 0)).toFixed(2)
      );

      // Determine the status - for future expenses, check if it's funded
      const isFutureExp = currentExpenseRef.current.isFutureExpense;
      const proratedAmountDue = isFutureExp
         ? currentExpenseRef.current.amountDue
         : null;

      // Don't change status if it's already paid
      let newStatus = currentExpenseRef.current.status;
      if (currentExpenseRef.current.status !== "paid") {
         newStatus = isFutureExp
            ? newAmount >= proratedAmountDue
               ? "funded"
               : "future"
            : determineExpenseStatus(
                 currentExpenseRef.current.amountSpent,
                 newAmount,
                 currentExpenseRef.current.amountDue,
                 currentExpenseRef.current.date,
                 currentExpenseRef.current.startDate,
                 currentExpenseRef.current.endDate,
                 currentExpenseRef.current.status
              );
      }

      // First, create an optimistic update for the UI
      const updatedExpense = {
         ...currentExpenseRef.current,
         amountAssigned: Number(newAmount.toFixed(2)),
         amountAvailable: newAvailable,
         status: newStatus,
      };

      // OPTIMISTIC UPDATES: Update UI immediately for better UX
      // 1. Update expense row display
      Object.assign(currentExpenseRef.current, updatedExpense);
      setCurrentExpense(updatedExpense);

      // 2. Update Zustand store (Ready to Assign amount)
      const { updateAssignedAmount } = useBalanceStore.getState();
      updateAssignedAmount(difference);

      // Save the prorated values for future expenses before the update
      const isFutureExpense = currentExpenseRef.current.isFutureExpense;
      const originalAmountDue = isFutureExpense
         ? currentExpenseRef.current.originalAmountDue
         : null;

      // Update using the standard endpoint
      expenseUpdaterRef.current.queueUpdate(
         expense._id,
         {
            amountAssigned: updatedExpense.amountAssigned,
            amountAvailable: updatedExpense.amountAvailable,
            updateUserAssigned: difference,
            // For regular expenses, update status normally
            // For future expenses, don't send status so server won't recalculate it
            ...(isFutureExpense ? {} : { status: updatedExpense.status }),
         },
         {
            onSuccess: (result) => {
               if (isFutureExpense) {
                  // Check if the future expense is funded but keep status as "future"
                  const isFutureFunded =
                     updatedExpense.amountAssigned >= proratedAmountDue;

                  // For future expenses, preserve the special properties from before the update
                  const updatedResult = {
                     ...result,
                     status: "future", // Always keep as "future"
                     isFutureExpense: true,
                     isFutureFunded: isFutureFunded, // Add this property to indicate if it's funded
                     amountDue: proratedAmountDue,
                     originalAmountDue: originalAmountDue || result.amountDue,
                     isFutureProrated: true,
                  };
                  Object.assign(currentExpenseRef.current, updatedResult);
                  setCurrentExpense((prev) => ({ ...prev, ...updatedResult }));
               } else {
                  // For regular expenses, use the server result as is
                  Object.assign(currentExpenseRef.current, result);
                  setCurrentExpense((prev) => ({ ...prev, ...result }));
               }
            },
            onError: () => {
               // Only revert the expense row on database failure
               // Keep the Zustand store updates as they represent user intent
               Object.assign(currentExpenseRef.current, {
                  amountAssigned: oldAmount,
                  amountAvailable:
                     oldAmount + (currentExpenseRef.current.amountSpent || 0),
                  status: determineExpenseStatus(
                     currentExpenseRef.current.amountSpent,
                     oldAmount,
                     currentExpenseRef.current.amountDue
                  ),
               });
               setCurrentExpense((prev) => ({
                  ...prev,
                  amountAssigned: oldAmount,
                  amountAvailable: oldAmount + (prev.amountSpent || 0),
                  status: determineExpenseStatus(
                     prev.amountSpent,
                     oldAmount,
                     prev.amountDue
                  ),
               }));

               // Note: We intentionally do NOT revert the Zustand store
               // The store updates represent the user's intent and should persist
               // even if the database update fails (user can retry later)
            },
         }
      );

      setIsEditingAssigned(false);
      setLocalAssignedValue("");
   };

   const handleEdit = (e) => {
      e.stopPropagation();
      // Set initial tab based on expense status
      if (currentExpense.status === "projected") {
         setDetailModalInitialTab("rules"); // Open the rules tab for projected expenses
      } else {
         setDetailModalInitialTab("details"); // Open the details tab for other expenses
      }
      setShowDetailModal(true);
   };

   const handleSpentClick = (e) => {
      e.stopPropagation();
      // Set the tab before showing the modal
      setDetailModalInitialTab("transactions");
      setShowDetailModal(true);

      // Log to verify the tab is being set correctly
      console.log("Opening modal with transactions tab");
   };

   const handleDelete = (e) => {
      e.stopPropagation();
      onDelete(expense._id);
   };

   // Helper function to calculate available amount
   const calculateAvailableAmount = (amountAssigned, amountSpent) => {
      return Number(((amountAssigned || 0) + (amountSpent || 0)).toFixed(2));
   };

   // Handle collecting available amounts
   const handleCollectAmount = async (expense) => {
      if (isCollectAssignProcessing) return;

      try {
         setIsCollectAssignProcessing(true);

         if (currentExpense.isAggregated) {
            let totalCollected = 0;
            const updatedSubExpenses = [...currentExpense.expenses];

            for (const weekExpense of currentExpense.expenses) {
               const availableAmount = calculateAvailableAmount(
                  weekExpense.amountAssigned,
                  weekExpense.amountSpent
               );
               if (availableAmount <= 0) continue;

               const amountToCollect = availableAmount;
               const newAssignedAmount =
                  weekExpense.amountAssigned - amountToCollect;

               // Update the sub-expense in our tracking array
               const subExpenseIndex = updatedSubExpenses.findIndex(
                  (subExp) => subExp._id === weekExpense._id
               );
               if (subExpenseIndex !== -1) {
                  updatedSubExpenses[subExpenseIndex] = {
                     ...weekExpense,
                     amountAssigned: newAssignedAmount,
                     amountAvailable: calculateAvailableAmount(
                        newAssignedAmount,
                        weekExpense.amountSpent
                     ),
                  };
               }

               // Queue the update for this sub-expense
               expenseUpdaterRef.current.queueUpdate(
                  weekExpense._id,
                  {
                     amountAssigned: newAssignedAmount,
                     updateUserAssigned: -amountToCollect,
                  },
                  {
                     onSuccess: (result) => {
                        // Update the sub-expense with server response
                        const subIndex = updatedSubExpenses.findIndex(
                           (subExp) => subExp._id === weekExpense._id
                        );
                        if (subIndex !== -1) {
                           updatedSubExpenses[subIndex] = {
                              ...updatedSubExpenses[subIndex],
                              ...result,
                           };
                           // Update the parent expense state
                           const updatedParent = {
                              ...currentExpenseRef.current,
                              expenses: updatedSubExpenses,
                              amountAssigned: updatedSubExpenses.reduce(
                                 (sum, exp) => sum + (exp.amountAssigned || 0),
                                 0
                              ),
                              amountAvailable: updatedSubExpenses.reduce(
                                 (sum, exp) =>
                                    sum +
                                    calculateAvailableAmount(
                                       exp.amountAssigned,
                                       exp.amountSpent
                                    ),
                                 0
                              ),
                           };
                           Object.assign(
                              currentExpenseRef.current,
                              updatedParent
                           );
                           setCurrentExpense(updatedParent);
                        }
                     },
                     onError: () => {
                        // Revert this sub-expense on error
                        const subIndex = updatedSubExpenses.findIndex(
                           (subExp) => subExp._id === weekExpense._id
                        );
                        if (subIndex !== -1) {
                           updatedSubExpenses[subIndex] = weekExpense;
                           // Revert the parent expense state
                           const revertedParent = {
                              ...currentExpenseRef.current,
                              expenses: updatedSubExpenses,
                              amountAssigned: updatedSubExpenses.reduce(
                                 (sum, exp) => sum + (exp.amountAssigned || 0),
                                 0
                              ),
                              amountAvailable: updatedSubExpenses.reduce(
                                 (sum, exp) =>
                                    sum +
                                    calculateAvailableAmount(
                                       exp.amountAssigned,
                                       exp.amountSpent
                                    ),
                                 0
                              ),
                           };
                           Object.assign(
                              currentExpenseRef.current,
                              revertedParent
                           );
                           setCurrentExpense(revertedParent);
                        }
                     },
                  }
               );

               totalCollected += amountToCollect;
            }

            // Optimistically update the aggregated expense display
            const updatedExpense = {
               ...currentExpenseRef.current,
               expenses: updatedSubExpenses,
               amountAssigned: updatedSubExpenses.reduce(
                  (sum, exp) => sum + (exp.amountAssigned || 0),
                  0
               ),
               amountAvailable: updatedSubExpenses.reduce(
                  (sum, exp) =>
                     sum +
                     calculateAvailableAmount(
                        exp.amountAssigned,
                        exp.amountSpent
                     ),
                  0
               ),
            };

            Object.assign(currentExpenseRef.current, updatedExpense);
            setCurrentExpense(updatedExpense);

            // Update store with collected amount (negative because we're removing assigned)
            const { updateAssignedAmount } = useBalanceStore.getState();
            updateAssignedAmount(-totalCollected);
         } else {
            // Handle individual expense
            const availableAmount = calculateAvailableAmount(
               currentExpenseRef.current.amountAssigned,
               currentExpenseRef.current.amountSpent
            );
            const amountToCollect = availableAmount;
            const newAssignedAmount =
               currentExpenseRef.current.amountAssigned - amountToCollect;

            // Create optimistic update
            const updatedExpense = {
               ...currentExpenseRef.current,
               amountAssigned: newAssignedAmount,
               amountAvailable: calculateAvailableAmount(
                  newAssignedAmount,
                  currentExpenseRef.current.amountSpent
               ),
            };

            // Update UI optimistically
            Object.assign(currentExpenseRef.current, updatedExpense);
            setCurrentExpense(updatedExpense);

            // Update store
            const { updateAssignedAmount } = useBalanceStore.getState();
            updateAssignedAmount(-amountToCollect);

            // Queue the database update
            expenseUpdaterRef.current.queueUpdate(
               expense._id,
               {
                  amountAssigned: newAssignedAmount,
                  updateUserAssigned: -amountToCollect,
               },
               {
                  onSuccess: (result) => {
                     Object.assign(currentExpenseRef.current, result);
                     setCurrentExpense((prev) => ({ ...prev, ...result }));
                  },
                  onError: () => {
                     // Revert on error
                     Object.assign(currentExpenseRef.current, expense);
                     setCurrentExpense(expense);
                     // Revert store changes
                     const { updateAssignedAmount } =
                        useBalanceStore.getState();
                     updateAssignedAmount(amountToCollect);
                  },
               }
            );
         }
      } catch (error) {
         console.error("Error collecting amount:", error);
         // Revert optimistic updates on error
         Object.assign(currentExpenseRef.current, expense);
         setCurrentExpense(expense);
      } finally {
         setIsCollectAssignProcessing(false);
      }
   };

   // Handle assigning needed amounts
   const handleAssignAmount = async (expense) => {
      if (isCollectAssignProcessing) return;

      try {
         setIsCollectAssignProcessing(true);

         if (currentExpense.isAggregated) {
            let totalNeeded = 0;
            const updatedSubExpenses = [...currentExpense.expenses];

            // Calculate total needed and update sub-expenses
            for (const weekExpense of currentExpense.expenses) {
               if (weekExpense.amountAvailable < 0) {
                  const amountNeeded = Math.abs(weekExpense.amountAvailable);
                  const newAssignedAmount =
                     weekExpense.amountAssigned + amountNeeded;

                  // Update the sub-expense in our tracking array
                  const subExpenseIndex = updatedSubExpenses.findIndex(
                     (subExp) => subExp._id === weekExpense._id
                  );
                  if (subExpenseIndex !== -1) {
                     updatedSubExpenses[subExpenseIndex] = {
                        ...weekExpense,
                        amountAssigned: newAssignedAmount,
                        amountAvailable: 0, // Should be 0 after assigning needed amount
                     };
                  }

                  // Queue the update for this sub-expense
                  expenseUpdaterRef.current.queueUpdate(
                     weekExpense._id,
                     {
                        amountAssigned: newAssignedAmount,
                        updateUserAssigned: amountNeeded,
                     },
                     {
                        onSuccess: (result) => {
                           // Update the sub-expense with server response
                           const subIndex = updatedSubExpenses.findIndex(
                              (subExp) => subExp._id === weekExpense._id
                           );
                           if (subIndex !== -1) {
                              updatedSubExpenses[subIndex] = {
                                 ...updatedSubExpenses[subIndex],
                                 ...result,
                              };
                              // Update the parent expense state
                              const updatedParent = {
                                 ...currentExpenseRef.current,
                                 expenses: updatedSubExpenses,
                                 amountAssigned: updatedSubExpenses.reduce(
                                    (sum, exp) =>
                                       sum + (exp.amountAssigned || 0),
                                    0
                                 ),
                                 amountAvailable: updatedSubExpenses.reduce(
                                    (sum, exp) =>
                                       sum +
                                       calculateAvailableAmount(
                                          exp.amountAssigned,
                                          exp.amountSpent
                                       ),
                                    0
                                 ),
                              };
                              Object.assign(
                                 currentExpenseRef.current,
                                 updatedParent
                              );
                              setCurrentExpense(updatedParent);
                           }
                        },
                        onError: () => {
                           // Revert this sub-expense on error
                           const subIndex = updatedSubExpenses.findIndex(
                              (subExp) => subExp._id === weekExpense._id
                           );
                           if (subIndex !== -1) {
                              updatedSubExpenses[subIndex] = weekExpense;
                              // Revert the parent expense state
                              const revertedParent = {
                                 ...currentExpenseRef.current,
                                 expenses: updatedSubExpenses,
                                 amountAssigned: updatedSubExpenses.reduce(
                                    (sum, exp) =>
                                       sum + (exp.amountAssigned || 0),
                                    0
                                 ),
                                 amountAvailable: updatedSubExpenses.reduce(
                                    (sum, exp) =>
                                       sum +
                                       calculateAvailableAmount(
                                          exp.amountAssigned,
                                          exp.amountSpent
                                       ),
                                    0
                                 ),
                              };
                              Object.assign(
                                 currentExpenseRef.current,
                                 revertedParent
                              );
                              setCurrentExpense(revertedParent);
                           }
                        },
                     }
                  );

                  totalNeeded += amountNeeded;
               }
            }

            // Optimistically update the aggregated expense display
            const updatedExpense = {
               ...currentExpenseRef.current,
               expenses: updatedSubExpenses,
               amountAssigned: updatedSubExpenses.reduce(
                  (sum, exp) => sum + (exp.amountAssigned || 0),
                  0
               ),
               amountAvailable: updatedSubExpenses.reduce(
                  (sum, exp) =>
                     sum +
                     calculateAvailableAmount(
                        exp.amountAssigned,
                        exp.amountSpent
                     ),
                  0
               ),
            };

            Object.assign(currentExpenseRef.current, updatedExpense);
            setCurrentExpense(updatedExpense);

            // Update store with assigned amount
            const { updateAssignedAmount } = useBalanceStore.getState();
            updateAssignedAmount(totalNeeded);
         } else {
            // Handle individual expense
            const amountNeeded = Math.abs(
               currentExpenseRef.current.amountAvailable
            );
            const newAssignedAmount =
               currentExpenseRef.current.amountAssigned + amountNeeded;

            // Create optimistic update
            const updatedExpense = {
               ...currentExpenseRef.current,
               amountAssigned: newAssignedAmount,
               amountAvailable: 0, // Should be 0 after assigning needed amount
            };

            // Update UI optimistically
            Object.assign(currentExpenseRef.current, updatedExpense);
            setCurrentExpense(updatedExpense);

            // Update store
            const { updateAssignedAmount } = useBalanceStore.getState();
            updateAssignedAmount(amountNeeded);

            // Queue the database update
            expenseUpdaterRef.current.queueUpdate(
               expense._id,
               {
                  amountAssigned: newAssignedAmount,
                  updateUserAssigned: amountNeeded,
               },
               {
                  onSuccess: (result) => {
                     Object.assign(currentExpenseRef.current, result);
                     setCurrentExpense((prev) => ({ ...prev, ...result }));
                  },
                  onError: () => {
                     // Revert on error
                     Object.assign(currentExpenseRef.current, expense);
                     setCurrentExpense(expense);
                     // Revert store changes
                     const { updateAssignedAmount } =
                        useBalanceStore.getState();
                     updateAssignedAmount(-amountNeeded);
                  },
               }
            );
         }
      } catch (error) {
         console.error("Error assigning amount:", error);
         // Revert optimistic updates on error
         Object.assign(currentExpenseRef.current, expense);
         setCurrentExpense(expense);
      } finally {
         setIsCollectAssignProcessing(false);
      }
   };

   // Handler for when expense is updated in the modal
   const handleExpenseUpdate = (updatedExpense) => {
      // Update the local state
      setCurrentExpense(updatedExpense);
      // Also update the ref for consistency
      currentExpenseRef.current = updatedExpense;
   };

   const handlePillClick = (expenseId) => {
      setLocalShowAvailableDropdown(!localShowAvailableDropdown);
   };

   return (
      <>
         <tr
            className={`hover:bg-gray-50 dark:hover:bg-gray-800 ${
               currentExpense.status === "projected"
                  ? "bg-gray-50/10 dark:bg-gray-600/5"
                  : ""
            }`}
         >
            <td className="px-4 py-1 whitespace-normal break-words text-sm">
               <div className="flex flex-col max-w-[180px] sm:max-w-[280px] md:max-w-full">
                  <span className="font-medium text-gray-900 dark:text-gray-100 break-words">
                     {currentExpense.description}
                     {currentExpense.isDebtPayment && (
                        <span className="ml-2 px-1.5 py-0.5 text-xs font-semibold rounded-md bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                           Debt
                        </span>
                     )}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                     {currentExpense.isFutureExpense ? (
                        // For future expenses, show relative time regardless of funded status
                        <>
                           {calculateTimeUntilDue(
                              currentExpense.date,
                              dateRange
                           )}
                           <span className="ml-1 text-xs text-gray-500">
                              (
                              {format(
                                 currentExpense.date instanceof Date
                                    ? currentExpense.date
                                    : parseISO(currentExpense.date),
                                 "MMM d, yyyy"
                              )}
                              )
                           </span>
                        </>
                     ) : currentExpense.startDate && currentExpense.endDate ? (
                        // For date range expenses
                        <>
                           {format(
                              currentExpense.startDate instanceof Date
                                 ? currentExpense.startDate
                                 : parseISO(currentExpense.startDate),
                              "MMM d"
                           )}
                           {" - "}
                           {format(
                              currentExpense.endDate instanceof Date
                                 ? currentExpense.endDate
                                 : parseISO(currentExpense.endDate),
                              "MMM d, yyyy"
                           )}
                        </>
                     ) : (
                        // For regular expenses
                        format(
                           currentExpense.date instanceof Date
                              ? currentExpense.date
                              : parseISO(currentExpense.date),
                           "MMM d, yyyy"
                        )
                     )}
                  </span>
               </div>
            </td>
            <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-center">
               <div className="flex items-center justify-center space-x-2">
                  <StatusBadge
                     status={
                        currentExpense.isHidden
                           ? "hidden"
                           : isOverspent
                           ? `${currentExpense.status}-overspent`
                           : currentExpense.status
                     }
                     isFutureFunded={currentExpense.isFutureFunded}
                  />
               </div>
            </td>

            {/* Mobile view - selectable column */}
            {isMobileView ? (
               // Mobile view - show only selected column
               <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right">
                  {selectedMobileColumn === "due" && (
                     <div className="relative group">
                        <button
                           onClick={handleDueClick}
                           className={`w-full text-right hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer ${
                              currentExpense.isFutureExpense
                                 ? "flex items-center justify-end"
                                 : ""
                           }`}
                        >
                           <FormatAmountDisplay
                              value={currentExpense.amountDue}
                           />
                           {currentExpense.isFutureExpense && (
                              <span className="ml-1 inline-flex items-center">
                                 <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 text-gray-500"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                 >
                                    <path
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                       strokeWidth={2}
                                       d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                 </svg>
                              </span>
                           )}
                        </button>
                     </div>
                  )}

                  {selectedMobileColumn === "assigned" && (
                     <div>
                        {isEditingAssigned ? (
                           <input
                              type="text"
                              value={localAssignedValue}
                              onChange={handleAssignedChange}
                              onBlur={handleAssignedBlur}
                              onKeyDown={handleAssignedKeyDown}
                              className="w-24 px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                              autoFocus
                              onClick={(e) => e.stopPropagation()}
                           />
                        ) : (
                           <button
                              onClick={handleAssignedClick}
                              className="w-full text-right hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                           >
                              <FormatAmountDisplay
                                 value={currentExpense.amountAssigned}
                              />
                           </button>
                        )}
                     </div>
                  )}

                  {selectedMobileColumn === "spent" && (
                     <button
                        onClick={handleSpentClick}
                        className="w-full text-right hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                     >
                        <FormatAmountDisplay
                           value={currentExpense.amountSpent}
                        />
                     </button>
                  )}

                  {selectedMobileColumn === "available" && (
                     <div ref={availablePillRef}>
                        <AvailablePill
                           expense={currentExpense}
                           showDropdown={localShowAvailableDropdown}
                           onPillClick={(expenseId) =>
                              handlePillClick(expenseId)
                           }
                           onCollectAmount={handleCollectAmount}
                           onAssignAmount={handleAssignAmount}
                           isProcessing={isCollectAssignProcessing}
                        />
                     </div>
                  )}
               </td>
            ) : (
               // Desktop view - show all columns
               <>
                  <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right">
                     {isEditingDue ? (
                        <input
                           type="number"
                           value={localDueValue}
                           onChange={handleDueChange}
                           onBlur={handleDueBlur}
                           onKeyDown={handleDueKeyDown}
                           className="w-24 px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                           autoFocus
                           step="0.01"
                           min="0"
                           onClick={(e) => e.stopPropagation()}
                        />
                     ) : (
                        <div className="relative group">
                           <button
                              onClick={handleDueClick}
                              className={`w-full text-right hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer ${
                                 currentExpense.isFutureExpense
                                    ? "flex items-center justify-end"
                                    : ""
                              }`}
                           >
                              <FormatAmountDisplay
                                 value={currentExpense.amountDue}
                              />

                              {/* Show indication for future expenses */}
                              {currentExpense.isFutureExpense && (
                                 <span className="ml-1 inline-flex items-center">
                                    <svg
                                       xmlns="http://www.w3.org/2000/svg"
                                       className="h-4 w-4 text-gray-500"
                                       fill="none"
                                       viewBox="0 0 24 24"
                                       stroke="currentColor"
                                    >
                                       <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                       />
                                    </svg>
                                 </span>
                              )}
                           </button>

                           {/* Tooltip for future expenses */}
                           {currentExpense.isFutureExpense && (
                              <div className="absolute z-10 invisible group-hover:visible bg-gray-800 dark:bg-gray-700 text-white dark:text-gray-100 text-xs rounded py-1 px-2 right-0 bottom-full mb-1 w-48">
                                 <p>
                                    Prorated amount: $
                                    {parseFloat(
                                       currentExpense.amountDue
                                    ).toFixed(2)}
                                 </p>
                                 <p>
                                    Original amount: $
                                    {parseFloat(
                                       currentExpense.originalAmountDue ||
                                          currentExpense.amountDue
                                    ).toFixed(2)}
                                 </p>
                                 <div className="arrow-down absolute h-2 w-2 bg-gray-800 dark:bg-gray-700 transform rotate-45 right-3 -bottom-1"></div>
                              </div>
                           )}
                        </div>
                     )}
                  </td>
                  <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right">
                     {isEditingAssigned ? (
                        <input
                           type="text"
                           value={localAssignedValue}
                           onChange={handleAssignedChange}
                           onBlur={handleAssignedBlur}
                           onKeyDown={handleAssignedKeyDown}
                           className="w-24 px-2 py-1 text-sm border rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white text-right"
                           autoFocus
                           onClick={(e) => e.stopPropagation()}
                        />
                     ) : (
                        <button
                           onClick={handleAssignedClick}
                           className="w-full text-right hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                        >
                           <FormatAmountDisplay
                              value={currentExpense.amountAssigned}
                           />
                        </button>
                     )}
                  </td>
                  <td className="px-4 py-1 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 text-right">
                     <button
                        onClick={handleSpentClick}
                        className="w-full text-right hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                     >
                        <FormatAmountDisplay
                           value={currentExpense.amountSpent}
                        />
                     </button>
                  </td>
                  <td className="px-4 py-1 whitespace-nowrap text-right">
                     <div ref={availablePillRef}>
                        <AvailablePill
                           expense={currentExpense}
                           showDropdown={localShowAvailableDropdown}
                           onPillClick={(expenseId) =>
                              handlePillClick(expenseId)
                           }
                           onCollectAmount={handleCollectAmount}
                           onAssignAmount={handleAssignAmount}
                           isProcessing={isCollectAssignProcessing}
                        />
                     </div>
                  </td>
               </>
            )}

            {!isMobileView && (
               <td className="px-4 py-1 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                     {currentExpense.status?.toLowerCase() === "projected" && (
                        <>
                           {!currentExpense.isHidden && (
                              <button
                                 onClick={(e) => {
                                    e.stopPropagation(); // Prevent row click
                                    onSchedule(currentExpense);
                                 }}
                                 className="text-sm px-2 py-1 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                                 title="Schedule"
                              >
                                 Schedule
                              </button>
                           )}
                           <button
                              onClick={(e) => {
                                 e.stopPropagation(); // Prevent row click
                                 currentExpense.isHidden
                                    ? onUnhide(currentExpense, e)
                                    : onHide(currentExpense, e);
                              }}
                              className="text-sm px-2 py-1 text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 font-medium"
                              title={
                                 currentExpense.isHidden ? "Unskip" : "Skip"
                              }
                           >
                              {currentExpense.isHidden ? "Unskip" : "Skip"}
                           </button>
                           <button
                              onClick={handleEdit}
                              className="text-sm px-2 py-1 text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 font-medium"
                              title="Edit"
                           >
                              Edit
                           </button>
                        </>
                     )}
                     {currentExpense.status !== "projected" && (
                        <DropdownMenu>
                           <DropdownMenuTrigger className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 flex items-center">
                              <svg
                                 className="w-5 h-5"
                                 fill="none"
                                 stroke="currentColor"
                                 viewBox="0 0 24 24"
                              >
                                 <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                                 />
                              </svg>
                           </DropdownMenuTrigger>
                           <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem onClick={handleEdit}>
                                 <svg
                                    className="w-4 h-4 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                 >
                                    <path
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                       strokeWidth={2}
                                       d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                    />
                                 </svg>
                                 Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                 onClick={handleDelete}
                                 className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                              >
                                 <svg
                                    className="w-4 h-4 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                 >
                                    <path
                                       strokeLinecap="round"
                                       strokeLinejoin="round"
                                       strokeWidth={2}
                                       d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                    />
                                 </svg>
                                 Delete
                              </DropdownMenuItem>
                           </DropdownMenuContent>
                        </DropdownMenu>
                     )}
                  </div>
               </td>
            )}
         </tr>

         {/* Expense Detail Modal */}
         <ExpenseDetailModal
            expense={currentExpense}
            isOpen={showDetailModal}
            onClose={() => setShowDetailModal(false)}
            formatAmount={formatAmount}
            onExpenseUpdate={handleExpenseUpdate}
            initialTab={detailModalInitialTab}
         />
      </>
   );
};

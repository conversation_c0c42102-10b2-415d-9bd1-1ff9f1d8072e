"use client";

import { Fragment } from "react";
import { format } from "date-fns";
import { ExpenseRow } from "./ExpenseRow";
import WeeklyExpenseGroup from "./WeeklyExpenseGroup";

export default function ExpenseCategory({
   categoryName,
   categoryLabel,
   expenses,
   isExpanded,
   onToggleExpanded,
   onSchedule,
   onHide,
   onUnhide,
   onDelete,
   onEdit,
   onToggleExpand,
   expandedRows,
   dateRange,
   isMobileView,
   selectedMobileColumn,
}) {
   if (!expenses || expenses.length === 0) {
      return null;
   }

   return (
      <Fragment key={`${categoryName}-section`}>
         <tr className="bg-gray-50 dark:bg-gray-800/50">
            <td colSpan={isMobileView ? "3" : "8"} className="px-4 py-1">
               <button
                  onClick={() => onToggleExpanded(categoryName)}
                  className="flex items-center justify-between w-full text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
               >
                  <span>{categoryLabel}</span>
                  <svg
                     className={`w-4 h-4 transform transition-transform ${
                        isExpanded ? "rotate-90" : ""
                     }`}
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24"
                  >
                     <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                     />
                  </svg>
               </button>
            </td>
         </tr>
         {isExpanded && (
            <>
               {categoryName === "weekly" ? (
                  // Use WeeklyExpenseGroup for weekly expenses
                  <WeeklyExpenseGroup
                     weeklyExpenses={expenses}
                     onSchedule={onSchedule}
                     onHide={onHide}
                     onUnhide={onUnhide}
                     onDelete={onDelete}
                     onEdit={onEdit}
                     dateRange={dateRange}
                     isMobileView={isMobileView}
                     selectedMobileColumn={selectedMobileColumn}
                  />
               ) : (
                  // Use ExpenseRow for non-weekly expenses
                  expenses.map((expense) => (
                     <ExpenseRow
                        key={
                           expense._id ||
                           `${categoryName}-${expense.description}-${expense.date}`
                        }
                        expense={expense}
                        onSchedule={onSchedule}
                        onHide={onHide}
                        onUnhide={onUnhide}
                        onDelete={onDelete}
                        onEdit={onEdit}
                        dateRange={dateRange}
                        isMobileView={isMobileView}
                        selectedMobileColumn={selectedMobileColumn}
                     />
                  ))
               )}
            </>
         )}
      </Fragment>
   );
}

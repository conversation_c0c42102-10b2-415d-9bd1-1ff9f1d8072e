import { formatAmount } from "../../lib/utils/expenseUtils";

// Overspent Icon Component
export const OverspentIcon = () => (
   <div className="relative inline-block ml-2">
      <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-red-600 dark:bg-red-500 text-white dark:text-red-100 cursor-help hover:bg-red-700 dark:hover:bg-red-600 transition-colors peer">
         <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
         >
            <path
               strokeLinecap="round"
               strokeLinejoin="round"
               strokeWidth={2}
               d="M12 5v9m0 4v.01"
            />
         </svg>
      </div>
      <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-1 hidden peer-hover:block z-10">
         <div className="px-2 py-1 text-xs rounded shadow-lg bg-red-600 dark:bg-red-500 text-white dark:text-red-100 whitespace-nowrap">
            Overspent
         </div>
      </div>
   </div>
);

// Format Amount Display Component
export const FormatAmountDisplay = ({ amount, value }) => {
   const amountToFormat = value !== undefined ? value : amount;
   const { formattedValue, isNegative } = formatAmount(amountToFormat);
   return (
      <span className="tabular-nums">
         {isNegative ? "-" : ""}
         <span className="text-gray-500 dark:text-gray-400">$</span>
         {formattedValue.replace("$", "")}
      </span>
   );
};

// Loading Overlay Component
export const LoadingOverlay = () => (
   <div className="absolute inset-0 bg-gray-900/50 dark:bg-gray-900/70 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex items-center space-x-3">
         <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-500"></div>
         <span className="text-gray-900 dark:text-gray-100">Processing...</span>
      </div>
   </div>
);

// Error Message Component
export const ErrorMessage = ({ message }) => (
   <div className="text-center py-4">
      <div className="text-red-500 dark:text-red-400">{message}</div>
   </div>
);

// Loading Message Component
export const LoadingMessage = () => (
   <div className="text-center py-4">
      <div className="text-gray-500 dark:text-gray-400">Loading...</div>
   </div>
);

// Empty State Component
export const EmptyState = () => (
   <tr>
      <td colSpan="8" className="text-center py-8">
         <p className="text-gray-500 dark:text-gray-400">
            No expenses found for this period.
         </p>
      </td>
   </tr>
);

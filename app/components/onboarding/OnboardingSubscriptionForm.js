"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Info } from "lucide-react";
import PricingTable from "@/app/components/subscription/PricingTable";

export function OnboardingSubscriptionForm({ onSubmit, onSkip }) {
   const [loading, setLoading] = useState(false);
   const [currentPlan, setCurrentPlan] = useState("free");

   // Check current subscription status
   useEffect(() => {
      const checkCurrentPlan = async () => {
         try {
            const response = await fetch("/api/stripe/subscription");
            if (response.ok) {
               const data = await response.json();
               setCurrentPlan(data.plan.id);
            }
         } catch (error) {
            console.error("Error checking subscription:", error);
         }
      };

      checkCurrentPlan();
   }, []);

   const handleSkipForNow = () => {
      // Skip and continue with current plan
      onSkip();
   };

   const handlePlanSelect = async (planId) => {
      setLoading(true);
      try {
         // Create checkout session
         const response = await fetch("/api/stripe/create-checkout", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ planId }),
         });

         if (response.ok) {
            const { url } = await response.json();
            if (url) {
               // Redirect to Stripe checkout
               window.location.href = url;
            }
         } else {
            console.error("Failed to create checkout session");
         }
      } catch (error) {
         console.error("Error selecting plan:", error);
      } finally {
         setLoading(false);
      }
   };

   // Handle subscription completion (called from success page)
   const handleSubscriptionCompleted = async () => {
      try {
         const response = await fetch("/api/stripe/subscription");
         if (response.ok) {
            const data = await response.json();
            const newPlan = data.plan.id;

            setCurrentPlan(newPlan);

            // Continue to next step with the new plan
            onSubmit({
               id: newPlan,
               name: data.plan.name,
               stripePlan: newPlan === "free" ? null : newPlan,
            });
         }
      } catch (error) {
         console.error("Error checking subscription after completion:", error);
      }
   };

   // Check for subscription changes (if user completes checkout in another tab)
   useEffect(() => {
      const interval = setInterval(async () => {
         try {
            const response = await fetch("/api/stripe/subscription");
            if (response.ok) {
               const data = await response.json();
               const newPlan = data.plan.id;

               if (newPlan !== currentPlan && newPlan !== "free") {
                  setCurrentPlan(newPlan);
                  // Auto-continue if user upgraded
                  setTimeout(() => {
                     onSubmit({
                        id: newPlan,
                        name: data.plan.name,
                        stripePlan: newPlan,
                     });
                  }, 2000);
               }
            }
         } catch (error) {
            console.error("Error checking subscription status:", error);
         }
      }, 3000); // Check every 3 seconds

      return () => clearInterval(interval);
   }, [currentPlan, onSubmit]);

   return (
      <div className="space-y-8">
         <div className="text-center">
            <h2 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">
               Choose Your Plan
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
               Select the plan that best fits your budgeting needs. You can
               always upgrade later.
            </p>
         </div>

         {/* Current Plan Status */}
         {currentPlan !== "free" && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
               <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                     <Info className="w-5 h-5 text-blue-500" />
                     <div>
                        <h3 className="font-medium text-blue-900 dark:text-blue-100">
                           Current Plan:{" "}
                           {currentPlan.charAt(0).toUpperCase() +
                              currentPlan.slice(1)}
                        </h3>
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                           You already have an active subscription. You can
                           continue with your current plan or upgrade.
                        </p>
                     </div>
                  </div>
                  <motion.button
                     whileHover={{ scale: 1.02 }}
                     whileTap={{ scale: 0.98 }}
                     onClick={() =>
                        onSubmit({
                           id: currentPlan,
                           name:
                              currentPlan.charAt(0).toUpperCase() +
                              currentPlan.slice(1),
                           stripePlan: currentPlan,
                        })
                     }
                     className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                     Continue with{" "}
                     {currentPlan.charAt(0).toUpperCase() +
                        currentPlan.slice(1)}
                  </motion.button>
               </div>
            </div>
         )}

         {/* Free Plan Status */}
         {currentPlan === "free" && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
               <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                     <Info className="w-5 h-5 text-green-500" />
                     <div>
                        <h3 className="font-medium text-green-900 dark:text-green-100">
                           Current Plan: Free
                        </h3>
                        <p className="text-sm text-green-700 dark:text-green-300">
                           You're on the free plan with access to basic
                           budgeting features. You can upgrade anytime for more
                           advanced features.
                        </p>
                     </div>
                  </div>
                  <motion.button
                     whileHover={{ scale: 1.02 }}
                     whileTap={{ scale: 0.98 }}
                     onClick={() =>
                        onSubmit({
                           id: "free",
                           name: "Free",
                           stripePlan: null,
                        })
                     }
                     className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                     Continue with Free
                  </motion.button>
               </div>
            </div>
         )}

         {/* Stripe Pricing Table */}
         <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="mb-4">
               <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Choose Your Plan
               </h3>
               <p className="text-sm text-gray-600 dark:text-gray-300">
                  Select the plan that best fits your needs. You can always
                  change it later.
               </p>
            </div>

            <PricingTable
               currentPlan={currentPlan}
               onPlanSelect={handlePlanSelect}
            />
         </div>

         {/* Action Buttons */}
         <div className="flex justify-between items-center pt-6">
            <button
               onClick={handleSkipForNow}
               className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            >
               Skip for now
            </button>

            <div className="text-center">
               <p className="text-xs text-gray-500 dark:text-gray-400">
                  You can change your plan anytime in Settings. No long-term
                  commitments.
               </p>
            </div>
         </div>

         {/* FAQ Section */}
         <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
               Frequently Asked Questions
            </h3>
            <div className="space-y-4">
               <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                     Can I upgrade later?
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                     Yes, you can upgrade your plan at any time from the
                     Settings page. Your new features will be available
                     immediately.
                  </p>
               </div>
               <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                     What happens to my data if I downgrade?
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                     Your data is always safe. If you downgrade, you'll just
                     lose access to premium features, but all your financial
                     data remains intact.
                  </p>
               </div>
               <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                     Is there a free trial?
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                     The free plan gives you full access to basic budgeting
                     features. You can try premium features by upgrading
                     anytime.
                  </p>
               </div>
            </div>
         </div>
      </div>
   );
}

"use client";

import { useState, useRef, useEffect } from "react";
import { DAYS_OF_WEEK } from "@/app/lib/constants";

export function OnboardingExpenseForm({
   onSubmit,
   showList = true,
   existingExpenses = [],
   selectedExpense = null,
}) {
   const [expenses, setExpenses] = useState(existingExpenses);
   const [isEditing, setIsEditing] = useState(false);
   const [editingId, setEditingId] = useState(null);
   const [currentExpense, setCurrentExpense] = useState({
      name: "",
      amount: "",
      frequency: "monthly",
      dueDay: "1",
      dueMonth: "1",
      enabled: true,
      weeklyChargeType: "one-time",
      type: "regular",
      isAutomated: false,
   });
   const nameInputRef = useRef(null);

   useEffect(() => {
      if (selectedExpense) {
         setIsEditing(true);
         setEditingId(selectedExpense.index);
         setCurrentExpense({
            name: selectedExpense.name,
            amount: selectedExpense.amount,
            frequency: selectedExpense.frequency,
            dueDay: selectedExpense.dueDay,
            dueMonth: selectedExpense.dueMonth || "1",
            enabled: selectedExpense.enabled,
            weeklyChargeType: selectedExpense.weeklyChargeType || "one-time",
            type: selectedExpense.type || "regular",
            isAutomated: selectedExpense.isAutomated,
         });
         nameInputRef.current?.focus();
      }
   }, [selectedExpense]);

   useEffect(() => {
      setExpenses(existingExpenses);
   }, [existingExpenses]);

   const handleSubmit = (e) => {
      e.preventDefault();
      if (currentExpense.name && currentExpense.amount) {
         let updatedExpenses;

         if (isEditing && editingId !== null) {
            // Update existing expense
            updatedExpenses = expenses.map((expense, index) =>
               index === editingId
                  ? {
                       ...expense,
                       name: currentExpense.name,
                       amount: parseFloat(currentExpense.amount).toFixed(2),
                       frequency: currentExpense.frequency,
                       dueDay: String(currentExpense.dueDay),
                       enabled: Boolean(currentExpense.enabled),
                       type: currentExpense.type || "regular",
                       isAutomated: Boolean(currentExpense.isAutomated),
                       ...(currentExpense.frequency === "weekly"
                          ? {
                               weeklyChargeType:
                                  currentExpense.weeklyChargeType,
                            }
                          : {}),
                       ...(currentExpense.frequency === "annually"
                          ? { dueMonth: currentExpense.dueMonth }
                          : {}),
                    }
                  : expense
            );
         } else {
            // Add new expense
            const expenseToSubmit = {
               ...currentExpense,
               id: Date.now(),
               // Only add dueMonth if frequency is annually
               ...(currentExpense.frequency === "annually"
                  ? { dueMonth: currentExpense.dueMonth || "1" }
                  : {}),
            };
            updatedExpenses = [...expenses, expenseToSubmit];
         }

         setExpenses(updatedExpenses);
         onSubmit(updatedExpenses);

         // Reset form
         setCurrentExpense({
            name: "",
            amount: "",
            frequency: "monthly",
            dueDay: "1",
            dueMonth: "1",
            enabled: true,
            weeklyChargeType: "one-time",
            type: "regular",
            isAutomated: false,
         });
         setIsEditing(false);
         setEditingId(null);
         nameInputRef.current?.focus();
      }
   };

   const handleChange = (e) => {
      const { name, value } = e.target;
      setCurrentExpense((prev) => {
         const updated = {
            ...prev,
            [name]: value,
         };

         // Add weeklyChargeType only when frequency is weekly
         if (name === "frequency" && value === "weekly") {
            updated.weeklyChargeType = "one-time";
            updated.dueDay = "1"; // Reset to Monday
         } else if (name === "frequency" && value !== "weekly") {
            // Remove weeklyChargeType when switching away from weekly
            const { weeklyChargeType, ...rest } = updated;

            // Reset dueDay and dueMonth when changing frequency
            if (value === "annually") {
               rest.dueDay = "1"; // Reset to January
               rest.dueMonth = "1"; // Reset to 1st day
            } else {
               rest.dueDay = "1"; // Reset to 1st day for monthly/quarterly
            }

            return rest;
         }

         return updated;
      });
   };

   // Update the submit button text based on editing state
   const submitButtonText = isEditing ? "Update Expense" : "Add Expense";

   return (
      <div className="space-y-6">
         <form onSubmit={handleSubmit} className="space-y-4">
            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Expense Name
               </label>
               <input
                  ref={nameInputRef}
                  type="text"
                  name="name"
                  value={currentExpense.name}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
               />
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Amount
               </label>
               <input
                  type="number"
                  name="amount"
                  value={currentExpense.amount}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
                  min="0"
                  step="0.01"
               />
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Frequency
               </label>
               <select
                  name="frequency"
                  value={currentExpense.frequency}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
               >
                  <option value="monthly">Monthly</option>
                  <option value="weekly">Weekly</option>
                  <option value="biweekly">Bi-weekly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="annually">Annually</option>
               </select>
            </div>

            {currentExpense.frequency === "weekly" && (
               <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                     Weekly Charge Type
                  </label>
                  <select
                     name="weeklyChargeType"
                     value={currentExpense.weeklyChargeType}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  >
                     <option value="one-time">One Time</option>
                     <option value="spread">Spread Across Week</option>
                  </select>
               </div>
            )}

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Due Day
               </label>
               <select
                  name="dueDay"
                  value={currentExpense.dueDay}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
               >
                  {currentExpense.frequency === "annually"
                     ? Array.from({ length: 12 }, (_, i) => {
                          const monthNames = [
                             "January",
                             "February",
                             "March",
                             "April",
                             "May",
                             "June",
                             "July",
                             "August",
                             "September",
                             "October",
                             "November",
                             "December",
                          ];
                          return (
                             <option key={i + 1} value={String(i + 1)}>
                                {monthNames[i]}
                             </option>
                          );
                       })
                     : currentExpense.frequency === "weekly"
                     ? DAYS_OF_WEEK.map((day) => (
                          <option key={day.value} value={day.value}>
                             {day.label}
                          </option>
                       ))
                     : [...Array(31)].map((_, i) => (
                          <option key={i + 1} value={String(i + 1)}>
                             {i + 1}
                          </option>
                       ))}
               </select>
            </div>

            {currentExpense.frequency === "annually" && (
               <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                     Due Day
                  </label>
                  <select
                     name="dueMonth"
                     value={currentExpense.dueMonth || "1"}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  >
                     {Array.from({ length: 31 }, (_, i) => (
                        <option key={i + 1} value={String(i + 1)}>
                           {i + 1}
                        </option>
                     ))}
                  </select>
               </div>
            )}

            <button
               type="submit"
               className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800"
            >
               {submitButtonText}
            </button>
            {isEditing && (
               <button
                  type="button"
                  onClick={() => {
                     setIsEditing(false);
                     setEditingId(null);
                     setCurrentExpense({
                        name: "",
                        amount: "",
                        frequency: "monthly",
                        dueDay: "1",
                        dueMonth: "1",
                        enabled: true,
                        weeklyChargeType: "one-time",
                        type: "regular",
                        isAutomated: false,
                     });
                  }}
                  className="mt-2 w-full px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900"
               >
                  Cancel Editing
               </button>
            )}
         </form>

         {showList && expenses.length > 0 && (
            <div>
               <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">
                  Added Expenses:
               </h4>
               <ul className="space-y-2">
                  {expenses.map((expense) => (
                     <li
                        key={expense.id}
                        className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded"
                     >
                        <span className="text-gray-900 dark:text-gray-100">
                           {expense.name}
                        </span>
                        <div className="flex items-center space-x-4">
                           <span className="text-gray-500 dark:text-gray-400">
                              {expense.frequency === "annually"
                                 ? (() => {
                                      const monthNames = [
                                         "Jan",
                                         "Feb",
                                         "Mar",
                                         "Apr",
                                         "May",
                                         "Jun",
                                         "Jul",
                                         "Aug",
                                         "Sep",
                                         "Oct",
                                         "Nov",
                                         "Dec",
                                      ];
                                      const monthIndex =
                                         parseInt(expense.dueDay) - 1;
                                      return `${monthNames[monthIndex]} ${expense.dueMonth}`;
                                   })()
                                 : expense.frequency}
                           </span>
                           <span className="text-gray-900 dark:text-gray-100">
                              ${parseFloat(expense.amount).toFixed(2)}
                           </span>
                        </div>
                     </li>
                  ))}
               </ul>
            </div>
         )}
      </div>
   );
}

"use client";

import { useState, useRef, useEffect } from "react";
import { useSession } from "next-auth/react";
import { usePlaidLink } from "react-plaid-link";
import { motion } from "framer-motion";
import {
   CheckCircle,
   XCircle,
   Loader2,
   CreditCard,
   Building,
   Landmark,
   Edit3,
   Link,
} from "lucide-react";
import mongoose from "mongoose";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import SyncDatePickerModal from "../settings/SyncDatePickerModal";

export function OnboardingAccountForm({
   onSubmit,
   showList = true,
   existingAccounts = [],
   selectedAccount = null,
}) {
   const { data: session } = useSession();
   const [accounts, setAccounts] = useState(existingAccounts);
   const [isEditing, setIsEditing] = useState(false);
   const [editingId, setEditingId] = useState(null);
   const [activeTab, setActiveTab] = useState("manual");
   const [hasBankConnectionFeature, setHasBankConnectionFeature] =
      useState(false);
   const [linkToken, setLinkToken] = useState(null);
   const [plaidConnection, setPlaidConnection] = useState(null);
   const [isConnectingPlaid, setIsConnectingPlaid] = useState(false);
   const [error, setError] = useState(null);
   const [showSyncDatePicker, setShowSyncDatePicker] = useState(false);
   const [selectedSyncDate, setSelectedSyncDate] = useState(null);
   const [currentAccount, setCurrentAccount] = useState({
      name: "",
      bank: "",
      accountType: "cash",
      balance: "",
      active: true,
      plaidItemId: null,
   });
   const nameInputRef = useRef(null);

   // Check if user has bank connection feature
   useEffect(() => {
      const checkFeatureAccess = async () => {
         try {
            // For now, assume all authenticated users have bank connection feature
            // This should be properly implemented with your subscription system
            setHasBankConnectionFeature(!!session?.user);
         } catch (error) {
            console.error("Error checking feature access:", error);
         }
      };
      checkFeatureAccess();
   }, [session?.user]);

   useEffect(() => {
      if (selectedAccount) {
         setIsEditing(true);
         setEditingId(selectedAccount.index);
         setCurrentAccount({
            name: selectedAccount.name,
            bank: selectedAccount.bank,
            accountType: selectedAccount.accountType,
            balance: selectedAccount.balance,
            active: selectedAccount.active,
            plaidItemId: selectedAccount.plaidItemId || null,
         });
         // Set the appropriate tab based on whether the account has a plaid connection
         setActiveTab(selectedAccount.plaidItemId ? "linked" : "manual");
         nameInputRef.current?.focus();
      }
   }, [selectedAccount]);

   useEffect(() => {
      setAccounts(existingAccounts);
   }, [existingAccounts]);

   // Update bank name when Plaid connection is established
   useEffect(() => {
      if (plaidConnection) {
         setCurrentAccount((prev) => ({
            ...prev,
            bank: plaidConnection.institutionName,
            plaidItemId: plaidConnection.itemId,
            // Auto-populate balance from Plaid if available
            balance:
               plaidConnection.balance !== undefined
                  ? plaidConnection.balance.toString()
                  : prev.balance,
         }));
         setIsConnectingPlaid(false);
      }
   }, [plaidConnection]);

   const handleSubmit = (e) => {
      e.preventDefault();
      if (currentAccount.name && currentAccount.balance) {
         let updatedAccounts;

         if (isEditing && editingId !== null) {
            // Update existing account
            updatedAccounts = accounts.map((account, index) =>
               index === editingId
                  ? {
                       ...account,
                       ...currentAccount,
                       _id: account._id,
                    }
                  : account
            );
         } else {
            // Add new account
            const newAccount = {
               ...currentAccount,
               _id: new mongoose.Types.ObjectId().toString(),
               // Set plaidItemId based on current tab and connection state
               plaidItemId:
                  activeTab === "linked" && plaidConnection
                     ? plaidConnection.itemId
                     : null,
            };
            updatedAccounts = [...accounts, newAccount];
         }

         setAccounts(updatedAccounts);
         onSubmit(updatedAccounts);

         // Reset form
         setCurrentAccount({
            name: "",
            bank: "",
            accountType: "cash",
            balance: "",
            active: true,
            plaidItemId: null,
         });
         setIsEditing(false);
         setEditingId(null);
         setPlaidConnection(null);
         setError(null);
         nameInputRef.current?.focus();
      }
   };

   const handleChange = (e) => {
      const { name, value, type, checked } = e.target;
      // Prevent manual changes to bank name when connected to Plaid
      if (name === "bank" && plaidConnection) {
         return;
      }

      // If the user manually changes the balance after it was auto-populated,
      // we should still allow it but track that it's been modified
      if (
         name === "balance" &&
         plaidConnection &&
         plaidConnection.balance !== undefined
      ) {
         // The balance has been manually modified from the auto-populated value
         const numericValue = parseFloat(value) || 0;
         const originalBalance = plaidConnection.balance || 0;

         // Optional: You could add logic here to track if the balance was manually modified
         // For now, we'll just allow the change
      }

      setCurrentAccount((prev) => ({
         ...prev,
         [name]: type === "checkbox" ? checked : value,
      }));
   };

   // Plaid Link handlers
   const handlePlaidSuccess = async (publicToken, metadata) => {
      try {
         setError(null);
         // Exchange public token for access token
         const response = await fetch("/api/plaid/exchange-token", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               public_token: publicToken,
               institution: metadata.institution,
               syncStartDate: selectedSyncDate?.date || "today",
            }),
         });

         if (!response.ok) {
            throw new Error("Failed to exchange token");
         }

         const data = await response.json();
         console.log("Plaid exchange response:", data);

         // Set the connection data with balance information
         setPlaidConnection({
            institutionName: metadata.institution.name,
            institutionId: metadata.institution.institution_id,
            itemId: data.plaidItems?.[0]?.itemId || null,
            // Include balance from the response if available (for onboarding mode)
            balance:
               data.onboardingMode && data.primaryBalance !== undefined
                  ? data.primaryBalance
                  : undefined,
            balances: data.balances || [],
            onboardingMode: data.onboardingMode || false,
         });

         // Show success message with balance info
         if (data.onboardingMode && data.primaryBalance !== undefined) {
            console.log(
               "Auto-populated balance from bank:",
               data.primaryBalance
            );
         }
      } catch (error) {
         console.error("Error connecting bank account:", error);
         setError("Failed to connect bank account. Please try again.");
         setIsConnectingPlaid(false);
      }
   };

   // Configure Plaid Link
   const config = {
      token: linkToken,
      onSuccess: handlePlaidSuccess,
      onExit: () => {
         setLinkToken(null);
         setIsConnectingPlaid(false);
      },
   };

   const { open, ready } = usePlaidLink(config);

   // Effect to open Plaid when token is ready
   useEffect(() => {
      if (linkToken && ready) {
         open();
      }
   }, [linkToken, ready, open]);

   // Function to initiate Plaid Link
   const handleLinkBank = async () => {
      setError(null);
      // Show date picker modal first
      setShowSyncDatePicker(true);
   };

   const handleDatePickerConfirm = async (dateSelection) => {
      setSelectedSyncDate(dateSelection);
      setShowSyncDatePicker(false);

      try {
         setIsConnectingPlaid(true);

         const response = await fetch("/api/plaid/create-link-token", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               syncStartDate: dateSelection.date,
            }),
         });

         if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));

            if (response.status === 403 && errorData.requiresUpgrade) {
               setError(
                  "Bank connections require a Basic or Pro subscription plan. Please upgrade your subscription to connect your bank account."
               );
               setIsConnectingPlaid(false);
               return;
            }

            throw new Error(errorData.message || "Failed to create link token");
         }

         const data = await response.json();
         setLinkToken(data.linkToken);
      } catch (error) {
         console.error("Error initiating bank link:", error);
         setError("Failed to initiate bank connection. Please try again.");
         setIsConnectingPlaid(false);
      }
   };

   const handleStartOver = () => {
      setPlaidConnection(null);
      setCurrentAccount((prev) => ({
         ...prev,
         bank: "",
         plaidItemId: null,
         // Reset balance to empty when disconnecting from Plaid
         balance: "",
      }));
      setError(null);
   };

   // Update the submit button text based on editing state
   const submitButtonText = isEditing ? "Update Account" : "Add Account";

   return (
      <div className="space-y-6">
         {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
               <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
         )}

         <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
         >
            <TabsList className="grid w-full grid-cols-2">
               <TabsTrigger value="manual" className="flex items-center gap-2">
                  <Edit3 className="w-4 h-4" />
                  Manual
               </TabsTrigger>
               <TabsTrigger value="linked" className="flex items-center gap-2">
                  <Link className="w-4 h-4" />
                  Linked
               </TabsTrigger>
            </TabsList>

            <TabsContent value="manual" className="space-y-4">
               <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                     <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Account Name
                     </label>
                     <input
                        ref={nameInputRef}
                        type="text"
                        name="name"
                        value={currentAccount.name}
                        onChange={handleChange}
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        required
                     />
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Bank Name
                     </label>
                     <input
                        type="text"
                        name="bank"
                        value={currentAccount.bank}
                        onChange={handleChange}
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Enter bank name (e.g., Chase, Bank of America)"
                     />
                     <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Enter your bank name manually for this account
                     </p>
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Account Type
                     </label>
                     <select
                        name="accountType"
                        value={currentAccount.accountType}
                        onChange={handleChange}
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                     >
                        <option value="cash">Cash</option>
                     </select>
                  </div>

                  <div>
                     <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Current Balance
                     </label>
                     <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                           <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                              $
                           </span>
                        </div>
                        <input
                           type="number"
                           name="balance"
                           value={currentAccount.balance}
                           onChange={handleChange}
                           className="mt-1 block w-full pl-7 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                           required
                           step="0.01"
                           placeholder="0.00"
                        />
                     </div>
                     <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Enter your current account balance manually
                     </p>
                  </div>

                  <button
                     type="submit"
                     className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800"
                  >
                     {submitButtonText}
                  </button>
                  {isEditing && (
                     <button
                        type="button"
                        onClick={() => {
                           setIsEditing(false);
                           setEditingId(null);
                           setCurrentAccount({
                              name: "",
                              bank: "",
                              accountType: "cash",
                              balance: "",
                              active: true,
                              plaidItemId: null,
                           });
                           setPlaidConnection(null);
                           setError(null);
                        }}
                        className="mt-2 w-full px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900"
                     >
                        Cancel Editing
                     </button>
                  )}
               </form>
            </TabsContent>

            <TabsContent value="linked" className="space-y-4">
               {hasBankConnectionFeature ? (
                  <form onSubmit={handleSubmit} className="space-y-4">
                     <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                           Account Name
                        </label>
                        <input
                           ref={nameInputRef}
                           type="text"
                           name="name"
                           value={currentAccount.name}
                           onChange={handleChange}
                           className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                           required
                        />
                     </div>

                     <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                           Bank Connection
                        </label>
                        <div className="mt-1 space-y-3">
                           {!plaidConnection && (
                              <button
                                 type="button"
                                 onClick={handleLinkBank}
                                 disabled={isConnectingPlaid}
                                 className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                              >
                                 {isConnectingPlaid ? (
                                    <>
                                       <Loader2 className="w-4 h-4 animate-spin" />
                                       Connecting...
                                    </>
                                 ) : (
                                    <>
                                       <Building className="w-4 h-4" />
                                       Connect Bank Account
                                    </>
                                 )}
                              </button>
                           )}

                           {plaidConnection && (
                              <motion.div
                                 initial={{ opacity: 0, y: -10 }}
                                 animate={{ opacity: 1, y: 0 }}
                                 className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md"
                              >
                                 <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                       <CheckCircle className="h-5 w-5 text-green-500" />
                                       <div>
                                          <p className="text-sm font-medium text-green-800 dark:text-green-200">
                                             Successfully Connected to Bank
                                          </p>
                                          <p className="text-sm text-green-700 dark:text-green-300">
                                             {plaidConnection.institutionName}
                                             {plaidConnection.balance !==
                                                undefined && (
                                                <span className="block">
                                                   Current balance: $
                                                   {plaidConnection.balance.toFixed(
                                                      2
                                                   )}
                                                </span>
                                             )}
                                          </p>
                                       </div>
                                    </div>
                                    <button
                                       type="button"
                                       onClick={handleStartOver}
                                       className="text-red-600 hover:text-red-700 flex items-center gap-1"
                                    >
                                       <XCircle className="w-4 h-4" />
                                       Start Over
                                    </button>
                                 </div>
                              </motion.div>
                           )}

                           {!plaidConnection && (
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                 Connect your bank account for automatic
                                 transaction syncing
                              </p>
                           )}
                        </div>
                     </div>

                     <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                           Account Type
                        </label>
                        <select
                           name="accountType"
                           value={currentAccount.accountType}
                           onChange={handleChange}
                           className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        >
                           <option value="cash">Cash</option>
                        </select>
                     </div>

                     <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                           Current Balance
                           {plaidConnection &&
                              plaidConnection.balance !== undefined && (
                                 <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                                    (Auto-populated from bank)
                                 </span>
                              )}
                        </label>
                        <div className="mt-1 relative rounded-md shadow-sm">
                           <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                                 $
                              </span>
                           </div>
                           <input
                              type="number"
                              name="balance"
                              value={currentAccount.balance}
                              onChange={handleChange}
                              className={`mt-1 block w-full pl-7 rounded-md shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:text-white sm:text-sm ${
                                 plaidConnection &&
                                 plaidConnection.balance !== undefined
                                    ? "bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600"
                                    : "border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                              }`}
                              required
                              step="0.01"
                           />
                        </div>
                        {plaidConnection &&
                           plaidConnection.balance !== undefined && (
                              <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                                 This balance was automatically retrieved from
                                 your bank account.
                              </p>
                           )}
                     </div>

                     <button
                        type="submit"
                        className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800"
                     >
                        {submitButtonText}
                     </button>
                     {isEditing && (
                        <button
                           type="button"
                           onClick={() => {
                              setIsEditing(false);
                              setEditingId(null);
                              setCurrentAccount({
                                 name: "",
                                 bank: "",
                                 accountType: "cash",
                                 balance: "",
                                 active: true,
                                 plaidItemId: null,
                              });
                              setPlaidConnection(null);
                              setError(null);
                           }}
                           className="mt-2 w-full px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900"
                        >
                           Cancel Editing
                        </button>
                     )}
                  </form>
               ) : (
                  <div className="text-center py-8">
                     <div className="p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex items-center justify-center mb-4">
                           <Landmark className="w-12 h-12 text-blue-500" />
                        </div>
                        <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
                           Bank Connection Available with Basic Plan
                        </h3>
                        <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
                           Upgrade to the Basic plan to connect your bank
                           accounts and automatically sync transactions.
                        </p>
                        <div className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                           <p>• Automatic transaction syncing</p>
                           <p>• Real-time balance updates</p>
                           <p>• Enhanced financial insights</p>
                        </div>
                     </div>
                  </div>
               )}
            </TabsContent>
         </Tabs>

         {showList && accounts.length > 0 && (
            <div>
               <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">
                  Added Accounts:
               </h4>
               <ul className="space-y-2">
                  {accounts.map((account) => (
                     <li
                        key={account._id}
                        className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded"
                     >
                        <div>
                           <span className="text-gray-900 dark:text-gray-100">
                              {account.name}
                           </span>
                           <span className="text-gray-500 dark:text-gray-400 ml-2">
                              ({account.accountType})
                           </span>
                           {account.bank && (
                              <span className="text-gray-500 dark:text-gray-400 ml-2">
                                 - {account.bank}
                              </span>
                           )}
                        </div>
                        <span className="text-gray-900 dark:text-gray-100">
                           ${parseFloat(account.balance).toFixed(2)}
                        </span>
                     </li>
                  ))}
               </ul>
            </div>
         )}

         <SyncDatePickerModal
            isOpen={showSyncDatePicker}
            onClose={() => setShowSyncDatePicker(false)}
            onConfirm={handleDatePickerConfirm}
            institutionName="your bank"
         />
      </div>
   );
}

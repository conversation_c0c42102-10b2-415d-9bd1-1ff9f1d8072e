"use client";

import { useState, useRef, useEffect } from "react";
import mongoose from "mongoose";
import { DAYS_OF_WEEK, PAY_PERIODS } from "@/app/lib/constants";

export function OnboardingIncomeForm({
   onSubmit,
   showList = true,
   existingIncomes = [],
   selectedIncome = null,
}) {
   const [incomes, setIncomes] = useState(existingIncomes);
   const [isEditing, setIsEditing] = useState(false);
   const [editingId, setEditingId] = useState(null);
   const descriptionInputRef = useRef(null);
   const [currentIncome, setCurrentIncome] = useState({
      description: "",
      payAmount: "",
      payPeriod: "monthly",
      payDay: "1",
      payDayOfWeek: "5", // Default to Friday
      payWeekDay: "1",
      enabled: true,
      isMainIncome: false,
   });

   useEffect(() => {
      if (selectedIncome) {
         setIsEditing(true);
         setEditingId(selectedIncome.index);
         setCurrentIncome({
            description: selectedIncome.description,
            payAmount: selectedIncome.payAmount,
            payPeriod: selectedIncome.payPeriod,
            payDay: selectedIncome.payDay || "1",
            payDayOfWeek: selectedIncome.payDayOfWeek || "5",
            payWeekDay: selectedIncome.payWeekDay || "1",
            enabled: selectedIncome.enabled,
            isMainIncome: selectedIncome.isMainIncome,
         });
         descriptionInputRef.current?.focus();
      }
   }, [selectedIncome]);

   useEffect(() => {
      setIncomes(existingIncomes);
   }, [existingIncomes]);

   const handleSubmit = (e) => {
      e.preventDefault();
      if (currentIncome.description && currentIncome.payAmount) {
         let updatedIncomes;

         if (isEditing && editingId !== null) {
            // Update existing income
            updatedIncomes = incomes.map((income, index) =>
               index === editingId
                  ? {
                       ...income,
                       description: currentIncome.description,
                       payAmount: parseFloat(currentIncome.payAmount).toFixed(
                          2
                       ),
                       payPeriod: currentIncome.payPeriod,
                       ...(currentIncome.payPeriod === "monthly"
                          ? { payDay: currentIncome.payDay }
                          : { payDayOfWeek: currentIncome.payDayOfWeek }),
                    }
                  : income
            );
         } else {
            // Add new income
            const newIncome = {
               description: currentIncome.description,
               payAmount: parseFloat(currentIncome.payAmount).toFixed(2),
               payPeriod: currentIncome.payPeriod,
               enabled: true,
               ...(currentIncome.payPeriod === "monthly"
                  ? { payDay: currentIncome.payDay }
                  : { payDayOfWeek: currentIncome.payDayOfWeek }),
               isMainIncome: incomes.length === 0,
            };
            updatedIncomes = [...incomes, newIncome];
         }

         setIncomes(updatedIncomes);
         onSubmit(updatedIncomes);

         // Reset form
         setCurrentIncome({
            description: "",
            payAmount: "",
            payPeriod: "monthly",
            payDay: "1",
            payDayOfWeek: "5",
            payWeekDay: "1",
            enabled: true,
            isMainIncome: false,
         });
         setIsEditing(false);
         setEditingId(null);
         descriptionInputRef.current?.focus();
      }
   };

   const handleChange = (e) => {
      const { name, value, type, checked } = e.target;
      setCurrentIncome((prev) => ({
         ...prev,
         [name]: type === "checkbox" ? checked : value,
         // Reset pay day fields when pay period changes
         ...(name === "payPeriod" && {
            payDay: value === "monthly" ? "1" : undefined,
            payDayOfWeek: value !== "monthly" ? "5" : undefined,
         }),
      }));
   };

   const handleSetMainIncome = (index) => {
      const updatedIncomes = incomes.map((income, i) => ({
         ...income,
         isMainIncome: i === index,
      }));
      setIncomes(updatedIncomes);
      onSubmit(updatedIncomes);
   };

   const startEditing = (income, index) => {
      setIsEditing(true);
      setEditingId(index);
      setCurrentIncome({
         description: income.description,
         payAmount: income.payAmount,
         payPeriod: income.payPeriod,
         payDay: income.payDay || "1",
         payDayOfWeek: income.payDayOfWeek || "5",
         payWeekDay: income.payWeekDay || "1",
         enabled: income.enabled,
         isMainIncome: income.isMainIncome,
      });
      descriptionInputRef.current?.focus();
   };

   // Update the submit button text based on editing state
   const submitButtonText = isEditing ? "Update Income" : "Add Income";

   return (
      <div className="space-y-6">
         <form onSubmit={handleSubmit} className="space-y-4">
            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Income Description
               </label>
               <input
                  ref={descriptionInputRef}
                  type="text"
                  name="description"
                  value={currentIncome.description}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
               />
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Pay Amount
               </label>
               <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                     <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                        $
                     </span>
                  </div>
                  <input
                     type="number"
                     name="payAmount"
                     value={currentIncome.payAmount}
                     onChange={handleChange}
                     className="pl-7 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                     required
                     min="0"
                     step="0.01"
                  />
               </div>
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Pay Period
               </label>
               <select
                  name="payPeriod"
                  value={currentIncome.payPeriod}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
               >
                  {PAY_PERIODS.map((period) => (
                     <option key={period.value} value={period.value}>
                        {period.label}
                     </option>
                  ))}
               </select>
            </div>

            {currentIncome.payPeriod === "monthly" ? (
               <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                     Pay Day of Month
                  </label>
                  <select
                     name="payDay"
                     value={currentIncome.payDay}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  >
                     {[...Array(31)].map((_, i) => (
                        <option key={i + 1} value={String(i + 1)}>
                           {i + 1}
                        </option>
                     ))}
                  </select>
               </div>
            ) : (
               <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                     Pay Day of Week
                  </label>
                  <select
                     name="payDayOfWeek"
                     value={currentIncome.payDayOfWeek}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  >
                     {DAYS_OF_WEEK.map((day) => (
                        <option key={day.value} value={day.value}>
                           {day.label}
                        </option>
                     ))}
                  </select>
               </div>
            )}

            <button
               type="submit"
               className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800"
            >
               {submitButtonText}
            </button>
            {isEditing && (
               <button
                  type="button"
                  onClick={() => {
                     setIsEditing(false);
                     setEditingId(null);
                     setCurrentIncome({
                        description: "",
                        payAmount: "",
                        payPeriod: "monthly",
                        payDay: "1",
                        payDayOfWeek: "5",
                        payWeekDay: "1",
                        enabled: true,
                        isMainIncome: false,
                     });
                  }}
                  className="mt-2 w-full px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900"
               >
                  Cancel Editing
               </button>
            )}
         </form>

         {showList && incomes.length > 0 && (
            <div>
               <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">
                  Added Incomes:
               </h4>
               <ul className="space-y-2">
                  {incomes.map((income, index) => (
                     <li
                        key={index}
                        className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded"
                     >
                        <div className="flex flex-col">
                           <span className="text-gray-900 dark:text-gray-100 font-medium">
                              {income.description}
                           </span>
                           <span className="text-sm text-gray-500 dark:text-gray-400">
                              {
                                 PAY_PERIODS.find(
                                    (p) => p.value === income.payPeriod
                                 )?.label
                              }{" "}
                              - ${parseFloat(income.payAmount).toFixed(2)}
                           </span>
                        </div>
                        <div className="flex items-center space-x-4">
                           <button
                              type="button"
                              onClick={() => handleSetMainIncome(index)}
                              className={`px-2 py-1 text-sm rounded ${
                                 income.isMainIncome
                                    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                                    : "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100"
                              }`}
                           >
                              {income.isMainIncome
                                 ? "Main Income"
                                 : "Set as Main"}
                           </button>
                           <button
                              type="button"
                              onClick={() => startEditing(income, index)}
                              className="px-2 py-1 text-sm rounded bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100"
                           >
                              Edit
                           </button>
                        </div>
                     </li>
                  ))}
               </ul>
            </div>
         )}
      </div>
   );
}

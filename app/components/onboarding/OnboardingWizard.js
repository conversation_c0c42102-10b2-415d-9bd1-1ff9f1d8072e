"use client";

import { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { OnboardingAccountForm } from "./OnboardingAccountForm";
import { OnboardingIncomeForm } from "./OnboardingIncomeForm";
import { OnboardingExpenseForm } from "./OnboardingExpenseForm";
import { OnboardingDebtForm } from "./OnboardingDebtForm";
import { CheckCircle, LogOut } from "lucide-react";
import { DAYS_OF_WEEK } from "@/app/lib/constants";
import { motion, AnimatePresence } from "framer-motion";

const STEPS = {
   WELCOME: "welcome",
   INCOME: "income",
   ACCOUNTS: "accounts",
   DEBTS: "debts",
   EXPENSES: "expenses",
   COMPLETE: "complete",
};

// Add these animation variants near the top of the file
const containerVariants = {
   hidden: { opacity: 0, y: 20 },
   visible: {
      opacity: 1,
      y: 0,
      transition: {
         duration: 0.4,
         ease: "easeOut",
      },
   },
   exit: {
      opacity: 0,
      y: -20,
      transition: {
         duration: 0.3,
      },
   },
};

const listItemVariants = {
   hidden: { opacity: 0, x: -20 },
   visible: {
      opacity: 1,
      x: 0,
      transition: {
         duration: 0.3,
      },
   },
   exit: {
      opacity: 0,
      x: 20,
      transition: {
         duration: 0.2,
      },
   },
};

export function OnboardingWizard({ userId }) {
   const router = useRouter();
   const { data: session, status, update } = useSession();
   const [currentStep, setCurrentStep] = useState(STEPS.WELCOME);
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState("");
   const [userData, setUserData] = useState({
      incomes: [],
      accounts: [],
      debts: [],
      expenses: [],
   });
   const [selectedIncome, setSelectedIncome] = useState(null);
   const [selectedAccount, setSelectedAccount] = useState(null);
   const [selectedDebt, setSelectedDebt] = useState(null);
   const [selectedExpense, setSelectedExpense] = useState(null);

   const handleComplete = async () => {
      try {
         setLoading(true);
         setError("");

         const response = await fetch(`/api/user/${userId}/onboarding`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(userData),
         });

         if (!response.ok) {
            throw new Error("Failed to complete onboarding");
         }

         // Update the NextAuth session to reflect onboarding completion
         await update({ onboardingComplete: true });

         // Redirect to budget page after successful onboarding completion
         router.push("/budget");
      } catch (err) {
         setError(err.message);
      } finally {
         setLoading(false);
      }
   };

   const handleIncomeClick = (income, index) => {
      setCurrentStep(STEPS.INCOME);
      setSelectedIncome({ ...income, index });
      const incomeFormElement = document.querySelector('[data-form="income"]');
      if (incomeFormElement) {
         incomeFormElement.scrollIntoView({ behavior: "smooth" });
      }
   };

   const handleAccountClick = (account, index) => {
      setCurrentStep(STEPS.ACCOUNTS);
      setSelectedAccount({ ...account, index });
      const accountFormElement = document.querySelector(
         '[data-form="account"]'
      );
      if (accountFormElement) {
         accountFormElement.scrollIntoView({ behavior: "smooth" });
      }
   };

   const handleExpenseClick = (expense, index) => {
      setCurrentStep(STEPS.EXPENSES);
      setSelectedExpense({ ...expense, index });
      const expenseFormElement = document.querySelector(
         '[data-form="expense"]'
      );
      if (expenseFormElement) {
         expenseFormElement.scrollIntoView({ behavior: "smooth" });
      }
   };

   const handleDebtClick = (debt, index) => {
      setCurrentStep(STEPS.DEBTS);
      setSelectedDebt({ ...debt, index });
      const debtFormElement = document.querySelector('[data-form="debt"]');
      if (debtFormElement) {
         debtFormElement.scrollIntoView({ behavior: "smooth" });
      }
   };

   const handleIncomeRemove = (index) => {
      const updatedIncomes = userData.incomes.filter((_, i) => i !== index);
      setUserData((prev) => ({
         ...prev,
         incomes: updatedIncomes,
      }));
   };

   const handleAccountRemove = (index) => {
      const updatedAccounts = userData.accounts.filter((_, i) => i !== index);
      setUserData((prev) => ({
         ...prev,
         accounts: updatedAccounts,
      }));
   };

   const handleExpenseRemove = (index) => {
      const updatedExpenses = userData.expenses.filter((_, i) => i !== index);
      setUserData((prev) => ({
         ...prev,
         expenses: updatedExpenses,
      }));
   };

   const handleDebtRemove = (index) => {
      const updatedDebts = userData.debts.filter((_, i) => i !== index);
      setUserData((prev) => ({
         ...prev,
         debts: updatedDebts,
      }));
   };

   const renderStepForm = () => {
      switch (currentStep) {
         case STEPS.WELCOME:
            return (
               <div className="text-center">
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     Welcome to Your Financial Journey!
                  </h2>
                  <p className="mb-6 text-gray-600 dark:text-gray-300">
                     Let's set up your account with some basic information.
                  </p>
                  <motion.button
                     whileHover={{ scale: 1.02 }}
                     whileTap={{ scale: 0.98 }}
                     onClick={() => setCurrentStep(STEPS.INCOME)}
                     className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors duration-200"
                  >
                     Get Started
                  </motion.button>
               </div>
            );

         case STEPS.INCOME:
            return (
               <div data-form="income">
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     {selectedIncome
                        ? "Edit Income Source"
                        : "Add Your Income Sources"}
                  </h2>
                  <p className="mb-6 text-gray-600 dark:text-gray-300">
                     {selectedIncome
                        ? "Update your income information below."
                        : "Add your regular sources of income."}
                  </p>
                  <OnboardingIncomeForm
                     existingIncomes={userData.incomes}
                     selectedIncome={selectedIncome}
                     onSubmit={(incomes) => {
                        setUserData((prev) => ({
                           ...prev,
                           incomes,
                        }));
                        setSelectedIncome(null);
                     }}
                     showList={false}
                  />
                  {userData.incomes.length > 0 && (
                     <button
                        onClick={() => setCurrentStep(STEPS.ACCOUNTS)}
                        className="mt-4 w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800"
                     >
                        Continue to Accounts
                     </button>
                  )}
               </div>
            );

         case STEPS.ACCOUNTS:
            return (
               <div data-form="account">
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     {selectedAccount ? "Edit Account" : "Add Your Accounts"}
                  </h2>
                  <p className="mb-6 text-gray-600 dark:text-gray-300">
                     {selectedAccount
                        ? "Update your account information below."
                        : "Add your bank accounts and credit cards."}
                  </p>
                  <OnboardingAccountForm
                     existingAccounts={userData.accounts}
                     selectedAccount={selectedAccount}
                     onSubmit={(accounts) => {
                        setUserData((prev) => ({
                           ...prev,
                           accounts,
                        }));
                        setSelectedAccount(null);
                     }}
                     showList={false}
                  />
                  {userData.accounts.length > 0 && (
                     <button
                        onClick={() => setCurrentStep(STEPS.DEBTS)}
                        className="mt-4 w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800"
                     >
                        Continue to Debts
                     </button>
                  )}
               </div>
            );

         case STEPS.DEBTS:
            return (
               <div data-form="debt">
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     {selectedDebt ? "Edit Debt" : "Add Your Debts"}
                  </h2>
                  <p className="mb-6 text-gray-600 dark:text-gray-300">
                     {selectedDebt
                        ? "Update your debt information below."
                        : "Add any loans, credit cards, or other debts you have."}
                  </p>
                  <OnboardingDebtForm
                     existingDebts={userData.debts}
                     selectedDebt={selectedDebt}
                     onSubmit={(debts) => {
                        setUserData((prev) => ({
                           ...prev,
                           debts,
                        }));
                        setSelectedDebt(null);
                     }}
                     showList={false}
                  />
                  <button
                     onClick={() => setCurrentStep(STEPS.EXPENSES)}
                     className="mt-4 w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800"
                  >
                     Continue to Expenses
                  </button>
               </div>
            );

         case STEPS.EXPENSES:
            return (
               <div data-form="expense">
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     {selectedExpense
                        ? "Edit Expense"
                        : "Add Your Regular Expenses"}
                  </h2>
                  <p className="mb-6 text-gray-600 dark:text-gray-300">
                     {selectedExpense
                        ? "Update your expense information below."
                        : "Add any recurring bills or expenses you have."}
                  </p>
                  <OnboardingExpenseForm
                     existingExpenses={userData.expenses}
                     selectedExpense={selectedExpense}
                     onSubmit={(expenses) => {
                        setUserData((prev) => ({
                           ...prev,
                           expenses,
                        }));
                        setSelectedExpense(null);
                     }}
                     showList={false}
                  />
                  {userData.expenses.length > 0 && (
                     <button
                        onClick={() => setCurrentStep(STEPS.COMPLETE)}
                        className="mt-4 w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800"
                     >
                        Continue to Complete
                     </button>
                  )}
               </div>
            );

         case STEPS.COMPLETE:
            return (
               <div className="text-center">
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                     You're All Set!
                  </h2>
                  <p className="mb-6 text-gray-600 dark:text-gray-300">
                     We've collected all the information needed to help you
                     manage your finances.
                  </p>
                  <button
                     onClick={handleComplete}
                     disabled={loading}
                     className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                  >
                     {loading
                        ? "Setting up your account..."
                        : "Go to Dashboard"}
                  </button>
               </div>
            );
      }
   };

   const renderSummaryPanel = () => {
      if (
         currentStep === STEPS.WELCOME ||
         currentStep === STEPS.COMPLETE ||
         (userData.incomes.length === 0 &&
            userData.accounts.length === 0 &&
            userData.debts.length === 0 &&
            userData.expenses.length === 0)
      ) {
         return null;
      }

      return (
         <>
            {userData.incomes.length > 0 && currentStep !== STEPS.COMPLETE && (
               <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium mb-3 text-gray-900 dark:text-gray-100">
                     Income Sources
                  </h4>
                  <ul className="space-y-3">
                     {userData.incomes.map((income, index) => (
                        <motion.li
                           key={index}
                           variants={listItemVariants}
                           initial="hidden"
                           animate="visible"
                           exit="exit"
                           layout
                           className="border border-gray-100 dark:border-gray-600 rounded-lg p-3 relative group"
                        >
                           <div className="flex justify-between items-start">
                              <div
                                 onClick={() =>
                                    handleIncomeClick(income, index)
                                 }
                                 className="flex-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors rounded p-1"
                              >
                                 <div className="flex justify-between items-start">
                                    <div className="space-y-1">
                                       <div className="font-medium text-gray-900 dark:text-gray-100">
                                          {income.description}
                                          {income.isMainIncome && (
                                             <span className="ml-2 text-xs bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100 px-2 py-0.5 rounded">
                                                Main Income
                                             </span>
                                          )}
                                       </div>
                                       <div className="text-sm text-gray-500 dark:text-gray-400 space-y-0.5">
                                          <div>
                                             Frequency: {income.payPeriod}
                                          </div>
                                          <div>
                                             Pay Day:{" "}
                                             {income.payPeriod === "monthly"
                                                ? `${income.payDay}th of month`
                                                : DAYS_OF_WEEK.find(
                                                     (day) =>
                                                        day.value ===
                                                        income.payDayOfWeek
                                                  )?.label}
                                          </div>
                                       </div>
                                    </div>
                                    <div className="text-right">
                                       <div className="font-semibold text-gray-900 dark:text-gray-100">
                                          $
                                          {parseFloat(income.payAmount).toFixed(
                                             2
                                          )}
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <motion.button
                                 whileHover={{ scale: 1.1 }}
                                 whileTap={{ scale: 0.9 }}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    handleIncomeRemove(index);
                                 }}
                                 className="ml-2 p-1.5 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 opacity-0 group-hover:opacity-100 transition-all flex-shrink-0"
                                 title="Remove Income"
                              >
                                 <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                 >
                                    <path
                                       fillRule="evenodd"
                                       d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                       clipRule="evenodd"
                                    />
                                 </svg>
                              </motion.button>
                           </div>
                        </motion.li>
                     ))}
                  </ul>
               </div>
            )}

            {userData.accounts.length > 0 && currentStep !== STEPS.COMPLETE && (
               <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium mb-3 text-gray-900 dark:text-gray-100">
                     Accounts
                  </h4>
                  <ul className="space-y-3">
                     {userData.accounts.map((account, index) => (
                        <motion.li
                           key={index}
                           variants={listItemVariants}
                           initial="hidden"
                           animate="visible"
                           exit="exit"
                           layout
                           className="border border-gray-100 dark:border-gray-600 rounded-lg p-3 relative group"
                        >
                           <div className="flex justify-between items-start">
                              <div
                                 onClick={() =>
                                    handleAccountClick(account, index)
                                 }
                                 className="flex-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors rounded p-1"
                              >
                                 <div className="flex justify-between items-start">
                                    <div className="space-y-1">
                                       <div className="font-medium text-gray-900 dark:text-gray-100">
                                          {account.name}
                                       </div>
                                       <div className="text-sm text-gray-500 dark:text-gray-400 space-y-0.5">
                                          <div>Type: {account.accountType}</div>
                                          {account.bank && (
                                             <div>Bank: {account.bank}</div>
                                          )}
                                       </div>
                                    </div>
                                    <div className="text-right">
                                       <div className="font-semibold text-gray-900 dark:text-gray-100">
                                          $
                                          {parseFloat(account.balance).toFixed(
                                             2
                                          )}
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <motion.button
                                 whileHover={{ scale: 1.1 }}
                                 whileTap={{ scale: 0.9 }}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    handleAccountRemove(index);
                                 }}
                                 className="ml-2 p-1.5 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 opacity-0 group-hover:opacity-100 transition-all flex-shrink-0"
                                 title="Remove Account"
                              >
                                 <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                 >
                                    <path
                                       fillRule="evenodd"
                                       d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                       clipRule="evenodd"
                                    />
                                 </svg>
                              </motion.button>
                           </div>
                        </motion.li>
                     ))}
                  </ul>
               </div>
            )}

            {userData.debts.length > 0 && currentStep !== STEPS.COMPLETE && (
               <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium mb-3 text-gray-900 dark:text-gray-100">
                     Debts
                  </h4>
                  <ul className="space-y-3">
                     {userData.debts.map((debt, index) => (
                        <motion.li
                           key={index}
                           variants={listItemVariants}
                           initial="hidden"
                           animate="visible"
                           exit="exit"
                           layout
                           className="border border-gray-100 dark:border-gray-600 rounded-lg p-3 relative group"
                        >
                           <div className="flex justify-between items-start">
                              <div
                                 onClick={() => handleDebtClick(debt, index)}
                                 className="flex-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors rounded p-1"
                              >
                                 <div className="flex justify-between items-start">
                                    <div className="space-y-1">
                                       <div className="font-medium text-gray-900 dark:text-gray-100">
                                          {debt.lender}
                                       </div>
                                       <div className="text-sm text-gray-500 dark:text-gray-400 space-y-0.5">
                                          <div>Type: {debt.debtType}</div>
                                          <div>APR: {debt.apr}%</div>
                                          <div>Due Date: {debt.dueDate}</div>
                                       </div>
                                    </div>
                                    <div className="text-right">
                                       <div className="font-semibold text-gray-900 dark:text-gray-100">
                                          ${parseFloat(debt.balance).toFixed(2)}
                                       </div>
                                       <div className="text-sm text-gray-500 dark:text-gray-400">
                                          Min: $
                                          {parseFloat(
                                             debt.minimumPayment
                                          ).toFixed(2)}
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <motion.button
                                 whileHover={{ scale: 1.1 }}
                                 whileTap={{ scale: 0.9 }}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    handleDebtRemove(index);
                                 }}
                                 className="ml-2 p-1.5 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 opacity-0 group-hover:opacity-100 transition-all flex-shrink-0"
                                 title="Remove Debt"
                              >
                                 <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                 >
                                    <path
                                       fillRule="evenodd"
                                       d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                       clipRule="evenodd"
                                    />
                                 </svg>
                              </motion.button>
                           </div>
                        </motion.li>
                     ))}
                  </ul>
               </div>
            )}

            {userData.expenses.length > 0 && currentStep !== STEPS.COMPLETE && (
               <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium mb-3 text-gray-900 dark:text-gray-100">
                     Regular Expenses
                  </h4>
                  <ul className="space-y-3">
                     {userData.expenses.map((expense, index) => (
                        <motion.li
                           key={index}
                           variants={listItemVariants}
                           initial="hidden"
                           animate="visible"
                           exit="exit"
                           layout
                           className="border border-gray-100 dark:border-gray-600 rounded-lg p-3 relative group"
                        >
                           <div className="flex justify-between items-start">
                              <div
                                 onClick={() =>
                                    handleExpenseClick(expense, index)
                                 }
                                 className="flex-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors rounded p-1"
                              >
                                 <div className="flex justify-between items-start">
                                    <div className="space-y-1">
                                       <div className="font-medium text-gray-900 dark:text-gray-100">
                                          {expense.name}
                                       </div>
                                       <div className="text-sm text-gray-500 dark:text-gray-400 space-y-0.5">
                                          <div>
                                             Frequency: {expense.frequency}
                                          </div>
                                          <div>
                                             Due:{" "}
                                             {expense.frequency === "weekly"
                                                ? DAYS_OF_WEEK.find(
                                                     (day) =>
                                                        day.value ===
                                                        expense.dueDay
                                                  )?.label
                                                : expense.frequency ===
                                                  "annually"
                                                ? (() => {
                                                     const monthNames = [
                                                        "Jan",
                                                        "Feb",
                                                        "Mar",
                                                        "Apr",
                                                        "May",
                                                        "Jun",
                                                        "Jul",
                                                        "Aug",
                                                        "Sep",
                                                        "Oct",
                                                        "Nov",
                                                        "Dec",
                                                     ];
                                                     const monthIndex =
                                                        parseInt(
                                                           expense.dueDay
                                                        ) - 1;
                                                     return `${monthNames[monthIndex]} ${expense.dueMonth}`;
                                                  })()
                                                : `${expense.dueDay}th of ${expense.frequency}`}
                                          </div>
                                          {expense.frequency === "weekly" &&
                                             expense.weeklyChargeType && (
                                                <div>
                                                   Type:{" "}
                                                   {expense.weeklyChargeType}
                                                </div>
                                             )}
                                       </div>
                                    </div>
                                    <div className="text-right">
                                       <div className="font-semibold text-gray-900 dark:text-gray-100">
                                          $
                                          {parseFloat(expense.amount).toFixed(
                                             2
                                          )}
                                       </div>
                                    </div>
                                 </div>
                              </div>
                              <motion.button
                                 whileHover={{ scale: 1.1 }}
                                 whileTap={{ scale: 0.9 }}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    handleExpenseRemove(index);
                                 }}
                                 className="ml-2 p-1.5 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 opacity-0 group-hover:opacity-100 transition-all flex-shrink-0"
                                 title="Remove Expense"
                              >
                                 <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                 >
                                    <path
                                       fillRule="evenodd"
                                       d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                       clipRule="evenodd"
                                    />
                                 </svg>
                              </motion.button>
                           </div>
                        </motion.li>
                     ))}
                  </ul>
               </div>
            )}
         </>
      );
   };

   // Add a progress indicator at the top
   const progressSteps = [
      { step: STEPS.WELCOME, label: "Welcome" },
      { step: STEPS.INCOME, label: "Income" },
      { step: STEPS.ACCOUNTS, label: "Accounts" },
      { step: STEPS.DEBTS, label: "Debts" },
      { step: STEPS.EXPENSES, label: "Expenses" },
      { step: STEPS.COMPLETE, label: "Complete" },
   ];

   const Progress = () => (
      <div className="mb-12 max-w-3xl mx-auto">
         <div className="flex justify-between items-center">
            {progressSteps.map((step, index) => (
               <div
                  key={step.step}
                  className="relative flex flex-col items-center"
               >
                  {/* Step circle */}
                  <motion.div
                     initial={false}
                     animate={{
                        scale: currentStep === step.step ? 1.1 : 1,
                        backgroundColor:
                           progressSteps.findIndex(
                              (s) => s.step === currentStep
                           ) >= index
                              ? "rgb(59, 130, 246)"
                              : "transparent",
                        borderColor:
                           progressSteps.findIndex(
                              (s) => s.step === currentStep
                           ) >= index
                              ? "rgb(59, 130, 246)"
                              : "rgb(209, 213, 219)",
                     }}
                     className="relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 bg-white dark:bg-gray-800 transition-all duration-300 shadow-sm"
                  >
                     {progressSteps.findIndex((s) => s.step === currentStep) >
                     index ? (
                        <motion.svg
                           initial={{ opacity: 0, scale: 0.5 }}
                           animate={{ opacity: 1, scale: 1 }}
                           className="h-4 w-4 text-white"
                           fill="none"
                           viewBox="0 0 24 24"
                           stroke="currentColor"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2.5}
                              d="M5 13l4 4L19 7"
                           />
                        </motion.svg>
                     ) : (
                        <motion.span
                           animate={{
                              color:
                                 progressSteps.findIndex(
                                    (s) => s.step === currentStep
                                 ) >= index
                                    ? "rgb(255, 255, 255)"
                                    : "rgb(107, 114, 128)",
                           }}
                           className="text-xs font-semibold"
                        >
                           {index + 1}
                        </motion.span>
                     )}
                  </motion.div>

                  {/* Step label */}
                  <motion.span
                     initial={false}
                     animate={{
                        color:
                           currentStep === step.step
                              ? "rgb(59, 130, 246)"
                              : "rgb(107, 114, 128)",
                     }}
                     className="absolute -bottom-6 text-sm font-medium transition-colors duration-300 whitespace-nowrap"
                  >
                     {step.label}
                  </motion.span>
               </div>
            ))}
         </div>
      </div>
   );

   return (
      <div className="bg-gray-50 dark:bg-gray-900">
         {/* Logout button */}
         <div className="absolute top-4 right-4 z-50">
            <button
               onClick={() => signOut({ callbackUrl: "/auth/login" })}
               className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm transition-colors"
            >
               <LogOut className="w-4 h-4" />
               <span>Sign Out</span>
            </button>
         </div>

         {error && (
            <motion.div
               initial={{ opacity: 0, y: -20 }}
               animate={{ opacity: 1, y: 0 }}
               className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 p-4 bg-red-100 text-red-700 rounded shadow-lg"
            >
               {error}
            </motion.div>
         )}

         {/* Main Layout Container - Two Columns */}
         <div className="flex">
            {/* Left Column - Steps and Forms */}
            <div className="w-[50vw] h-[100vh] flex flex-col p-8">
               {/* Progress Steps */}
               <div className="mb-8">
                  <Progress />
               </div>

               {/* Form Content */}
               <div className="flex-1 flex items-center justify-center">
                  <div className="w-full max-w-xl">
                     <AnimatePresence mode="wait">
                        <motion.div
                           key={currentStep}
                           variants={containerVariants}
                           initial="hidden"
                           animate="visible"
                           exit="exit"
                           className="w-full bg-white dark:bg-gray-800 rounded-lg shadow p-6"
                        >
                           {renderStepForm()}
                        </motion.div>
                     </AnimatePresence>
                  </div>
               </div>
            </div>

            {/* Right Column - Financial Summary */}
            {(userData.incomes.length > 0 ||
               userData.accounts.length > 0 ||
               userData.debts.length > 0 ||
               userData.expenses.length > 0) &&
            currentStep !== STEPS.COMPLETE &&
            currentStep !== STEPS.WELCOME ? (
               <div className="w-[50vw] h-[100vh] p-8">
                  <div className="h-full bg-gray-50 dark:bg-gray-800 rounded-lg shadow">
                     <div className="p-6 h-full flex flex-col">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                           Your Financial Summary
                        </h3>
                        <div className="overflow-y-auto flex-1 space-y-6 pr-2">
                           {renderSummaryPanel()}
                        </div>
                     </div>
                  </div>
               </div>
            ) : (
               // Empty right column when no summary to show
               <div className="w-[50vw] h-[100vh] bg-gray-100 dark:bg-gray-800"></div>
            )}
         </div>
      </div>
   );
}

"use client";

import { useState, useRef, useEffect } from "react";
import mongoose from "mongoose";

export function OnboardingDebtForm({
   onSubmit,
   showList = true,
   existingDebts = [],
   selectedDebt = null,
}) {
   const [debts, setDebts] = useState(existingDebts);
   const [isEditing, setIsEditing] = useState(false);
   const [editingId, setEditingId] = useState(null);
   const [currentDebt, setCurrentDebt] = useState({
      debtType: "credit card",
      lender: "",
      balance: "",
      apr: "",
      minimumPayment: "",
      dueDate: "1",
      active: true,
   });
   const lenderInputRef = useRef(null);

   useEffect(() => {
      if (selectedDebt) {
         setIsEditing(true);
         setEditingId(selectedDebt.index);
         setCurrentDebt({
            debtType: selectedDebt.debtType,
            lender: selectedDebt.lender,
            balance: selectedDebt.balance,
            apr: selectedDebt.apr,
            minimumPayment: selectedDebt.minimumPayment,
            dueDate: selectedDebt.dueDate || "1",
            active: selectedDebt.active,
         });
         lenderInputRef.current?.focus();
      }
   }, [selectedDebt]);

   useEffect(() => {
      setDebts(existingDebts);
   }, [existingDebts]);

   const handleSubmit = (e) => {
      e.preventDefault();
      if (
         currentDebt.lender &&
         currentDebt.balance &&
         currentDebt.apr &&
         currentDebt.minimumPayment
      ) {
         let updatedDebts;

         if (isEditing && editingId !== null) {
            // Update existing debt
            updatedDebts = debts.map((debt, index) =>
               index === editingId
                  ? {
                       ...debt,
                       ...currentDebt,
                       _id: debt._id,
                    }
                  : debt
            );
         } else {
            // Add new debt
            const newDebt = {
               ...currentDebt,
               _id: new mongoose.Types.ObjectId().toString(),
            };
            updatedDebts = [...debts, newDebt];
         }

         setDebts(updatedDebts);
         onSubmit(updatedDebts);

         // Reset form
         setCurrentDebt({
            debtType: "credit card",
            lender: "",
            balance: "",
            apr: "",
            minimumPayment: "",
            dueDate: "1",
            active: true,
         });
         setIsEditing(false);
         setEditingId(null);
         lenderInputRef.current?.focus();
      }
   };

   const handleChange = (e) => {
      const { name, value, type, checked } = e.target;
      setCurrentDebt((prev) => ({
         ...prev,
         [name]: type === "checkbox" ? checked : value,
      }));
   };

   const handleNumberChange = (e) => {
      const { name, value } = e.target;
      // Allow only numbers and decimal point
      if (value === "" || /^\d*\.?\d*$/.test(value)) {
         setCurrentDebt((prev) => ({ ...prev, [name]: value }));
      }
   };

   // Update the submit button text based on editing state
   const submitButtonText = isEditing ? "Update Debt" : "Add Debt";

   return (
      <div className="space-y-6">
         <form onSubmit={handleSubmit} className="space-y-4">
            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Debt Type
               </label>
               <select
                  name="debtType"
                  value={currentDebt.debtType}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
               >
                  <option value="credit card">Credit Card</option>
                  <option value="personal loan">Personal Loan</option>
                  <option value="student loan">Student Loan</option>
                  <option value="mortgage">Mortgage</option>
                  <option value="auto loan">Auto Loan</option>
                  <option value="medical debt">Medical Debt</option>
                  <option value="other">Other</option>
               </select>
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Lender Name
               </label>
               <input
                  ref={lenderInputRef}
                  type="text"
                  name="lender"
                  value={currentDebt.lender}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  placeholder="Chase, Bank of America, etc."
                  required
               />
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Current Balance
               </label>
               <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                     <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                        $
                     </span>
                  </div>
                  <input
                     type="text"
                     name="balance"
                     value={currentDebt.balance}
                     onChange={handleNumberChange}
                     className="mt-1 block w-full pl-7 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                     placeholder="0.00"
                     required
                  />
               </div>
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  APR (%)
               </label>
               <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                     type="text"
                     name="apr"
                     value={currentDebt.apr}
                     onChange={handleNumberChange}
                     className="mt-1 block w-full pr-8 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                     placeholder="0.00"
                     required
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                     <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                        %
                     </span>
                  </div>
               </div>
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Minimum Payment
               </label>
               <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                     <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                        $
                     </span>
                  </div>
                  <input
                     type="text"
                     name="minimumPayment"
                     value={currentDebt.minimumPayment}
                     onChange={handleNumberChange}
                     className="mt-1 block w-full pl-7 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                     placeholder="0.00"
                     required
                  />
               </div>
            </div>

            <div>
               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Due Date
               </label>
               <select
                  name="dueDate"
                  value={currentDebt.dueDate}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                  required
               >
                  {[...Array(31)].map((_, i) => (
                     <option key={i + 1} value={i + 1}>
                        {i + 1}
                     </option>
                  ))}
               </select>
            </div>

            <button
               type="submit"
               className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800"
            >
               {submitButtonText}
            </button>
            {isEditing && (
               <button
                  type="button"
                  onClick={() => {
                     setIsEditing(false);
                     setEditingId(null);
                     setCurrentDebt({
                        debtType: "credit card",
                        lender: "",
                        balance: "",
                        apr: "",
                        minimumPayment: "",
                        dueDate: "1",
                        active: true,
                     });
                  }}
                  className="mt-2 w-full px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-900"
               >
                  Cancel Editing
               </button>
            )}
         </form>

         {showList && debts.length > 0 && (
            <div>
               <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">
                  Added Debts:
               </h4>
               <ul className="space-y-2">
                  {debts.map((debt) => (
                     <li
                        key={debt._id}
                        className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded"
                     >
                        <div>
                           <span className="text-gray-900 dark:text-gray-100">
                              {debt.lender}
                           </span>
                           <span className="text-gray-500 dark:text-gray-400 ml-2">
                              ({debt.debtType})
                           </span>
                        </div>
                        <div className="text-right">
                           <div className="text-gray-900 dark:text-gray-100">
                              ${parseFloat(debt.balance).toFixed(2)}
                           </div>
                           <div className="text-sm text-gray-500 dark:text-gray-400">
                              {debt.apr}% APR
                           </div>
                        </div>
                     </li>
                  ))}
               </ul>
            </div>
         )}
      </div>
   );
}

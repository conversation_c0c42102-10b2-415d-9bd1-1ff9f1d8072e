"use client";

import { useState, useCallback } from "react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import DebtList from "../debts/DebtList";

export default function BudgetDebtSection({
   debts,
   setDebts,
   loading,
   viewType,
   dateRange,
   isMobileView,
}) {
   const [debtError, setDebtError] = useState("");
   const [debtsExpanded, setDebtsExpanded] = useState(true);

   // Define fetchDebts function
   const fetchDebts = useCallback(async () => {
      try {
         const response = await fetch("/api/user/debts");
         if (response.ok) {
            const data = await response.json();
            setDebts(data.debts || []);
            setDebtError("");
         }
      } catch (error) {
         console.error("Error fetching debts:", error);
         setDebtError("Failed to load debts");
      }
   }, [setDebts]);

   const handleDebtDelete = async (id) => {
      try {
         const response = await fetch(`/api/user/debts/${id}`, {
            method: "DELETE",
         });

         const result = await response.json();

         if (!response.ok) {
            if (result.hasAssociatedExpenses) {
               setDebtError(
                  result.message ||
                     "Cannot delete debt with associated expenses"
               );
            } else {
               throw new Error(result.message || "Failed to delete debt");
            }
            return;
         }

         // Clear any previous debt errors
         setDebtError("");

         // Handle different deletion actions
         if (result.action === "deactivated") {
            // Debt was marked as inactive
            setDebts(
               debts.map((debt) =>
                  debt._id === id ? { ...debt, active: false } : debt
               )
            );
         } else if (result.action === "deleted") {
            // Debt was permanently deleted - remove from list
            setDebts(debts.filter((debt) => debt._id !== id));
         }
      } catch (error) {
         console.error("Error deleting debt:", error);
      }
   };

   const handleDebtEdit = async (updatedDebt) => {
      try {
         // Ensure we're not losing the active status in the update
         const originalDebt = debts.find((d) => d._id === updatedDebt._id);
         const debtToUpdate = {
            ...updatedDebt,
            // If active status is undefined, maintain the original value
            active:
               typeof updatedDebt.active !== "undefined"
                  ? updatedDebt.active
                  : originalDebt?.active,
         };

         // Send API request to update the debt
         const response = await fetch("/api/user/debts", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ debt: debtToUpdate }),
         });

         if (!response.ok) {
            throw new Error("Failed to update debt");
         }

         const data = await response.json();
         console.log("Debt updated:", data.debt);

         // Update local state with the returned debt
         setDebts(
            debts.map((debt) => (debt._id === data.debt._id ? data.debt : debt))
         );

         // If this was an active status change, log it
         if (originalDebt && originalDebt.active !== data.debt.active) {
            console.log(
               `Debt ${data.debt.lender} active status changed from ${originalDebt.active} to ${data.debt.active}`
            );
         }
      } catch (error) {
         console.error("Error updating debt:", error);
      }
   };

   const handleDebtAdd = async (newDebt) => {
      try {
         // The debt is already created by the AddDebtModal, just update local state
         if (newDebt && newDebt._id) {
            setDebts([...debts, newDebt]);
         } else {
            console.error("Received invalid debt data:", newDebt);
         }
      } catch (error) {
         console.error("Error adding debt to local state:", error);
      }
   };

   return (
      <div
         className={`overflow-auto flex-1 ${
            isMobileView ? "border-t border-b" : "border-t"
         } border-gray-200 dark:border-gray-700`}
      >
         {isMobileView && (
            <h2
               className="text-lg font-semibold px-4 py-2 bg-gray-100 dark:bg-gray-800 sticky top-0 z-20 flex border-b border-gray-200 dark:border-gray-700 justify-between items-center cursor-pointer"
               onClick={() => setDebtsExpanded(!debtsExpanded)}
            >
               <span>Debts</span>
               <ChevronDownIcon
                  className={`h-5 w-5 transition-transform ${
                     !debtsExpanded ? "transform rotate-180" : ""
                  }`}
               />
            </h2>
         )}
         <DebtList
            debts={debts}
            onDebtDelete={handleDebtDelete}
            onDebtEdit={handleDebtEdit}
            onDebtAdd={handleDebtAdd}
            loading={loading}
            viewType={viewType}
            dateRange={dateRange}
            isMobileView={isMobileView}
            isCollapsed={isMobileView && !debtsExpanded}
         />
      </div>
   );
}

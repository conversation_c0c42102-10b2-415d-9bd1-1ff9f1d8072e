"use client";

import { useState, useCallback } from "react";
import { format } from "date-fns";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import IncomeList from "../incomes/IncomeList";
import AddIncomeModal from "../incomes/AddIncomeModal";

export default function BudgetIncomeSection({
   dateRange,
   incomes,
   setIncomes,
   userPaySettings,
   loading,
   isMobileView,
   onRefreshBalances,
   onFetchPeriodData,
   onFetchInitialData,
}) {
   const [showIncomeModal, setShowIncomeModal] = useState(false);
   const [incomeExpanded, setIncomeExpanded] = useState(true);

   const handleIncomeDelete = async (id) => {
      try {
         // First delete the income
         const response = await fetch(`/api/incomes/${id}`, {
            method: "DELETE",
         });

         if (!response.ok) throw new Error("Failed to delete income");

         // Then update transactions for the deleted income
         const transactionResponse = await fetch(`/api/transactions`, {
            method: "PATCH",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               assignedToId: id,
               type: "Income",
            }),
         });

         if (!transactionResponse.ok) {
            throw new Error("Failed to update transactions");
         }

         // After updating transactions, fetch the updated income list and refresh balances
         await Promise.all([onFetchPeriodData(), onRefreshBalances()]);
      } catch (error) {
         console.error("Error deleting income:", error);
         // Ensure UI is consistent even after error
         await onFetchPeriodData();
      }
   };

   const handleIncomeSchedule = async (projectedIncome) => {
      try {
         // Handle the case where we're passing a refresh flag instead of an income
         if (projectedIncome && projectedIncome.refreshAll === true) {
            // Just refresh all data without creating a new income
            await onFetchPeriodData();
            await onRefreshBalances();
            return;
         }

         // Original logic for scheduling a single income
         // Optimistically update the UI
         const newIncome = {
            ...projectedIncome,
            status: "scheduled",
            _id: `temp-${Date.now()}`, // Temporary ID for optimistic update
         };

         setIncomes((prevIncomes) => [...prevIncomes, newIncome]);

         const response = await fetch("/api/incomes", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               date:
                  projectedIncome.date instanceof Date
                     ? format(projectedIncome.date, "yyyy-MM-dd")
                     : projectedIncome.date,
               expectedAmount: parseFloat(projectedIncome.expectedAmount),
               description: projectedIncome.description,
               category: "Salary",
               status: "scheduled",
            }),
         });

         if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
               errorData.details ||
                  errorData.error ||
                  "Failed to schedule income"
            );
         }

         // Refresh both the data and balances
         await Promise.all([onFetchPeriodData(), onRefreshBalances()]);
      } catch (error) {
         console.error("Error scheduling income:", error);
         // Revert optimistic update on error
         await onFetchPeriodData();
      }
   };

   const handleIncomeEdit = async (updatedIncome) => {
      try {
         // Optimistically update the UI
         setIncomes((prevIncomes) =>
            prevIncomes.map((income) =>
               income._id === updatedIncome._id ? updatedIncome : income
            )
         );

         const response = await fetch(`/api/incomes/${updatedIncome._id}`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(updatedIncome),
         });

         if (!response.ok) throw new Error("Failed to update income");

         // Refresh both the data and balances
         await Promise.all([onFetchPeriodData(), onRefreshBalances()]);
      } catch (error) {
         console.error("Error updating income:", error);
         // Revert optimistic update on error
         await onFetchPeriodData();
      }
   };

   const handleIncomeAdd = async (newIncome) => {
      try {
         // Optimistically update the UI
         const tempIncome = {
            ...newIncome,
            _id: `temp-${Date.now()}`, // Temporary ID for optimistic update
         };
         setIncomes((prevIncomes) => [...prevIncomes, tempIncome]);

         const response = await fetch("/api/incomes", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(newIncome),
         });

         if (!response.ok) throw new Error("Failed to add income");

         // Refresh both the data and balances
         await Promise.all([onFetchPeriodData(), onRefreshBalances()]);
      } catch (error) {
         console.error("Error adding income:", error);
         // Revert optimistic update on error
         await onFetchPeriodData();
      }
   };

   const handleIncomeModalSave = async (incomeData, isRecurring) => {
      try {
         if (isRecurring) {
            // Add recurring income to user settings
            const response = await fetch("/api/user/recurring-incomes", {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify(incomeData),
            });

            if (!response.ok) {
               throw new Error("Failed to add recurring income");
            }

            // Refresh user data to get updated recurring incomes
            await onFetchInitialData();
         } else {
            // Add one-time income
            await handleIncomeAdd(incomeData);
         }
         setShowIncomeModal(false);
      } catch (error) {
         console.error("Error adding income:", error);
      }
   };

   return (
      <>
         {/* Income Modal */}
         {showIncomeModal && (
            <AddIncomeModal
               onClose={() => setShowIncomeModal(false)}
               onSave={handleIncomeModalSave}
            />
         )}

         {/* Income Section */}
         <div className="overflow-auto">
            {isMobileView && (
               <h2
                  className="text-lg font-semibold px-4 py-2 bg-gray-100 dark:bg-gray-800 sticky top-0 z-10 flex justify-between items-center cursor-pointer border-b border-gray-200 dark:border-gray-700"
                  onClick={() => setIncomeExpanded(!incomeExpanded)}
               >
                  <span>Income</span>
                  <ChevronDownIcon
                     className={`h-5 w-5 transition-transform ${
                        !incomeExpanded ? "transform rotate-180" : ""
                     }`}
                  />
               </h2>
            )}
            <IncomeList
               dateRange={dateRange}
               incomes={incomes}
               userPaySettings={userPaySettings}
               onIncomeDelete={handleIncomeDelete}
               onIncomeSchedule={handleIncomeSchedule}
               onIncomeEdit={handleIncomeEdit}
               onIncomeAdd={handleIncomeAdd}
               loading={loading}
               isMobileView={isMobileView}
               isCollapsed={isMobileView && !incomeExpanded}
            />
         </div>
      </>
   );
}

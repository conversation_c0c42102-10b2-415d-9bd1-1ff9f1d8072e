"use client";

import { useState, useCallback } from "react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import ExpenseList from "../expenses/ExpenseList";
import ExpenseForm from "../expenses/ExpenseForm";
import useBalanceStore from "../../lib/stores/balanceStore";

export default function BudgetExpenseSection({
   dateRange,
   expenses,
   setExpenses,
   userPaySettings,
   setUserPaySettings,
   debts,
   loading,
   error,
   isMobileView,
   refreshKey,
   setRefreshKey,
   onDateRangeChange,
   onRefreshBalances,
   onFetchPeriodData,
}) {
   // Get balances from Zustand store
   const { balances, updateAssignedAmount } = useBalanceStore();

   const [showForm, setShowForm] = useState(false);
   const [hidePaidExpenses, setHidePaidExpenses] = useState(false);
   const [expensesExpanded, setExpensesExpanded] = useState(true);

   // Legacy handleAssignedChange function removed - all balance updates now handled directly by Zustand store in individual components

   const handleExpenseSuccess = async (newExpense) => {
      try {
         // Close the form immediately for better UX
         setShowForm(false);

         // Increment refresh key to trigger a re-render
         setRefreshKey((prev) => prev + 1);

         // Refresh expense data and balances
         await Promise.all([onFetchPeriodData(), onRefreshBalances()]);
      } catch (error) {
         console.error("Error handling expense success:", error);
      }
   };

   const handleExpensesChange = useCallback(
      (updatedExpenses, updatedSettings = null) => {
         if (updatedExpenses) {
            setExpenses(updatedExpenses);
         }

         // If updatedSettings is provided, update the userPaySettings
         if (updatedSettings) {
            setUserPaySettings(updatedSettings);
         }
      },
      [setExpenses, setUserPaySettings]
   );

   const handleAddExpense = () => {
      setShowForm(true);
   };

   return (
      <>
         {/* Expense Modal */}
         {showForm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
               <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-2xl mx-4">
                  <div className="flex justify-between items-center mb-4">
                     <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                        Add Expense
                     </h2>
                     <button
                        onClick={() => setShowForm(false)}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                     >
                        <svg
                           className="w-6 h-6"
                           fill="none"
                           stroke="currentColor"
                           viewBox="0 0 24 24"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                           />
                        </svg>
                     </button>
                  </div>
                  <ExpenseForm
                     onSuccess={handleExpenseSuccess}
                     onCancel={() => setShowForm(false)}
                  />
               </div>
            </div>
         )}

         {/* Expense Section */}
         {isMobileView && (
            <h2
               className="text-lg font-semibold px-4 py-2 bg-gray-100 dark:bg-gray-800 sticky top-0 z-20 flex border-b border-gray-200 dark:border-gray-700 justify-between items-center cursor-pointer"
               onClick={() => setExpensesExpanded(!expensesExpanded)}
            >
               <span>Expenses</span>
               <ChevronDownIcon
                  className={`h-5 w-5 transition-transform ${
                     !expensesExpanded ? "transform rotate-180" : ""
                  }`}
               />
            </h2>
         )}
         <div className={`overflow-auto ${!isMobileView ? "h-full" : ""}`}>
            <ExpenseList
               key={refreshKey}
               dateRange={dateRange}
               onDateRangeChange={onDateRangeChange}
               showForm={showForm}
               onShowFormChange={setShowForm}
               expenses={expenses}
               userExpenseSettings={{
                  ...userPaySettings,
                  debts: debts,
               }}
               loading={loading}
               error={error}
               onExpensesChange={handleExpensesChange}
               onRefreshBalances={onRefreshBalances}
               onFetchPeriodData={onFetchPeriodData}
               hidePaidExpenses={hidePaidExpenses}
               onHidePaidExpensesChange={setHidePaidExpenses}
               isMobileView={isMobileView}
               isCollapsed={isMobileView && !expensesExpanded}
            />
         </div>
      </>
   );
}

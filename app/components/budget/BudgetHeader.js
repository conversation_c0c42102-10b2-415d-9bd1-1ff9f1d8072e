"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import DateRangeSelector from "../DateRangeSelector";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { useSession } from "next-auth/react";

export default function BudgetHeader({
   onDateRangeChange,
   userPaySettings,
   userCreatedAt,
   viewType,
   currentDate,
   accounts,
   payPeriods,
   isInitialLoad,
}) {
   const { data: session } = useSession();
   const router = useRouter();
   const [unassignedCount, setUnassignedCount] = useState(0);

   // Function to fetch the actual unassigned count from the API
   const fetchUnassignedCount = useCallback(async () => {
      try {
         const response = await fetch("/api/transactions/unassigned/count");
         if (response.ok) {
            const data = await response.json();
            setUnassignedCount(data.count);
            return data.count;
         }
      } catch (error) {
         console.error("Error fetching unassigned count:", error);
         return 0;
      }
   }, []);

   // Function to handle navigation to transactions page
   const handleViewUncategorizedTransactions = useCallback(() => {
      router.push("/transactions");
   }, [router]);

   // Fetch unassigned transactions count after initial data is loaded
   useEffect(() => {
      if (session?.user?.id && !isInitialLoad) {
         fetchUnassignedCount();
      }
   }, [session?.user?.id, isInitialLoad, fetchUnassignedCount]);

   return (
      <>
         {/* Date Selector */}
         <div className="bg-gray-50 dark:bg-gray-900 px-0 pl-4 pr-4 py-0 pt-4 pb-4 sm:py-0 sm:pt-0 sm:pb-0 border-b border-gray-200 dark:border-gray-700">
            <DateRangeSelector
               onRangeChange={onDateRangeChange}
               userPaySettings={userPaySettings}
               userCreatedAt={userCreatedAt}
               externalViewType={viewType}
               externalCurrentDate={currentDate}
               payPeriods={payPeriods}
            />
         </div>

         {/* Uncategorized Transactions Notification */}
         {unassignedCount > 0 && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-700">
               <div className="px-4 py-3">
                  <button
                     onClick={handleViewUncategorizedTransactions}
                     className="w-full flex items-center justify-between text-left hover:bg-yellow-100 dark:hover:bg-yellow-900/40 rounded-lg p-2 transition-colors group"
                  >
                     <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                           <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                        </div>
                        <div>
                           <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                              {unassignedCount} Uncategorized Transaction
                              {unassignedCount === 1 ? "" : "s"}
                           </p>
                           <p className="text-xs text-yellow-600 dark:text-yellow-400">
                              Click to review and categorize
                           </p>
                        </div>
                     </div>
                     <div className="flex-shrink-0">
                        <svg
                           className="h-4 w-4 text-yellow-600 dark:text-yellow-400 group-hover:translate-x-1 transition-transform"
                           fill="none"
                           viewBox="0 0 24 24"
                           stroke="currentColor"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                           />
                        </svg>
                     </div>
                  </button>
               </div>
            </div>
         )}
      </>
   );
}

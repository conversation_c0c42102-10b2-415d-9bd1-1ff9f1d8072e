"use client";

import { useState, useEffect, useRef } from "react";
import { Combobox } from "../../components/ui/combobox";
import { Save } from "lucide-react";
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
} from "../../../components/ui/dialog";
import { But<PERSON> } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Label } from "../../../components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "../../../components/ui/select";
import { Alert, AlertDescription } from "../../../components/ui/alert";
import { Card, CardContent } from "../../../components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "../../../components/ui/tabs";
import { Calendar } from "../../../components/ui/calendar";
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from "../../../components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { parseTimezoneDate } from "../../lib/utils/transactionsUtils";

// Define the base transaction types
const BASE_TRANSACTION_TYPES = ["Expense", "Income", "Adjustment"];
const TRANSACTION_STATUS = ["pending", "cleared"];

export default function TransactionForm({
   onSuccess,
   onBalanceUpdate,
   onClose,
   isOpen,
}) {
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState("");
   const [expenses, setExpenses] = useState([]);
   const [incomes, setIncomes] = useState([]);
   const [accounts, setAccounts] = useState([]);
   const [availableAssignedTo, setAvailableAssignedTo] = useState([]);
   const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
   const [transactionTypes, setTransactionTypes] = useState(
      BASE_TRANSACTION_TYPES
   );
   const amountInputRef = useRef(null);
   const statusButtonRef = useRef(null);
   const [formData, setFormData] = useState({
      type: BASE_TRANSACTION_TYPES[0],
      amount: "",
      payee: "",
      assignedTo: "",
      accountId:
         localStorage.getItem("selectedAccountFilter") === "all"
            ? ""
            : localStorage.getItem("selectedAccountFilter"),
      status: "pending",
      date: (() => {
         const today = new Date();
         return new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate(),
            12,
            0,
            0
         )
            .toISOString()
            .split("T")[0];
      })(),
      transferDetails: {
         fromAccountId: "",
         toAccountId: "",
      },
      newBalance: "", // Add new field for adjustment
   });

   // Update available categories when expenses, incomes, or transaction type changes
   useEffect(() => {
      setAvailableAssignedTo(getAvailableAssignedTo());
   }, [expenses, incomes, formData.type, formData.date]);

   // Fetch expenses and incomes
   useEffect(() => {
      fetchExpensesAndIncomes();
      fetchAccounts();
   }, []);

   const fetchExpensesAndIncomes = async () => {
      try {
         // Fetch expenses
         const expensesResponse = await fetch("/api/expenses");
         if (!expensesResponse.ok) throw new Error("Failed to fetch expenses");
         const expensesData = await expensesResponse.json();
         setExpenses(expensesData);

         // Fetch incomes with scheduled status
         const incomesResponse = await fetch("/api/incomes?status=scheduled");
         if (!incomesResponse.ok) throw new Error("Failed to fetch incomes");
         const incomesData = await incomesResponse.json();
         setIncomes(incomesData);
      } catch (err) {
         setError("Failed to fetch categories");
      }
   };

   const fetchAccounts = async () => {
      try {
         const response = await fetch("/api/user/accounts");
         if (!response.ok) throw new Error("Failed to fetch accounts");
         const data = await response.json();
         const fetchedAccounts = data.accounts || [];
         setAccounts(fetchedAccounts);

         // If there's only one account, automatically set it in the form
         if (fetchedAccounts.length === 1) {
            setFormData((prev) => ({
               ...prev,
               accountId: fetchedAccounts[0]._id,
            }));
         }

         // Update transaction types based on number of accounts
         if (fetchedAccounts.length > 1) {
            setTransactionTypes([...BASE_TRANSACTION_TYPES, "Transfer"]);
         } else {
            setTransactionTypes(BASE_TRANSACTION_TYPES);
            // If current type is Transfer but we no longer have multiple accounts, reset to Expense
            if (formData.type === "Transfer") {
               setFormData((prev) => ({ ...prev, type: "Expense" }));
            }
         }
      } catch (err) {
         setError("Failed to fetch accounts");
      }
   };

   // Filter expenses based on selected date
   const getFilteredExpenses = () => {
      // Parse the date string into components
      const [year, month, day] = formData.date.split("-").map(Number);
      const selectedDate = new Date(year, month - 1, day, 12, 0, 0);
      const selectedMonth = selectedDate.getMonth();
      const selectedYear = selectedDate.getFullYear();

      const filteredExpenses = expenses.filter((expense) => {
         // For weekly expenses, check if they have startDate and endDate
         if (expense.startDate && expense.endDate) {
            // Parse start date
            const startComponents = expense.startDate
               .split("T")[0]
               .split("-")
               .map(Number);
            const startDate = new Date(
               startComponents[0],
               startComponents[1] - 1,
               startComponents[2],
               12,
               0,
               0
            );

            // Parse end date
            const endComponents = expense.endDate
               .split("T")[0]
               .split("-")
               .map(Number);
            const endDate = new Date(
               endComponents[0],
               endComponents[1] - 1,
               endComponents[2],
               12,
               0,
               0
            );

            // For weekly expenses, we only care about the date range, not the month
            return selectedDate >= startDate && selectedDate <= endDate;
         }

         // For monthly expenses (no startDate/endDate), check if they match the selected month and year
         const expenseComponents = expense.date
            .split("T")[0]
            .split("-")
            .map(Number);
         const expenseDate = new Date(
            expenseComponents[0],
            expenseComponents[1] - 1,
            expenseComponents[2],
            12,
            0,
            0
         );
         return (
            expenseDate.getMonth() === selectedMonth &&
            expenseDate.getFullYear() === selectedYear
         );
      });

      return filteredExpenses;
   };

   // Get available categories based on transaction type and date
   const getAvailableAssignedTo = () => {
      const formatDate = (dateStr) => {
         const date = new Date(dateStr);
         return date.toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
         });
      };

      // Get all expenses with available amounts (positive remaining balance)
      // This should be done BEFORE any date filtering to ensure we catch all expenses with funds
      const expensesWithAvailableAmount = expenses.filter((expense) => {
         const remainingAmount =
            expense.amountAssigned + (expense.amountSpent || 0);
         return remainingAmount > 0;
      });

      // Get date-filtered expenses only as a supplement
      const filteredExpenses = getFilteredExpenses();

      // Combine expenses, prioritizing those with available amounts
      const combinedExpenseIds = new Set();
      const combinedExpenses = [];

      // First add ALL expenses with available amounts
      expensesWithAvailableAmount.forEach((expense) => {
         combinedExpenseIds.add(expense._id);
         combinedExpenses.push(expense);
      });

      // Then add any date-filtered expenses not already included
      // (This is likely less important but ensures date-filtered expenses also appear)
      filteredExpenses.forEach((expense) => {
         if (!combinedExpenseIds.has(expense._id)) {
            combinedExpenseIds.add(expense._id);
            combinedExpenses.push(expense);
         }
      });

      // Deduplicate expenses by _id to prevent duplicates
      const deduplicatedExpenses = combinedExpenses.filter(
         (expense, index, self) =>
            self.findIndex(
               (e) => e._id?.toString() === expense._id?.toString()
            ) === index
      );

      // Create the unassigned option
      const unassignedOption = {
         value: "",
         label: "Unassigned",
         description: "No assignedTo assigned",
      };

      if (formData.type === "Income") {
         // For income transactions, return both expenses and income categories
         const deduplicatedIncomes = incomes.filter(
            (income, index, self) =>
               self.findIndex(
                  (i) => i._id?.toString() === income._id?.toString()
               ) === index
         );

         return [
            unassignedOption,
            ...deduplicatedExpenses.map((expense) => ({
               value: expense._id,
               label: expense.description,
               description: `${formatDate(expense.date)} • Available: $${(
                  expense.amountAvailable || 0
               ).toFixed(2)}`,
               isIncome: false,
            })),
            ...deduplicatedIncomes.map((income) => ({
               value: income._id,
               label: income.description,
               description: `${formatDate(income.date)} • Expected: $${(
                  income.expectedAmount || 0
               ).toFixed(2)}`,
               isIncome: true,
            })),
         ];
      } else {
         // For expense transactions, return only expenses
         return [
            unassignedOption,
            ...deduplicatedExpenses.map((expense) => ({
               value: expense._id,
               label: expense.description,
               description: `${formatDate(expense.date)} • Available: $${(
                  expense.amountAvailable || 0
               ).toFixed(2)}`,
               isIncome: false,
            })),
         ];
      }
   };

   const handleTypeChange = (type) => {
      setFormData((prev) => ({
         ...prev,
         type,
         assignedTo:
            type === "Transfer" || type === "Adjustment"
               ? null
               : prev.assignedTo,
         assignedToType:
            type === "Transfer" || type === "Adjustment"
               ? null
               : prev.assignedToType,
         // Reset newBalance when switching transaction types
         newBalance: type === "Adjustment" ? "" : prev.newBalance,
         // Reset amount when switching to Adjustment
         amount: type === "Adjustment" ? "" : prev.amount,
         // Set status to "cleared" for Adjustment transactions
         status: type === "Adjustment" ? "cleared" : prev.status,
      }));
   };

   const handleChange = (e) => {
      const { name, value } = e.target;

      if (name === "amount" || name === "newBalance") {
         // Strip any non-numeric characters except decimal point
         const numericValue = value.replace(/[^0-9.]/g, "");

         // Ensure only one decimal point
         const parts = numericValue.split(".");
         const sanitizedValue =
            parts.length > 2
               ? `${parts[0]}.${parts.slice(1).join("")}`
               : numericValue;

         setFormData((prev) => ({
            ...prev,
            [name]: sanitizedValue,
         }));

         // If changing newBalance, calculate the adjustment amount
         if (name === "newBalance") {
            const selectedAccount = accounts.find(
               (account) => account._id === formData.accountId
            );
            if (selectedAccount && sanitizedValue) {
               const currentBalance = selectedAccount.balance || 0;
               const newBalance = parseFloat(sanitizedValue) || 0;
               const adjustmentAmount = (newBalance - currentBalance).toFixed(
                  2
               );

               setFormData((prev) => ({
                  ...prev,
                  amount: Math.abs(adjustmentAmount).toString(),
               }));
            }
         }
      } else {
         setFormData((prev) => ({
            ...prev,
            [name]: value,
         }));
      }
   };

   const handleSubmit = async (e) => {
      e.preventDefault();
      if (!validateForm()) return;

      try {
         setLoading(true);
         setError("");

         // Create a copy of the form data and adjust the amount for expenses
         const [year, month, day] = formData.date.split("-").map(Number);
         const submissionDate = new Date(year, month - 1, day, 12, 0, 0);

         // Initialize submissionData with base properties
         let submissionData = {
            ...formData,
            date: submissionDate.toISOString(),
            amount:
               formData.type === "Expense"
                  ? -Math.abs(parseFloat(formData.amount))
                  : parseFloat(formData.amount),
            accountId:
               formData.type === "Transfer" ? null : formData.accountId || null,
         };

         // For transfers, generate the payee
         if (formData.type === "Transfer") {
            const fromAccount = accounts.find(
               (a) => a._id === parseInt(formData.transferDetails.fromAccountId)
            );
            const toAccount = accounts.find(
               (a) => a._id === parseInt(formData.transferDetails.toAccountId)
            );

            submissionData = {
               ...submissionData,
               type: "Transfer",
               transferDetails: {
                  fromAccountId: parseInt(
                     formData.transferDetails.fromAccountId
                  ),
                  toAccountId: parseInt(formData.transferDetails.toAccountId),
               },
               isAdjustment: true,
            };
         }

         // For adjustments, mark as adjustment and set correct amount direction
         if (formData.type === "Adjustment") {
            const selectedAccount = accounts.find(
               (account) => account._id === formData.accountId
            );
            const currentBalance = selectedAccount
               ? selectedAccount.balance || 0
               : 0;
            const newBalance = parseFloat(formData.newBalance) || 0;
            const adjustmentAmount = newBalance - currentBalance;

            submissionData = {
               ...submissionData,
               type: "Adjustment",
               isAdjustment: true,
               amount: adjustmentAmount,
               payee: `Balance Adjustment - ${
                  selectedAccount?.name || "Account"
               }`,
               status: "cleared", // Force status to cleared for adjustments
            };
         }

         const response = await fetch("/api/transactions", {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify(submissionData),
         });

         if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Failed to create transaction");
         }

         // Get the response data
         const responseData = await response.json();

         // Pass transaction data to success handler
         if (onSuccess) {
            await onSuccess(responseData);
         }

         // Reset form
         setFormData({
            type: transactionTypes[0],
            amount: "",
            payee: "",
            assignedTo: "",
            accountId: "",
            status: "pending",
            date: new Date().toISOString().split("T")[0],
            transferDetails: {
               fromAccountId: "",
               toAccountId: "",
            },
            newBalance: "",
         });

         // Close the form
         if (onClose) {
            onClose();
         }
      } catch (err) {
         setError(err.message);
      } finally {
         setLoading(false);
      }
   };

   const validateForm = () => {
      if (
         formData.type !== "Transfer" &&
         formData.type !== "Adjustment" &&
         !formData.payee.trim()
      ) {
         setError("Payee is required");
         return false;
      }

      if (
         formData.type !== "Adjustment" &&
         (!formData.amount ||
            isNaN(formData.amount) ||
            parseFloat(formData.amount) <= 0)
      ) {
         setError("Please enter a valid amount");
         return false;
      }

      if (
         formData.type === "Adjustment" &&
         (!formData.newBalance || isNaN(formData.newBalance))
      ) {
         setError("Please enter a valid new balance");
         return false;
      }

      if (!formData.date) {
         setError("Date is required");
         return false;
      }

      if (formData.type === "Transfer") {
         if (!formData.transferDetails.fromAccountId) {
            setError("Please select a source account");
            return false;
         }
         if (!formData.transferDetails.toAccountId) {
            setError("Please select a destination account");
            return false;
         }
         if (
            formData.transferDetails.fromAccountId ===
            formData.transferDetails.toAccountId
         ) {
            setError("Source and destination accounts must be different");
            return false;
         }
      }

      if (formData.type === "Adjustment" && !formData.accountId) {
         setError("Please select an account");
         return false;
      }

      return true;
   };

   // Add focus effect
   useEffect(() => {
      if (amountInputRef.current) {
         amountInputRef.current.focus();
      }
   }, []);

   const handleAssignedToSelect = (selectedOption) => {
      // Find the selected assignedTo from availableAssignedTo
      const selectedAssignedTo = availableAssignedTo.find(
         (assignedTo) => assignedTo.value === selectedOption.value
      );

      // Determine the assignedToType based on the selected assignedTo
      const assignedToType = selectedAssignedTo?.isIncome
         ? "Income"
         : "Expense";

      setFormData((prev) => ({
         ...prev,
         assignedTo: selectedOption.value,
         assignedToType: selectedOption.value ? assignedToType : null,
      }));
   };

   // Add a function to handle transfer account changes
   const handleTransferAccountChange = (field, value) => {
      setFormData((prev) => ({
         ...prev,
         transferDetails: {
            ...prev.transferDetails,
            [field]: value,
         },
      }));
   };

   const handleDateSelect = (date) => {
      if (date) {
         // Convert Date object to YYYY-MM-DD string format
         const dateString = format(date, "yyyy-MM-dd");
         setFormData((prev) => ({
            ...prev,
            date: dateString,
         }));
      }
      setIsDatePickerOpen(false);
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="sm:max-w-[600px] max-h-[90vh] min-h-[80vh] overflow-y-auto bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
            <DialogHeader>
               <DialogTitle className="text-gray-900 dark:text-gray-100">
                  Add New Transaction
               </DialogTitle>
            </DialogHeader>

            {/* Error Display */}
            {error && (
               <Alert
                  variant="destructive"
                  className="bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
               >
                  <AlertDescription className="text-red-700 dark:text-red-400">
                     {error}
                  </AlertDescription>
               </Alert>
            )}

            <form
               onSubmit={handleSubmit}
               className="flex flex-col h-full min-h-[70vh]"
            >
               <div className="flex-1 space-y-4">
                  {/* Type Selection Tabs */}
                  <div className="space-y-2">
                     <Label className="text-gray-900 dark:text-gray-100">
                        Type
                     </Label>
                     <Tabs
                        value={formData.type}
                        onValueChange={handleTypeChange}
                     >
                        <TabsList
                           className={`grid w-full ${
                              transactionTypes.length === 4
                                 ? "grid-cols-4"
                                 : "grid-cols-3"
                           } bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 p-1`}
                        >
                           {transactionTypes.map((type) => (
                              <TabsTrigger
                                 key={type}
                                 value={type}
                                 className="data-[state=active]:bg-white data-[state=active]:dark:bg-gray-700 data-[state=active]:text-gray-900 data-[state=active]:dark:text-gray-100 data-[state=active]:shadow-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-all"
                              >
                                 {type}
                              </TabsTrigger>
                           ))}
                        </TabsList>
                     </Tabs>
                  </div>

                  {/* Account Selector */}
                  {formData.type !== "Transfer" && (
                     <div className="space-y-2">
                        <Label
                           htmlFor="accountId"
                           className="text-gray-900 dark:text-gray-100"
                        >
                           Account
                        </Label>
                        {accounts.length > 1 ? (
                           <Select
                              value={formData.accountId}
                              onValueChange={(value) =>
                                 setFormData((prev) => ({
                                    ...prev,
                                    accountId: value,
                                    // Reset newBalance when account changes in Adjustment mode
                                    ...(prev.type === "Adjustment"
                                       ? { newBalance: "" }
                                       : {}),
                                 }))
                              }
                           >
                              <SelectTrigger className="h-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
                                 <SelectValue placeholder="Select account" />
                              </SelectTrigger>
                              <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                                 {accounts.map((account) => (
                                    <SelectItem
                                       key={account._id}
                                       value={account._id}
                                       className="text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                                    >
                                       {account.name}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                        ) : (
                           <Input
                              value={
                                 accounts[0]?.name || "No accounts available"
                              }
                              disabled
                              className="h-10 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                           />
                        )}
                     </div>
                  )}

                  {/* Amount Input - Show for non-adjustment transactions */}
                  {formData.type !== "Adjustment" ? (
                     <div className="flex gap-4">
                        <div className="w-2/3 space-y-2">
                           <Label
                              htmlFor="amount"
                              className="text-gray-900 dark:text-gray-100"
                           >
                              Amount
                           </Label>
                           <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                 <span className="text-gray-500 dark:text-gray-400 text-sm">
                                    $
                                 </span>
                              </div>
                              <Input
                                 type="text"
                                 inputMode="decimal"
                                 name="amount"
                                 id="amount"
                                 ref={amountInputRef}
                                 value={formData.amount}
                                 onChange={handleChange}
                                 autoComplete="off"
                                 className="h-10 !pl-7 pr-3 py-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                                 placeholder="0.00"
                              />
                           </div>
                        </div>

                        {/* Date Picker for non-adjustment transactions */}
                        <div className="w-1/3 space-y-2">
                           <Label className="text-gray-900 dark:text-gray-100">
                              Date
                           </Label>
                           <Popover
                              open={isDatePickerOpen}
                              onOpenChange={setIsDatePickerOpen}
                           >
                              <PopoverTrigger asChild>
                                 <Button
                                    variant="outline"
                                    className="h-10 w-full justify-start text-left font-normal border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                                 >
                                    <CalendarIcon className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                                    {formData.date ? (
                                       format(
                                          parseTimezoneDate(formData.date),
                                          "PPP"
                                       )
                                    ) : (
                                       <span className="text-gray-500 dark:text-gray-400">
                                          Pick a date
                                       </span>
                                    )}
                                 </Button>
                              </PopoverTrigger>
                              <PopoverContent
                                 className="w-auto p-0 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600"
                                 align="start"
                              >
                                 <Calendar
                                    mode="single"
                                    selected={
                                       formData.date
                                          ? parseTimezoneDate(formData.date)
                                          : undefined
                                    }
                                    onSelect={handleDateSelect}
                                    initialFocus
                                 />
                              </PopoverContent>
                           </Popover>
                        </div>
                     </div>
                  ) : (
                     /* Adjustment Balance Fields */
                     <div className="w-full space-y-4">
                        {/* Current Balance and Date in same row */}
                        <div className="flex gap-4">
                           {/* Current Balance Display */}
                           <div className="flex-grow space-y-2">
                              <Label className="text-gray-900 dark:text-gray-100">
                                 Current Balance
                              </Label>
                              <div className="relative">
                                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-400 text-sm">
                                       $
                                    </span>
                                 </div>
                                 <Input
                                    type="text"
                                    value={(() => {
                                       const selectedAccount = accounts.find(
                                          (account) =>
                                             account._id === formData.accountId
                                       );
                                       return selectedAccount
                                          ? selectedAccount.balance.toFixed(2)
                                          : "0.00";
                                    })()}
                                    disabled
                                    className="h-10 !pl-7 pr-3 py-2 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                 />
                              </div>
                           </div>

                           {/* Date Picker */}
                           <div className="w-1/3 space-y-2">
                              <Label className="text-gray-900 dark:text-gray-100">
                                 Date
                              </Label>
                              <Popover
                                 open={isDatePickerOpen}
                                 onOpenChange={setIsDatePickerOpen}
                              >
                                 <PopoverTrigger asChild>
                                    <Button
                                       variant="outline"
                                       className="h-10 w-full justify-start text-left font-normal border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                                    >
                                       <CalendarIcon className="mr-2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                                       {formData.date ? (
                                          format(
                                             parseTimezoneDate(formData.date),
                                             "PPP"
                                          )
                                       ) : (
                                          <span className="text-gray-500 dark:text-gray-400">
                                             Pick a date
                                          </span>
                                       )}
                                    </Button>
                                 </PopoverTrigger>
                                 <PopoverContent
                                    className="w-auto p-0 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600"
                                    align="start"
                                 >
                                    <Calendar
                                       mode="single"
                                       selected={
                                          formData.date
                                             ? parseTimezoneDate(formData.date)
                                             : undefined
                                       }
                                       onSelect={handleDateSelect}
                                       initialFocus
                                    />
                                 </PopoverContent>
                              </Popover>
                           </div>
                        </div>

                        {/* New Balance and Adjustment Amount in same row */}
                        <div className="flex gap-4">
                           {/* New Balance Input */}
                           <div className="flex-grow space-y-2">
                              <Label
                                 htmlFor="newBalance"
                                 className="text-gray-900 dark:text-gray-100"
                              >
                                 New Balance
                              </Label>
                              <div className="relative">
                                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-400 text-sm">
                                       $
                                    </span>
                                 </div>
                                 <Input
                                    type="text"
                                    inputMode="decimal"
                                    name="newBalance"
                                    id="newBalance"
                                    value={formData.newBalance}
                                    onChange={handleChange}
                                    autoComplete="off"
                                    className="h-10 !pl-7 pr-3 py-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                                    placeholder="0.00"
                                 />
                              </div>
                           </div>

                           {/* Adjustment Amount Display */}
                           <div className="w-1/3 space-y-2">
                              <Label
                                 htmlFor="adjustmentAmount"
                                 className="text-gray-900 dark:text-gray-100"
                              >
                                 Adjustment
                              </Label>
                              <Input
                                 id="adjustmentAmount"
                                 type="text"
                                 value={(() => {
                                    const selectedAccount = accounts.find(
                                       (account) =>
                                          account._id === formData.accountId
                                    );
                                    if (
                                       !selectedAccount ||
                                       !formData.newBalance
                                    )
                                       return "";
                                    const currentBalance =
                                       selectedAccount.balance || 0;
                                    const newBalance =
                                       parseFloat(formData.newBalance) || 0;
                                    const diff = newBalance - currentBalance;
                                    return `${
                                       diff > 0 ? "+" : diff < 0 ? "-" : ""
                                    }$${Math.abs(diff).toFixed(2)}`;
                                 })()}
                                 disabled
                                 className={`h-10 text-center border-gray-300 dark:border-gray-600 ${(() => {
                                    const selectedAccount = accounts.find(
                                       (account) =>
                                          account._id === formData.accountId
                                    );
                                    if (
                                       !selectedAccount ||
                                       !formData.newBalance
                                    )
                                       return "";
                                    const currentBalance =
                                       selectedAccount.balance || 0;
                                    const newBalance =
                                       parseFloat(formData.newBalance) || 0;
                                    const diff = newBalance - currentBalance;
                                    return diff > 0
                                       ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400"
                                       : diff < 0
                                       ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400"
                                       : "bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100";
                                 })()}`}
                              />
                           </div>
                        </div>
                     </div>
                  )}

                  {/* Payee Input - Hide for Transfer and Adjustment */}
                  {formData.type !== "Transfer" &&
                     formData.type !== "Adjustment" && (
                        <div className="space-y-2">
                           <Label
                              htmlFor="payee"
                              className="text-gray-900 dark:text-gray-100"
                           >
                              Payee/ Description
                           </Label>
                           <Input
                              type="text"
                              name="payee"
                              id="payee"
                              value={formData.payee}
                              onChange={handleChange}
                              autoComplete="off"
                              placeholder="Enter payee"
                              className="h-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                           />
                        </div>
                     )}

                  {/* AssignedTo Selector - Hide for Transfers */}
                  {formData.type !== "Transfer" &&
                     formData.type !== "Adjustment" && (
                        <div className="space-y-2">
                           <Label className="text-gray-900 dark:text-gray-100">
                              Assigned To
                           </Label>
                           <div className="border border-gray-300 dark:border-gray-600 rounded-md">
                              <Combobox
                                 options={availableAssignedTo}
                                 onChange={handleAssignedToSelect}
                                 value={{
                                    value: formData.assignedTo,
                                    label:
                                       availableAssignedTo.find(
                                          (c) => c.value === formData.assignedTo
                                       )?.label || "Unassigned",
                                 }}
                                 placeholder="Select assigned to"
                                 className="min-w-[250px]"
                              />
                           </div>
                        </div>
                     )}

                  {/* Transfer Account Selectors */}
                  {formData.type === "Transfer" && (
                     <div className="space-y-4">
                        <div className="space-y-2">
                           <Label
                              htmlFor="fromAccount"
                              className="text-gray-900 dark:text-gray-100"
                           >
                              From Account
                           </Label>
                           <Select
                              value={formData.transferDetails.fromAccountId}
                              onValueChange={(value) =>
                                 handleTransferAccountChange(
                                    "fromAccountId",
                                    value
                                 )
                              }
                           >
                              <SelectTrigger className="h-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
                                 <SelectValue placeholder="Select source account" />
                              </SelectTrigger>
                              <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                                 {accounts.map((account) => (
                                    <SelectItem
                                       key={account._id}
                                       value={account._id}
                                       className="text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                                    >
                                       {account.name}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                        </div>

                        <div className="space-y-2">
                           <Label
                              htmlFor="toAccount"
                              className="text-gray-900 dark:text-gray-100"
                           >
                              To Account
                           </Label>
                           <Select
                              value={formData.transferDetails.toAccountId}
                              onValueChange={(value) =>
                                 handleTransferAccountChange(
                                    "toAccountId",
                                    value
                                 )
                              }
                           >
                              <SelectTrigger className="h-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
                                 <SelectValue placeholder="Select destination account" />
                              </SelectTrigger>
                              <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                                 {accounts.map((account) => (
                                    <SelectItem
                                       key={account._id}
                                       value={account._id}
                                       className="text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                                    >
                                       {account.name}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                        </div>
                     </div>
                  )}

                  {/* Status Selector - Hide for Adjustment */}
                  {formData.type !== "Adjustment" && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Status
                        </Label>
                        <Select
                           value={formData.status}
                           onValueChange={(value) =>
                              setFormData((prev) => ({
                                 ...prev,
                                 status: value,
                              }))
                           }
                        >
                           <SelectTrigger className="h-10 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
                              <SelectValue />
                           </SelectTrigger>
                           <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                              {TRANSACTION_STATUS.map((status) => (
                                 <SelectItem
                                    key={status}
                                    value={status}
                                    className="text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                                 >
                                    {status.charAt(0).toUpperCase() +
                                       status.slice(1)}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     </div>
                  )}
               </div>

               {/* Submit Button */}
               <div className="mt-6">
                  <Button
                     type="submit"
                     disabled={loading}
                     className="w-full bg-blue-600 hover:bg-blue-700 text-white border-none"
                  >
                     <Save className="w-4 h-4 mr-2" />
                     {loading ? "Processing..." : "Add Transaction"}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
}

"use client";

import { createPortal } from "react-dom";
import { Combobox } from "../ui/combobox";
import { isCashAccount } from "@/app/lib/utils/transactionsUtils";

export const DropdownPortals = ({
   showCategoryModal,
   editingTransaction,
   availableAssignedTo,
   dropdownPosition,
   categoryDropdownRef,
   handleCategoryChange,
   onAddIncome,
   onAddExpense,
   getCategoryDescription,
   showStatusDropdown,
   statusDropdownRef,
   transactions,
   TRANSACTION_STATUS,
   onStatusChange,
   showAccountDropdown,
   accountDropdownRef,
   accounts,
   onAccountChange,
   currentCategoryCellRef,
   onCategoryClick,
   setShowStatusDropdown, // Add this prop
   setShowAccountDropdown, // Add this prop
}) => {
   return (
      <>
         {/* Category Edit Dropdown */}
         {showCategoryModal &&
            editingTransaction &&
            createPortal(
               <div
                  className="fixed z-[9999] rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-600 dark:ring-opacity-75 border border-gray-200 dark:border-gray-600"
                  style={{
                     top: dropdownPosition.top,
                     left: dropdownPosition.left,
                     width: "320px",
                     maxHeight: "200px",
                     padding: 0,
                     margin: 0,
                     display: "flex",
                     overflow: "visible",
                     flexDirection: "column",
                     transform: dropdownPosition.isAbove
                        ? "translateY(calc(-100% - 10px))"
                        : "none",
                     transformOrigin: dropdownPosition.isAbove
                        ? "bottom center"
                        : "top center",
                  }}
                  ref={categoryDropdownRef}
               >
                  <Combobox
                     options={[
                        ...(editingTransaction?.assignedTo &&
                        editingTransaction.assignedTo !== "Unassigned"
                           ? [
                                availableAssignedTo.find(
                                   (c) =>
                                      c.description ===
                                      editingTransaction.assignedTo
                                ),
                             ]
                                .filter(Boolean)
                                .map((category) => ({
                                   value: category._id,
                                   label: category.description,
                                   description:
                                      getCategoryDescription(category),
                                }))
                           : []),
                        { value: "", label: "Unassigned" },
                        ...availableAssignedTo
                           .filter(
                              (c) =>
                                 c.description !==
                                 editingTransaction?.assignedTo
                           )
                           .filter(
                              (category, index, self) =>
                                 self.findIndex(
                                    (c) =>
                                       c._id?.toString() ===
                                       category._id?.toString()
                                 ) === index
                           )
                           .map((category) => ({
                              value: category._id,
                              label: category.description,
                              description: getCategoryDescription(category),
                           })),
                     ]}
                     value={
                        editingTransaction?.assignedTo &&
                        editingTransaction.assignedTo !== "Unassigned"
                           ? {
                                value:
                                   availableAssignedTo.find(
                                      (c) =>
                                         c.description ===
                                         editingTransaction.assignedTo
                                   )?._id || "",
                                label: editingTransaction.assignedTo,
                             }
                           : { value: "", label: "Unassigned" }
                     }
                     onChange={handleCategoryChange}
                     placeholder="Search categories..."
                     autoFocus={true}
                     position={dropdownPosition.isAbove ? "top" : "bottom"}
                     onEscape={() => {
                        onCategoryClick(null);
                        requestAnimationFrame(() => {
                           currentCategoryCellRef.current?.focus();
                        });
                     }}
                     noResultsAction={{
                        label:
                           editingTransaction?.type === "Income"
                              ? "Add New Income"
                              : "Add New Expense",
                        onClick: () => {
                           if (editingTransaction?.type === "Income") {
                              onAddIncome && onAddIncome(editingTransaction);
                           } else {
                              onAddExpense && onAddExpense(editingTransaction);
                           }
                        },
                     }}
                     className="min-w-[250px]"
                  />
               </div>,
               document.body
            )}

         {/* Status Dropdown Portal */}
         {showStatusDropdown &&
            createPortal(
               <div
                  className="fixed rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-600 dark:ring-opacity-75 overflow-hidden z-[9999] border border-gray-200 dark:border-gray-600"
                  style={{
                     top: dropdownPosition.top,
                     left: dropdownPosition.left,
                     padding: 0,
                     margin: 0,
                     width: "150px", // Fixed width for the status dropdown
                     transform: dropdownPosition.isAbove
                        ? "translateY(calc(-100% - 10px))"
                        : "none",
                     transformOrigin: dropdownPosition.isAbove
                        ? "bottom center"
                        : "top center",
                  }}
                  ref={statusDropdownRef}
               >
                  <div className="py-1">
                     {TRANSACTION_STATUS.map((status) => (
                        <button
                           key={status}
                           onClick={() => {
                              onStatusChange(showStatusDropdown, status);
                              setShowStatusDropdown(null);
                           }}
                           className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                              transactions.find(
                                 (t) => t._id === showStatusDropdown
                              )?.status === status
                                 ? "bg-gray-50 dark:bg-gray-900/50 text-gray-900 dark:text-gray-100 font-medium"
                                 : "text-gray-900 dark:text-gray-100"
                           }`}
                        >
                           {status.charAt(0).toUpperCase() + status.slice(1)}
                        </button>
                     ))}
                  </div>
               </div>,
               document.body
            )}

         {/* Account Dropdown Portal */}
         {showAccountDropdown &&
            createPortal(
               <div
                  className="fixed rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-600 dark:ring-opacity-75 overflow-hidden z-[9999] border border-gray-200 dark:border-gray-600"
                  style={{
                     top: dropdownPosition.top,
                     left: dropdownPosition.left,
                     padding: 0,
                     margin: 0,
                     width: "200px", // Fixed width for account dropdown
                     transform: dropdownPosition.isAbove
                        ? "translateY(calc(-100% - 10px))"
                        : "none",
                     transformOrigin: dropdownPosition.isAbove
                        ? "bottom center"
                        : "top center",
                  }}
                  ref={accountDropdownRef}
               >
                  <div className="py-1">
                     <button
                        onClick={() => {
                           onAccountChange(showAccountDropdown, null);
                           setShowAccountDropdown(null); // Add this line
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                           !transactions.find(
                              (t) => t._id === showAccountDropdown
                           )?.accountId
                              ? "bg-gray-50 dark:bg-gray-900/50 text-gray-900 dark:text-gray-100 font-medium"
                              : "text-gray-900 dark:text-gray-100"
                        }`}
                     >
                        Unassigned
                     </button>
                     {accounts
                        .filter(
                           (account) =>
                              isCashAccount(account) ||
                              account.accountType === "credit"
                        )
                        .map((account) => (
                           <button
                              key={account._id}
                              onClick={() => {
                                 onAccountChange(
                                    showAccountDropdown,
                                    account._id
                                 );
                                 setShowAccountDropdown(null); // Add this line
                              }}
                              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                                 transactions.find(
                                    (t) => t._id === showAccountDropdown
                                 )?.accountId === account._id
                                    ? "bg-gray-50 dark:bg-gray-900/50 text-gray-900 dark:text-gray-100 font-medium"
                                    : "text-gray-900 dark:text-gray-100"
                              }`}
                           >
                              {account.name}
                           </button>
                        ))}
                  </div>
               </div>,
               document.body
            )}
      </>
   );
};

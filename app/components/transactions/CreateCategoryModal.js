"use client";

import { useState, useEffect } from "react";
import {
   <PERSON><PERSON>,
   <PERSON>alog<PERSON>ontent,
   <PERSON><PERSON>Header,
   DialogTitle,
   DialogFooter,
} from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import { Label } from "../../../components/ui/label";
import { Switch } from "@headlessui/react";
import { Calendar } from "../../../components/ui/calendar";
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from "../../../components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";

const CreateCategoryModal = ({
   isOpen,
   onClose,
   onSave,
   categoryName,
   transactionId,
   transactionAmount,
   showBothOptions = true,
}) => {
   const [formData, setFormData] = useState({
      name: categoryName || "",
      amount: Math.abs(transactionAmount) || 0,
      type: "expense", // "expense" or "income"
      dueDate: new Date(),
      isRecurring: false,
      frequency: "monthly", // Default frequency
      weeklyChargeType: "one-time", // For weekly expenses
      dueMonth: "1", // For annual expenses
      payDayOfWeek: "1", // For weekly/biweekly incomes
      payWeekDay: "Monday", // For weekly/biweekly incomes
      lastPaymentDate: new Date().toISOString().split("T")[0], // For biweekly incomes
   });
   const [isCalendarOpen, setIsCalendarOpen] = useState(false);
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState("");

   // Update form data when props change
   useEffect(() => {
      if (isOpen) {
         setFormData({
            name: categoryName || "",
            amount: Math.abs(transactionAmount) || 0,
            type: "expense",
            dueDate: new Date(),
            isRecurring: false,
            frequency: "monthly",
            weeklyChargeType: "one-time",
            dueMonth: "1",
            payDayOfWeek: "1",
            payWeekDay: "Monday",
            lastPaymentDate: new Date().toISOString().split("T")[0],
         });
         setError("");
      }
   }, [isOpen, categoryName, transactionAmount]);

   const handleInputChange = (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   const handleAmountChange = (e) => {
      const value = parseFloat(e.target.value) || 0;
      setFormData((prev) => ({
         ...prev,
         amount: value,
      }));
   };

   const handleTypeChange = (type) => {
      setFormData((prev) => ({
         ...prev,
         type,
      }));
   };

   const handleRecurringToggle = (isRecurring) => {
      setFormData((prev) => ({
         ...prev,
         isRecurring,
      }));
   };

   const handleFrequencyChange = (frequency) => {
      setFormData((prev) => {
         const updated = { ...prev, frequency };

         // Update related fields based on frequency
         if (frequency === "weekly") {
            // Don't reset weeklyChargeType - let user choose between "one-time" and "spread"
            if (prev.type === "income") {
               updated.payDayOfWeek = "1";
               updated.payWeekDay = "Monday";
            }
         } else if (frequency === "biweekly" && prev.type === "income") {
            updated.payDayOfWeek = "1";
            updated.payWeekDay = "Monday";
            updated.lastPaymentDate = new Date().toISOString().split("T")[0];
         } else if (frequency === "annually" && prev.type === "expense") {
            updated.dueMonth = "1";
         }

         return updated;
      });
   };

   const handleWeeklyChargeTypeChange = (weeklyChargeType) => {
      setFormData((prev) => ({
         ...prev,
         weeklyChargeType,
      }));
   };

   const handleDueMonthChange = (dueMonth) => {
      setFormData((prev) => ({
         ...prev,
         dueMonth,
      }));
   };

   const handlePayDayOfWeekChange = (payDayOfWeek) => {
      const dayNames = [
         "Monday",
         "Tuesday",
         "Wednesday",
         "Thursday",
         "Friday",
         "Saturday",
         "Sunday",
      ];
      const payWeekDay = dayNames[parseInt(payDayOfWeek) - 1];

      setFormData((prev) => ({
         ...prev,
         payDayOfWeek,
         payWeekDay,
      }));
   };

   const handleLastPaymentDateChange = (e) => {
      setFormData((prev) => ({
         ...prev,
         lastPaymentDate: e.target.value,
      }));
   };

   const handleDateInputChange = (e) => {
      const dateValue = e.target.value;
      console.log("Date input changed:", dateValue);
      if (dateValue) {
         const selectedDate = new Date(dateValue);
         console.log("Parsed date:", selectedDate);
         setFormData((prev) => ({
            ...prev,
            dueDate: selectedDate,
         }));
      }
   };

   const handleSubmit = async (e) => {
      e.preventDefault();
      setLoading(true);
      setError("");

      try {
         await onSave(formData, transactionId);
         onClose();
      } catch (err) {
         console.error("Error creating category:", err);
         setError(err.message || "Failed to create category");
      } finally {
         setLoading(false);
      }
   };

   const handleClose = () => {
      setFormData({
         name: categoryName || "",
         amount: Math.abs(transactionAmount) || 0,
         type: "expense",
         dueDate: new Date(),
         isRecurring: false,
         frequency: "monthly",
         weeklyChargeType: "one-time",
         dueMonth: "1",
         payDayOfWeek: "1",
         payWeekDay: "Monday",
         lastPaymentDate: new Date().toISOString().split("T")[0],
      });
      setError("");
      onClose();
   };

   return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
         <DialogContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 sm:max-w-md">
            <DialogHeader>
               <DialogTitle className="text-gray-900 dark:text-gray-100">
                  Create New Category
               </DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
               {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-3 py-2 rounded text-sm">
                     {error}
                  </div>
               )}

               <div className="space-y-2">
                  <Label className="text-gray-900 dark:text-gray-100">
                     Category Name
                  </Label>
                  <input
                     type="text"
                     name="name"
                     value={formData.name}
                     onChange={handleInputChange}
                     required
                     className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                     placeholder="Enter category name"
                  />
               </div>

               <div className="space-y-2">
                  <Label className="text-gray-900 dark:text-gray-100">
                     Amount Due
                  </Label>
                  <div className="relative">
                     <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                           $
                        </span>
                     </div>
                     <input
                        type="number"
                        name="amount"
                        value={formData.amount}
                        onChange={handleAmountChange}
                        required
                        step="0.01"
                        className="h-10 !pl-7 pr-3 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0.00"
                     />
                  </div>
               </div>

               {showBothOptions && (
                  <div className="space-y-2">
                     <Label className="text-gray-900 dark:text-gray-100">
                        Type
                     </Label>
                     <div className="flex space-x-4">
                        <button
                           type="button"
                           onClick={() => handleTypeChange("expense")}
                           className={`h-10 px-4 py-2 border rounded-md transition-colors ${
                              formData.type === "expense"
                                 ? "bg-blue-500 text-white border-blue-500"
                                 : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                           }`}
                        >
                           Expense
                        </button>
                        <button
                           type="button"
                           onClick={() => handleTypeChange("income")}
                           className={`h-10 px-4 py-2 border rounded-md transition-colors ${
                              formData.type === "income"
                                 ? "bg-blue-500 text-white border-blue-500"
                                 : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                           }`}
                        >
                           Income
                        </button>
                     </div>
                  </div>
               )}

               {!showBothOptions && (
                  <div className="space-y-2">
                     <Label className="text-gray-900 dark:text-gray-100">
                        Type
                     </Label>
                     <div className="h-10 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 flex items-center">
                        Expense
                     </div>
                  </div>
               )}

               {!formData.isRecurring && (
                  <div className="space-y-2">
                     <Label className="text-gray-900 dark:text-gray-100">
                        Due Date
                     </Label>
                     <input
                        type="date"
                        value={
                           formData.dueDate
                              ? formData.dueDate.toISOString().split("T")[0]
                              : ""
                        }
                        onChange={handleDateInputChange}
                        className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                     />
                  </div>
               )}

               <div className="flex items-center justify-between">
                  <Label className="text-gray-900 dark:text-gray-100">
                     Recurring {formData.type}
                  </Label>
                  <Switch
                     checked={formData.isRecurring}
                     onChange={handleRecurringToggle}
                     className={`${
                        formData.isRecurring
                           ? "bg-blue-600"
                           : "bg-gray-200 dark:bg-gray-700"
                     } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900`}
                  >
                     <span className="sr-only">
                        Enable recurring {formData.type}
                     </span>
                     <span
                        className={`${
                           formData.isRecurring
                              ? "translate-x-6"
                              : "translate-x-1"
                        } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                     />
                  </Switch>
               </div>

               {formData.isRecurring && (
                  <div className="space-y-2">
                     <Label className="text-gray-900 dark:text-gray-100">
                        Frequency
                     </Label>
                     <div className="grid grid-cols-2 gap-2">
                        {formData.type === "income"
                           ? [
                                "weekly",
                                "biweekly",
                                "semimonthly",
                                "monthly",
                             ].map((freq) => (
                                <button
                                   key={freq}
                                   type="button"
                                   onClick={() => handleFrequencyChange(freq)}
                                   className={`h-10 px-3 py-2 text-sm border rounded-md transition-colors ${
                                      formData.frequency === freq
                                         ? "bg-blue-500 text-white border-blue-500"
                                         : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                                   }`}
                                >
                                   {freq === "semimonthly"
                                      ? "Semi-monthly"
                                      : freq.charAt(0).toUpperCase() +
                                        freq.slice(1)}
                                </button>
                             ))
                           : [
                                "weekly",
                                "biweekly",
                                "monthly",
                                "quarterly",
                                "annually",
                             ].map((freq) => (
                                <button
                                   key={freq}
                                   type="button"
                                   onClick={() => handleFrequencyChange(freq)}
                                   className={`h-10 px-3 py-2 text-sm border rounded-md transition-colors ${
                                      formData.frequency === freq
                                         ? "bg-blue-500 text-white border-blue-500"
                                         : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                                   }`}
                                >
                                   {freq.charAt(0).toUpperCase() +
                                      freq.slice(1)}
                                </button>
                             ))}
                     </div>
                  </div>
               )}

               {formData.isRecurring &&
                  formData.type === "expense" &&
                  formData.frequency === "weekly" && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Due Day of Week
                        </Label>
                        <select
                           value={formData.payDayOfWeek}
                           onChange={(e) =>
                              handlePayDayOfWeekChange(e.target.value)
                           }
                           className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                           <option value="1">Monday</option>
                           <option value="2">Tuesday</option>
                           <option value="3">Wednesday</option>
                           <option value="4">Thursday</option>
                           <option value="5">Friday</option>
                           <option value="6">Saturday</option>
                           <option value="7">Sunday</option>
                        </select>
                     </div>
                  )}

               {formData.isRecurring &&
                  formData.type === "expense" &&
                  formData.frequency === "weekly" && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Charge Type
                        </Label>
                        <div className="grid grid-cols-2 gap-2">
                           <button
                              type="button"
                              onClick={() =>
                                 handleWeeklyChargeTypeChange("one-time")
                              }
                              className={`h-10 px-3 py-2 text-sm border rounded-md transition-colors ${
                                 formData.weeklyChargeType === "one-time"
                                    ? "bg-blue-500 text-white border-blue-500"
                                    : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                              }`}
                           >
                              One-time
                           </button>
                           <button
                              type="button"
                              onClick={() =>
                                 handleWeeklyChargeTypeChange("spread")
                              }
                              className={`h-10 px-3 py-2 text-sm border rounded-md transition-colors ${
                                 formData.weeklyChargeType === "spread"
                                    ? "bg-blue-500 text-white border-blue-500"
                                    : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                              }`}
                           >
                              Spread
                           </button>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                           {formData.weeklyChargeType === "one-time"
                              ? "The entire amount will be charged on the specified day"
                              : "The amount will be spread across the entire week"}
                        </p>
                     </div>
                  )}

               {formData.isRecurring &&
                  formData.type === "expense" &&
                  formData.frequency === "annually" && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Due Month
                        </Label>
                        <select
                           value={formData.dueMonth}
                           onChange={(e) =>
                              handleDueMonthChange(e.target.value)
                           }
                           className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                           {[
                              "January",
                              "February",
                              "March",
                              "April",
                              "May",
                              "June",
                              "July",
                              "August",
                              "September",
                              "October",
                              "November",
                              "December",
                           ].map((month, index) => (
                              <option
                                 key={index + 1}
                                 value={(index + 1).toString()}
                              >
                                 {month}
                              </option>
                           ))}
                        </select>
                     </div>
                  )}

               {formData.isRecurring &&
                  formData.type === "income" &&
                  (formData.frequency === "weekly" ||
                     formData.frequency === "biweekly") && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Pay Day of Week
                        </Label>
                        <select
                           value={formData.payDayOfWeek}
                           onChange={(e) =>
                              handlePayDayOfWeekChange(e.target.value)
                           }
                           className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                           <option value="1">Monday</option>
                           <option value="2">Tuesday</option>
                           <option value="3">Wednesday</option>
                           <option value="4">Thursday</option>
                           <option value="5">Friday</option>
                           <option value="6">Saturday</option>
                           <option value="7">Sunday</option>
                        </select>
                     </div>
                  )}

               {formData.isRecurring &&
                  formData.type === "income" &&
                  formData.frequency === "biweekly" && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Last Payment Date
                        </Label>
                        <input
                           type="date"
                           value={formData.lastPaymentDate}
                           onChange={handleLastPaymentDateChange}
                           className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                     </div>
                  )}

               {formData.isRecurring &&
                  formData.type === "income" &&
                  formData.frequency === "semimonthly" && (
                     <div className="space-y-2">
                        <Label className="text-gray-900 dark:text-gray-100">
                           Pay Days
                        </Label>
                        <div className="h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 flex items-center">
                           Fixed on 1st and 15th of each month
                        </div>
                     </div>
                  )}

               {formData.isRecurring && formData.frequency === "monthly" && (
                  <div className="space-y-2">
                     <Label className="text-gray-900 dark:text-gray-100">
                        Day of Month
                     </Label>
                     <select
                        value={formData.dueMonth}
                        onChange={(e) => handleDueMonthChange(e.target.value)}
                        className="h-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                     >
                        {Array.from({ length: 31 }, (_, i) => (
                           <option key={i + 1} value={(i + 1).toString()}>
                              {i + 1}
                           </option>
                        ))}
                     </select>
                  </div>
               )}

               <DialogFooter className="flex justify-end space-x-2">
                  <Button
                     type="button"
                     variant="outline"
                     onClick={handleClose}
                     disabled={loading}
                     className="h-10 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     disabled={loading || !formData.name.trim()}
                     className="h-10 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
                  >
                     {loading ? "Creating..." : "Create & Assign"}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
};

export default CreateCategoryModal;

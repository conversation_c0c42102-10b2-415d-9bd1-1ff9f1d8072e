"use client";

import { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { parseTimezoneDate } from "../../lib/utils/transactionsUtils";

export default function DuplicateTransactionsModal({
   duplicates,
   onClose,
   onResolve,
}) {
   const [resolutions, setResolutions] = useState(
      duplicates.reduce(
         (acc, dup) => ({
            ...acc,
            [dup.plaidTransaction.plaidTransactionId]: {
               action: "skip", // 'skip', 'merge', or 'add'
               selectedTransactionId: dup.existingTransactions[0]?._id, // for merge
               plaidTransaction: dup.plaidTransaction, // Include the full plaid transaction
            },
         }),
         {}
      )
   );

   const handleResolutionChange = (
      plaidTransactionId,
      action,
      existingTransactionId = null
   ) => {
      setResolutions((prev) => ({
         ...prev,
         [plaidTransactionId]: {
            ...prev[plaidTransactionId],
            action,
            selectedTransactionId: existingTransactionId,
         },
      }));
   };

   const handleSubmit = () => {
      onResolve(resolutions);
      onClose();
   };

   return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
         <div className="flex items-center justify-center min-h-screen">
            <div className="fixed inset-0 bg-black bg-opacity-50"></div>
            <div className="relative bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full mx-4">
               <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                     Review Duplicate Transactions
                  </h3>
                  <button
                     onClick={onClose}
                     className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                  >
                     <XMarkIcon className="h-6 w-6" />
                  </button>
               </div>

               <div className="p-4 overflow-auto max-h-[calc(90vh-8rem)]">
                  {duplicates.map(
                     ({ plaidTransaction, existingTransactions }) => (
                        <div
                           key={plaidTransaction.plaidTransactionId}
                           className="mb-6 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg"
                        >
                           <div className="mb-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                 New Transaction from Bank:
                              </h4>
                              <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                                 <div className="flex justify-between">
                                    <span>{plaidTransaction.payee}</span>
                                    <span className="font-mono">
                                       {plaidTransaction.amount >= 0
                                          ? "+"
                                          : "-"}
                                       $
                                       {Math.abs(
                                          plaidTransaction.amount
                                       ).toFixed(2)}
                                    </span>
                                 </div>
                                 <div className="text-sm text-gray-500 dark:text-gray-400">
                                    {parseTimezoneDate(
                                       plaidTransaction.date
                                    ).toLocaleDateString()}
                                 </div>
                              </div>
                           </div>

                           <div className="mb-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                 Possible Matches:
                              </h4>
                              <div className="space-y-2">
                                 {existingTransactions.map((tx) => (
                                    <div
                                       key={tx._id}
                                       className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm"
                                    >
                                       <div className="flex justify-between">
                                          <span>{tx.payee}</span>
                                          <span className="font-mono">
                                             {tx.amount >= 0 ? "+" : "-"}$
                                             {Math.abs(tx.amount).toFixed(2)}
                                          </span>
                                       </div>
                                       <div className="text-sm text-gray-500 dark:text-gray-400">
                                          {parseTimezoneDate(
                                             tx.date
                                          ).toLocaleDateString()}
                                       </div>
                                    </div>
                                 ))}
                              </div>
                           </div>

                           <div className="flex flex-col gap-2">
                              <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                 How would you like to handle this?
                              </label>
                              <div className="space-y-2">
                                 <label className="flex items-center">
                                    <input
                                       type="radio"
                                       name={`resolution-${plaidTransaction.plaidTransactionId}`}
                                       checked={
                                          resolutions[
                                             plaidTransaction.plaidTransactionId
                                          ]?.action === "skip"
                                       }
                                       onChange={() =>
                                          handleResolutionChange(
                                             plaidTransaction.plaidTransactionId,
                                             "skip"
                                          )
                                       }
                                       className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 dark:border-gray-600"
                                    />
                                    <span className="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                       Skip this transaction (don't import)
                                    </span>
                                 </label>

                                 {existingTransactions.map((tx) => (
                                    <label
                                       key={tx._id}
                                       className="flex items-center"
                                    >
                                       <input
                                          type="radio"
                                          name={`resolution-${plaidTransaction.plaidTransactionId}`}
                                          checked={
                                             resolutions[
                                                plaidTransaction
                                                   .plaidTransactionId
                                             ]?.action === "merge" &&
                                             resolutions[
                                                plaidTransaction
                                                   .plaidTransactionId
                                             ]?.selectedTransactionId === tx._id
                                          }
                                          onChange={() =>
                                             handleResolutionChange(
                                                plaidTransaction.plaidTransactionId,
                                                "merge",
                                                tx._id
                                             )
                                          }
                                          className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 dark:border-gray-600"
                                       />
                                       <span className="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                          Merge with existing transaction (
                                          {tx.payee})
                                       </span>
                                    </label>
                                 ))}

                                 <label className="flex items-center">
                                    <input
                                       type="radio"
                                       name={`resolution-${plaidTransaction.plaidTransactionId}`}
                                       checked={
                                          resolutions[
                                             plaidTransaction.plaidTransactionId
                                          ]?.action === "add"
                                       }
                                       onChange={() =>
                                          handleResolutionChange(
                                             plaidTransaction.plaidTransactionId,
                                             "add"
                                          )
                                       }
                                       className="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 dark:border-gray-600"
                                    />
                                    <span className="ml-2 text-sm text-gray-900 dark:text-gray-100">
                                       Add as new transaction
                                    </span>
                                 </label>
                              </div>
                           </div>
                        </div>
                     )
                  )}
               </div>

               <div className="px-4 py-3 bg-gray-50 dark:bg-gray-900 sm:px-6 flex justify-end gap-3 border-t border-gray-200 dark:border-gray-700">
                  <button
                     onClick={onClose}
                     className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                  >
                     Cancel
                  </button>
                  <button
                     onClick={handleSubmit}
                     className="px-4 py-2 text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 rounded-md"
                  >
                     Apply Resolutions
                  </button>
               </div>
            </div>
         </div>
      </div>
   );
}

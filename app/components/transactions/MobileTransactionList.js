"use client";

import { useState, useRef, useEffect } from "react";
import { TransactionHelpers } from "../../lib/utils/transactionsUtils";
import { TransactionEventHandlers } from "../../lib/utils/transactionEventHandlers";
import { PaginationControls } from "./PaginationControls";
import { DropdownPortals } from "./DropdownPortals";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "../../../components/ui/dropdown-menu";
import { ChevronDownIcon, TrashIcon } from "@heroicons/react/24/outline";
import CreateCategoryModal from "./CreateCategoryModal";

const MobileTransactionList = ({
   transactions,
   availableAssignedTo,
   accounts,
   onAssignedToChange,
   onStatusChange,
   onAccountChange,
   onDelete,
   onDateChange,
   onPayeeChange,
   onAmountChange,
   onAddIncome,
   onAddExpense,
   onCategoryCreated,
   TRANSACTION_STATUS,
   currentCategoryCellRef,
   loading,
   searchLoading,
   editingTransaction,
   onCategoryClick,
   pagination,
   onPageChange,
}) => {
   const [showCategoryModal, setShowCategoryModal] = useState(false);
   const [showStatusDropdown, setShowStatusDropdown] = useState(null);
   const [showAccountDropdown, setShowAccountDropdown] = useState(null);
   const [editingPayee, setEditingPayee] = useState(null);
   const [editingAmount, setEditingAmount] = useState(null);
   const [showDatePicker, setShowDatePicker] = useState(null);
   const [dropdownPosition, setDropdownPosition] = useState({
      top: 0,
      left: 0,
      isAbove: false,
   });
   const [deletingId, setDeletingId] = useState(null); // Track which transaction is being deleted
   const [createCategoryModal, setCreateCategoryModal] = useState({
      isOpen: false,
      categoryName: "",
      transactionId: null,
      showBothOptions: true,
   });

   const categoryDropdownRef = useRef(null);
   const statusDropdownRef = useRef(null);
   const accountDropdownRef = useRef(null);
   const payeeInputRef = useRef(null);
   const amountInputRef = useRef(null);

   const getStatusPillColor = TransactionHelpers.getStatusPillColor;

   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            categoryDropdownRef.current &&
            !categoryDropdownRef.current.contains(event.target)
         ) {
            setShowCategoryModal(false);
         }
         if (
            statusDropdownRef.current &&
            !statusDropdownRef.current.contains(event.target)
         ) {
            setShowStatusDropdown(null);
         }
         if (
            accountDropdownRef.current &&
            !accountDropdownRef.current.contains(event.target)
         ) {
            setShowAccountDropdown(null);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, []);

   const handleCategoryClick = (transaction, event) => {
      TransactionEventHandlers.handleCategoryClick(
         transaction,
         event,
         currentCategoryCellRef,
         onCategoryClick,
         setShowCategoryModal,
         setDropdownPosition,
         editingTransaction,
         TransactionHelpers
      );
   };

   const handleAccountClick = (transactionId, event) => {
      TransactionEventHandlers.handleAccountClick(
         transactionId,
         event,
         showAccountDropdown,
         setShowAccountDropdown,
         setDropdownPosition,
         TransactionHelpers
      );
   };

   const handlePayeeClick = (transactionId) => {
      TransactionEventHandlers.handlePayeeClick(transactionId, setEditingPayee);
   };

   const handlePayeeSubmit = (transactionId, value) => {
      if (value.trim()) {
         onPayeeChange(transactionId, "payee", value.trim());
      }
      setEditingPayee(null);
   };

   const handleAmountClick = (transactionId) => {
      TransactionEventHandlers.handleAmountClick(
         transactionId,
         setEditingAmount
      );
   };

   const handleAmountSubmit = (transactionId, value, transaction) => {
      const numericValue = parseFloat(value.replace(/[$,]/g, ""));
      if (!isNaN(numericValue)) {
         const finalAmount =
            transaction.type === "Expense"
               ? -Math.abs(numericValue)
               : Math.abs(numericValue);
         onAmountChange(transactionId, "amount", finalAmount);
      }
      setEditingAmount(null);
   };

   const handleDelete = async (transactionId) => {
      try {
         setDeletingId(transactionId);
         await TransactionEventHandlers.handleDelete(
            transactionId,
            transactions,
            accounts,
            onDelete
         );
      } catch (error) {
         console.error("Error deleting transaction:", error);
      } finally {
         setDeletingId(null);
      }
   };

   const handleCategoryChange = (option) => {
      TransactionEventHandlers.handleCategoryChange(
         editingTransaction,
         option,
         availableAssignedTo,
         onAssignedToChange,
         setShowCategoryModal,
         onCategoryClick,
         currentCategoryCellRef
      );
   };

   const handleDateSubmit = (transactionId, date) => {
      onDateChange(transactionId, "date", date);
      setShowDatePicker(null);
   };

   const handleCreateCategory = (searchTerm, transactionId) => {
      const transaction = transactions.find((t) => t._id === transactionId);
      setCreateCategoryModal({
         isOpen: true,
         categoryName: searchTerm,
         transactionId: transactionId,
         showBothOptions: transaction ? transaction.amount >= 0 : true,
      });
   };

   const handleCreateCategorySave = async (formData, transactionId) => {
      try {
         // Create the expense or income
         let createdCategory;
         if (formData.type === "expense") {
            if (formData.isRecurring) {
               // Create recurring expense
               const recurringExpenseData = {
                  name: formData.name,
                  amount: formData.amount,
                  frequency: "monthly",
                  dueDay: formData.dueDate.getDate().toString(),
                  enabled: true,
               };

               const response = await fetch("/api/user/recurring-expenses", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(recurringExpenseData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create recurring expense");
               }

               // For recurring expenses, we need to create a one-time expense for the current transaction
               const expenseData = {
                  description: formData.name,
                  amountDue: formData.amount,
                  date: formData.dueDate.toISOString(),
                  category: formData.name,
                  status: "pending",
               };

               const expenseResponse = await fetch("/api/expenses", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(expenseData),
               });

               if (!expenseResponse.ok) {
                  throw new Error("Failed to create expense");
               }

               createdCategory = await expenseResponse.json();
            } else {
               // Create one-time expense
               const expenseData = {
                  description: formData.name,
                  amountDue: formData.amount,
                  date: formData.dueDate.toISOString(),
                  category: formData.name,
                  status: "pending",
               };

               const response = await fetch("/api/expenses", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(expenseData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create expense");
               }

               createdCategory = await response.json();
            }
         } else {
            // Create income
            if (formData.isRecurring) {
               // Create recurring income
               const recurringIncomeData = {
                  description: formData.name,
                  payAmount: formData.amount,
                  payPeriod: "monthly",
                  payDay: formData.dueDate.getDate().toString(),
                  enabled: true,
               };

               const response = await fetch("/api/user/recurring-incomes", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(recurringIncomeData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create recurring income");
               }

               // For recurring incomes, we need to create a one-time income for the current transaction
               const incomeData = {
                  description: formData.name,
                  expectedAmount: formData.amount,
                  date: formData.dueDate.toISOString(),
                  category: "Salary",
                  status: "scheduled",
               };

               const incomeResponse = await fetch("/api/incomes", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(incomeData),
               });

               if (!incomeResponse.ok) {
                  throw new Error("Failed to create income");
               }

               createdCategory = await incomeResponse.json();
            } else {
               // Create one-time income
               const incomeData = {
                  description: formData.name,
                  expectedAmount: formData.amount,
                  date: formData.dueDate.toISOString(),
                  category: "Salary",
                  status: "scheduled",
               };

               const response = await fetch("/api/incomes", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(incomeData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create income");
               }

               createdCategory = await response.json();
            }
         }

         // Refresh the available categories first
         if (onCategoryCreated) {
            await onCategoryCreated();
         }

         // Then assign the transaction to the created category, passing the category object directly
         if (onAssignedToChange) {
            onAssignedToChange(
               transactionId,
               createdCategory._id,
               formData.type === "expense" ? "Expense" : "Income",
               createdCategory
            );
         }

         setCreateCategoryModal({
            isOpen: false,
            categoryName: "",
            transactionId: null,
            showBothOptions: true,
         });
      } catch (error) {
         console.error("Error creating category:", error);
         throw error;
      }
   };

   const handleCreateCategoryClose = () => {
      setCreateCategoryModal({
         isOpen: false,
         categoryName: "",
         transactionId: null,
         showBothOptions: true,
      });
   };

   if (loading) {
      return (
         <div className="flex flex-col items-center justify-center py-12">
            <svg
               className="animate-spin h-8 w-8 text-gray-500 mb-4"
               xmlns="http://www.w3.org/2000/svg"
               fill="none"
               viewBox="0 0 24 24"
            >
               <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
               ></circle>
               <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
               ></path>
            </svg>
            <span className="text-gray-500 dark:text-gray-400">
               Loading transactions...
            </span>
         </div>
      );
   }

   if (searchLoading) {
      return (
         <div className="flex justify-center items-center py-12 text-gray-500 dark:text-gray-400">
            <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
               <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
               />
               <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
               />
            </svg>
            Searching...
         </div>
      );
   }

   if (transactions.length === 0) {
      return (
         <div className="flex items-center justify-center py-12">
            <div className="text-center text-gray-500 dark:text-gray-400">
               No transactions found
            </div>
         </div>
      );
   }

   return (
      <div className="flex flex-col h-full">
         <div className="flex-1 min-h-0">
            <div className="space-y-3 px-2 py-2 overflow-auto h-full">
               {transactions.map((transaction) => (
                  <div
                     key={transaction._id}
                     className={`${
                        (transaction.assignedTo === "Unassigned" ||
                           !transaction.assignedTo) &&
                        !transaction.isAdjustment
                           ? "bg-yellow-50 dark:bg-yellow-900/10 border-yellow-200 dark:border-yellow-800"
                           : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                     } rounded-lg border p-4 shadow-sm ${
                        transaction.isAdjustment
                           ? "border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-900/20"
                           : ""
                     }`}
                  >
                     {/* Transaction Header */}
                     <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                           {/* Type Indicator */}
                           <div
                              className={`h-3 w-3 rounded-full ${
                                 transaction.type === "Transfer"
                                    ? "bg-gray-500"
                                    : transaction.type === "Income"
                                    ? "bg-green-500"
                                    : "bg-red-500"
                              }`}
                              title={transaction.type}
                           />

                           {/* Amount */}
                           {editingAmount === transaction._id ? (
                              <input
                                 ref={amountInputRef}
                                 type="text"
                                 defaultValue={Math.abs(
                                    transaction.amount
                                 ).toFixed(2)}
                                 className="text-lg font-semibold bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-24"
                                 autoFocus
                                 onBlur={() => setEditingAmount(null)}
                                 onKeyDown={(e) => {
                                    if (e.key === "Escape") {
                                       setEditingAmount(null);
                                    } else if (e.key === "Enter") {
                                       handleAmountSubmit(
                                          transaction._id,
                                          e.target.value,
                                          transaction
                                       );
                                    }
                                 }}
                              />
                           ) : (
                              <button
                                 onClick={() =>
                                    handleAmountClick(transaction._id)
                                 }
                                 className={`text-lg font-semibold hover:text-gray-600 dark:hover:text-gray-400 transition-colors ${
                                    transaction.amount >= 0
                                       ? "text-green-600 dark:text-green-400"
                                       : "text-red-600 dark:text-red-400"
                                 }`}
                              >
                                 {transaction.amount >= 0 ? "+" : ""}$
                                 {Math.abs(transaction.amount).toLocaleString(
                                    "en-US",
                                    {
                                       minimumFractionDigits: 2,
                                       maximumFractionDigits: 2,
                                    }
                                 )}
                              </button>
                           )}
                        </div>

                        {/* Status Badge */}
                        <DropdownMenu>
                           <DropdownMenuTrigger asChild>
                              <button
                                 className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200 ${getStatusPillColor(
                                    transaction.status
                                 )}`}
                              >
                                 {(transaction.status || "pending")
                                    .charAt(0)
                                    .toUpperCase() +
                                    (transaction.status || "pending").slice(1)}
                                 <ChevronDownIcon className="ml-1 h-3 w-3" />
                              </button>
                           </DropdownMenuTrigger>
                           <DropdownMenuContent align="center" className="w-32">
                              {TRANSACTION_STATUS.map((status) => (
                                 <DropdownMenuItem
                                    key={status}
                                    onClick={() =>
                                       onStatusChange(transaction._id, status)
                                    }
                                    className={`text-sm cursor-pointer ${
                                       transaction.status === status
                                          ? "bg-gray-50 dark:bg-gray-900/50 text-gray-900 dark:text-gray-100"
                                          : "text-gray-700 dark:text-gray-300"
                                    }`}
                                 >
                                    {status.charAt(0).toUpperCase() +
                                       status.slice(1)}
                                 </DropdownMenuItem>
                              ))}
                           </DropdownMenuContent>
                        </DropdownMenu>
                     </div>

                     {/* Payee */}
                     <div className="mb-3">
                        {editingPayee === transaction._id ? (
                           <input
                              ref={payeeInputRef}
                              type="text"
                              defaultValue={transaction.payee}
                              className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm"
                              autoFocus
                              onBlur={(e) =>
                                 handlePayeeSubmit(
                                    transaction._id,
                                    e.target.value
                                 )
                              }
                              onKeyDown={(e) => {
                                 if (e.key === "Escape") {
                                    setEditingPayee(null);
                                 } else if (e.key === "Enter") {
                                    handlePayeeSubmit(
                                       transaction._id,
                                       e.target.value
                                    );
                                 }
                              }}
                           />
                        ) : (
                           <button
                              onClick={() => handlePayeeClick(transaction._id)}
                              className="text-sm font-medium text-gray-900 dark:text-gray-100 hover:text-gray-600 dark:hover:text-gray-400 transition-colors truncate w-full text-left"
                           >
                              {transaction.payee}
                           </button>
                        )}
                     </div>

                     {/* Account & Category */}
                     <div className="grid grid-cols-2 gap-3 text-sm mb-3">
                        {/* Account */}
                        <div className="flex flex-col">
                           <span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                              Account
                           </span>
                           <div className="relative">
                              <button
                                 onClick={(e) =>
                                    handleAccountClick(transaction._id, e)
                                 }
                                 className={`inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                                    showAccountDropdown === transaction._id
                                       ? "bg-gray-100 dark:bg-gray-700 ring-2 ring-blue-500 dark:ring-blue-400"
                                       : ""
                                 }`}
                              >
                                 <span className="truncate max-w-[100px]">
                                    {transaction.accountId
                                       ? accounts.find(
                                            (a) =>
                                               a._id === transaction.accountId
                                         )?.name || "Unknown"
                                       : "Unassigned"}
                                 </span>
                                 <ChevronDownIcon className="ml-1 h-3 w-3 text-gray-600 dark:text-gray-300" />
                              </button>
                           </div>
                        </div>

                        {/* Category - Hide for transfers */}
                        {transaction.type !== "Transfer" && (
                           <div className="flex flex-col">
                              <span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                 Category
                              </span>
                              {transaction.isAdjustment ? (
                                 <div className="px-2.5 py-1 text-xs text-gray-500 dark:text-gray-400 italic border border-gray-200 dark:border-gray-700 rounded-md">
                                    Balance adjustment
                                 </div>
                              ) : (
                                 <div className="relative">
                                    <button
                                       onClick={(e) =>
                                          handleCategoryClick(transaction, e)
                                       }
                                       className={`inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                                          editingTransaction?._id ===
                                          transaction._id
                                             ? "bg-gray-100 dark:bg-gray-700 ring-2 ring-blue-500 dark:ring-blue-400"
                                             : ""
                                       }`}
                                    >
                                       <span className="truncate max-w-[100px]">
                                          {transaction.assignedTo ||
                                             "Unassigned"}
                                       </span>
                                       <ChevronDownIcon className="ml-1 h-3 w-3 text-gray-600 dark:text-gray-300" />
                                    </button>
                                 </div>
                              )}
                           </div>
                        )}
                     </div>

                     {/* Delete button for transfer transactions */}
                     {transaction.type === "Transfer" && (
                        <div className="mt-3 flex justify-end">
                           <button
                              onClick={() => handleDelete(transaction._id)}
                              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                              disabled={deletingId === transaction._id}
                           >
                              {deletingId === transaction._id ? (
                                 <svg
                                    className="animate-spin h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                 >
                                    <circle
                                       className="opacity-25"
                                       cx="12"
                                       cy="12"
                                       r="10"
                                       stroke="currentColor"
                                       strokeWidth="4"
                                    ></circle>
                                    <path
                                       className="opacity-75"
                                       fill="currentColor"
                                       d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                 </svg>
                              ) : (
                                 <TrashIcon className="h-4 w-4" />
                              )}
                           </button>
                        </div>
                     )}
                  </div>
               ))}
            </div>
         </div>

         <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
            <PaginationControls
               pagination={pagination}
               onPageChange={onPageChange}
            />
         </div>

         <DropdownPortals
            showCategoryModal={showCategoryModal}
            editingTransaction={editingTransaction}
            availableAssignedTo={availableAssignedTo}
            dropdownPosition={dropdownPosition}
            categoryDropdownRef={categoryDropdownRef}
            handleCategoryChange={handleCategoryChange}
            onAddIncome={onAddIncome}
            onAddExpense={onAddExpense}
            showStatusDropdown={showStatusDropdown}
            statusDropdownRef={statusDropdownRef}
            transactions={transactions}
            TRANSACTION_STATUS={TRANSACTION_STATUS}
            onStatusChange={onStatusChange}
            showAccountDropdown={showAccountDropdown}
            accountDropdownRef={accountDropdownRef}
            accounts={accounts}
            onAccountChange={onAccountChange}
            currentCategoryCellRef={currentCategoryCellRef}
            onCategoryClick={onCategoryClick}
            setShowStatusDropdown={setShowStatusDropdown}
            setShowAccountDropdown={setShowAccountDropdown}
         />

         <CreateCategoryModal
            isOpen={createCategoryModal.isOpen}
            onClose={handleCreateCategoryClose}
            onSave={handleCreateCategorySave}
            categoryName={createCategoryModal.categoryName}
            transactionId={createCategoryModal.transactionId}
            transactionAmount={
               createCategoryModal.transactionId
                  ? transactions.find(
                       (t) => t._id === createCategoryModal.transactionId
                    )?.amount || 0
                  : 0
            }
            showBothOptions={createCategoryModal.showBothOptions}
         />
      </div>
   );
};

export default MobileTransactionList;

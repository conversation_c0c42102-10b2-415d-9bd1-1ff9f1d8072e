"use client";
import { useState, useEffect } from "react";
import { Combobox } from "../ui/combobox";
import { Trash2, X, Save } from "lucide-react";
import {
   isCashAccount,
   parseTimezoneDate,
} from "@/app/lib/utils/transactionsUtils";

const EditTransactionForm = ({
   initialData,
   onSuccess,
   onClose,
   onDelete,
   accounts = [],
   availableAssignedTo = [],
   TRANSACTION_STATUS,
}) => {
   const [formData, setFormData] = useState(initialData);
   const [filteredCategories, setFilteredCategories] = useState([]);

   // Set initial form data
   useEffect(() => {
      if (initialData) {
         setFormData(initialData);
      }
   }, [initialData]);

   useEffect(() => {
      if (!formData || !Array.isArray(availableAssignedTo)) {
         return;
      }

      // Filter categories based on transaction type
      const filtered = availableAssignedTo.filter((category) => {
         if (formData.type === "Income") {
            return category.expectedAmount > 0;
         }
         if (formData.type === "Expense") {
            return !category.expectedAmount || category.expectedAmount === 0;
         }
         return false; // For Transfer type, no categories
      });

      // Deduplicate filtered categories by _id
      const deduplicatedFiltered = filtered.filter(
         (category, index, self) =>
            self.findIndex(
               (c) => c._id?.toString() === category._id?.toString()
            ) === index
      );

      setFilteredCategories(deduplicatedFiltered);
   }, [formData?.type, availableAssignedTo]);

   const handleSubmit = (e) => {
      e.preventDefault();
      onSuccess(formData);
   };

   // Get the current category value for the Combobox
   const selectedCategory = filteredCategories.find(
      (c) =>
         c._id === formData?.assignedTo ||
         c.description === formData?.assignedTo
   );
   const categoryValue =
      formData?.assignedTo && selectedCategory
         ? {
              value: selectedCategory._id,
              label: selectedCategory.description,
              description: selectedCategory.date
                 ? `${new Date(selectedCategory.date).toLocaleDateString(
                      "en-US",
                      {
                         year: "numeric",
                         month: "short",
                         day: "numeric",
                      }
                   )} • ${
                      selectedCategory.expectedAmount
                         ? `Received: $${(
                              selectedCategory.receivedAmount || 0
                           ).toFixed(2)}`
                         : `Available: $${(
                              selectedCategory.amountAvailable || 0
                           ).toFixed(2)}`
                   }`
                 : "",
           }
         : { value: "", label: "Unassigned" };

   return (
      <form onSubmit={handleSubmit} className="space-y-4">
         <div>
            <label className="block text-sm font-medium mb-2">Type</label>
            <div className="flex rounded-lg border border-gray-300 dark:border-gray-600 p-1 gap-1">
               <button
                  type="button"
                  onClick={() => setFormData({ ...formData, type: "Income" })}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                     formData.type === "Income"
                        ? "bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
               >
                  Income
               </button>
               <button
                  type="button"
                  onClick={() => setFormData({ ...formData, type: "Expense" })}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                     formData.type === "Expense"
                        ? "bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
               >
                  Expense
               </button>
               <button
                  type="button"
                  onClick={() => setFormData({ ...formData, type: "Transfer" })}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                     formData.type === "Transfer"
                        ? "bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
               >
                  Transfer
               </button>
            </div>
         </div>

         <div>
            <label className="block text-sm font-medium mb-1">Amount</label>
            <div className="relative">
               <input
                  type="number"
                  step="0.01"
                  value={Math.abs(formData.amount)}
                  onChange={(e) =>
                     setFormData({
                        ...formData,
                        amount:
                           formData.type === "Expense"
                              ? -Math.abs(parseFloat(e.target.value))
                              : Math.abs(parseFloat(e.target.value)),
                     })
                  }
                  className="w-full p-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-right"
                  required
               />
               <span className="absolute right-8 top-2 text-gray-500">$</span>
            </div>
         </div>

         <div>
            <label className="block text-sm font-medium mb-1">Payee</label>
            <input
               type="text"
               value={formData.payee}
               onChange={(e) =>
                  setFormData({ ...formData, payee: e.target.value })
               }
               className="w-full p-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
               required
            />
         </div>

         <div>
            <label className="block text-sm font-medium mb-1">Category</label>
            <Combobox
               options={[
                  ...(selectedCategory && selectedCategory._id
                     ? [
                          {
                             value: selectedCategory._id,
                             label: selectedCategory.description,
                             description: selectedCategory.date
                                ? `${new Date(
                                     selectedCategory.date
                                  ).toLocaleDateString("en-US", {
                                     year: "numeric",
                                     month: "short",
                                     day: "numeric",
                                  })} • ${
                                     selectedCategory.expectedAmount
                                        ? `Received: $${(
                                             selectedCategory.receivedAmount ||
                                             0
                                          ).toFixed(2)}`
                                        : `Available: $${(
                                             selectedCategory.amountAvailable ||
                                             0
                                          ).toFixed(2)}`
                                  }`
                                : "",
                          },
                       ]
                     : []),
                  { value: "", label: "Unassigned" },
                  ...filteredCategories
                     .filter(
                        (category) => category._id !== selectedCategory?._id
                     )
                     .map((category) => ({
                        value: category._id,
                        label: category.description,
                        description: category.date
                           ? `${new Date(category.date).toLocaleDateString(
                                "en-US",
                                {
                                   year: "numeric",
                                   month: "short",
                                   day: "numeric",
                                }
                             )} • ${
                                category.expectedAmount
                                   ? `Received: $${(
                                        category.receivedAmount || 0
                                     ).toFixed(2)}`
                                   : `Available: $${(
                                        category.amountAvailable || 0
                                     ).toFixed(2)}`
                             }`
                           : "",
                     })),
               ]}
               value={categoryValue}
               onChange={(option) => {
                  setFormData({
                     ...formData,
                     category: option.value || null,
                     assignedToType: option.value
                        ? availableAssignedTo.find(
                             (c) => c._id === option.value
                          )?.expectedAmount
                           ? "Income"
                           : "Expense"
                        : null,
                  });
               }}
               placeholder="Select category..."
               className="min-w-[250px]"
            />
         </div>

         <div>
            <label className="block text-sm font-medium mb-1">Date</label>
            <input
               type="date"
               value={(() => {
                  // Parse the date string to avoid timezone issues
                  const dateStr = formData.date;
                  if (dateStr.includes && dateStr.includes("T")) {
                     // ISO string - extract date components
                     return dateStr.split("T")[0];
                  } else {
                     // Regular date - convert to YYYY-MM-DD format
                     return parseTimezoneDate(dateStr)
                        .toISOString()
                        .split("T")[0];
                  }
               })()}
               onChange={(e) => {
                  // Create date at noon to avoid timezone issues
                  const [year, month, day] = e.target.value
                     .split("-")
                     .map(Number);
                  const localDate = new Date(year, month - 1, day, 12, 0, 0);
                  setFormData({ ...formData, date: localDate });
               }}
               className="w-full p-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
               required
            />
         </div>

         <div>
            <label className="block text-sm font-medium mb-1">Account</label>
            <select
               value={formData.accountId || ""}
               onChange={(e) =>
                  setFormData({ ...formData, accountId: e.target.value })
               }
               className="w-full p-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
               <option value="">Unassigned</option>
               {accounts
                  .filter(
                     (account) =>
                        isCashAccount(account) ||
                        account.accountType === "credit"
                  )
                  .map((account) => (
                     <option key={account._id} value={account._id}>
                        {account.name}
                     </option>
                  ))}
            </select>
         </div>

         <div>
            <label className="block text-sm font-medium mb-1">Status</label>
            <select
               value={formData.status}
               onChange={(e) =>
                  setFormData({ ...formData, status: e.target.value })
               }
               className="w-full p-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
               {TRANSACTION_STATUS.map((status) => (
                  <option key={status} value={status}>
                     {status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
               ))}
            </select>
         </div>

         <div className="flex gap-2 mt-4 w-full">
            <button
               type="submit"
               className="w-full py-2 text-sm font-medium rounded-md transition-all duration-150 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 flex items-center justify-center gap-1.5"
            >
               <Save className="w-4 h-4" />
               Save
            </button>
            <button
               type="button"
               onClick={onClose}
               className="w-full py-2 text-sm font-medium rounded-md transition-all duration-150 border border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50 text-gray-700 hover:text-gray-900 focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 flex items-center justify-center gap-1.5"
            >
               <X className="w-4 h-4" />
               Cancel
            </button>
         </div>
      </form>
   );
};

export default EditTransactionForm;

"use client";

import { useState, useRef, useEffect } from "react";
import { TransactionHelpers } from "../../lib/utils/transactionsUtils";
import { TransactionsUtils } from "../../lib/utils/transactionsUtils";
import { TransactionEventHandlers } from "../../lib/utils/transactionEventHandlers";
import { TransactionTableHeader } from "./TransactionTableHeader";
import { TransactionRow } from "./TransactionRow";
import { PaginationControls } from "./PaginationControls";
import { DropdownPortals } from "./DropdownPortals";

const TransactionList = ({
   transactions,
   availableAssignedTo,
   accounts,
   onAssignedToChange,
   onStatusChange,
   onAccountChange,
   onDelete,
   onDateChange,
   onPayeeChange,
   onAmountChange,
   onAddIncome,
   onAddExpense,
   onCategoryCreated,
   TRANSACTION_STATUS,
   currentCategoryCellRef,
   selectedAccountFilter,
   loading,
   searchLoading,
   editingTransaction,
   onCategoryClick,
   pagination,
   onPageChange,
   transactionAssignedToOptions,
}) => {
   const [showCategoryModal, setShowCategoryModal] = useState(false);
   const [showStatusDropdown, setShowStatusDropdown] = useState(null);
   const [showAccountDropdown, setShowAccountDropdown] = useState(null);
   const [editingPayee, setEditingPayee] = useState(null);
   const [editingAmount, setEditingAmount] = useState(null);
   const [showDatePicker, setShowDatePicker] = useState(null);
   const [dropdownPosition, setDropdownPosition] = useState({
      top: 0,
      left: 0,
      isAbove: false,
   });
   const [deletingId, setDeletingId] = useState(null); // Track which transaction is being deleted

   const categoryDropdownRef = useRef(null);
   const statusDropdownRef = useRef(null);
   const accountDropdownRef = useRef(null);

   // Use the shared helper from TransactionHelpers
   const getStatusPillColor = TransactionHelpers.getStatusPillColor;

   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            categoryDropdownRef.current &&
            !categoryDropdownRef.current.contains(event.target)
         ) {
            setShowCategoryModal(false);
         }
         if (
            statusDropdownRef.current &&
            !statusDropdownRef.current.contains(event.target)
         ) {
            setShowStatusDropdown(null);
         }
         if (
            accountDropdownRef.current &&
            !accountDropdownRef.current.contains(event.target)
         ) {
            setShowAccountDropdown(null);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, []);

   const handleCategoryClick = (transaction, event) => {
      TransactionEventHandlers.handleCategoryClick(
         transaction,
         event,
         currentCategoryCellRef,
         onCategoryClick,
         setShowCategoryModal,
         setDropdownPosition,
         editingTransaction,
         TransactionHelpers
      );
   };

   const handleStatusClick = (transactionId, event) => {
      TransactionEventHandlers.handleStatusClick(
         transactionId,
         event,
         showStatusDropdown,
         setShowStatusDropdown,
         setDropdownPosition,
         TransactionHelpers
      );
   };

   const handleAccountClick = (transactionId, event) => {
      TransactionEventHandlers.handleAccountClick(
         transactionId,
         event,
         showAccountDropdown,
         setShowAccountDropdown,
         setDropdownPosition,
         TransactionHelpers
      );
   };

   const handleDateClick = (transactionId, event) => {
      TransactionEventHandlers.handleDateClick(
         transactionId,
         event,
         showDatePicker,
         setShowDatePicker,
         setDropdownPosition,
         TransactionHelpers
      );
   };

   const handlePayeeClick = (transactionId) => {
      TransactionEventHandlers.handlePayeeClick(transactionId, setEditingPayee);
   };

   const handlePayeeSubmit = (transactionId, value) => {
      if (value.trim()) {
         onPayeeChange(transactionId, "payee", value.trim());
      }
      setEditingPayee(null);
   };

   const handleAmountClick = (transactionId) => {
      TransactionEventHandlers.handleAmountClick(
         transactionId,
         setEditingAmount
      );
   };

   const handleAmountSubmit = (transactionId, value, transaction) => {
      const numericValue = parseFloat(value.replace(/[$,]/g, ""));
      if (!isNaN(numericValue)) {
         const finalAmount =
            transaction.type === "Expense"
               ? -Math.abs(numericValue)
               : Math.abs(numericValue);
         onAmountChange(transactionId, "amount", finalAmount);
      }
      setEditingAmount(null);
   };

   const handleDelete = async (transactionId) => {
      try {
         setDeletingId(transactionId);
         await TransactionEventHandlers.handleDelete(
            transactionId,
            transactions,
            accounts,
            onDelete
         );
      } catch (error) {
         console.error("Error deleting transaction:", error);
      } finally {
         setDeletingId(null);
      }
   };

   const handleCategoryChange = (option) => {
      TransactionEventHandlers.handleCategoryChange(
         editingTransaction,
         option,
         availableAssignedTo,
         onAssignedToChange,
         setShowCategoryModal,
         onCategoryClick,
         currentCategoryCellRef
      );
   };

   const handleDateSubmit = (transactionId, date) => {
      onDateChange(transactionId, "date", date);
      setShowDatePicker(null);
   };

   const getCategoryDescription =
      TransactionEventHandlers.getCategoryDescription;

   return (
      <div className="flex flex-col h-full">
         <div className="flex-1 min-h-0 relative">
            <div className="absolute inset-0 overflow-auto">
               <table className="w-full divide-y table-auto divide-gray-200 dark:divide-gray-700">
                  <TransactionTableHeader
                     selectedAccountFilter={selectedAccountFilter}
                  />
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                     {loading ? (
                        <tr>
                           <td
                              colSpan={
                                 selectedAccountFilter === "all" ? "8" : "7"
                              }
                              className="px-4 py-4"
                           >
                              <div className="flex flex-col items-center justify-center gap-2">
                                 <svg
                                    className="animate-spin h-8 w-8 text-gray-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                 >
                                    <circle
                                       className="opacity-25"
                                       cx="12"
                                       cy="12"
                                       r="10"
                                       stroke="currentColor"
                                       strokeWidth="4"
                                    ></circle>
                                    <path
                                       className="opacity-75"
                                       fill="currentColor"
                                       d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                 </svg>
                                 <span className="text-gray-500 dark:text-gray-400">
                                    Loading transactions...
                                 </span>
                              </div>
                           </td>
                        </tr>
                     ) : searchLoading ? (
                        <tr>
                           <td
                              colSpan={
                                 selectedAccountFilter === "all" ? "8" : "7"
                              }
                              className="px-4 py-4"
                           >
                              <div className="flex justify-center items-center text-gray-500 dark:text-gray-400">
                                 <svg
                                    className="animate-spin h-5 w-5 mr-3"
                                    viewBox="0 0 24 24"
                                 >
                                    <circle
                                       className="opacity-25"
                                       cx="12"
                                       cy="12"
                                       r="10"
                                       stroke="currentColor"
                                       strokeWidth="4"
                                       fill="none"
                                    />
                                    <path
                                       className="opacity-75"
                                       fill="currentColor"
                                       d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    />
                                 </svg>
                                 Searching...
                              </div>
                           </td>
                        </tr>
                     ) : transactions.length === 0 ? (
                        <tr>
                           <td
                              colSpan={
                                 selectedAccountFilter === "all" &&
                                 accounts.length > 1
                                    ? "8"
                                    : "7"
                              }
                              className="px-4 py-4"
                           >
                              <div className="text-center text-gray-500 dark:text-gray-400">
                                 No transactions found
                              </div>
                           </td>
                        </tr>
                     ) : (
                        transactions.map((transaction) => (
                           <TransactionRow
                              key={transaction._id}
                              transaction={transaction}
                              selectedAccountFilter={selectedAccountFilter}
                              accounts={accounts}
                              availableAssignedTo={availableAssignedTo}
                              editingTransaction={editingTransaction}
                              showStatusDropdown={showStatusDropdown}
                              showAccountDropdown={showAccountDropdown}
                              showDatePicker={showDatePicker}
                              editingPayee={editingPayee}
                              editingAmount={editingAmount}
                              onHandlers={{
                                 handleDateClick,
                                 handleAccountClick,
                                 handlePayeeClick,
                                 handlePayeeSubmit,
                                 handleCategoryClick,
                                 handleAssignedToChange: onAssignedToChange,
                                 handleStatusClick,
                                 onStatusChange,
                                 handleAmountClick,
                                 handleAmountSubmit,
                                 handleDelete,
                                 handleDateSubmit,
                                 onPayeeChange,
                                 onCategoryCreated,
                                 setEditingPayee,
                                 setEditingAmount,
                              }}
                              TRANSACTION_STATUS={TRANSACTION_STATUS}
                              getStatusPillColor={getStatusPillColor}
                              assignedToOptions={
                                 transactionAssignedToOptions?.get(
                                    transaction._id
                                 ) || []
                              }
                           />
                        ))
                     )}
                  </tbody>
               </table>
            </div>
         </div>

         <div className="mt-auto">
            <PaginationControls
               pagination={pagination}
               onPageChange={onPageChange}
            />
         </div>

         <DropdownPortals
            showCategoryModal={showCategoryModal}
            editingTransaction={editingTransaction}
            availableAssignedTo={availableAssignedTo}
            dropdownPosition={dropdownPosition}
            categoryDropdownRef={categoryDropdownRef}
            handleCategoryChange={handleCategoryChange}
            onAddIncome={onAddIncome}
            onAddExpense={onAddExpense}
            getCategoryDescription={getCategoryDescription}
            showStatusDropdown={showStatusDropdown}
            statusDropdownRef={statusDropdownRef}
            transactions={transactions}
            TRANSACTION_STATUS={TRANSACTION_STATUS}
            onStatusChange={onStatusChange}
            showAccountDropdown={showAccountDropdown}
            accountDropdownRef={accountDropdownRef}
            accounts={accounts}
            onAccountChange={onAccountChange}
            currentCategoryCellRef={currentCategoryCellRef}
            onCategoryClick={onCategoryClick}
            setShowStatusDropdown={setShowStatusDropdown}
            setShowAccountDropdown={setShowAccountDropdown}
         />
      </div>
   );
};

export default TransactionList;

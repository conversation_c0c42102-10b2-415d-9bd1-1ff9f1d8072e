"use client";

export const TransactionTableHeader = ({ selectedAccountFilter }) => {
   return (
      <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0 z-[1] border-b border-gray-200 dark:border-gray-700">
         <tr>
            <th className="px-1 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-3"></th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left w-[100px] whitespace-nowrap">
               Date
            </th>
            {selectedAccountFilter === "all" && (
               <th className="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">
                  Account
               </th>
            )}
            <th className="px-0 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left w-20 whitespace-nowrap">
               Status
            </th>
            <th className="px-6 pr-0 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left w-32">
               Assigned To
            </th>
            <th className="px-6 pl-2 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">
               Payee
            </th>
            <th className="pl-6 pr-2 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-right whitespace-nowrap">
               Amount
            </th>
            <th className="pl-2 pr-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-right w-12"></th>
         </tr>
      </thead>
   );
};

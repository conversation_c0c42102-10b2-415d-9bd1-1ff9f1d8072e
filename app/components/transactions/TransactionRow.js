"use client";

import { useRef, useState } from "react";
import { ChevronDownIcon, LinkIcon } from "@heroicons/react/24/outline";
import { Combobox } from "../ui/combobox";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "../../../components/ui/dropdown-menu";
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from "../../../components/ui/popover";
import { Calendar } from "../../../components/ui/calendar";
import { Button } from "../../../components/ui/button";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import CreateCategoryModal from "./CreateCategoryModal";
import { parseTimezoneDate } from "../../lib/utils/transactionsUtils";

export const TransactionRow = ({
   transaction,
   selectedAccountFilter,
   accounts,
   availableAssignedTo,
   editingTransaction,
   showStatusDropdown,
   showAccountDropdown,
   showDatePicker,
   editingPayee,
   editingAmount,
   onHandlers,
   TRANSACTION_STATUS,
   getStatusPillColor,
   assignedToOptions,
}) => {
   const payeeInputRef = useRef(null);
   const amountInputRef = useRef(null);
   const [showPlaidTooltip, setShowPlaidTooltip] = useState(false);
   const [datePickerOpen, setDatePickerOpen] = useState(false);
   const [deletingId, setDeletingId] = useState(null); // Track which transaction is being deleted
   const [createAssignedToModal, setCreateAssignedToModal] = useState({
      isOpen: false,
      assignedToName: "",
      showBothOptions: true,
   });

   const handleStatusChange = (newStatus) => {
      if (onHandlers.onStatusChange) {
         onHandlers.onStatusChange(transaction._id, newStatus);
      }
   };

   const handleCreateAssignedTo = (searchTerm) => {
      setCreateAssignedToModal({
         isOpen: true,
         assignedToName: searchTerm,
         showBothOptions: transaction.amount >= 0,
      });
   };

   const handleCreateAssignedToSave = async (formData, transactionId) => {
      try {
         console.log("=== DEBUG: Form data received ===", formData);
         console.log("Frequency:", formData.frequency);
         console.log("WeeklyChargeType:", formData.weeklyChargeType);
         console.log("Type:", formData.type);
         console.log("IsRecurring:", formData.isRecurring);

         // Create the expense or income
         let createdAssignedTo;
         if (formData.type === "expense") {
            if (formData.isRecurring) {
               // Create recurring expense
               const recurringExpenseData = {
                  name: formData.name,
                  amount: formData.amount,
                  frequency: formData.frequency,
                  dueDay:
                     formData.frequency === "weekly"
                        ? formData.payDayOfWeek
                        : formData.dueMonth,
                  enabled: true,
                  // Add weeklyChargeType for weekly expenses
                  ...(formData.frequency === "weekly" && {
                     weeklyChargeType: formData.weeklyChargeType,
                  }),
               };

               // Add dueMonth for annually recurring expenses
               if (formData.frequency === "annually") {
                  // For annual expenses: dueDay is the month (1-12), dueMonth is the day (1-31)
                  recurringExpenseData.dueDay = formData.dueMonth; // Month (1-12)
                  recurringExpenseData.dueMonth = "1"; // Day (1-31) - default to 1st since we don't collect day in modal
               }

               console.log(
                  "Creating recurring expense with data:",
                  recurringExpenseData
               );

               const response = await fetch("/api/user/recurring-expenses", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(recurringExpenseData),
               });

               if (!response.ok) {
                  const errorData = await response.json();
                  console.error(
                     "Failed to create recurring expense:",
                     errorData
                  );
                  throw new Error("Failed to create recurring expense");
               }

               const recurringResult = await response.json();
               console.log(
                  "Recurring expense created successfully:",
                  recurringResult
               );

               // Now create a scheduled expense that aligns with the recurring schedule
               // Calculate the due date based on the current period, not the next future occurrence
               let nextDueDate = new Date();

               console.log(
                  "Calculating due date for current period, frequency:",
                  formData.frequency
               );

               if (formData.frequency === "weekly") {
                  // For weekly expenses, schedule based on the recurring pattern day
                  const targetDay = parseInt(formData.payDayOfWeek); // 1 = Monday, 7 = Sunday
                  const today = new Date();
                  const currentDay = today.getDay() === 0 ? 7 : today.getDay(); // Convert Sunday (0) to 7

                  // Calculate days to the target day
                  let daysToTarget = targetDay - currentDay;

                  // For weekly recurring expenses, we want to schedule for the current week's occurrence
                  // If the target day has already passed this week, schedule for that day in the current week
                  if (daysToTarget < 0) {
                     // Target day already passed this week, schedule for that day in the current week
                     nextDueDate.setDate(today.getDate() + daysToTarget);
                  } else if (daysToTarget === 0) {
                     // Target day is today
                     nextDueDate = new Date(today);
                  } else {
                     // Target day is later this week
                     nextDueDate.setDate(today.getDate() + daysToTarget);
                  }

                  console.log(
                     "Weekly: target day",
                     targetDay,
                     "current day",
                     currentDay,
                     "days to target",
                     daysToTarget,
                     "due date",
                     nextDueDate
                  );
               } else if (formData.frequency === "monthly") {
                  // For monthly expenses, use the current month's specified day
                  const targetDay = parseInt(formData.dueMonth);
                  const today = new Date();

                  // Try to set to the target day of the current month
                  const currentMonth = today.getMonth();
                  const currentYear = today.getFullYear();

                  // Get the last day of the current month to handle edge cases
                  const lastDayOfMonth = new Date(
                     currentYear,
                     currentMonth + 1,
                     0
                  ).getDate();
                  const adjustedTargetDay = Math.min(targetDay, lastDayOfMonth);

                  nextDueDate = new Date(
                     currentYear,
                     currentMonth,
                     adjustedTargetDay
                  );

                  // If the target day has already passed this month, still use current month
                  // This allows for creating expenses for the current period
                  console.log(
                     "Monthly: target day",
                     targetDay,
                     "adjusted day",
                     adjustedTargetDay,
                     "due date",
                     nextDueDate
                  );
               } else if (formData.frequency === "quarterly") {
                  // For quarterly expenses, use the current quarter's specified day
                  const targetDay = parseInt(formData.dueMonth);
                  const today = new Date();

                  // Find the current quarter's month
                  const currentMonth = today.getMonth();
                  const quarterStartMonth = Math.floor(currentMonth / 3) * 3;

                  const currentYear = today.getFullYear();
                  const lastDayOfQuarterMonth = new Date(
                     currentYear,
                     quarterStartMonth + 1,
                     0
                  ).getDate();
                  const adjustedTargetDay = Math.min(
                     targetDay,
                     lastDayOfQuarterMonth
                  );

                  nextDueDate = new Date(
                     currentYear,
                     quarterStartMonth,
                     adjustedTargetDay
                  );

                  console.log(
                     "Quarterly: target day",
                     targetDay,
                     "quarter start month",
                     quarterStartMonth,
                     "due date",
                     nextDueDate
                  );
               } else if (formData.frequency === "annually") {
                  // For annual expenses, use the current year's specified month and day
                  // Note: We store dueDay as month (1-12) and dueMonth as day (1-31)
                  const targetMonth = parseInt(formData.dueMonth) - 1; // Month is 0-indexed in JavaScript
                  const targetDay = 1; // Default to 1st since we don't collect day for annual in the modal
                  const today = new Date();
                  const currentYear = today.getFullYear();

                  // Create date for the current year
                  nextDueDate = new Date(currentYear, targetMonth, targetDay);

                  // Validate the date (handles Feb 29 on non-leap years, etc.)
                  if (nextDueDate.getMonth() !== targetMonth) {
                     const lastDayOfMonth = new Date(
                        currentYear,
                        targetMonth + 1,
                        0
                     ).getDate();
                     nextDueDate = new Date(
                        currentYear,
                        targetMonth,
                        Math.min(targetDay, lastDayOfMonth)
                     );
                  }

                  console.log(
                     "Annually: target month",
                     targetMonth + 1, // Show 1-12 for readability
                     "target day",
                     targetDay,
                     "due date",
                     nextDueDate
                  );
               } else if (formData.frequency === "biweekly") {
                  // For biweekly expenses, use today as the starting point
                  const today = new Date();
                  nextDueDate = new Date(today);
                  console.log("Biweekly: due date set to today", nextDueDate);
               }

               console.log("Final calculated next due date:", nextDueDate);

               // Create the base expense data
               const expenseData = {
                  description: formData.name,
                  amountDue: formData.amount,
                  date: nextDueDate.toISOString(),
                  category: formData.name,
                  status: "scheduled",
                  frequency: formData.frequency,
                  recurringExpenseId: recurringResult.id, // Link to the recurring expense
               };

               // Add weekly-specific properties for weekly expenses
               if (formData.frequency === "weekly") {
                  expenseData.weeklyChargeType = formData.weeklyChargeType;

                  if (formData.weeklyChargeType === "spread") {
                     // For spread weekly expenses, calculate the week period based on the recurring pattern
                     const targetDay = parseInt(formData.payDayOfWeek); // 1 = Monday, 7 = Sunday
                     const today = new Date();
                     const currentDay =
                        today.getDay() === 0 ? 7 : today.getDay(); // Convert Sunday (0) to 7

                     // Calculate the start of the week (the designated day)
                     let weekStart = new Date(today);
                     let daysToWeekStart = targetDay - currentDay;

                     // If the target day has already passed this week, use that day from the current week
                     if (daysToWeekStart < 0) {
                        // Target day already passed this week, go back to that day
                        weekStart.setDate(today.getDate() + daysToWeekStart);
                     } else if (daysToWeekStart === 0) {
                        // Target day is today
                        weekStart = new Date(today);
                     } else {
                        // Target day is later this week
                        weekStart.setDate(today.getDate() + daysToWeekStart);
                     }

                     // Calculate the end of the week (6 days after start)
                     const weekEnd = new Date(weekStart);
                     weekEnd.setDate(weekStart.getDate() + 6);

                     expenseData.startDate = weekStart.toISOString();
                     expenseData.endDate = weekEnd.toISOString();

                     console.log(
                        "Spread weekly: target day",
                        targetDay,
                        "current day",
                        currentDay,
                        "week start",
                        weekStart,
                        "week end",
                        weekEnd
                     );
                  } else {
                     // For one-time weekly expenses, startDate is the due date
                     expenseData.startDate = nextDueDate.toISOString();
                  }
               }

               console.log(
                  "Creating scheduled expense with data:",
                  expenseData
               );

               const expenseResponse = await fetch("/api/expenses", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(expenseData),
               });

               if (!expenseResponse.ok) {
                  const errorData = await expenseResponse.json();
                  console.error(
                     "Failed to create scheduled expense:",
                     errorData
                  );
                  throw new Error("Failed to create scheduled expense");
               }

               createdAssignedTo = await expenseResponse.json();
               console.log(
                  "Scheduled expense created successfully:",
                  createdCategory
               );
            } else {
               // Create one-time expense
               const expenseData = {
                  description: formData.name,
                  amountDue: formData.amount,
                  date: formData.dueDate.toISOString(),
                  category: formData.name,
                  status: "scheduled",
                  frequency: "oneoff",
               };

               const response = await fetch("/api/expenses", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(expenseData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create expense");
               }

               createdCategory = await response.json();
            }
         } else {
            // Create income
            if (formData.isRecurring) {
               // Create recurring income
               const recurringIncomeData = {
                  description: formData.name,
                  payAmount: formData.amount,
                  payPeriod: formData.frequency,
                  enabled: true,
               };

               // Add required fields based on frequency
               if (formData.frequency === "monthly") {
                  recurringIncomeData.payDay = formData.dueMonth;
               } else if (formData.frequency === "semimonthly") {
                  // For semimonthly, payDay is not needed as it's fixed on 1st and 15th
                  // The API will handle this automatically
               } else if (
                  formData.frequency === "weekly" ||
                  formData.frequency === "biweekly"
               ) {
                  recurringIncomeData.payDayOfWeek = formData.payDayOfWeek;
                  recurringIncomeData.payWeekDay = formData.payWeekDay;

                  if (formData.frequency === "biweekly") {
                     recurringIncomeData.lastPaymentDate = new Date(
                        formData.lastPaymentDate
                     );
                  }
               }

               const response = await fetch("/api/user/recurring-incomes", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(recurringIncomeData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create recurring income");
               }

               // Now create a scheduled income that aligns with the recurring schedule
               // Calculate the next due date based on the recurring pattern
               let nextDueDate = new Date();

               if (formData.frequency === "weekly") {
                  // Find the next occurrence of the specified day of the week
                  const targetDay = parseInt(formData.payDayOfWeek); // 1 = Monday, 7 = Sunday
                  const today = new Date();
                  const currentDay = today.getDay() === 0 ? 7 : today.getDay(); // Convert Sunday (0) to 7
                  const daysUntilTarget =
                     targetDay >= currentDay
                        ? targetDay - currentDay
                        : 7 - currentDay + targetDay;
                  nextDueDate.setDate(today.getDate() + daysUntilTarget);
               } else if (formData.frequency === "biweekly") {
                  // For biweekly, use the last payment date to calculate the next one
                  const lastPayment = new Date(formData.lastPaymentDate);
                  nextDueDate = new Date(
                     lastPayment.getTime() + 14 * 24 * 60 * 60 * 1000
                  ); // Add 14 days
               } else if (formData.frequency === "monthly") {
                  // Set to the specified day of the current month, or next month if past
                  const targetDay = parseInt(formData.dueMonth);
                  nextDueDate.setDate(targetDay);
                  if (nextDueDate < new Date()) {
                     nextDueDate.setMonth(nextDueDate.getMonth() + 1);
                  }
               } else if (formData.frequency === "semimonthly") {
                  // For semimonthly, next due date is either 1st or 15th
                  const today = new Date();
                  if (today.getDate() <= 1) {
                     nextDueDate.setDate(1);
                  } else if (today.getDate() <= 15) {
                     nextDueDate.setDate(15);
                  } else {
                     nextDueDate.setMonth(nextDueDate.getMonth() + 1, 1);
                  }
               }

               const incomeData = {
                  description: formData.name,
                  expectedAmount: formData.amount,
                  date: nextDueDate.toISOString(),
                  category: "Salary",
                  status: "scheduled",
               };

               const incomeResponse = await fetch("/api/incomes", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(incomeData),
               });

               if (!incomeResponse.ok) {
                  throw new Error("Failed to create scheduled income");
               }

               createdAssignedTo = await incomeResponse.json();
            } else {
               // Create one-time income
               const incomeData = {
                  description: formData.name,
                  expectedAmount: formData.amount,
                  date: formData.dueDate.toISOString(),
                  category: "Salary",
                  status: "scheduled",
               };

               const response = await fetch("/api/incomes", {
                  method: "POST",
                  headers: {
                     "Content-Type": "application/json",
                  },
                  body: JSON.stringify(incomeData),
               });

               if (!response.ok) {
                  throw new Error("Failed to create income");
               }

               createdCategory = await response.json();
            }
         }

         // Refresh categories and wait for completion
         await onHandlers.onCategoryCreated();

         // Assign the transaction to the newly created category
         // Pass the category object directly to avoid lookup issues
         await onHandlers.handleAssignedToChange(
            transaction._id,
            createdAssignedTo._id,
            formData.type === "expense" ? "Expense" : "Income",
            createdAssignedTo // Pass the assignedTo object directly
         );
      } catch (error) {
         console.error("Error creating assignedTo:", error);
         throw error;
      }
   };

   const handleCreateAssignedToClose = () => {
      setCreateAssignedToModal({
         isOpen: false,
         assignedToName: "",
         showBothOptions: true,
      });
   };

   return (
      <>
         <tr
            key={transaction._id}
            className={`${
               (transaction.assignedTo === "Unassigned" ||
                  !transaction.assignedTo) &&
               !transaction.isAdjustment
                  ? "bg-yellow-50 hover:bg-yellow-100 dark:bg-yellow-900/10 dark:hover:bg-yellow-900/20"
                  : "bg-white hover:bg-gray-50 dark:hover:bg-gray-800"
            } ${
               transaction.isAdjustment
                  ? "bg-gray-50/50 dark:bg-gray-900/20 italic"
                  : "dark:bg-gray-900"
            }`}
         >
            <td className="px-4 py-2 whitespace-nowrap w-3">
               <div className="flex items-center justify-center h-full">
                  <div
                     className={`h-3 w-3 rounded-full ${
                        transaction.type === "Transfer"
                           ? "bg-gray-500/90"
                           : transaction.type === "Income"
                           ? "bg-green-500/90"
                           : "bg-red-500/90"
                     }`}
                     title={transaction.type}
                     aria-label={`Transaction type: ${transaction.type}`}
                  />
               </div>
            </td>
            <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200 w-28 text-left">
               <div className="relative">
                  <Popover
                     open={datePickerOpen}
                     onOpenChange={setDatePickerOpen}
                  >
                     <PopoverTrigger asChild>
                        <Button
                           variant="ghost"
                           className="w-full justify-start text-left font-normal p-0 h-auto hover:text-gray-600 dark:hover:text-gray-400 transition-colors duration-200"
                        >
                           {format(
                              parseTimezoneDate(transaction.date),
                              "MM/dd/yyyy"
                           )}
                        </Button>
                     </PopoverTrigger>
                     <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                           mode="single"
                           selected={parseTimezoneDate(transaction.date)}
                           onSelect={(date) => {
                              if (date && onHandlers.handleDateSubmit) {
                                 onHandlers.handleDateSubmit(
                                    transaction._id,
                                    date
                                 );
                                 setDatePickerOpen(false); // Close the popover after selection
                              }
                           }}
                           disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                           }
                           captionLayout="dropdown"
                        />
                     </PopoverContent>
                  </Popover>
               </div>
            </td>
            {selectedAccountFilter === "all" && accounts.length > 1 && (
               <td className="px-4 w-1/12 py-2 whitespace-nowrap text-sm text-center">
                  <div className="relative flex">
                     <button
                        onClick={(e) =>
                           onHandlers.handleAccountClick(transaction._id, e)
                        }
                        className={`inline-flex items-center px-2.5 py-1 rounded-md text-sm font-medium transition-all duration-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                           showAccountDropdown === transaction._id
                              ? "bg-gray-100 dark:bg-gray-700 ring-2 ring-blue-500 dark:ring-blue-400"
                              : ""
                        }`}
                     >
                        <span className="truncate max-w-[100px]">
                           {transaction.accountId
                              ? accounts.find(
                                   (a) => a._id === transaction.accountId
                                )?.name || "Unknown"
                              : "Unassigned"}
                        </span>
                        <ChevronDownIcon className="ml-1 h-4 w-4 text-gray-600 dark:text-gray-300" />
                     </button>
                  </div>
               </td>
            )}
            <td className="px-0 py-2 whitespace-nowrap text-sm text-left">
               <div className="relative flex justify-start">
                  <DropdownMenu>
                     <DropdownMenuTrigger asChild>
                        <button
                           className={`inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200 hover:ring-2 hover:ring-offset-2 hover:ring-offset-white dark:hover:ring-offset-gray-800 ${getStatusPillColor(
                              transaction.status
                           )}`}
                        >
                           {(transaction.status || "pending")
                              .charAt(0)
                              .toUpperCase() +
                              (transaction.status || "pending").slice(1)}
                           <ChevronDownIcon className="ml-1 h-4 w-4" />
                        </button>
                     </DropdownMenuTrigger>
                     <DropdownMenuContent align="center" className="w-32">
                        {TRANSACTION_STATUS.map((status) => (
                           <DropdownMenuItem
                              key={status}
                              onClick={() => handleStatusChange(status)}
                              className={`text-sm cursor-pointer ${
                                 transaction.status === status
                                    ? "bg-gray-50 dark:bg-gray-900/50 text-gray-900 dark:text-gray-100"
                                    : "text-gray-700 dark:text-gray-300"
                              }`}
                           >
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                           </DropdownMenuItem>
                        ))}
                     </DropdownMenuContent>
                  </DropdownMenu>
               </div>
            </td>
            {/* AssignedTo Column - Hide for transfers */}
            {transaction.type !== "Transfer" && (
               <td className="px-4 pr-0 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200 w-32">
                  <div className="flex w-full">
                     {transaction.isAdjustment ? (
                        <div className="text-xs text-gray-500 dark:text-gray-400 italic">
                           Balance adjustment
                        </div>
                     ) : (
                        <Combobox
                           options={assignedToOptions}
                           value={
                              transaction.assignedTo &&
                              transaction.assignedTo !== "Unassigned"
                                 ? {
                                      value:
                                         availableAssignedTo.find(
                                            (c) =>
                                               c.description ===
                                               transaction.assignedTo
                                         )?._id || "",
                                      label: transaction.assignedTo,
                                   }
                                 : { value: "", label: "Unassigned" }
                           }
                           onChange={(option) => {
                              // Find the selected assignedTo to determine its type
                              const selectedAssignedTo = option.value
                                 ? availableAssignedTo.find(
                                      (c) => c._id === option.value
                                   )
                                 : null;

                              // Determine assignedTo type based on whether it has expectedAmount (Income) or not (Expense)
                              const assignedToType =
                                 selectedAssignedTo?.expectedAmount
                                    ? "Income"
                                    : selectedAssignedTo
                                    ? "Expense"
                                    : null;

                              onHandlers.handleAssignedToChange &&
                                 onHandlers.handleAssignedToChange(
                                    transaction._id,
                                    option.value,
                                    assignedToType
                                 );
                           }}
                           placeholder="Select assigned to..."
                           className="w-full text-xs min-w-[250px]"
                           displayValue={(option) =>
                              option?.label || "Unassigned"
                           }
                           noResultsAction={
                              transaction.amount >= 0
                                 ? {
                                      onClick: handleCreateAssignedTo,
                                      showBothOptions: true,
                                   }
                                 : {
                                      onClick: handleCreateAssignedTo,
                                      showBothOptions: false,
                                   }
                           }
                        />
                     )}
                  </div>
               </td>
            )}
            <td
               className={`px-6 pl-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200`}
               colSpan={
                  transaction.type === "Transfer" ||
                  transaction.type === "Payment"
                     ? 2
                     : 1
               }
            >
               {transaction.type === "Transfer" ? (
                  <div className="truncate" title={transaction.payee}>
                     {transaction.payee}
                  </div>
               ) : editingPayee === transaction._id ? (
                  <form
                     onSubmit={(e) => {
                        e.preventDefault();
                        onHandlers.handlePayeeSubmit(
                           transaction._id,
                           e.target.payee.value
                        );
                     }}
                     className="flex"
                  >
                     <input
                        type="text"
                        name="payee"
                        defaultValue={transaction.payee}
                        ref={payeeInputRef}
                        className="block rounded-md border-0 w-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-700 focus:ring-2 focus:ring-gray-600 sm:text-sm sm:leading-6 px-3 py-1.5"
                        onBlur={() => onHandlers.handlePayeeClick(null)}
                        onKeyDown={(e) => {
                           if (e.key === "Escape") {
                              onHandlers.handlePayeeClick(null);
                           } else if (e.key === "Enter") {
                              e.preventDefault();
                              onHandlers.handlePayeeSubmit(
                                 transaction._id,
                                 e.target.value
                              );
                           }
                        }}
                        autoFocus
                     />
                  </form>
               ) : (
                  <div className="flex items-center gap-1">
                     {transaction.isPlaidTransaction && (
                        <div className="relative">
                           <button
                              onClick={() =>
                                 setShowPlaidTooltip(!showPlaidTooltip)
                              }
                              className="focus:outline-none"
                           >
                              <LinkIcon className="h-3.5 w-3.5 text-gray-500" />
                           </button>
                           {showPlaidTooltip && (
                              <div className="absolute z-10 left-6 top-0 w-48 p-2 bg-gray-900 dark:bg-gray-700 text-white dark:text-gray-100 text-xs rounded shadow-lg whitespace-normal">
                                 Imported automatically from bank via Plaid
                                 <div className="absolute -left-1 top-2 w-2 h-2 bg-gray-900 rotate-45" />
                              </div>
                           )}
                        </div>
                     )}
                     <button
                        onClick={() =>
                           onHandlers.handlePayeeClick(transaction._id)
                        }
                        className="hover:text-gray-600 dark:hover:text-gray-400 transition-colors duration-200 w-full text-left"
                     >
                        <div className="truncate">{transaction.payee}</div>
                     </button>
                  </div>
               )}
            </td>
            <td className="pl-6 pr-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
               {editingAmount === transaction._id ? (
                  <form
                     onSubmit={(e) => {
                        e.preventDefault();
                        onHandlers.handleAmountSubmit(
                           transaction._id,
                           e.target.amount.value,
                           transaction
                        );
                     }}
                  >
                     <div className="relative flex items-center justify-between w-full">
                        {transaction.type === "Expense" && (
                           <span className="absolute left-3 text-gray-900 dark:text-gray-100">
                              -
                           </span>
                        )}
                        <input
                           type="text"
                           name="amount"
                           defaultValue={`$${Math.abs(
                              transaction.amount
                           ).toFixed(2)}`}
                           ref={amountInputRef}
                           className={`block rounded-md border-0 w-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-700 focus:ring-2 focus:ring-gray-600 sm:text-sm sm:leading-6 px-3 py-1.5 ${
                              transaction.type === "Expense" ? "pl-6" : ""
                           } text-right`}
                           onBlur={() => onHandlers.handleAmountClick(null)}
                           onKeyDown={(e) => {
                              if (e.key === "Escape") {
                                 onHandlers.handleAmountClick(null);
                              } else if (e.key === "Enter") {
                                 e.preventDefault();
                                 onHandlers.handleAmountSubmit(
                                    transaction._id,
                                    e.target.value,
                                    transaction
                                 );
                              }
                           }}
                           autoFocus
                        />
                     </div>
                  </form>
               ) : (
                  <button
                     onClick={() =>
                        onHandlers.handleAmountClick(transaction._id)
                     }
                     className="hover:text-gray-600 dark:hover:text-gray-400 transition-colors duration-200 w-full text-right"
                  >
                     {transaction.type === "Expense" && "-"}$
                     {Math.abs(transaction.amount).toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                     })}
                  </button>
               )}
            </td>
            <td className="pl-2 pr-6 py-2 whitespace-nowrap text-right text-sm font-medium w-12">
               <button
                  onClick={(e) => {
                     e.stopPropagation();
                     setDeletingId(transaction._id);
                     onHandlers
                        .handleDelete(transaction._id)
                        .finally(() => setDeletingId(null));
                  }}
                  onKeyDown={(e) => {
                     if (e.key === "Enter" || e.key === " ") {
                        e.stopPropagation();
                        setDeletingId(transaction._id);
                        onHandlers
                           .handleDelete(transaction._id)
                           .finally(() => setDeletingId(null));
                     }
                  }}
                  className="text-red-500 hover:text-red-400 dark:text-red-400 dark:hover:text-red-300"
                  aria-label={`Delete transaction ${transaction.payee}`}
                  disabled={deletingId === transaction._id}
               >
                  {deletingId === transaction._id ? (
                     <svg
                        className="animate-spin h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                     >
                        <circle
                           className="opacity-25"
                           cx="12"
                           cy="12"
                           r="10"
                           stroke="currentColor"
                           strokeWidth="4"
                        ></circle>
                        <path
                           className="opacity-75"
                           fill="currentColor"
                           d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                     </svg>
                  ) : (
                     <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                     >
                        <path d="M3 6h18" />
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                     </svg>
                  )}
               </button>
            </td>
         </tr>

         <CreateCategoryModal
            isOpen={createAssignedToModal.isOpen}
            onClose={handleCreateAssignedToClose}
            onSave={handleCreateAssignedToSave}
            categoryName={createAssignedToModal.assignedToName}
            transactionId={transaction._id}
            transactionAmount={transaction.amount}
            showBothOptions={createAssignedToModal.showBothOptions}
         />
      </>
   );
};

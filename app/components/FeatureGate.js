"use client";

import { useState, useEffect } from "react";
import { checkFeatureAccess } from "@/app/lib/utils/featureAccess";

/**
 * FeatureGate component to restrict access to features based on subscription plans
 * @param {Object} props Component props
 * @param {string} props.feature The feature name to check access for
 * @param {React.ReactNode} props.children Content to render if user has access
 * @param {React.ReactNode} props.fallback Content to render if user doesn't have access
 * @returns {React.ReactNode} Either children or fallback based on feature access
 */
export default function FeatureGate({ feature, children, fallback }) {
   const [hasAccess, setHasAccess] = useState(false);
   const [loading, setLoading] = useState(true);

   useEffect(() => {
      const checkAccess = async () => {
         try {
            // Check if user has access to the feature
            const access = await checkFeatureAccess(feature);
            setHasAccess(access);
         } catch (error) {
            console.error("Error checking feature access:", error);
            setHasAccess(false);
         } finally {
            setLoading(false);
         }
      };

      checkAccess();
   }, [feature]);

   if (loading) {
      return (
         <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-gray-100"></div>
         </div>
      );
   }

   if (hasAccess) {
      return children;
   }

   if (fallback) {
      return fallback;
   }

   return (
      <div className="rounded-lg border border-amber-200 bg-amber-50 p-4 dark:border-amber-900 dark:bg-amber-900/20">
         <h3 className="font-medium text-amber-800 dark:text-amber-200">
            Premium Feature
         </h3>
         <p className="text-sm text-amber-700 dark:text-amber-300">
            This feature requires a subscription upgrade. Please visit the
            Subscription tab in Settings to upgrade your plan.
         </p>
      </div>
   );
}

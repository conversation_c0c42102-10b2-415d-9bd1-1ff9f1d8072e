"use client";

import {
   useState,
   useEffect,
   Fragment,
   useMemo,
   useCallback,
   useRef,
} from "react";
import {
   format,
   parseISO,
   isWithinInterval,
   addDays,
   addWeeks,
   addMonths,
} from "date-fns";
import { useSession } from "next-auth/react";
import EditIncomeModal from "./EditIncomeModal";
import AddIncomeModal from "./AddIncomeModal";
import {
   roundToTwoDecimals,
   formatAmount,
   calculateVisibleTotals,
   combineAndSortIncomes,
} from "../../lib/utils/incomeUtils";
import { StatusBadge } from "../expenses/ExpenseStatusComponents";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "../../../components/ui/dropdown-menu";

export default function IncomeList({
   dateRange,
   incomes: propIncomes,
   userPaySettings: propUserPaySettings,
   onIncomeDelete,
   onIncomeSchedule,
   onIncomeEdit,
   onIncomeAdd,
   loading: propLoading,
   isMobileView = false,
   isCollapsed = false,
}) {
   const { data: session } = useSession();
   const [incomes, setIncomes] = useState(propIncomes);
   const [expectedIncomes, setExpectedIncomes] = useState([]);
   const [loading, setLoading] = useState(propLoading);
   const [error, setError] = useState("");
   const [userPaySettings, setUserPaySettings] = useState(propUserPaySettings);
   const [selectedIncome, setSelectedIncome] = useState(null);
   const [isEditModalOpen, setIsEditModalOpen] = useState(false);
   const [isAddModalOpen, setIsAddModalOpen] = useState(false);
   const [hiddenProjections, setHiddenProjections] = useState([]);
   const [isHiddenSectionExpanded, setIsHiddenSectionExpanded] =
      useState(false);
   const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
   const [incomeToDelete, setIncomeToDelete] = useState(null);
   const [isExpanded, setIsExpanded] = useState(true);
   const [errorMessage, setErrorMessage] = useState("");

   // Update internal state when props change
   useEffect(() => {
      setIncomes(propIncomes);
   }, [propIncomes]);

   useEffect(() => {
      setUserPaySettings(propUserPaySettings);
   }, [propUserPaySettings]);

   useEffect(() => {
      setLoading(propLoading);
   }, [propLoading]);

   const isProjectionHidden = useCallback(
      (income) => {
         if (!session?.user?.id || income.status?.toLowerCase() !== "projected")
            return false;

         const incomeDate = format(
            income.date instanceof Date ? income.date : parseISO(income.date),
            "yyyy-MM-dd"
         );

         return hiddenProjections.some(
            (hidden) =>
               hidden.description.toLowerCase() ===
                  income.description.toLowerCase() && hidden.date === incomeDate
         );
      },
      [session?.user, hiddenProjections]
   );

   // Add filter function to restrict incomes to the actual date range (not extended)
   const filterIncomesByDate = useCallback(
      (incomesToFilter) => {
         if (!dateRange?.start || !dateRange?.end) return incomesToFilter;

         return incomesToFilter.filter((income) => {
            const incomeDate = new Date(
               income.date instanceof Date ? income.date : parseISO(income.date)
            );

            // Only include incomes that fall within the exact date range
            return incomeDate >= dateRange.start && incomeDate <= dateRange.end;
         });
      },
      [dateRange]
   );

   // Combine actual and expected incomes - simplified without aggregation
   const allIncomes = useMemo(() => {
      // First combine all incomes and expected incomes
      const combinedIncomes = combineAndSortIncomes(
         incomes,
         expectedIncomes,
         dateRange
      );

      // Then filter to only include dates in the visible range
      return filterIncomesByDate(combinedIncomes);
   }, [incomes, expectedIncomes, dateRange, filterIncomesByDate]);

   // Separate visible and hidden incomes
   const { visibleIncomes, hiddenIncomes } = useMemo(() => {
      const visible = [];
      const hidden = [];

      allIncomes.forEach((income) => {
         if (isProjectionHidden(income)) {
            hidden.push(income);
         } else {
            visible.push(income);
         }
      });

      return { visibleIncomes: visible, hiddenIncomes: hidden };
   }, [allIncomes, isProjectionHidden]);

   useEffect(() => {
      if (session?.user?.id) {
         const stored =
            localStorage.getItem(`skipped-projections-${session.user.id}`) ||
            localStorage.getItem(`hidden-projections-${session.user.id}`);
         if (stored) {
            setHiddenProjections(JSON.parse(stored));
         }
      }
   }, [session?.user]);

   useEffect(() => {
      if (dateRange && userPaySettings) {
         generateExpectedIncomes();
      }
   }, [dateRange, userPaySettings]);

   const generateExpectedIncomes = () => {
      if (!userPaySettings?.recurringIncomes) return [];

      const expectedIncomes = [];
      const currentDate = new Date();
      const endDate = dateRange?.end || addMonths(currentDate, 3);

      // Process each recurring income that is enabled
      const { recurringIncomes } = userPaySettings;

      recurringIncomes.forEach((income) => {
         if (!income.enabled) return;

         // For bi-weekly incomes, start from the last payment date
         let currentDate;
         if (income.payPeriod === "biweekly" && income.lastPaymentDate) {
            // Create a UTC date at noon to avoid timezone issues
            currentDate = new Date(
               new Date(income.lastPaymentDate).toISOString().split("T")[0] +
                  "T12:00:00.000Z"
            );

            // Add the last payment date to projections if it falls within our date range
            if (
               currentDate >= dateRange.start &&
               currentDate <= dateRange.end
            ) {
               expectedIncomes.push({
                  date: currentDate,
                  expectedAmount: income.payAmount,
                  status: "projected",
                  description: income.description,
                  payPeriod: "biweekly",
               });
            }
         } else {
            // For other frequencies, start from the beginning of the date range
            currentDate = new Date(dateRange.start);
         }

         const payAmount = income.payAmount;

         // Safety counter to prevent infinite loops
         let safetyCounter = 0;
         const MAX_ITERATIONS = 100;

         while (
            currentDate <= dateRange.end &&
            safetyCounter < MAX_ITERATIONS
         ) {
            let nextPayDate = null;
            safetyCounter++;

            if (income.payPeriod === "weekly") {
               // For weekly, use day-of-week calculation
               const targetDay = parseInt(income.payDayOfWeek) % 7;
               let currentDay = currentDate.getDay();
               let daysUntilPayDay = targetDay - currentDay;

               if (daysUntilPayDay < 0) {
                  daysUntilPayDay += 7;
               }

               nextPayDate = addDays(currentDate, daysUntilPayDay);
               if (nextPayDate < currentDate) {
                  nextPayDate = addDays(nextPayDate, 7);
               }
               currentDate = addDays(nextPayDate, 7);
            } else if (income.payPeriod === "biweekly") {
               // For bi-weekly, simply add 14 days to the last payment date
               nextPayDate = addDays(currentDate, 14);
               currentDate = nextPayDate; // Move to next period
            } else if (income.payPeriod === "semimonthly") {
               // Start with the current month
               const currentYear = currentDate.getFullYear();
               const currentMonth = currentDate.getMonth();

               // Create both payment dates for the current month
               const firstOfMonth = new Date(currentYear, currentMonth, 1, 12);
               const fifteenthOfMonth = new Date(
                  currentYear,
                  currentMonth,
                  15,
                  12
               );

               // If we're before the 15th of the current month
               if (currentDate <= fifteenthOfMonth) {
                  // If we haven't passed the 1st yet, use it
                  if (currentDate <= firstOfMonth) {
                     nextPayDate = firstOfMonth;
                     currentDate = fifteenthOfMonth;
                  } else {
                     // We're between 1st and 15th
                     nextPayDate = fifteenthOfMonth;
                     currentDate = addMonths(firstOfMonth, 1);
                  }
               } else {
                  // We're past the 15th, move to next month's 1st
                  nextPayDate = addMonths(firstOfMonth, 1);
                  currentDate = addMonths(fifteenthOfMonth, 1);
               }
            } else if (income.payPeriod === "monthly") {
               // Get the target pay day for the current month
               const payDayNum = parseInt(income.payDay);
               const currentYear = currentDate.getFullYear();
               const currentMonth = currentDate.getMonth();

               // Create the next pay date
               nextPayDate = new Date(currentYear, currentMonth, payDayNum, 12);

               // If we've already passed this month's pay date, move to next month
               if (currentDate > nextPayDate) {
                  nextPayDate = new Date(
                     currentYear,
                     currentMonth + 1,
                     payDayNum,
                     12
                  );
               }

               // Move current date to the first day of next month
               currentDate = addMonths(nextPayDate, 1);
            }

            // Only add the income if it falls strictly within our date range
            if (
               nextPayDate &&
               nextPayDate >= dateRange.start &&
               nextPayDate <= dateRange.end
            ) {
               expectedIncomes.push({
                  date: nextPayDate,
                  expectedAmount: payAmount,
                  status: "projected",
                  description: income.description,
                  payPeriod: income.payPeriod,
               });
            }
         }

         if (safetyCounter >= MAX_ITERATIONS) {
            console.warn(
               `Maximum iterations reached for income: ${income.description}`
            );
         }
      });

      // Sort expected incomes by date
      expectedIncomes.sort((a, b) => a.date - b.date);
      setExpectedIncomes(expectedIncomes);
   };

   const handleDelete = async (id, e) => {
      e.preventDefault();
      e.stopPropagation();

      setIncomeToDelete(id);
      setDeleteConfirmOpen(true);
   };

   const handleConfirmDelete = async () => {
      try {
         // Extract the ID from the income object
         const incomeId =
            incomeToDelete?._id || incomeToDelete?.id || incomeToDelete;

         // Close modal immediately
         setDeleteConfirmOpen(false);
         setIncomeToDelete(null);

         // Then perform the delete operation
         await onIncomeDelete(incomeId);
      } catch (error) {
         console.error("Error deleting income:", error);
         setErrorMessage(error.message || "Failed to delete income");
      }
   };

   const handleCancelDelete = () => {
      setDeleteConfirmOpen(false);
      setIncomeToDelete(null);
   };

   const handleScheduleIncome = async (projectedIncome) => {
      await onIncomeSchedule(projectedIncome);
   };

   const handleEditModalSave = (updatedIncome) => {
      onIncomeEdit(updatedIncome);
      setIsEditModalOpen(false);
      setSelectedIncome(null);
   };

   const handleAddIncomeSave = (newIncome) => {
      onIncomeAdd(newIncome);
      setIsAddModalOpen(false);
   };

   const handleRowClick = (e, income) => {
      // Prevent clicks on buttons/dropdowns from triggering row click
      if (
         e.target.closest("button") ||
         e.target.closest(".dropdown-menu") ||
         e.target.closest(".menu-container") ||
         e.target.closest("[data-radix-collection-item]") ||
         e.target.closest("[role='menu']") ||
         e.target.closest("[role='menuitem']")
      ) {
         return;
      }

      // Only allow editing of actual incomes (not projected ones)
      if (income.status !== "projected") {
         setSelectedIncome(income);
         setIsEditModalOpen(true);
      }
   };

   const handleEditModalClose = () => {
      setIsEditModalOpen(false);
      setSelectedIncome(null);
   };

   const handleHideProjection = (income, e) => {
      // Make event parameter optional since dropdown menus might not pass it
      if (e) {
         e.preventDefault();
         e.stopPropagation();
      }

      if (!session?.user?.id) return;

      const incomeDate = format(
         income.date instanceof Date ? income.date : parseISO(income.date),
         "yyyy-MM-dd"
      );

      const hiddenProjection = {
         description: income.description,
         date: incomeDate,
      };

      setHiddenProjections((prev) => {
         const newHiddenProjections = [...prev, hiddenProjection];
         localStorage.setItem(
            `skipped-projections-${session.user.id}`,
            JSON.stringify(newHiddenProjections)
         );
         return newHiddenProjections;
      });
   };

   const handleUnhideProjection = (income, e) => {
      // Make event parameter optional since dropdown menus might not pass it
      if (e) {
         e.preventDefault();
         e.stopPropagation();
      }

      if (!session?.user?.id) return;

      const incomeDate = format(
         income.date instanceof Date ? income.date : parseISO(income.date),
         "yyyy-MM-dd"
      );

      setHiddenProjections((prev) => {
         const newHiddenProjections = prev.filter(
            (hidden) =>
               !(
                  hidden.description.toLowerCase() ===
                     income.description.toLowerCase() &&
                  hidden.date === incomeDate
               )
         );
         localStorage.setItem(
            `skipped-projections-${session.user.id}`,
            JSON.stringify(newHiddenProjections)
         );
         return newHiddenProjections;
      });
   };

   const handleScheduleAllIncomes = async () => {
      try {
         setLoading(true);
         const projectedIncomes = visibleIncomes.filter(
            (income) => income.status === "projected"
         );

         if (projectedIncomes.length === 0) {
            setErrorMessage("No projected incomes found to schedule.");
            return;
         }

         const results = await Promise.allSettled(
            projectedIncomes.map((income) => handleScheduleIncome(income))
         );

         const successCount = results.filter(
            (result) => result.status === "fulfilled"
         ).length;
         const failCount = results.length - successCount;

         if (failCount > 0) {
            setErrorMessage(
               `Successfully scheduled ${successCount} income(s). ${failCount} failed to schedule.`
            );
         } else {
            setErrorMessage(
               `Successfully scheduled all ${successCount} projected income(s).`
            );
         }
      } catch (error) {
         console.error("Error scheduling incomes:", error);
         setErrorMessage("An error occurred while scheduling incomes.");
      } finally {
         setLoading(false);
      }
   };

   const formatAmountDisplay = (amount) => {
      if (amount === null || amount === undefined) return "--";
      return formatAmount(amount).formattedValue;
   };

   // Calculate total expected and received amounts
   const totalAmounts = useMemo(() => {
      const total = visibleIncomes.reduce(
         (acc, income) => ({
            expected: acc.expected + income.expectedAmount,
            received: acc.received + (income.receivedAmount || 0),
         }),
         { expected: 0, received: 0 }
      );

      return {
         ...total,
         progress:
            total.expected > 0 ? (total.received / total.expected) * 100 : 0,
      };
   }, [visibleIncomes]);

   // Calculate if there are any projected incomes available to schedule
   const hasProjectedIncomes = useMemo(
      () => visibleIncomes.some((income) => income.status === "projected"),
      [visibleIncomes]
   );

   if (loading) {
      return (
         <div className="text-center py-4">
            <div className="text-gray-500 dark:text-gray-400">Loading...</div>
         </div>
      );
   }

   if (error) {
      return (
         <div className="text-center py-4">
            <div className="text-red-500 dark:text-red-400">{error}</div>
         </div>
      );
   }

   if (allIncomes.length === 0) {
      return (
         <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
               No incomes found for this period.
            </p>
         </div>
      );
   }

   // Instead of returning null, use CSS to animate the height
   return (
      <div
         className={`space-y-4 overflow-hidden transition-all duration-300 ease-in-out ${
            isMobileView && isCollapsed ? "max-h-0" : "max-h-[2000px]"
         }`}
      >
         <div className="bg-white dark:bg-gray-900">
            {/* Table Structure for Income List */}
            <table className="w-full">
               <thead>
                  {/* Header Row */}
                  <tr className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
                     <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Incomes
                     </th>
                     <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20">
                        Status
                     </th>
                     <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">
                        Received
                     </th>
                     <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">
                        Expected
                     </th>
                     <th className="px-4 py-2 w-10">
                        <div className="relative float-right">
                           <div className="flex items-center justify-end space-x-2">
                              {/* Schedule All button - only show when there are projected incomes */}
                              {hasProjectedIncomes && (
                                 <button
                                    onClick={handleScheduleAllIncomes}
                                    disabled={loading}
                                    title="Schedule All Projected Incomes"
                                    className="inline-flex items-center p-1 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                                 >
                                    {loading ? (
                                       <svg
                                          className="animate-spin h-4 w-4"
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                       >
                                          <circle
                                             className="opacity-25"
                                             cx="12"
                                             cy="12"
                                             r="10"
                                             stroke="currentColor"
                                             strokeWidth="4"
                                          ></circle>
                                          <path
                                             className="opacity-75"
                                             fill="currentColor"
                                             d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                          ></path>
                                       </svg>
                                    ) : (
                                       <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-4 w-4"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                       >
                                          <path
                                             strokeLinecap="round"
                                             strokeLinejoin="round"
                                             strokeWidth={2}
                                             d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                          />
                                       </svg>
                                    )}
                                 </button>
                              )}
                              <DropdownMenu>
                                 <DropdownMenuTrigger className="inline-flex items-center p-1 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900">
                                    <svg
                                       className="w-4 h-4"
                                       fill="none"
                                       stroke="currentColor"
                                       viewBox="0 0 24 24"
                                    >
                                       <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"
                                       />
                                    </svg>
                                 </DropdownMenuTrigger>
                                 <DropdownMenuContent
                                    align="end"
                                    className="w-48"
                                 >
                                    <DropdownMenuItem
                                       onClick={() => setIsAddModalOpen(true)}
                                    >
                                       Add Income
                                    </DropdownMenuItem>
                                 </DropdownMenuContent>
                              </DropdownMenu>
                           </div>
                        </div>
                     </th>
                  </tr>
               </thead>

               <tbody className={`${isExpanded ? "" : "hidden"}`}>
                  {/* Income List Rows - All incomes shown as regular rows */}
                  {visibleIncomes.map((income, index) => {
                     const incomeId =
                        income._id ||
                        (income.status === "projected"
                           ? `projected-${income.description}-${format(
                                income.date instanceof Date
                                   ? income.date
                                   : parseISO(income.date),
                                "yyyy-MM-dd"
                             )}`
                           : `income-${index}`);

                     return (
                        <tr
                           key={incomeId}
                           className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer"
                           onClick={(e) => handleRowClick(e, income)}
                        >
                           <td className="px-4 py-1">
                              <div className="flex flex-col">
                                 <div className="text-sm font-medium text-gray-900 dark:text-white">
                                    {income.description}
                                 </div>
                                 <div className="flex items-center space-x-2">
                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                       {format(
                                          income.date instanceof Date
                                             ? income.date
                                             : parseISO(income.date),
                                          "MMM d, yyyy"
                                       )}
                                    </span>
                                 </div>
                              </div>
                           </td>
                           <td className="px-4 py-1 text-center">
                              <StatusBadge status={income.status} />
                           </td>
                           <td className="px-4 py-1 text-right text-sm font-medium tabular-nums">
                              {income.status !== "projected" ? (
                                 <span className="text-gray-900 dark:text-white">
                                    $
                                    {
                                       formatAmount(income.receivedAmount || 0)
                                          .formattedValue
                                    }
                                 </span>
                              ) : (
                                 <span className="text-gray-400 dark:text-gray-500">
                                    --
                                 </span>
                              )}
                           </td>
                           <td className="px-4 py-1 text-right text-sm font-medium text-gray-700 dark:text-gray-300 tabular-nums">
                              $
                              {
                                 formatAmount(income.expectedAmount)
                                    .formattedValue
                              }
                           </td>
                           <td className="px-4 py-1 text-right">
                              {income.status === "projected" ? (
                                 <div className="flex justify-end space-x-2">
                                    <button
                                       onClick={() =>
                                          handleScheduleIncome(income)
                                       }
                                       className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                       title="Schedule"
                                    >
                                       <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-5 w-5"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                       >
                                          <path
                                             strokeLinecap="round"
                                             strokeLinejoin="round"
                                             strokeWidth={2}
                                             d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                          />
                                       </svg>
                                    </button>
                                    <button
                                       onClick={() =>
                                          handleHideProjection(income)
                                       }
                                       className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                       title="Skip"
                                    >
                                       <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-5 w-5"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                       >
                                          <path
                                             strokeLinecap="round"
                                             strokeLinejoin="round"
                                             strokeWidth={2}
                                             d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                                          />
                                       </svg>
                                    </button>
                                 </div>
                              ) : (
                                 <DropdownMenu>
                                    <DropdownMenuTrigger className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                                       <svg
                                          className="w-4 h-4"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                       >
                                          <path
                                             strokeLinecap="round"
                                             strokeLinejoin="round"
                                             strokeWidth={2}
                                             d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                                          />
                                       </svg>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                       <DropdownMenuItem
                                          onClick={() => {
                                             setSelectedIncome(income);
                                             setIsEditModalOpen(true);
                                          }}
                                       >
                                          Edit
                                       </DropdownMenuItem>
                                       <DropdownMenuItem
                                          onClick={() => {
                                             setIncomeToDelete(income);
                                             setDeleteConfirmOpen(true);
                                          }}
                                          className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                                       >
                                          Delete
                                       </DropdownMenuItem>
                                    </DropdownMenuContent>
                                 </DropdownMenu>
                              )}
                           </td>
                        </tr>
                     );
                  })}
               </tbody>

               {/* Totals Row - Only show when there are multiple visible incomes */}
               {visibleIncomes.length > 1 && (
                  <tfoot>
                     <tr className="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                        <td className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                           Totals
                        </td>
                        <td className="px-4 py-2"></td>
                        <td className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 tabular-nums">
                           ${formatAmount(totalAmounts.received).formattedValue}
                        </td>
                        <td className="px-4 py-2 text-right text-sm font-medium text-gray-700 dark:text-gray-200 tabular-nums">
                           ${formatAmount(totalAmounts.expected).formattedValue}
                        </td>
                        <td className="px-4 py-2"></td>
                     </tr>
                  </tfoot>
               )}
            </table>

            {/* Hidden Projections Section */}
            {hiddenIncomes.length > 0 && (
               <div>
                  <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800/50 border-y border-gray-200 dark:border-gray-700">
                     <button
                        className="w-full text-left"
                        onClick={() =>
                           setIsHiddenSectionExpanded(!isHiddenSectionExpanded)
                        }
                     >
                        <div className="flex justify-between items-center">
                           <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              Skipped Incomes ({hiddenIncomes.length})
                           </span>
                           <svg
                              className={`w-5 h-5 transform transition-transform text-gray-500 dark:text-gray-400 ${
                                 isHiddenSectionExpanded ? "rotate-180" : ""
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                           >
                              <path
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                                 d="M19 9l-7 7-7-7"
                              />
                           </svg>
                        </div>
                     </button>
                  </div>

                  {isHiddenSectionExpanded && (
                     <div className="divide-y divide-gray-200 dark:divide-gray-700">
                        {hiddenIncomes.map((income) => (
                           <div
                              key={`hidden-${income.description}-${income.date}`}
                              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                           >
                              <div className="flex justify-between items-center">
                                 <div className="flex-1">
                                    <div className="flex items-center space-x-2">
                                       <span className="text-sm text-gray-500 dark:text-gray-400">
                                          {format(
                                             income.date instanceof Date
                                                ? income.date
                                                : parseISO(income.date),
                                             "MMM d, yyyy"
                                          )}
                                       </span>
                                       <StatusBadge status={income.status} />
                                    </div>
                                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                                       {income.description}
                                    </div>
                                 </div>
                                 <div className="flex items-center space-x-6">
                                    <div className="flex gap-8">
                                       <div className="w-24 text-right">
                                          <div className="text-sm text-gray-400 dark:text-gray-500">
                                             --
                                          </div>
                                       </div>
                                       <div className="w-24 text-right">
                                          <div className="text-sm text-gray-500 dark:text-gray-400 tabular-nums">
                                             $
                                             {
                                                formatAmount(
                                                   income.expectedAmount
                                                ).formattedValue
                                             }
                                          </div>
                                       </div>
                                    </div>
                                    <div className="w-10 flex justify-end">
                                       <button
                                          onClick={(e) =>
                                             handleUnhideProjection(income, e)
                                          }
                                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                          title="Unskip"
                                       >
                                          <svg
                                             xmlns="http://www.w3.org/2000/svg"
                                             className="h-5 w-5"
                                             fill="none"
                                             viewBox="0 0 24 24"
                                             stroke="currentColor"
                                          >
                                             <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                             />
                                             <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                             />
                                          </svg>
                                       </button>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        ))}
                     </div>
                  )}
               </div>
            )}
         </div>

         {/* Modals */}
         {isEditModalOpen && selectedIncome && (
            <EditIncomeModal
               income={selectedIncome}
               onClose={handleEditModalClose}
               onSave={handleEditModalSave}
            />
         )}
         {isAddModalOpen && (
            <AddIncomeModal
               onClose={() => setIsAddModalOpen(false)}
               onSave={handleAddIncomeSave}
            />
         )}
         {deleteConfirmOpen && incomeToDelete && (
            <div className="fixed inset-0 flex items-center justify-center z-50">
               <div
                  className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                  onClick={handleCancelDelete}
               ></div>
               <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 md:mx-auto p-6 z-50">
                  <div className="flex items-start mb-4">
                     <div className="flex-shrink-0 mr-3">
                        <svg
                           className="h-8 w-8 text-red-500"
                           fill="none"
                           viewBox="0 0 24 24"
                           stroke="currentColor"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                           />
                        </svg>
                     </div>
                     <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                           Delete Income
                        </h3>
                        <p className="mt-2 text-gray-600 dark:text-gray-300">
                           Are you sure you want to delete this income? This
                           action cannot be undone.
                        </p>
                     </div>
                  </div>
                  <div className="mt-5 flex justify-end gap-2">
                     <button
                        type="button"
                        className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        onClick={handleCancelDelete}
                     >
                        Cancel
                     </button>
                     <button
                        type="button"
                        className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        onClick={handleConfirmDelete}
                     >
                        Delete
                     </button>
                  </div>
               </div>
            </div>
         )}

         {/* Full screen modal for success/error messages */}
         {errorMessage && (
            <div className="fixed inset-0 flex items-center justify-center z-50">
               <div
                  className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                  onClick={() => setErrorMessage("")}
               ></div>
               <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 md:mx-auto p-6 z-50">
                  <div className="flex items-start mb-4">
                     {errorMessage.toLowerCase().includes("success") ? (
                        <div className="flex-shrink-0 mr-3">
                           <svg
                              className="h-8 w-8 text-green-500"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                           >
                              <path
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                                 d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                           </svg>
                        </div>
                     ) : (
                        <div className="flex-shrink-0 mr-3">
                           <svg
                              className="h-8 w-8 text-red-500"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                           >
                              <path
                                 strokeLinecap="round"
                                 strokeLinejoin="round"
                                 strokeWidth={2}
                                 d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                           </svg>
                        </div>
                     )}
                     <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                           {errorMessage.toLowerCase().includes("success")
                              ? "Success"
                              : "Notice"}
                        </h3>
                        <p className="mt-2 text-gray-600 dark:text-gray-300">
                           {errorMessage}
                        </p>
                     </div>
                  </div>
                  <div className="mt-5 flex justify-end">
                     <button
                        type="button"
                        className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        onClick={() => setErrorMessage("")}
                     >
                        Close
                     </button>
                  </div>
               </div>
            </div>
         )}
      </div>
   );
}

import { useState } from "react";
import { Switch } from "@headlessui/react";

const PAY_PERIODS = [
   { value: "weekly", label: "Weekly" },
   { value: "biweekly", label: "Bi-weekly" },
   { value: "monthly", label: "Monthly" },
];

const DAYS_OF_WEEK = [
   { value: "1", label: "Monday" },
   { value: "2", label: "Tuesday" },
   { value: "3", label: "Wednesday" },
   { value: "4", label: "Thursday" },
   { value: "5", label: "Friday" },
   { value: "6", label: "Saturday" },
   { value: "7", label: "Sunday" },
];

export default function AddIncomeModal({ onClose, onSave }) {
   const [isRecurring, setIsRecurring] = useState(false);
   const [income, setIncome] = useState({
      description: "",
      payAmount: "",
      date: new Date().toLocaleDateString("en-CA"),
      payPeriod: "monthly",
      payDay: "1",
      payDayOfWeek: "1",
      payWeekDay: "Monday",
      enabled: true,
   });

   const handleChange = (e) => {
      const { name, value } = e.target;
      setIncome((prev) => {
         const updates = { [name]: value };

         // Update related fields based on pay period
         if (name === "payPeriod") {
            if (value === "monthly") {
               updates.payDay = "1";
               updates.payDayOfWeek = undefined;
               updates.payWeekDay = undefined;
            } else {
               updates.payDay = undefined;
               updates.payDayOfWeek = "1";
               updates.payWeekDay = DAYS_OF_WEEK.find(
                  (day) => day.value === "1"
               )?.label;
            }
         } else if (name === "payDayOfWeek") {
            // Update payWeekDay when payDayOfWeek changes
            const selectedDay = DAYS_OF_WEEK.find((day) => day.value === value);
            if (selectedDay) {
               updates.payWeekDay = selectedDay.label;
            }
         }

         return { ...prev, ...updates };
      });
   };

   const handleSubmit = async (e) => {
      e.preventDefault();

      try {
         if (isRecurring) {
            // Handle recurring income
            const recurringIncome = {
               description: income.description,
               payAmount: parseFloat(income.payAmount),
               payPeriod: income.payPeriod,
               payDay: income.payDay,
               payDayOfWeek: income.payDayOfWeek,
               payWeekDay: income.payWeekDay,
               enabled: income.enabled,
            };
            onSave(recurringIncome, true);
         } else {
            // Handle one-time income
            const oneTimeIncome = {
               description: income.description,
               expectedAmount: parseFloat(income.payAmount),
               date: new Date(income.date),
               category: "Salary",
               status: "scheduled",
            };
            onSave(oneTimeIncome, false);
         }
      } catch (error) {
         console.error("Error adding income:", error);
      }
   };

   return (
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-50">
         <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
               <div className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                  <div className="absolute right-0 top-0 pr-4 pt-4">
                     <button
                        type="button"
                        onClick={onClose}
                        className="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                     >
                        <span className="sr-only">Close</span>
                        <svg
                           className="h-6 w-6"
                           fill="none"
                           viewBox="0 0 24 24"
                           strokeWidth="1.5"
                           stroke="currentColor"
                        >
                           <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M6 18L18 6M6 6l12 12"
                           />
                        </svg>
                     </button>
                  </div>

                  <div className="sm:flex sm:items-start">
                     <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 className="text-lg font-semibold leading-6 text-gray-900 dark:text-gray-100">
                           Add Income
                        </h3>

                        <form
                           onSubmit={handleSubmit}
                           className="mt-4 space-y-4"
                        >
                           <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-700 dark:text-gray-300">
                                 Recurring Income
                              </span>
                              <Switch
                                 checked={isRecurring}
                                 onChange={setIsRecurring}
                                 className={`${
                                    isRecurring
                                       ? "bg-gray-600"
                                       : "bg-gray-200 dark:bg-gray-700"
                                 } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900`}
                              >
                                 <span className="sr-only">
                                    Enable recurring income
                                 </span>
                                 <span
                                    className={`${
                                       isRecurring
                                          ? "translate-x-6"
                                          : "translate-x-1"
                                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                                 />
                              </Switch>
                           </div>

                           <div>
                              <label
                                 htmlFor="description"
                                 className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                              >
                                 Description
                              </label>
                              <input
                                 type="text"
                                 id="description"
                                 name="description"
                                 value={income.description}
                                 onChange={handleChange}
                                 required
                                 className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                              />
                           </div>

                           <div>
                              <label
                                 htmlFor="payAmount"
                                 className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                              >
                                 Amount
                              </label>
                              <div className="mt-1 relative rounded-md shadow-sm">
                                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-400 sm:text-sm">
                                       $
                                    </span>
                                 </div>
                                 <input
                                    type="number"
                                    id="payAmount"
                                    name="payAmount"
                                    value={income.payAmount}
                                    onChange={handleChange}
                                    required
                                    min="0"
                                    step="0.01"
                                    className="mt-1 block w-full pl-7 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                 />
                              </div>
                           </div>

                           {!isRecurring ? (
                              <div>
                                 <label
                                    htmlFor="date"
                                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                                 >
                                    Date
                                 </label>
                                 <input
                                    type="date"
                                    id="date"
                                    name="date"
                                    value={income.date}
                                    onChange={handleChange}
                                    required
                                    className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                 />
                              </div>
                           ) : (
                              <>
                                 <div>
                                    <label
                                       htmlFor="payPeriod"
                                       className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                                    >
                                       Pay Period
                                    </label>
                                    <select
                                       id="payPeriod"
                                       name="payPeriod"
                                       value={income.payPeriod}
                                       onChange={handleChange}
                                       className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                    >
                                       {PAY_PERIODS.map((period) => (
                                          <option
                                             key={period.value}
                                             value={period.value}
                                          >
                                             {period.label}
                                          </option>
                                       ))}
                                    </select>
                                 </div>

                                 {income.payPeriod === "monthly" ? (
                                    <div>
                                       <label
                                          htmlFor="payDay"
                                          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                                       >
                                          Pay Day of Month
                                       </label>
                                       <select
                                          id="payDay"
                                          name="payDay"
                                          value={income.payDay}
                                          onChange={handleChange}
                                          className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                       >
                                          {Array.from(
                                             { length: 31 },
                                             (_, i) => (
                                                <option
                                                   key={i + 1}
                                                   value={(i + 1).toString()}
                                                >
                                                   {i + 1}
                                                </option>
                                             )
                                          )}
                                       </select>
                                    </div>
                                 ) : (
                                    <div>
                                       <label
                                          htmlFor="payDayOfWeek"
                                          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                                       >
                                          Pay Day of Week
                                       </label>
                                       <select
                                          id="payDayOfWeek"
                                          name="payDayOfWeek"
                                          value={income.payDayOfWeek}
                                          onChange={handleChange}
                                          className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                                       >
                                          {DAYS_OF_WEEK.map((day) => (
                                             <option
                                                key={day.value}
                                                value={day.value}
                                             >
                                                {day.label}
                                             </option>
                                          ))}
                                       </select>
                                    </div>
                                 )}
                              </>
                           )}

                           <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                              <button
                                 type="submit"
                                 className="inline-flex w-full justify-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 sm:ml-3 sm:w-auto"
                              >
                                 Add Income
                              </button>
                              <button
                                 type="button"
                                 onClick={onClose}
                                 className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                              >
                                 Cancel
                              </button>
                           </div>
                        </form>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
}

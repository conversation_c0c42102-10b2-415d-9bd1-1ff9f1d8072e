"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";

export default function EditIncomeModal({ income, onClose, onSave }) {
   const [formData, setFormData] = useState({
      description: "",
      date: "",
      expectedAmount: "",
      receivedAmount: "",
      category: "",
   });

   useEffect(() => {
      if (income) {
         // Handle date - it should already be in YYYY-MM-DD format from the API
         let dateStr = income.date;
         if (income.date instanceof Date) {
            dateStr = income.date.toISOString().split("T")[0];
         } else if (
            typeof income.date === "string" &&
            income.date.includes("T")
         ) {
            // Handle ISO string format
            dateStr = new Date(income.date).toISOString().split("T")[0];
         } else if (
            typeof income.date === "string" &&
            income.date.match(/^\d{4}-\d{2}-\d{2}$/)
         ) {
            // Already in YYYY-MM-DD format
            dateStr = income.date;
         }

         setFormData({
            description: income.description || "",
            expectedAmount: income.expectedAmount || 0,
            receivedAmount: income.receivedAmount || 0,
            date: dateStr,
            category: income.category || "Salary",
            status: income.status || "scheduled",
            notes: income.notes || "",
         });
      }
   }, [income]);

   const handleSubmit = async (e) => {
      e.preventDefault();

      try {
         const { receivedAmount, ...updateData } = formData;
         const status =
            income.receivedAmount &&
            income.receivedAmount >= parseFloat(updateData.expectedAmount)
               ? "received"
               : "scheduled";

         const response = await fetch(`/api/incomes/${income._id}`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               ...updateData,
               date: updateData.date, // Keep as YYYY-MM-DD string
               expectedAmount: parseFloat(updateData.expectedAmount),
               status,
            }),
         });

         if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || "Failed to update income");
         }

         const updatedIncome = await response.json();
         onSave(updatedIncome);
      } catch (error) {
         console.error("Error updating income:", error);
         // You might want to show an error message to the user here
      }
   };

   const handleChange = (e) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
         ...prev,
         [name]: value,
      }));
   };

   return (
      <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
         <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="flex justify-between items-center mb-4">
               <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Edit Income
               </h3>
               <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
               >
                  <span className="sr-only">Close</span>
                  <svg
                     className="h-6 w-6"
                     fill="none"
                     viewBox="0 0 24 24"
                     stroke="currentColor"
                  >
                     <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                     />
                  </svg>
               </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
               <div>
                  <label
                     htmlFor="description"
                     className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                     Description
                  </label>
                  <input
                     type="text"
                     id="description"
                     name="description"
                     value={formData.description}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                     required
                  />
               </div>

               <div>
                  <label
                     htmlFor="date"
                     className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                     Date
                  </label>
                  <input
                     type="date"
                     id="date"
                     name="date"
                     value={formData.date}
                     onChange={handleChange}
                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                     required
                  />
               </div>

               <div>
                  <label
                     htmlFor="expectedAmount"
                     className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                     Expected Amount
                  </label>
                  <input
                     type="number"
                     id="expectedAmount"
                     name="expectedAmount"
                     value={formData.expectedAmount}
                     onChange={handleChange}
                     step="0.01"
                     min="0"
                     className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                     required
                  />
               </div>

               <div>
                  <label
                     htmlFor="receivedAmount"
                     className="block text-sm font-medium text-gray-500 dark:text-gray-400"
                  >
                     Received Amount (not editable)
                  </label>
                  <input
                     type="number"
                     id="receivedAmount"
                     name="receivedAmount"
                     value={formData.receivedAmount}
                     className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 dark:bg-gray-600 shadow-sm focus:ring-0 cursor-not-allowed text-gray-500 dark:text-gray-400 sm:text-sm"
                     disabled
                  />
               </div>

               <div className="flex justify-end space-x-3 mt-6">
                  <button
                     type="button"
                     onClick={onClose}
                     className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
                  >
                     Cancel
                  </button>
                  <button
                     type="submit"
                     className="px-4 py-2 text-sm font-medium text-white bg-gray-600 border border-transparent rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:hover:bg-gray-500"
                  >
                     Save Changes
                  </button>
               </div>
            </form>
         </div>
      </div>
   );
}

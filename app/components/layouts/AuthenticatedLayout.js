"use client";

import { useState, createContext, useContext, useEffect } from "react";
import { useSession } from "next-auth/react";
import { usePathname, useRouter } from "next/navigation";
import { Sidebar } from "../navigation/Sidebar";

const MobileMenuContext = createContext();

export function useMobileMenu() {
   const context = useContext(MobileMenuContext);
   if (!context) {
      throw new Error("useMobileMenu must be used within a MobileMenuProvider");
   }
   return context;
}

export default function AuthenticatedLayout({ children }) {
   const { data: session, status } = useSession();
   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
   const router = useRouter();
   const pathname = usePathname();

   // Force a re-render when the pathname changes
   useEffect(() => {
      if (status !== "loading" && session?.user && pathname) {
         router.refresh();
      }
   }, [pathname, status, session?.user, router]);

   // Check if current page is auth, onboarding, or subscription page
   const isAuthPage = pathname?.startsWith("/auth");
   const isOnboardingPage = pathname?.startsWith("/onboarding");
   const isSubscriptionPage = pathname?.startsWith("/subscription");

   // Handle unauthenticated state - but only redirect if NOT on auth pages
   useEffect(() => {
      if (status !== "loading" && !session?.user && !isAuthPage) {
         router.push("/auth/login");
      }
   }, [status, session?.user, router, isAuthPage]);

   // Show loading state or unauthenticated state without sidebar
   if (status === "loading" || (!session?.user && !isAuthPage)) {
      return (
         <MobileMenuContext.Provider
            value={{ isMobileMenuOpen, setIsMobileMenuOpen }}
         >
            <div className="min-h-screen h-screen flex bg-gray-50 dark:bg-gray-900">
               <main className="flex-1 overflow-auto">{children}</main>
            </div>
         </MobileMenuContext.Provider>
      );
   }

   // Only render the layout with sidebar if authenticated and not on auth, onboarding, or subscription
   return (
      <MobileMenuContext.Provider
         value={{ isMobileMenuOpen, setIsMobileMenuOpen }}
      >
         <div className="min-h-screen h-screen flex bg-gray-50 dark:bg-gray-900">
            {!isAuthPage && !isOnboardingPage && !isSubscriptionPage && (
               <Sidebar
                  isMobileMenuOpen={isMobileMenuOpen}
                  setIsMobileMenuOpen={setIsMobileMenuOpen}
               />
            )}
            <main className="flex-1 overflow-auto">{children}</main>
         </div>
      </MobileMenuContext.Provider>
   );
}

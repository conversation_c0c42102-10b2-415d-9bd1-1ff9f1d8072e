import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Debt from "../../../lib/mongodb/models/Debt";
import Transaction from "../../../lib/mongodb/models/Transaction";
import {
   startOfMonth,
   addMonths,
   eachDayOfInterval,
   format,
   startOfDay,
   addDays,
} from "date-fns";
import { isCashAccount } from "@/app/lib/utils/transactionsUtils";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      // Get months parameters from query string
      const { searchParams } = new URL(request.url);
      const monthsParam = searchParams.get("months");
      const pastMonthsParam = searchParams.get("pastMonths");
      const months = monthsParam ? parseInt(monthsParam) : 3;
      const pastMonths = pastMonthsParam ? parseInt(pastMonthsParam) : 0;

      // Validate months parameters
      if (isNaN(months) || months < 1 || months > 60) {
         return NextResponse.json(
            { error: "Invalid months parameter. Must be between 1 and 60." },
            { status: 400 }
         );
      }
      if (isNaN(pastMonths) || pastMonths < 0 || pastMonths > 60) {
         return NextResponse.json(
            {
               error: "Invalid pastMonths parameter. Must be between 0 and 60.",
            },
            { status: 400 }
         );
      }

      await dbConnect();

      // Find the user by Clerk userId
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Fetch debts for this user
      const debts = await Debt.find({ userId: user._id }).lean();

      // Calculate date ranges
      const currentDate = new Date();
      const todayStart = startOfDay(currentDate);
      const futureEndDate = addMonths(todayStart, months);

      // Find the earliest transaction date to limit historical data
      let earliestTransactionDate = null;
      let actualPastStartDate = todayStart;
      let transactions = [];

      if (pastMonths > 0) {
         // First, find the earliest transaction for this user
         const earliestTransaction = await Transaction.findOne({
            userId: user._id,
         })
            .sort({ date: 1 })
            .lean();

         if (earliestTransaction) {
            earliestTransactionDate = startOfDay(
               new Date(earliestTransaction.date)
            );
            const requestedPastStartDate = addMonths(todayStart, -pastMonths);

            // Use the later of the two dates (don't go back further than first transaction)
            actualPastStartDate =
               earliestTransactionDate > requestedPastStartDate
                  ? earliestTransactionDate
                  : requestedPastStartDate;

            // Fetch historical transactions within the valid range
            transactions = await Transaction.find({
               userId: user._id,
               date: {
                  $gte: actualPastStartDate,
                  $lt: todayStart,
               },
            }).lean();
         } else {
            // No transactions found, so no historical data to show
            actualPastStartDate = todayStart;
         }
      }

      // Generate all days in the range (past + future)
      const days = eachDayOfInterval({
         start: actualPastStartDate,
         end: futureEndDate,
      });

      // Calculate running balance for each day
      const runningBalanceData = [];

      // Current cash balance (sum of all cash accounts)
      const currentCashBalance = (user.accounts || [])
         .filter((account) => isCashAccount(account) && account.active)
         .reduce((sum, account) => sum + (account.balance || 0), 0);

      // Process historical data (past months)
      if (pastMonths > 0 && transactions.length > 0) {
         const historicalData = calculateHistoricalBalance(
            actualPastStartDate,
            todayStart,
            currentCashBalance,
            transactions
         );
         runningBalanceData.push(...historicalData);
      }

      // Calculate today's balance including any expenses due today
      const todayData = calculateTodayBalance(
         todayStart,
         currentCashBalance,
         user,
         debts
      );
      runningBalanceData.push(todayData);

      // Process future data (projection months)
      // Use the adjusted today's balance as the starting point for future calculations
      const futureData = calculateFutureBalance(
         todayStart,
         futureEndDate,
         todayData.balance,
         user,
         debts
      );
      runningBalanceData.push(...futureData);

      // Sort data by date to ensure proper order
      runningBalanceData.sort((a, b) => new Date(a.date) - new Date(b.date));

      // Ensure we have data points at regular intervals for smooth chart
      const smoothedData = smoothChartData(
         runningBalanceData,
         actualPastStartDate,
         futureEndDate,
         todayData.balance
      );

      return NextResponse.json({
         data: smoothedData,
         summary: {
            startingBalance: Number(currentCashBalance.toFixed(2)),
            endingBalance:
               smoothedData.length > 0
                  ? smoothedData[smoothedData.length - 1].balance
                  : currentCashBalance,
            earliestTransactionDate: earliestTransactionDate
               ? format(earliestTransactionDate, "yyyy-MM-dd")
               : null,
            actualHistoricalStartDate:
               actualPastStartDate !== todayStart
                  ? format(actualPastStartDate, "yyyy-MM-dd")
                  : null,
            totalProjectedIncome:
               user.recurringIncomes?.reduce(
                  (sum, income) =>
                     income.enabled
                        ? sum +
                          calculateMonthlyAmount(
                             income.payAmount,
                             income.payPeriod
                          ) *
                             months
                        : sum,
                  0
               ) || 0,
            totalProjectedExpenses:
               (user.recurringExpenses?.reduce(
                  (sum, expense) =>
                     expense.enabled
                        ? sum +
                          calculateMonthlyAmount(
                             expense.amount,
                             expense.frequency
                          ) *
                             months
                        : sum,
                  0
               ) || 0) +
               (debts?.reduce(
                  (sum, debt) =>
                     debt.active
                        ? sum + debt.minimumPayment * months // specified months of debt payments
                        : sum,
                  0
               ) || 0),
         },
      });
   } catch (error) {
      console.error("Error generating running balance report:", error);
      return NextResponse.json(
         { error: "Failed to generate running balance report" },
         { status: 500 }
      );
   }
}

// Helper function to calculate historical balance from transactions
function calculateHistoricalBalance(
   startDate,
   endDate,
   currentBalance,
   transactions
) {
   const data = [];
   const days = eachDayOfInterval({ start: startDate, end: endDate });

   // Group transactions by date
   const transactionsByDate = {};
   transactions.forEach((transaction) => {
      const dateKey = format(new Date(transaction.date), "yyyy-MM-dd");
      if (!transactionsByDate[dateKey]) {
         transactionsByDate[dateKey] = [];
      }
      transactionsByDate[dateKey].push(transaction);
   });

   // Work backwards from current balance to calculate historical balances
   // We start with today's balance and work backwards
   let endOfDayBalance = currentBalance;

   // Process days in reverse order (from today backwards)
   for (let i = days.length - 1; i >= 0; i--) {
      const day = days[i];
      const dateKey = format(day, "yyyy-MM-dd");
      const dayTransactions = transactionsByDate[dateKey] || [];

      // Calculate net change for this day (sum of all transactions)
      let dailyIncome = 0;
      let dailyExpenses = 0;
      let incomeDetails = [];
      let expenseDetails = [];

      dayTransactions.forEach((transaction) => {
         if (transaction.amount > 0) {
            dailyIncome += transaction.amount;
            incomeDetails.push({
               type: "income",
               name: transaction.description || "Transaction",
               amount: transaction.amount,
               frequency: "actual",
            });
         } else {
            const expenseAmount = Math.abs(transaction.amount);
            dailyExpenses += expenseAmount;
            expenseDetails.push({
               type: "expense",
               name: transaction.description || "Transaction",
               amount: expenseAmount,
               frequency: "actual",
            });
         }
      });

      // Calculate the balance at the start of this day (before transactions)
      const dayNetChange = dailyIncome - dailyExpenses;
      const startOfDayBalance = endOfDayBalance - dayNetChange;

      // Add data point if there were transactions or it's the start of a month
      // Show the balance AFTER the day's transactions (end of day balance)
      if (dayTransactions.length > 0 || day.getDate() === 1) {
         data.unshift({
            date: dateKey,
            balance: Number(endOfDayBalance.toFixed(2)), // End of day balance (after transactions)
            income: dailyIncome,
            expenses: dailyExpenses,
            incomeDetails: incomeDetails,
            expenseDetails: expenseDetails,
            isMonthStart: day.getDate() === 1,
            isHistorical: true,
         });
      }

      // Move to the previous day's end-of-day balance
      endOfDayBalance = startOfDayBalance;
   }

   return data;
}

// Helper function to calculate future balance from recurring items
function calculateFutureBalance(
   startDate,
   endDate,
   startingBalance,
   user,
   debts
) {
   const data = [];
   const days = eachDayOfInterval({
      start: addDays(startDate, 1),
      end: endDate,
   });
   let runningBalance = startingBalance;

   for (const day of days) {
      // Check for recurring income on this day
      let dailyIncome = 0;
      let incomeDetails = [];
      for (const income of user.recurringIncomes || []) {
         if (!income.enabled) continue;

         const shouldProcess = shouldProcessRecurringItem(
            income,
            day,
            income.payPeriod,
            income.payDay,
            income.payWeekDay,
            income.lastPaymentDate
         );

         if (shouldProcess) {
            dailyIncome += income.payAmount;
            incomeDetails.push({
               type: "income",
               name: income.description,
               amount: income.payAmount,
               frequency: income.payPeriod,
            });
         }
      }

      // Check for recurring expenses on this day
      let dailyExpenses = 0;
      let expenseDetails = [];

      for (const expense of user.recurringExpenses || []) {
         if (!expense.enabled) continue;

         if (
            shouldProcessRecurringItem(
               expense,
               day,
               expense.frequency,
               expense.dueDay
            )
         ) {
            dailyExpenses += expense.amount;
            expenseDetails.push({
               type: "expense",
               name: expense.name,
               amount: expense.amount,
               frequency: expense.frequency,
            });
         }
      }

      // Check for debt payments on this day (monthly payments) - treat as expenses
      for (const debt of debts || []) {
         if (!debt.active) continue;

         if (
            shouldProcessRecurringItem(
               debt,
               day,
               "monthly", // All debt payments are monthly
               debt.dueDate
            )
         ) {
            dailyExpenses += debt.minimumPayment;
            expenseDetails.push({
               type: "debt",
               name: debt.lender,
               amount: debt.minimumPayment,
               frequency: "monthly",
            });
         }
      }

      // Update running balance
      runningBalance += dailyIncome - dailyExpenses;

      // Add to data array (only add points where there's a change or it's the start of a month)
      if (dailyIncome > 0 || dailyExpenses > 0 || day.getDate() === 1) {
         data.push({
            date: format(day, "yyyy-MM-dd"),
            balance: Number(runningBalance.toFixed(2)),
            income: dailyIncome,
            expenses: dailyExpenses,
            incomeDetails: incomeDetails,
            expenseDetails: expenseDetails,
            isMonthStart: day.getDate() === 1,
            isFuture: true,
         });
      }
   }

   return data;
}

// Helper function to calculate today's balance including any expenses due today
function calculateTodayBalance(todayDate, currentCashBalance, user, debts) {
   let todayIncome = 0;
   let todayExpenses = 0;
   let incomeDetails = [];
   let expenseDetails = [];

   // Check for recurring income due today
   for (const income of user.recurringIncomes || []) {
      if (!income.enabled) continue;

      const shouldProcess = shouldProcessRecurringItem(
         income,
         todayDate,
         income.payPeriod,
         income.payDay,
         income.payWeekDay,
         income.lastPaymentDate
      );

      if (shouldProcess) {
         todayIncome += income.payAmount;
         incomeDetails.push({
            type: "income",
            name: income.description,
            amount: income.payAmount,
            frequency: income.payPeriod,
         });
      }
   }

   // Check for recurring expenses due today
   for (const expense of user.recurringExpenses || []) {
      if (!expense.enabled) continue;

      if (
         shouldProcessRecurringItem(
            expense,
            todayDate,
            expense.frequency,
            expense.dueDay
         )
      ) {
         todayExpenses += expense.amount;
         expenseDetails.push({
            type: "expense",
            name: expense.name,
            amount: expense.amount,
            frequency: expense.frequency,
         });
      }
   }

   // Check for debt payments due today
   for (const debt of debts || []) {
      if (!debt.active) continue;

      if (
         shouldProcessRecurringItem(
            debt,
            todayDate,
            "monthly", // All debt payments are monthly
            debt.dueDate
         )
      ) {
         todayExpenses += debt.minimumPayment;
         expenseDetails.push({
            type: "debt",
            name: debt.lender,
            amount: debt.minimumPayment,
            frequency: "monthly",
         });
      }
   }

   // Calculate balance after today's transactions
   const balanceAfterToday = currentCashBalance + todayIncome - todayExpenses;

   return {
      date: format(todayDate, "yyyy-MM-dd"),
      balance: Number(balanceAfterToday.toFixed(2)),
      income: todayIncome,
      expenses: todayExpenses,
      incomeDetails: incomeDetails,
      expenseDetails: expenseDetails,
      isToday: true,
   };
}

// Helper function to determine if a recurring item should be processed on a given day
function shouldProcessRecurringItem(
   item,
   targetDate,
   frequency,
   dueDay,
   dayOfWeek = null,
   lastPaymentDate = null
) {
   const day = targetDate.getDate();
   const dayOfWeekNum = targetDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
   const month = targetDate.getMonth();

   switch (frequency) {
      case "monthly":
         // Handle end-of-month edge cases
         const targetDay = parseInt(dueDay);
         const lastDayOfMonth = new Date(
            targetDate.getFullYear(),
            month + 1,
            0
         ).getDate();
         const effectiveDay = Math.min(targetDay, lastDayOfMonth);
         return day === effectiveDay;

      case "weekly":
         // For weekly items, use dayOfWeek if provided (for incomes), otherwise use dueDay
         if (dayOfWeek) {
            const dayMap = {
               Sunday: 0,
               Monday: 1,
               Tuesday: 2,
               Wednesday: 3,
               Thursday: 4,
               Friday: 5,
               Saturday: 6,
            };
            return dayOfWeekNum === dayMap[dayOfWeek];
         } else {
            // For expenses, dueDay might be a number (1-7) or day name
            if (isNaN(parseInt(dueDay))) {
               const dayMap = {
                  Sunday: 0,
                  Monday: 1,
                  Tuesday: 2,
                  Wednesday: 3,
                  Thursday: 4,
                  Friday: 5,
                  Saturday: 6,
               };
               return dayOfWeekNum === dayMap[dueDay];
            } else {
               // dueDay is a number (1-7, where 1 = Monday, 7 = Sunday)
               const adjustedDay =
                  parseInt(dueDay) === 7 ? 0 : parseInt(dueDay);
               return dayOfWeekNum === adjustedDay;
            }
         }

      case "biweekly":
         if (!lastPaymentDate) {
            return false;
         }
         const lastPayment = startOfDay(new Date(lastPaymentDate));
         const targetDateNormalized = startOfDay(targetDate);
         const daysSinceLastPayment = Math.floor(
            (targetDateNormalized - lastPayment) / (1000 * 60 * 60 * 24)
         );

         // Check if it's the right day of the week and exactly 14 days (or multiples)
         if (dayOfWeek) {
            const dayMap = {
               Sunday: 0,
               Monday: 1,
               Tuesday: 2,
               Wednesday: 3,
               Thursday: 4,
               Friday: 5,
               Saturday: 6,
            };
            const expectedDayOfWeek = dayMap[dayOfWeek];
            const normalizedDayOfWeek = targetDateNormalized.getDay();
            return (
               normalizedDayOfWeek === expectedDayOfWeek &&
               daysSinceLastPayment > 0 &&
               daysSinceLastPayment % 14 === 0
            );
         }
         return daysSinceLastPayment > 0 && daysSinceLastPayment % 14 === 0;

      case "semimonthly":
         // Typically 1st and 15th of the month
         return day === 1 || day === 15;

      case "quarterly":
         // First day of quarter months (Jan, Apr, Jul, Oct) on the specified day
         const isQuarterMonth = month % 3 === 0;
         const targetQuarterDay = parseInt(dueDay);
         const lastDayOfQuarter = new Date(
            targetDate.getFullYear(),
            month + 1,
            0
         ).getDate();
         const effectiveQuarterDay = Math.min(
            targetQuarterDay,
            lastDayOfQuarter
         );
         return isQuarterMonth && day === effectiveQuarterDay;

      case "annually":
         // January on the specified day
         const targetAnnualDay = parseInt(dueDay);
         const lastDayOfJanuary = new Date(
            targetDate.getFullYear(),
            1,
            0
         ).getDate();
         const effectiveAnnualDay = Math.min(targetAnnualDay, lastDayOfJanuary);
         return month === 0 && day === effectiveAnnualDay;

      default:
         return false;
   }
}

// Helper function to calculate monthly equivalent amount
function calculateMonthlyAmount(amount, frequency) {
   switch (frequency) {
      case "weekly":
         return amount * 4.33; // Average weeks per month
      case "biweekly":
         return amount * 2.17; // Average bi-weeks per month
      case "semimonthly":
         return amount * 2; // Twice per month
      case "monthly":
         return amount;
      case "quarterly":
         return amount / 3;
      case "annually":
         return amount / 12;
      default:
         return 0;
   }
}

// Helper function to smooth chart data for better visualization
function smoothChartData(data, startDate, endDate, todayBalance) {
   const smoothed = [];

   // Only add starting point if we don't have historical data
   // If we have historical data, it should already include the correct starting balance
   const hasHistoricalData = data.some((d) => d.isHistorical);
   const hasDataAtStart = data.some(
      (d) => d.date === format(startDate, "yyyy-MM-dd")
   );

   if (!hasHistoricalData && !hasDataAtStart) {
      // Add starting point only for future-only projections
      smoothed.push({
         date: format(startDate, "yyyy-MM-dd"),
         balance: Number(todayBalance.toFixed(2)),
         income: 0,
         expenses: 0,
         incomeDetails: [],
         expenseDetails: [],
         isMonthStart: true,
      });
   }

   // Add all data points
   smoothed.push(...data);

   // Add end point if needed
   const lastDataPoint = data[data.length - 1];
   if (lastDataPoint && lastDataPoint.date !== format(endDate, "yyyy-MM-dd")) {
      smoothed.push({
         date: format(endDate, "yyyy-MM-dd"),
         balance: lastDataPoint.balance,
         income: 0,
         expenses: 0,
         incomeDetails: [],
         expenseDetails: [],
         isMonthStart: false,
      });
   }

   // If no data points exist, add at least one more point to show a flat line
   if (data.length === 0) {
      smoothed.push({
         date: format(endDate, "yyyy-MM-dd"),
         balance: Number(todayBalance.toFixed(2)),
         income: 0,
         expenses: 0,
         incomeDetails: [],
         expenseDetails: [],
         isMonthStart: false,
      });
   }

   return smoothed;
}

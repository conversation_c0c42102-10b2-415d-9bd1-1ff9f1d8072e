import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";
import Income from "@/app/lib/mongodb/models/Income";
import Expense from "@/app/lib/mongodb/models/Expense";
import Debt from "@/app/lib/mongodb/models/Debt";
import { calculateCashFlowWarnings } from "@/app/lib/utils/warningUtils";
import { getCurrentMongoUser } from "@/app/lib/utils/userUtils";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user data first
      const user = await getCurrentMongoUser();
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Use user's preference for periods ahead, with optional override for testing
      const { searchParams } = new URL(request.url);
      const overridePeriodsAhead = searchParams.get("periodsAhead");

      let periodsAhead;
      if (overridePeriodsAhead) {
         // Allow override for testing purposes
         periodsAhead = parseInt(overridePeriodsAhead);
         if (periodsAhead < 1 || periodsAhead > 12) {
            return NextResponse.json(
               { error: "periodsAhead override must be between 1 and 12" },
               { status: 400 }
            );
         }
      } else {
         // Use user's preference, defaulting to 4
         periodsAhead = user.preferences?.cashFlowWarnings?.periodsAhead || 4;
      }

      // Check if warnings are enabled for this user
      const warningsEnabled =
         user.preferences?.cashFlowWarnings?.enabled !== false;
      if (!warningsEnabled) {
         return NextResponse.json({
            warningsEnabled: false,
            warning: null,
            message: "Cash flow warnings are disabled",
            periodsChecked: periodsAhead,
            usingUserPreference: !overridePeriodsAhead,
         });
      }

      // Calculate date range for fetching scheduled items
      const currentDate = new Date();
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 4);

      // Fetch scheduled incomes and expenses in parallel
      const [scheduledIncomes, scheduledExpenses, debts] = await Promise.all([
         Income.find({
            userId: user._id,
            date: { $gte: currentDate, $lte: futureDate },
            status: { $in: ["scheduled", "projected"] },
         }).lean(),

         Expense.find({
            userId: user._id,
            date: { $gte: currentDate, $lte: futureDate },
            status: {
               $in: [
                  "scheduled",
                  "projected",
                  "funded",
                  "paid",
                  "overpaid",
                  "underpaid",
                  "late",
               ],
            },
         }).lean(),

         Debt.find({
            userId: user._id,
            active: true,
         }).lean(),
      ]);

      // Calculate warnings
      const warning = calculateCashFlowWarnings(
         user,
         scheduledIncomes,
         scheduledExpenses,
         debts,
         currentDate,
         periodsAhead
      );

      // Return results
      return NextResponse.json({
         warningsEnabled: true,
         warning: warning,
         periodsChecked: periodsAhead,
         usingUserPreference: !overridePeriodsAhead,
         userPreference: user.preferences?.cashFlowWarnings?.periodsAhead || 4,
         calculatedAt: new Date().toISOString(),
         debug: {
            userHasMainIncome: !!user.mainIncomeId,
            recurringIncomesCount: user.recurringIncomes?.length || 0,
            recurringExpensesCount: user.recurringExpenses?.length || 0,
            scheduledIncomesCount: scheduledIncomes.length,
            scheduledExpensesCount: scheduledExpenses.length,
            debtsCount: debts.length,
         },
      });
   } catch (error) {
      console.error("Error in cash flow warnings API:", error);
      return NextResponse.json(
         {
            error: "Failed to calculate cash flow warnings",
            details: error.message,
         },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import {
   getUserSubscriptionPlan,
   cancelUserSubscription,
   downgradeToFree,
   reactivateSubscription,
} from "@/app/lib/stripe/subscriptionUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const user = await User.findById(userId);

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const plan = await getUserSubscriptionPlan(user._id);

      return NextResponse.json({
         plan: plan,
         subscription: user.subscription || null,
         stripeCustomerId: user.stripeCustomerId || null,
      });
   } catch (error) {
      console.error("Error getting subscription:", error);
      return NextResponse.json(
         { error: "Failed to get subscription" },
         { status: 500 }
      );
   }
}

export async function DELETE(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const user = await User.findById(userId);

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      await cancelUserSubscription(user._id);

      return NextResponse.json({
         success: true,
         message:
            "Subscription will be canceled at the end of the current period",
      });
   } catch (error) {
      console.error("Error canceling subscription:", error);
      return NextResponse.json(
         { error: "Failed to cancel subscription" },
         { status: 500 }
      );
   }
}

export async function PATCH(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { action } = await request.json();

      if (
         ![
            "downgrade_to_free",
            "reactivate",
            "check_preserved_connections",
         ].includes(action)
      ) {
         return NextResponse.json({ error: "Invalid action" }, { status: 400 });
      }

      await dbConnect();
      const user = await User.findById(userId);

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      if (action === "downgrade_to_free") {
         // Check if user is already on free tier
         if (!user.subscription || user.subscription.planId === "free") {
            return NextResponse.json(
               { error: "User is already on free tier" },
               { status: 400 }
            );
         }

         const result = await downgradeToFree(user._id);

         return NextResponse.json({
            success: true,
            message: result.message,
            plan: "free",
         });
      } else if (action === "reactivate") {
         // Check if user has a subscription that can be reactivated
         if (!user.subscription || !user.subscription.id) {
            return NextResponse.json(
               { error: "No subscription found to reactivate" },
               { status: 400 }
            );
         }

         if (!user.subscription.cancelAtPeriodEnd) {
            return NextResponse.json(
               { error: "Subscription is not scheduled for cancellation" },
               { status: 400 }
            );
         }

         const result = await reactivateSubscription(user._id);

         return NextResponse.json({
            success: true,
            message: result.message,
            plan: user.subscription.planId,
         });
      }
   } catch (error) {
      console.error("Error handling subscription action:", error);
      return NextResponse.json(
         { error: "Failed to process request" },
         { status: 500 }
      );
   }
}

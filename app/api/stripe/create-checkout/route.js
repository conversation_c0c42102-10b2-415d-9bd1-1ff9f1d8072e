import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import stripe, { SUBSCRIPTION_PLANS } from "@/app/lib/stripe/config";
import { createStripeCustomer } from "@/app/lib/stripe/subscriptionUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";

// Helper function to get the base URL with proper protocol
function getBaseUrl(request) {
   // Check if NEXT_PUBLIC_URL is set and has a protocol
   if (process.env.NEXT_PUBLIC_URL) {
      const url = process.env.NEXT_PUBLIC_URL;
      // If it already has a protocol, use it as is
      if (url.startsWith("http://") || url.startsWith("https://")) {
         return url;
      }
      // If no protocol, assume https for production
      return `https://${url}`;
   }

   // Try to construct from request headers
   if (request && request.headers) {
      const host = request.headers.get("host");
      const protocol = request.headers.get("x-forwarded-proto") || "https";
      if (host) {
         return `${protocol}://${host}`;
      }
   }

   // Fallback for development
   return process.env.NODE_ENV === "production"
      ? "https://your-production-domain.com" // You'll need to update this with your actual domain
      : "http://localhost:3000";
}

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      // Debug logging for URL construction
      const baseUrl = getBaseUrl(request);
      console.log("Base URL for Stripe checkout:", baseUrl);

      const { planId } = await request.json();

      // Connect to database and fetch user first
      await dbConnect();
      const user = await User.findById(userId);

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Debug logging for subscription state
      console.log("User subscription state:", {
         userId: user._id,
         hasSubscription: !!user.subscription,
         subscriptionId: user.subscription?.id,
         subscriptionStatus: user.subscription?.status,
         subscriptionPlanId: user.subscription?.planId,
      });

      // Handle free tier setup
      if (planId === "free") {
         const { setupFreeTier } = await import(
            "@/app/lib/stripe/subscriptionUtils"
         );

         // Set up free tier (database-only, no Stripe subscription needed)
         await setupFreeTier(user);

         return NextResponse.json({
            success: true,
            type: "free_tier",
            message: "Free tier set up successfully (no invoices created)",
         });
      }

      // Validate plan
      if (!planId || !SUBSCRIPTION_PLANS[planId]) {
         return NextResponse.json({ error: "Invalid plan" }, { status: 400 });
      }

      const plan = SUBSCRIPTION_PLANS[planId];
      if (!plan.priceId) {
         return NextResponse.json(
            { error: "Plan not configured" },
            { status: 400 }
         );
      }

      // Create Stripe customer if doesn't exist
      let stripeCustomerId = user.stripeCustomerId;
      if (!stripeCustomerId) {
         stripeCustomerId = await createStripeCustomer(user);
         await User.findByIdAndUpdate(user._id, { stripeCustomerId });
      }

      // Check if user already has an active subscription
      if (
         user.subscription &&
         user.subscription.status === "active" &&
         user.subscription.id
      ) {
         // If upgrading, create a subscription modification
         const currentPlan = SUBSCRIPTION_PLANS[user.subscription.planId];
         if (currentPlan && plan.price > currentPlan.price) {
            // This is an upgrade - handle subscription modification
            try {
               const subscription = await stripe.subscriptions.retrieve(
                  user.subscription.id
               );

               const updatedSubscription = await stripe.subscriptions.update(
                  user.subscription.id,
                  {
                     items: [
                        {
                           id: subscription.items.data[0].id,
                           price: plan.priceId,
                        },
                     ],
                     proration_behavior: "create_prorations",
                  }
               );

               // Update user's subscription in database
               await User.findByIdAndUpdate(user._id, {
                  "subscription.planId": planId,
                  "subscription.status": updatedSubscription.status,
                  "subscription.currentPeriodStart": new Date(
                     updatedSubscription.current_period_start * 1000
                  ),
                  "subscription.currentPeriodEnd": new Date(
                     updatedSubscription.current_period_end * 1000
                  ),
                  updatedAt: new Date(),
               });

               return NextResponse.json({ success: true, type: "upgrade" });
            } catch (stripeError) {
               console.error("Error upgrading subscription:", stripeError);
               console.error("User subscription ID:", user.subscription.id);
               return NextResponse.json(
                  { error: "Failed to upgrade subscription" },
                  { status: 500 }
               );
            }
         }
      }

      // Create checkout session for new subscription
      const session = await stripe.checkout.sessions.create({
         customer: stripeCustomerId,
         payment_method_types: ["card"],
         line_items: [
            {
               price: plan.priceId,
               quantity: 1,
            },
         ],
         mode: "subscription",
         allow_promotion_codes: true,
         success_url: `${baseUrl}/settings?tab=subscription&success=true`,
         cancel_url: `${baseUrl}/settings?tab=subscription&canceled=true`,
         metadata: {
            userId: user._id.toString(),
            planId: planId,
         },
      });

      return NextResponse.json({
         sessionId: session.id,
         url: session.url,
      });
   } catch (error) {
      console.error("Error creating checkout session:", error);
      return NextResponse.json(
         { error: "Failed to create checkout session" },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { headers } from "next/headers";
import stripe from "@/app/lib/stripe/config";
import { updateUserSubscription } from "@/app/lib/stripe/subscriptionUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";

export async function POST(request) {
   try {
      const body = await request.text();
      const signature = headers().get("stripe-signature");

      if (!signature) {
         return NextResponse.json({ error: "No signature" }, { status: 400 });
      }

      let event;
      try {
         event = stripe.webhooks.constructEvent(
            body,
            signature,
            process.env.STRIPE_WEBHOOK_SECRET
         );
      } catch (err) {
         console.error("Webhook signature verification failed:", err);
         return NextResponse.json(
            { error: "Invalid signature" },
            { status: 400 }
         );
      }

      await dbConnect();

      console.log("Processing webhook event:", event.type);
      console.log("Event data:", JSON.stringify(event.data.object, null, 2));

      switch (event.type) {
         case "checkout.session.completed":
            console.log("Processing checkout.session.completed");
            await handleCheckoutSessionCompleted(event.data.object);
            break;

         case "customer.subscription.created":
         case "customer.subscription.updated":
            console.log("Processing subscription updated/created");
            await handleSubscriptionUpdated(event.data.object);
            break;

         case "customer.subscription.deleted":
            console.log("Processing subscription deleted");
            await handleSubscriptionDeleted(event.data.object);
            break;

         case "invoice.payment_succeeded":
            console.log("Processing invoice payment succeeded");
            await handleInvoicePaymentSucceeded(event.data.object);
            break;

         case "invoice.payment_failed":
            console.log("Processing invoice payment failed");
            await handleInvoicePaymentFailed(event.data.object);
            break;

         case "customer.subscription.trial_will_end":
            console.log("Processing subscription trial will end");
            await handleTrialWillEnd(event.data.object);
            break;

         case "invoice.upcoming":
            console.log("Processing upcoming invoice");
            await handleUpcomingInvoice(event.data.object);
            break;

         case "customer.created":
            console.log("Processing customer created");
            await handleCustomerCreated(event.data.object);
            break;

         case "customer.updated":
            console.log("Processing customer updated");
            await handleCustomerUpdated(event.data.object);
            break;

         default:
            console.log(`Unhandled event type: ${event.type}`);
      }

      return NextResponse.json({ received: true });
   } catch (error) {
      console.error("Webhook error:", error);
      return NextResponse.json(
         { error: "Webhook handler failed" },
         { status: 500 }
      );
   }
}

async function handleCheckoutSessionCompleted(session) {
   console.log(
      "handleCheckoutSessionCompleted called with session:",
      session.id
   );

   const userId = session.metadata.userId;
   const planId = session.metadata.planId;

   console.log("Extracted metadata - userId:", userId, "planId:", planId);

   if (!userId || !planId) {
      console.error("Missing metadata in checkout session");
      return;
   }

   // Get the subscription
   console.log("Retrieving subscription:", session.subscription);
   const subscription = await stripe.subscriptions.retrieve(
      session.subscription
   );

   console.log(
      "Retrieved subscription:",
      subscription.id,
      "status:",
      subscription.status
   );

   const subscriptionData = {
      id: subscription.id,
      status: subscription.status,
      planId: planId,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
   };

   console.log("Updating user subscription with data:", subscriptionData);

   await updateUserSubscription(userId, subscriptionData);

   console.log("User subscription updated successfully");
}

async function handleSubscriptionUpdated(subscription) {
   console.log(
      "Subscription updated:",
      subscription.id,
      "status:",
      subscription.status
   );

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: subscription.customer });

   if (!user) {
      console.error("No user found for customer:", subscription.customer);
      return;
   }

   // Get the price to determine plan
   const priceId = subscription.items.data[0].price.id;
   let planId = "free";

   if (priceId === process.env.STRIPE_BASIC_PRICE_ID) {
      planId = "basic";
   } else if (priceId === process.env.STRIPE_PRO_PRICE_ID) {
      planId = "pro";
   }

   // If subscription is canceled or incomplete_expired, move to free plan
   if (
      subscription.status === "canceled" ||
      subscription.status === "incomplete_expired"
   ) {
      console.log(
         `Moving user ${user.email} to free plan due to subscription status: ${subscription.status}`
      );
      planId = "free";
   }

   console.log(
      `Updating subscription for user ${user.email}: plan=${planId}, status=${subscription.status}, cancel_at_period_end=${subscription.cancel_at_period_end}`
   );

   await updateUserSubscription(user._id, {
      id: subscription.id,
      status: subscription.status,
      planId: planId,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
   });

   // If subscription is active and user is upgrading from free, they will need to reconnect their banks
   if (subscription.status === "active" && planId !== "free") {
      console.log(
         `User ${user.email} upgraded to ${planId} plan and can now connect bank accounts`
      );
   }
}

async function handleSubscriptionDeleted(subscription) {
   console.log("Subscription deleted:", subscription.id);

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: subscription.customer });

   if (!user) {
      console.error("No user found for customer:", subscription.customer);
      return;
   }

   console.log(
      `Moving user ${user.email} to free plan due to subscription deletion`
   );

   // Disconnect Plaid connections to stop billing
   const { disconnectPlaidConnections } = await import(
      "@/app/lib/stripe/subscriptionUtils"
   );

   try {
      await disconnectPlaidConnections(user._id);
      console.log(`Disconnected Plaid connections for user ${user.email}`);
   } catch (error) {
      console.error(
         `Error disconnecting Plaid connections for user ${user.email}:`,
         error
      );
      // Continue with subscription update even if disconnection fails
   }

   // Reset user to free plan and clear subscription data
   await User.findByIdAndUpdate(user._id, {
      "subscription.id": null,
      "subscription.status": "active", // Keep status as active for free tier
      "subscription.planId": "free",
      "subscription.currentPeriodStart": new Date(),
      "subscription.currentPeriodEnd": null,
      "subscription.cancelAtPeriodEnd": false,
      "subscription.nextPlanId": null,
      updatedAt: new Date(),
   });

   console.log(
      `User ${user.email} successfully moved to free plan with suspended connections`
   );
}

async function handleInvoicePaymentSucceeded(invoice) {
   console.log("Invoice payment succeeded for customer:", invoice.customer);

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: invoice.customer });

   if (!user) {
      console.error("No user found for customer:", invoice.customer);
      return;
   }

   console.log(
      `Payment succeeded for user ${user.email}, setting subscription to active`
   );

   // Update subscription status to active
   await User.findByIdAndUpdate(user._id, {
      "subscription.status": "active",
      updatedAt: new Date(),
   });
}

async function handleInvoicePaymentFailed(invoice) {
   console.log("Invoice payment failed for customer:", invoice.customer);

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: invoice.customer });

   if (!user) {
      console.error("No user found for customer:", invoice.customer);
      return;
   }

   console.log(
      `Payment failed for user ${user.email}, setting subscription to past_due`
   );

   // Update subscription status to past_due
   await User.findByIdAndUpdate(user._id, {
      "subscription.status": "past_due",
      updatedAt: new Date(),
   });
}

async function handleTrialWillEnd(subscription) {
   // Handle trial ending notification
   console.log("Trial will end for subscription:", subscription.id);

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: subscription.customer });

   if (user) {
      // You could send an email notification here
      // or update a flag in the database
      console.log(`Trial ending soon for user: ${user.email}`);
   }
}

async function handleUpcomingInvoice(invoice) {
   // Handle upcoming payment notification
   console.log("Upcoming invoice for customer:", invoice.customer);

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: invoice.customer });

   if (user) {
      // You could send a payment reminder email here
      console.log(`Upcoming payment for user: ${user.email}`);
   }
}

async function handleCustomerCreated(customer) {
   // Handle customer creation - useful for backup sync
   console.log("Customer created:", customer.id);

   await dbConnect();

   // Try to find user by email and update with Stripe customer ID if missing
   if (customer.email) {
      const user = await User.findOne({
         email: customer.email,
         stripeCustomerId: { $exists: false },
      });

      if (user) {
         await User.findByIdAndUpdate(user._id, {
            stripeCustomerId: customer.id,
            updatedAt: new Date(),
         });
         console.log(`Updated user ${user.email} with Stripe customer ID`);
      }
   }
}

async function handleCustomerUpdated(customer) {
   // Handle customer updates
   console.log("Customer updated:", customer.id);

   await dbConnect();
   const user = await User.findOne({ stripeCustomerId: customer.id });

   if (user) {
      // Update user email if it changed in Stripe
      if (customer.email && customer.email !== user.email) {
         await User.findByIdAndUpdate(user._id, {
            email: customer.email,
            updatedAt: new Date(),
         });
         console.log(`Updated email for user: ${customer.email}`);
      }
   }
}

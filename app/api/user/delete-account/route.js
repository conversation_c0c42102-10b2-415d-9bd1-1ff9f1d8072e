import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import { plaidClient } from "../../../lib/plaid/config";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Transaction from "../../../lib/mongodb/models/Transaction";
import Expense from "../../../lib/mongodb/models/Expense";
import Income from "../../../lib/mongodb/models/Income";
import Debt from "../../../lib/mongodb/models/Debt";
import mongoose from "mongoose";

export async function DELETE(request) {
   let mongoSession;
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Start MongoDB transaction for data consistency
      mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      // Get user from MongoDB
      const user = await User.findById(userId).session(mongoSession);
      if (!user) {
         await mongoSession.abortTransaction();
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      console.log(`Starting account deletion for user: ${user._id}`);

      // Step 1: Disconnect all Plaid items
      if (user.plaidItems && user.plaidItems.length > 0) {
         console.log(`Disconnecting ${user.plaidItems.length} Plaid items...`);

         for (const plaidItem of user.plaidItems) {
            try {
               await plaidClient.itemRemove({
                  access_token: plaidItem.accessToken,
               });
               console.log(
                  `Successfully disconnected Plaid item: ${plaidItem.itemId}`
               );
            } catch (plaidError) {
               console.error(
                  `Error disconnecting Plaid item ${plaidItem.itemId}:`,
                  plaidError
               );
               // Continue with deletion even if Plaid disconnection fails
            }
         }
      }

      // Step 2: Delete all user data from MongoDB collections
      console.log("Deleting user data from MongoDB collections...");

      // Delete transactions
      const transactionDeleteResult = await Transaction.deleteMany(
         { userId: user._id },
         { session: mongoSession }
      );
      console.log(
         `Deleted ${transactionDeleteResult.deletedCount} transactions`
      );

      // Delete expenses
      const expenseDeleteResult = await Expense.deleteMany(
         { userId: user._id.toString() },
         { session: mongoSession }
      );
      console.log(`Deleted ${expenseDeleteResult.deletedCount} expenses`);

      // Delete incomes
      const incomeDeleteResult = await Income.deleteMany(
         { userId: user._id.toString() },
         { session: mongoSession }
      );
      console.log(`Deleted ${incomeDeleteResult.deletedCount} incomes`);

      // Delete debts
      const debtDeleteResult = await Debt.deleteMany(
         { userId: user._id },
         { session: mongoSession }
      );
      console.log(`Deleted ${debtDeleteResult.deletedCount} debts`);

      // Delete user document
      await User.deleteOne({ _id: user._id }, { session: mongoSession });
      console.log("Deleted user document from MongoDB");

      // Commit MongoDB transaction
      await mongoSession.commitTransaction();
      console.log("MongoDB transaction committed successfully");

      // Step 3: Delete user from Clerk
      try {
         const client = await clerkClient();
         await client.users.deleteUser(userId);
         console.log("Successfully deleted user from Clerk");
      } catch (clerkError) {
         console.error("Error deleting user from Clerk:", clerkError);
         // Note: MongoDB data is already deleted, so we can't rollback
         // This is a rare edge case that would need manual cleanup
         return NextResponse.json(
            {
               error: "Account data deleted but Clerk deletion failed. Please contact support.",
               details: clerkError.message,
            },
            { status: 500 }
         );
      }

      console.log(
         `Account deletion completed successfully for user: ${user._id}`
      );

      return NextResponse.json({
         success: true,
         message: "Account deleted successfully",
         deletedData: {
            transactions: transactionDeleteResult.deletedCount,
            expenses: expenseDeleteResult.deletedCount,
            incomes: incomeDeleteResult.deletedCount,
            debts: debtDeleteResult.deletedCount,
            plaidItems: user.plaidItems?.length || 0,
            accounts: user.accounts?.length || 0,
         },
      });
   } catch (error) {
      console.error("Error deleting account:", error);

      // Rollback MongoDB transaction if it exists
      if (mongoSession) {
         try {
            await mongoSession.abortTransaction();
            console.log("MongoDB transaction rolled back");
         } catch (rollbackError) {
            console.error("Error rolling back transaction:", rollbackError);
         }
      }

      return NextResponse.json(
         {
            error: "Failed to delete account",
            details: error.message,
         },
         { status: 500 }
      );
   } finally {
      // End MongoDB session
      if (mongoSession) {
         mongoSession.endSession();
      }
   }
}

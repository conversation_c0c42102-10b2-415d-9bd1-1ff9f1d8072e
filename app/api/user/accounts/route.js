import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import mongoose from "mongoose";
import Transaction from "../../../lib/mongodb/models/Transaction";
import Income from "../../../lib/mongodb/models/Income";
import { plaidClient } from "../../../lib/plaid/config";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Filter only active accounts
      const activeAccounts = user.accounts.filter((account) => account.active);

      return NextResponse.json({
         accounts: activeAccounts,
         plaidItems: user.plaidItems,
      });
   } catch (error) {
      console.error("Error in GET /api/user/accounts:", error);
      return NextResponse.json(
         { error: "Failed to fetch accounts" },
         { status: 500 }
      );
   }
}

export async function POST(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { account } = await request.json();
      if (!account) {
         return NextResponse.json(
            { error: "Account data is required" },
            { status: 400 }
         );
      }

      // Generate a numeric ID for the account
      const lastAccount =
         user.accounts.length > 0
            ? user.accounts.reduce((max, acc) => Math.max(max, acc._id), 0)
            : 0;
      const newId = lastAccount + 1;

      // Process account data
      const processedAccount = {
         ...account,
         _id: newId,
         balance: Number(account.balance || 0),
         // Handle credit/loan specific fields
         minimumPayment: Number(account.minimumPayment || 0),
         interestRate: Number(account.interestRate || 0),
         dueDate: account.dueDate || "1",
         // Ensure plaidItemId is preserved if provided
         plaidItemId: account.plaidItemId || null,
      };

      // Add the account
      user.accounts.push(processedAccount);
      await user.save();

      // If account has a starting balance, create income and transaction entries
      if (processedAccount.balance !== 0) {
         // Create a starting balance income entry
         const startingBalanceIncome = new Income({
            userId: user._id,
            description: `Starting Balance - ${processedAccount.name}`,
            expectedAmount: Math.abs(processedAccount.balance),
            receivedAmount: Math.abs(processedAccount.balance),
            status: "received",
            date: new Date(),
            type: "one-time",
         });

         await startingBalanceIncome.save();

         // Create a starting balance transaction
         const startingBalanceTransaction = new Transaction({
            userId: user._id,
            type: "Income",
            amount: processedAccount.balance,
            payee: `Starting Balance - ${processedAccount.name}`,
            date: new Date(),
            accountId: processedAccount._id,
            status: "cleared",
            assignedTo: startingBalanceIncome._id,
            assignedToType: "Income",
         });

         await startingBalanceTransaction.save();

         // Update user's total income and current balance
         const currentTotalIncome = user.totalIncome || 0;
         const currentBalance = user.currentBalance || 0;

         await User.findByIdAndUpdate(user._id, {
            $set: {
               totalIncome: Number(
                  (
                     currentTotalIncome + Math.abs(processedAccount.balance)
                  ).toFixed(2)
               ),
               currentBalance: Number(
                  (currentBalance + processedAccount.balance).toFixed(2)
               ),
            },
         });
      }

      return NextResponse.json({
         success: true,
         message: "Account created successfully",
         account: processedAccount,
      });
   } catch (error) {
      console.error("Error in POST /api/user/accounts:", error);
      return NextResponse.json(
         { error: "Failed to create account", details: error.message },
         { status: 500 }
      );
   }
}

export async function PUT(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { account } = await request.json();
      if (!account) {
         return NextResponse.json(
            { error: "Account data is required" },
            { status: 400 }
         );
      }

      // Find the account index
      const accountIndex = user.accounts.findIndex(
         (acc) => String(acc._id) === String(account._id)
      );

      if (accountIndex === -1) {
         return NextResponse.json(
            { error: `Account not found: ${account._id}` },
            { status: 404 }
         );
      }

      // Start a MongoDB session for transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Calculate current account balance from transactions
         const transactions = await Transaction.find({
            userId: user._id,
            accountId: account._id,
         }).session(mongoSession);

         let calculatedBalance = 0;
         transactions.forEach((transaction) => {
            if (transaction.type === "Transfer") {
               if (
                  String(transaction.transferDetails?.fromAccountId) ===
                  String(account._id)
               ) {
                  calculatedBalance -= Math.abs(transaction.amount);
               } else if (
                  String(transaction.transferDetails?.toAccountId) ===
                  String(account._id)
               ) {
                  calculatedBalance += Math.abs(transaction.amount);
               }
            } else {
               calculatedBalance += transaction.amount;
            }
         });

         calculatedBalance = Number(calculatedBalance.toFixed(2));

         // Get the user-provided balance and ensure it's a number
         const userProvidedBalance = Number(account.balance || 0);

         // Process account data without changing the balance yet
         const processedAccount = {
            ...user.accounts[accountIndex],
            ...account,
            // Keep the calculated balance for now
            balance: calculatedBalance,
            // Handle credit/loan specific fields
            minimumPayment: Number(account.minimumPayment || 0),
            interestRate: Number(account.interestRate || 0),
            dueDate: account.dueDate || "1",
         };

         // Check if there's a difference between the calculated and user-provided balance
         const balanceDifference = Number(
            (userProvidedBalance - calculatedBalance).toFixed(2)
         );

         // If there's a difference, create an adjustment transaction
         if (Math.abs(balanceDifference) > 0.01) {
            // Determine transaction type based on the sign of the difference
            const transactionType =
               balanceDifference > 0 ? "Income" : "Expense";

            // Create the adjustment transaction
            await Transaction.create(
               [
                  {
                     userId: user._id,
                     type: transactionType,
                     amount: balanceDifference,
                     payee: `Balance Adjustment - ${account.name}`,
                     date: new Date(),
                     accountId: account._id,
                     status: "cleared",
                     isAdjustment: true,
                     notes: "Manual balance adjustment",
                  },
               ],
               { session: mongoSession }
            );

            // Update the account balance with the new value
            processedAccount.balance = userProvidedBalance;
         }

         // Update the account in the user document
         user.accounts[accountIndex] = processedAccount;
         await user.save({ session: mongoSession });

         // Commit the transaction
         await mongoSession.commitTransaction();

         return NextResponse.json({
            success: true,
            message: "Account updated successfully",
            account: processedAccount,
         });
      } catch (error) {
         // If an error occurred, abort the transaction
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         // End the session
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error in PUT /api/user/accounts:", error);
      return NextResponse.json(
         { error: "Failed to update account", details: error.message },
         { status: 500 }
      );
   }
}

export async function DELETE(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const { accountId } = await request.json();
      if (!accountId) {
         return NextResponse.json(
            { error: "Account ID is required" },
            { status: 400 }
         );
      }

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Find the account to be deleted
      const account = user.accounts.find(
         (acc) => String(acc._id) === String(accountId)
      );
      if (!account) {
         return NextResponse.json(
            { error: "Account not found" },
            { status: 404 }
         );
      }

      // If the account is linked to Plaid, disconnect it
      if (account.plaidItemId) {
         const plaidItem = user.plaidItems.find(
            (item) => item.itemId === account.plaidItemId
         );
         if (plaidItem) {
            try {
               await plaidClient.itemRemove({
                  access_token: plaidItem.accessToken,
               });
            } catch (plaidError) {
               console.error("Error removing item from Plaid:", plaidError);
               // Continue with local cleanup even if Plaid removal fails
            }

            // Remove the Plaid item from the user's plaidItems array
            user.plaidItems = user.plaidItems.filter(
               (item) => item.itemId !== account.plaidItemId
            );
         }
      }

      // Remove the account
      user.accounts = user.accounts.filter(
         (acc) => String(acc._id) !== String(accountId)
      );

      // Delete all transactions associated with this account
      const transactionsToDelete = await Transaction.find({
         userId: user._id,
         accountId: accountId,
      });

      // Calculate the total amount impact before deletion
      let totalIncomeImpact = 0;
      let totalBalanceImpact = 0;

      transactionsToDelete.forEach((transaction) => {
         totalBalanceImpact += transaction.amount;

         // Only count as income if it's an income transaction not assigned to an expense assignedTo
         if (
            transaction.type === "Income" &&
            (!transaction.assignedTo || transaction.assignedToType === "Income")
         ) {
            totalIncomeImpact += transaction.amount;
         }
      });

      const deleteResult = await Transaction.deleteMany({
         userId: user._id,
         accountId: accountId,
      });

      console.log(
         `Deleted ${deleteResult.deletedCount} transactions for account ${accountId}`
      );

      // Also delete any transactions where this account was used in transfers
      const transferTransactionsToDelete = await Transaction.find({
         userId: user._id,
         $or: [
            { "transferDetails.fromAccountId": accountId },
            { "transferDetails.toAccountId": accountId },
         ],
      });

      // Calculate transfer impact on balance
      transferTransactionsToDelete.forEach((transaction) => {
         totalBalanceImpact += transaction.amount;

         if (
            transaction.type === "Income" &&
            (!transaction.assignedTo || transaction.assignedToType === "Income")
         ) {
            totalIncomeImpact += transaction.amount;
         }
      });

      const transferDeleteResult = await Transaction.deleteMany({
         userId: user._id,
         $or: [
            { "transferDetails.fromAccountId": accountId },
            { "transferDetails.toAccountId": accountId },
         ],
      });

      console.log(
         `Deleted ${transferDeleteResult.deletedCount} transfer transactions involving account ${accountId}`
      );

      // Delete any income entries that were created for starting balance for this account
      const incomeDeleteResult = await Income.deleteMany({
         userId: user._id,
         description: { $regex: `Starting Balance - ${account.name}` },
      });

      console.log(
         `Deleted ${incomeDeleteResult.deletedCount} starting balance income entries for account ${accountId}`
      );

      // Update user totals to reflect the deleted transactions
      const currentTotalIncome = user.totalIncome || 0;
      const currentBalance = user.currentBalance || 0;

      await User.findByIdAndUpdate(user._id, {
         $set: {
            totalIncome: Number(
               (currentTotalIncome - totalIncomeImpact).toFixed(2)
            ),
            currentBalance: Number(
               (currentBalance - totalBalanceImpact).toFixed(2)
            ),
         },
      });

      // Remove associated credit card payment expense if this was a credit card
      if (account.accountType === "credit") {
         user.recurringExpenses = user.recurringExpenses.filter(
            (expense) =>
               !(
                  String(expense.linkedAccountId) === String(accountId) &&
                  expense.type === "credit_card_payment"
               )
         );
      }

      await user.save();

      return NextResponse.json({
         success: true,
         message: "Account deleted successfully",
      });
   } catch (error) {
      console.error("Error in DELETE /api/user/accounts:", error);
      return NextResponse.json(
         { error: "Failed to delete account", details: error.message },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";
import Transaction from "@/app/lib/mongodb/models/Transaction";
import mongoose from "mongoose";
import Income from "@/app/lib/mongodb/models/Income";
import Debt from "@/app/lib/mongodb/models/Debt";

export async function POST(request, { params }) {
   try {
      const {
         session: authSession,
         userId: clerkUserId,
         error,
      } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const { userId } = await params;

      // Verify the user owns this resource
      if (clerkUserId !== userId) {
         return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }

      // Find the MongoDB user by Clerk ID
      const user = await User.findById(clerkUserId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const mongoUserId = user._id;

      // Start a MongoDB session for transaction atomicity
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         const { incomes, accounts, debts, expenses } = await request.json();

         // Get the current user to check for existing accounts
         const currentUser = await User.findById(mongoUserId);
         if (!currentUser) {
            throw new Error("User not found");
         }

         console.log("Onboarding completion - existing accounts:", {
            userId: mongoUserId,
            existingAccountCount: currentUser.accounts?.length || 0,
            existingAccounts:
               currentUser.accounts?.map((acc) => ({
                  id: acc._id,
                  name: acc.name,
                  plaidItemId: acc.plaidItemId,
                  balance: acc.balance,
               })) || [],
            incomingAccountCount: accounts.length,
         });

         // Handle accounts: update existing ones with plaidItemId, create new ones without
         const processedAccounts = [];
         let nextAccountId = 1;

         // Find the highest existing account ID
         if (currentUser.accounts && currentUser.accounts.length > 0) {
            nextAccountId =
               Math.max(...currentUser.accounts.map((acc) => acc._id)) + 1;
         }

         // Process each account from onboarding
         for (const [index, account] of accounts.entries()) {
            if (account.plaidItemId) {
               // Find existing account with this plaidItemId
               const existingAccount = currentUser.accounts.find(
                  (acc) => acc.plaidItemId === account.plaidItemId
               );

               if (existingAccount) {
                  // Update existing account with onboarding data
                  const updatedAccount = {
                     _id: existingAccount._id,
                     name: account.name,
                     bank: account.bank,
                     accountType: account.accountType,
                     balance: Number(account.balance),
                     active: Boolean(account.active),
                     plaidItemId: account.plaidItemId,
                  };

                  processedAccounts.push(updatedAccount);

                  console.log("Updated existing account:", {
                     accountId: existingAccount._id,
                     plaidItemId: account.plaidItemId,
                     oldName: existingAccount.name,
                     newName: account.name,
                     oldBalance: existingAccount.balance,
                     newBalance: updatedAccount.balance,
                  });
               } else {
                  // Create new account (shouldn't happen but handle it)
                  const newAccount = {
                     _id: nextAccountId++,
                     name: account.name,
                     bank: account.bank,
                     accountType: account.accountType,
                     balance: Number(account.balance),
                     active: Boolean(account.active),
                     plaidItemId: account.plaidItemId,
                  };

                  processedAccounts.push(newAccount);

                  console.log(
                     "Created new account with plaidItemId (unexpected):",
                     {
                        accountId: newAccount._id,
                        plaidItemId: account.plaidItemId,
                        name: account.name,
                     }
                  );
               }
            } else {
               // Create new account without plaidItemId
               const newAccount = {
                  _id: nextAccountId++,
                  name: account.name,
                  bank: account.bank,
                  accountType: account.accountType,
                  balance: Number(account.balance),
                  active: Boolean(account.active),
                  plaidItemId: null,
               };

               processedAccounts.push(newAccount);

               console.log("Created new account without plaidItemId:", {
                  accountId: newAccount._id,
                  name: account.name,
                  bank: account.bank,
               });
            }
         }

         console.log("Account processing complete:", {
            totalProcessedAccounts: processedAccounts.length,
            accountsWithPlaidItemId: processedAccounts.filter(
               (acc) => acc.plaidItemId
            ).length,
            accountsWithoutPlaidItemId: processedAccounts.filter(
               (acc) => !acc.plaidItemId
            ).length,
         });

         // Calculate total starting balance
         const totalStartingBalance = processedAccounts.reduce(
            (sum, account) => sum + Math.abs(account.balance),
            0
         );

         // Create a scheduled income for starting balances
         const startingBalanceIncome = new Income({
            userId: mongoUserId,
            description: "Starting Balance",
            expectedAmount: totalStartingBalance,
            receivedAmount: totalStartingBalance,
            status: "received",
            date: new Date(),
            type: "one-time",
         });

         await startingBalanceIncome.save({ session: mongoSession });

         // Process incomes to match schema and assign ObjectIds
         const processedIncomes = incomes.map((income) => {
            const incomeId = new mongoose.Types.ObjectId();
            return {
               _id: incomeId,
               description: income.description,
               payAmount: Number(income.payAmount),
               payPeriod: income.payPeriod,
               payDay: income.payDay ? String(income.payDay) : undefined,
               payDayOfWeek: income.payDayOfWeek
                  ? String(income.payDayOfWeek)
                  : undefined,
               payWeekDay: income.payWeekDay || "Monday",
               enabled: Boolean(income.enabled),
               isMainIncome: Boolean(income.isMainIncome),
            };
         });

         // Create debt entries in the Debt collection
         for (const debt of debts) {
            const newDebt = new Debt({
               userId: mongoUserId,
               debtType: debt.debtType,
               lender: debt.lender,
               balance: Number(debt.balance),
               apr: Number(debt.apr),
               minimumPayment: Number(debt.minimumPayment),
               dueDate: String(debt.dueDate),
               active: Boolean(debt.active),
               history: [],
            });
            await newDebt.save({ session: mongoSession });
         }

         // Process expenses to match schema
         const processedExpenses = expenses.map((expense, index) => ({
            id: index + 1,
            name: expense.name,
            amount: Number(expense.amount),
            frequency: expense.frequency,
            dueDay: String(expense.dueDay),
            enabled: Boolean(expense.enabled),
            type: expense.type || "regular",
            isAutomated: Boolean(expense.isAutomated),
            ...(expense.frequency === "weekly"
               ? { weeklyChargeType: expense.weeklyChargeType }
               : {}),
            ...(expense.frequency === "annually"
               ? { dueMonth: String(expense.dueMonth || "1") }
               : {}),
         }));

         // Find the main income
         const mainIncome = processedIncomes.find(
            (income) => income.isMainIncome
         );

         // Create update object
         const updateData = {
            recurringIncomes: processedIncomes,
            accounts: processedAccounts,
            recurringExpenses: processedExpenses,
            onboardingComplete: true,
         };

         // Only add main income ID if there is a main income
         if (mainIncome) {
            updateData.mainIncomeId = mainIncome._id;
         }

         // Update user with processed data
         console.log("Updating user with onboarding data:", {
            userId: mongoUserId,
            updateData: {
               recurringIncomesCount: updateData.recurringIncomes.length,
               accountsCount: updateData.accounts.length,
               recurringExpensesCount: updateData.recurringExpenses.length,
               onboardingComplete: updateData.onboardingComplete,
               mainIncomeId: updateData.mainIncomeId,
            },
         });

         const updatedUser = await User.findByIdAndUpdate(
            mongoUserId,
            { $set: updateData },
            { new: true, runValidators: true, session: mongoSession }
         );

         if (!updatedUser) {
            throw new Error("User not found");
         }

         console.log("Successfully updated user with onboarding data:", {
            userId: mongoUserId,
            onboardingComplete: updatedUser.onboardingComplete,
         });

         // Handle Plaid account pairing for accounts with plaidItemId
         for (const account of processedAccounts) {
            if (account.plaidItemId) {
               // Find and update the corresponding plaidItem to link it to this account
               await User.findOneAndUpdate(
                  {
                     _id: mongoUserId,
                     "plaidItems.itemId": account.plaidItemId,
                  },
                  {
                     $set: {
                        "plaidItems.$.linkedAccountId": account._id,
                     },
                  },
                  { session: mongoSession }
               );
            }
         }

         // Create starting balance transactions for each account
         const startingBalanceTransactions = processedAccounts
            .filter((account) => account.balance !== 0)
            .map((account) => ({
               userId: mongoUserId,
               type: "Income",
               amount: Math.abs(account.balance),
               payee: "Starting Balance",
               date: new Date(),
               accountId: account._id,
               status: "cleared",
               assignedTo: startingBalanceIncome._id,
               assignedToType: "Income",
            }));

         if (startingBalanceTransactions.length > 0) {
            await Transaction.insertMany(startingBalanceTransactions, {
               session: mongoSession,
            });
         }

         // Update user's total income and current balance
         await User.findByIdAndUpdate(
            mongoUserId,
            {
               $set: {
                  totalIncome: Number(totalStartingBalance.toFixed(2)),
                  currentBalance: Number(totalStartingBalance.toFixed(2)),
               },
            },
            { session: mongoSession }
         );

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json({ success: true, user: updatedUser });
      } catch (error) {
         console.error("Transaction error during onboarding:", {
            userId: mongoUserId,
            error: error.message,
            stack: error.stack,
         });
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         await mongoSession.endSession();
      }
   } catch (error) {
      console.error("Onboarding error:", {
         userId: clerkUserId,
         error: error.message,
         stack: error.stack,
         timestamp: new Date().toISOString(),
      });
      return NextResponse.json(
         { error: error.message || "Failed to complete onboarding" },
         { status: 500 }
      );
   }
}

export async function GET(request, { params }) {
   try {
      // Check for admin API key
      const authHeader = request.headers.get("authorization");

      // Debug logging
      console.log("Auth header received:", authHeader ? "Present" : "Missing");
      console.log(
         "Expected admin key:",
         process.env.ADMIN_API_KEY ? "Set" : "Not set"
      );

      if (!authHeader) {
         return NextResponse.json(
            { error: "Authorization header missing" },
            { status: 401 }
         );
      }

      if (!authHeader.startsWith("Bearer ")) {
         return NextResponse.json(
            { error: "Invalid authorization format" },
            { status: 401 }
         );
      }

      const providedKey = authHeader.split(" ")[1];
      if (providedKey !== process.env.ADMIN_API_KEY) {
         return NextResponse.json(
            { error: "Invalid API key" },
            { status: 401 }
         );
      }

      const { userId } = params;

      if (!userId) {
         return NextResponse.json(
            { error: "User ID is required" },
            { status: 400 }
         );
      }

      await dbConnect();

      // Find user by user ID
      const user = await User.findById(userId);

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      return NextResponse.json({
         onboardingComplete: user.onboardingComplete || false,
         userId: user._id,
      });
   } catch (error) {
      console.error("Error checking onboarding status:", error);
      return NextResponse.json(
         { error: "Internal server error" },
         { status: 500 }
      );
   }
}

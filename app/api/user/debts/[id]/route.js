import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../../lib/mongodb/dbConnect";
import User from "../../../../lib/mongodb/models/User";
import Debt from "../../../../lib/mongodb/models/Debt";
import mongoose from "mongoose";

export async function GET(request, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { id } = params;

      // Validate the ID
      if (!mongoose.Types.ObjectId.isValid(id)) {
         return NextResponse.json(
            { error: "Invalid debt ID format" },
            { status: 400 }
         );
      }

      // Find the debt by ID
      const debt = await Debt.findOne({
         _id: id,
         userId: user._id,
      });

      if (!debt) {
         return NextResponse.json({ error: "Debt not found" }, { status: 404 });
      }

      return NextResponse.json({ debt });
   } catch (error) {
      console.error(`Error in GET /api/user/debts/${params.id}:`, error);
      return NextResponse.json(
         { error: "Failed to fetch debt" },
         { status: 500 }
      );
   }
}

export async function DELETE(request, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const { id } = params;

      // Validate the ID
      if (!mongoose.Types.ObjectId.isValid(id)) {
         return NextResponse.json(
            { error: "Invalid debt ID format" },
            { status: 400 }
         );
      }

      // Find the debt by ID
      const debt = await Debt.findOne({
         _id: id,
         userId: user._id,
      });

      if (!debt) {
         return NextResponse.json({ error: "Debt not found" }, { status: 404 });
      }

      // Check if debt is currently active
      if (debt.active) {
         // For active debts: mark as inactive (soft delete)
         debt.active = false;
         await debt.save();

         return NextResponse.json({
            success: true,
            message: "Debt marked as inactive",
            action: "deactivated",
         });
      } else {
         // For inactive debts: check if any expenses reference this debt
         const { default: Expense } = await import(
            "../../../../lib/mongodb/models/Expense"
         );

         const expensesWithDebt = await Expense.find({
            userId: user._id,
            debtId: id,
         }).limit(1); // Just need to know if any exist

         if (expensesWithDebt.length > 0) {
            return NextResponse.json(
               {
                  error: "Cannot delete debt",
                  message:
                     "This debt has associated expense records. Please remove or reassign these expenses before deleting the debt permanently.",
                  hasAssociatedExpenses: true,
               },
               { status: 400 }
            );
         }

         // No associated expenses, safe to permanently delete
         await Debt.findByIdAndDelete(id);

         return NextResponse.json({
            success: true,
            message: "Debt permanently deleted",
            action: "deleted",
         });
      }
   } catch (error) {
      console.error(`Error in DELETE /api/user/debts/${params.id}:`, error);
      return NextResponse.json(
         { error: "Failed to delete debt", details: error.message },
         { status: 500 }
      );
   }
}

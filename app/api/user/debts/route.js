import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Debt from "../../../lib/mongodb/models/Debt";
import mongoose from "mongoose";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Fetch debts for the current user
      const debts = await Debt.find({
         userId: user._id,
      });

      return NextResponse.json({ debts });
   } catch (error) {
      console.error("Error in GET /api/user/debts:", error);
      return NextResponse.json(
         { error: "Failed to fetch debts" },
         { status: 500 }
      );
   }
}

export async function POST(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { debt } = await request.json();
      if (!debt) {
         return NextResponse.json(
            { error: "Debt data is required" },
            { status: 400 }
         );
      }

      // Process debt data - make sure we don't include any existing _id
      const processedDebt = {
         debtType: debt.debtType,
         lender: debt.lender,
         balance: Number(debt.balance || 0),
         apr: Number(debt.apr || 0),
         minimumPayment: Number(debt.minimumPayment || 0),
         dueDate: debt.dueDate,
         userId: user._id,
         active: true,
         history: [], // Initialize empty history array
      };

      // Create and save the new debt
      const newDebt = new Debt(processedDebt);
      await newDebt.save();

      return NextResponse.json({
         success: true,
         message: "Debt added successfully",
         debt: newDebt,
      });
   } catch (error) {
      console.error("Error in POST /api/user/debts:", error);
      return NextResponse.json(
         { error: "Failed to add debt", details: error.message },
         { status: 500 }
      );
   }
}

export async function PUT(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { debt } = await request.json();
      if (!debt || !debt._id) {
         return NextResponse.json(
            { error: "Debt data with ID is required" },
            { status: 400 }
         );
      }

      // Find the debt
      const existingDebt = await Debt.findOne({
         _id: debt._id,
         userId: user._id,
      });

      if (!existingDebt) {
         return NextResponse.json(
            { error: `Debt not found: ${debt._id}` },
            { status: 404 }
         );
      }

      // Create history entry if there are changes in financial data
      const newBalance =
         typeof debt.balance !== "undefined"
            ? Number(debt.balance)
            : existingDebt.balance;
      const newMinimumPayment =
         typeof debt.minimumPayment !== "undefined"
            ? Number(debt.minimumPayment)
            : existingDebt.minimumPayment;
      const newAPR =
         typeof debt.apr !== "undefined" ? Number(debt.apr) : existingDebt.apr;

      if (
         existingDebt.balance !== newBalance ||
         existingDebt.minimumPayment !== newMinimumPayment ||
         existingDebt.apr !== newAPR ||
         existingDebt.dueDate !== debt.dueDate ||
         (typeof debt.active !== "undefined" &&
            existingDebt.active !== debt.active)
      ) {
         const historyEntry = {
            date: new Date(),
            oldBalance: existingDebt.balance,
            newBalance: newBalance,
            oldMinimumPayment: existingDebt.minimumPayment,
            newMinimumPayment: newMinimumPayment,
            oldAPR: existingDebt.apr,
            newAPR: newAPR,
            oldDueDate: existingDebt.dueDate,
            newDueDate: debt.dueDate || existingDebt.dueDate,
            note:
               debt.note ||
               (typeof debt.active !== "undefined" &&
               existingDebt.active !== debt.active
                  ? debt.active
                     ? "Marked as active"
                     : "Marked as inactive"
                  : "Balance update"),
         };

         existingDebt.history.push(historyEntry);
      }

      // Update debt fields
      existingDebt.debtType = debt.debtType || existingDebt.debtType;
      existingDebt.lender = debt.lender || existingDebt.lender;
      existingDebt.balance =
         typeof debt.balance !== "undefined"
            ? Number(debt.balance)
            : existingDebt.balance;
      existingDebt.apr =
         typeof debt.apr !== "undefined" ? Number(debt.apr) : existingDebt.apr;
      existingDebt.minimumPayment =
         typeof debt.minimumPayment !== "undefined"
            ? Number(debt.minimumPayment)
            : existingDebt.minimumPayment;
      existingDebt.dueDate = debt.dueDate || existingDebt.dueDate;

      // Explicitly update the active status if it's provided
      // Use typeof check to handle false values properly
      if (typeof debt.active !== "undefined") {
         existingDebt.active = debt.active;
      }

      await existingDebt.save();

      return NextResponse.json({
         success: true,
         message: "Debt updated successfully",
         debt: existingDebt,
      });
   } catch (error) {
      console.error("Error in PUT /api/user/debts:", error);
      return NextResponse.json(
         { error: "Failed to update debt", details: error.message },
         { status: 500 }
      );
   }
}

export async function DELETE(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { debtId } = await request.json();
      if (!debtId) {
         return NextResponse.json(
            { error: "Debt ID is required" },
            { status: 400 }
         );
      }

      // Find the debt
      const debt = await Debt.findOne({
         _id: debtId,
         userId: user._id,
      });

      if (!debt) {
         return NextResponse.json({ error: "Debt not found" }, { status: 404 });
      }

      // Soft delete: mark as inactive
      debt.active = false;
      await debt.save();

      return NextResponse.json({
         success: true,
         message: "Debt deleted successfully",
      });
   } catch (error) {
      console.error("Error in DELETE /api/user/debts:", error);
      return NextResponse.json(
         { error: "Failed to delete debt", details: error.message },
         { status: 500 }
      );
   }
}

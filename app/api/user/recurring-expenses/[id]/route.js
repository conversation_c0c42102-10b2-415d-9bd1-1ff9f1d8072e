import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../../lib/mongodb/dbConnect";
import User from "../../../../lib/mongodb/models/User";
import Expense from "../../../../lib/mongodb/models/Expense";
import Transaction from "../../../../lib/mongodb/models/Transaction";

export async function GET(req, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const { id } = await params;

      // Find user
      const user = await User.findById(userId);
      if (!user) {
         return new NextResponse(JSON.stringify({ error: "User not found" }), {
            status: 404,
         });
      }

      // Find the specific recurring expense
      const expense = user.recurringExpenses.find(
         (exp) => String(exp.id) === String(id)
      );

      if (!expense) {
         return new NextResponse(
            JSON.stringify({ error: "Recurring expense not found" }),
            { status: 404 }
         );
      }

      return new NextResponse(JSON.stringify(expense), { status: 200 });
   } catch (error) {
      console.error("Error in GET /api/user/recurring-expenses/[id]:", error);
      return new NextResponse(
         JSON.stringify({
            error: "Failed to fetch recurring expense",
            details: error.message,
         }),
         { status: 500 }
      );
   }
}

export async function PATCH(req, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const { id } = await params;
      const data = await req.json();

      // Find user and update the specific expense
      const user = await User.findById(userId);
      if (!user) {
         return new NextResponse(JSON.stringify({ error: "User not found" }), {
            status: 404,
         });
      }

      // Find the expense index
      const expenseIndex = user.recurringExpenses.findIndex(
         (expense) => String(expense.id) === String(id)
      );

      if (expenseIndex === -1) {
         return new NextResponse(
            JSON.stringify({ error: "Expense not found" }),
            {
               status: 404,
            }
         );
      }

      // Update the expense
      user.recurringExpenses[expenseIndex] = {
         ...user.recurringExpenses[expenseIndex],
         ...data,
         id: user.recurringExpenses[expenseIndex].id, // Preserve the original ID
      };

      // Save the updated user document with validation disabled for password
      await user.save({ validateBeforeSave: false });

      return new NextResponse(
         JSON.stringify({
            message: "Expense updated successfully",
            expense: user.recurringExpenses[expenseIndex],
         }),
         { status: 200 }
      );
   } catch (error) {
      console.error("Error in PATCH /api/user/recurring-expenses/[id]:", error);
      return new NextResponse(
         JSON.stringify({
            error: "Failed to update expense",
            details: error.message,
         }),
         { status: 500 }
      );
   }
}

export async function DELETE(req, { params }) {
   const mongoose = require("mongoose");
   const mongoSession = await mongoose.startSession();
   mongoSession.startTransaction();

   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) {
         await mongoSession.abortTransaction();
         return error;
      }

      await dbConnect();
      const { id } = await params;

      // Get the action from request body - defaults to 'template-only' for safety
      const body = await req.json().catch(() => ({}));
      const action = body.action || "template-only";

      // Find user
      const user = await User.findById(userId).session(mongoSession);
      if (!user) {
         await session.abortTransaction();
         return new NextResponse(JSON.stringify({ error: "User not found" }), {
            status: 404,
         });
      }

      // Find the recurring expense before deletion
      const recurringExpense = user.recurringExpenses.find(
         (expense) => String(expense.id) === String(id)
      );

      if (!recurringExpense) {
         await session.abortTransaction();
         return new NextResponse(
            JSON.stringify({ error: "Recurring expense not found" }),
            { status: 404 }
         );
      }

      let result = {
         message: "Recurring expense template deleted successfully",
         action: action,
         recurringExpense: recurringExpense,
      };

      if (action === "template-and-all") {
         // Delete template AND all generated expenses (+ handle orphaned transactions)

         // Find all generated expenses from this recurring expense
         const generatedExpenses = await Expense.find({
            userId: user._id,
            recurringExpenseId: parseInt(id),
         }).session(mongoSession);

         const expenseIds = generatedExpenses.map((exp) => exp._id);

         // Handle transactions - unassign them (safest approach)
         const orphanedTransactions = await Transaction.find({
            userId: user._id,
            assignedTo: { $in: expenseIds },
            assignedToType: "Expense",
         }).session(mongoSession);

         await Transaction.updateMany(
            {
               userId: user._id,
               assignedTo: { $in: expenseIds },
               assignedToType: "Expense",
            },
            {
               $set: {
                  assignedTo: null,
                  assignedToType: null,
               },
            },
            { session }
         );

         // Delete all generated expenses
         await Expense.deleteMany(
            {
               userId: user._id,
               recurringExpenseId: parseInt(id),
            },
            { session }
         );

         result.deletedExpenses = generatedExpenses.length;
         result.unassignedTransactions = orphanedTransactions.length;
      } else {
         // Delete template only - make generated expenses standalone

         // Remove the recurringExpenseId reference from generated expenses
         const updateResult = await Expense.updateMany(
            {
               userId: user._id,
               recurringExpenseId: parseInt(id),
            },
            {
               $unset: { recurringExpenseId: 1 },
            },
            { session }
         );

         result.standaloneExpenses = updateResult.modifiedCount;
      }

      // Remove the recurring expense template from user
      user.recurringExpenses = user.recurringExpenses.filter(
         (expense) => String(expense.id) !== String(id)
      );

      // Save the user with validation disabled for password
      await user.save({ validateBeforeSave: false, session });
      await session.commitTransaction();

      return new NextResponse(JSON.stringify(result), { status: 200 });
   } catch (error) {
      await session.abortTransaction();
      console.error(
         "Error in DELETE /api/user/recurring-expenses/[id]:",
         error
      );
      return new NextResponse(
         JSON.stringify({
            error: "Failed to delete expense",
            details: error.message,
         }),
         { status: 500 }
      );
   } finally {
      session.endSession();
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../../../lib/mongodb/dbConnect";
import User from "../../../../../lib/mongodb/models/User";
import Expense from "../../../../../lib/mongodb/models/Expense";
import Transaction from "../../../../../lib/mongodb/models/Transaction";

export async function GET(req, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const { id } = await params;

      // Find user
      const user = await User.findById(userId);
      if (!user) {
         return new NextResponse(JSON.stringify({ error: "User not found" }), {
            status: 404,
         });
      }

      // Find the recurring expense
      const recurringExpense = user.recurringExpenses.find(
         (exp) => String(exp.id) === String(id)
      );

      if (!recurringExpense) {
         return new NextResponse(
            JSON.stringify({ error: "Recurring expense not found" }),
            { status: 404 }
         );
      }

      // Find all generated expenses from this recurring expense
      const generatedExpenses = await Expense.find({
         userId: user._id,
         recurringExpenseId: parseInt(id),
      }).lean();

      // Get expense IDs for transaction lookup
      const expenseIds = generatedExpenses.map((exp) => exp._id);

      // Find all transactions assigned to these expenses
      const assignedTransactions = await Transaction.find({
         userId: user._id,
         assignedTo: { $in: expenseIds },
         assignedToType: "Expense",
      }).lean();

      // Calculate total transaction amount
      const totalTransactionAmount = assignedTransactions.reduce(
         (sum, transaction) => sum + Math.abs(transaction.amount),
         0
      );

      const impactData = {
         recurringExpenseId: id,
         generatedExpenses: generatedExpenses.length,
         assignedTransactions: assignedTransactions.length,
         totalTransactionAmount: Number(totalTransactionAmount.toFixed(2)),
         generatedExpenseIds: expenseIds,
         transactionIds: assignedTransactions.map((t) => t._id),
      };

      return new NextResponse(JSON.stringify(impactData), { status: 200 });
   } catch (error) {
      console.error(
         "Error in GET /api/user/recurring-expenses/[id]/impact:",
         error
      );
      return new NextResponse(
         JSON.stringify({
            error: "Failed to fetch impact data",
            details: error.message,
         }),
         { status: 500 }
      );
   }
}

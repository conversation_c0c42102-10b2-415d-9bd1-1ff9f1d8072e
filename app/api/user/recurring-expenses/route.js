import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function POST(req) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return new NextResponse(JSON.stringify({ error: "User not found" }), {
            status: 404,
         });
      }

      const data = await req.json();
      console.log("Received expense data:", data);

      // Validate required fields
      if (!data.name || !data.amount || !data.frequency || !data.dueDay) {
         return new NextResponse(
            JSON.stringify({
               error: "Missing required fields",
               required: ["name", "amount", "frequency", "dueDay"],
            }),
            { status: 400 }
         );
      }

      // Validate frequency
      const validFrequencies = [
         "weekly",
         "biweekly",
         "monthly",
         "quarterly",
         "annually",
      ];
      if (!validFrequencies.includes(data.frequency)) {
         return new NextResponse(
            JSON.stringify({
               error: "Invalid frequency",
               validValues: validFrequencies,
            }),
            { status: 400 }
         );
      }
      console.log("Found user:", user._id, user.email);
      console.log("Current recurring expenses:", user.recurringExpenses);

      // Create new expense object matching the schema
      const newExpense = {
         id: Date.now(),
         name: data.name,
         amount: parseFloat(data.amount),
         frequency: data.frequency,
         dueDay: data.dueDay.toString(), // Schema expects string
         enabled: data.enabled ?? true, // Default to true if not provided
      };

      // Add weeklyChargeType for weekly expenses
      if (data.frequency === "weekly" && data.weeklyChargeType) {
         newExpense.weeklyChargeType = data.weeklyChargeType;
      }

      // Add dueMonth for annual expenses
      if (data.frequency === "annually" && data.dueMonth) {
         newExpense.dueMonth = data.dueMonth.toString(); // Schema expects string
      }
      console.log("New expense to add:", newExpense);

      // Initialize array if needed
      if (!Array.isArray(user.recurringExpenses)) {
         console.log("Initializing recurring expenses array");
         user.recurringExpenses = [];
      }

      // Add the new expense
      user.recurringExpenses.push(newExpense);
      console.log("Updated recurring expenses:", user.recurringExpenses);

      // Save and handle validation errors
      try {
         const savedUser = await user.save();
         console.log(
            "Saved user recurring expenses:",
            savedUser.recurringExpenses
         );
         return new NextResponse(
            JSON.stringify({
               message: "Recurring expense added successfully",
               expense: newExpense,
               currentExpenses: savedUser.recurringExpenses,
            }),
            { status: 200 }
         );
      } catch (saveError) {
         console.error("Error saving recurring expense:", saveError);
         return new NextResponse(
            JSON.stringify({
               error: "Failed to save recurring expense",
               details: saveError.message,
               validationErrors: saveError.errors,
            }),
            { status: 400 }
         );
      }
   } catch (error) {
      console.error("Error in POST /api/user/recurring-expenses:", error);
      return new NextResponse(
         JSON.stringify({
            error: "Failed to add recurring expense",
            details: error.message,
         }),
         { status: 500 }
      );
   }
}

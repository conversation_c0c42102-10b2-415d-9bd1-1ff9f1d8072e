import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]/route";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";
import { getCurrentMongoUser } from "@/app/lib/utils/userUtils";

export async function GET(request) {
   try {
      console.log("User API: Starting request");

      // Get the NextAuth session
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
         console.log("User API: No session or user ID");
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      console.log("User API: Session found for user:", session.user.id);
      await dbConnect();
      console.log("User API: DB connected");

      try {
         // Add timeout to prevent hanging on user lookup
         const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
               console.warn("User API: User lookup timeout after 6 seconds");
               reject(new Error("User lookup timeout"));
            }, 6000); // Reduced from 8 to 6 seconds
         });

         console.log("User API: Starting user lookup");
         const userPromise = getCurrentMongoUser();
         const user = await Promise.race([userPromise, timeoutPromise]);
         console.log(
            "User API: User lookup completed:",
            user ? "found" : "not found"
         );

         // If no user found, return default data structure
         if (!user) {
            return NextResponse.json({
               id: session.user.id,
               name: session.user.name || "",
               email: session.user.email || "",
               isAdmin: false,
               onboardingComplete: false,
               accounts: [],
               recurringIncomes: [],
               recurringExpenses: [],
               mainIncomeId: null,
               totalIncome: 0,
               totalAssigned: 0,
               readyToAssign: 0,
               currentBalance: 0,
               preferences: { showFutureExpenses: true },
               createdAt: new Date(),
               plaidItems: [],
            });
         }

         // Ensure account balances are numbers
         const processedAccounts = (user.accounts || []).map((account) => ({
            ...account.toObject(),
            balance: Number(account.balance) || 0,
         }));

         // Calculate totals
         const totalIncome = Number(user.totalIncome) || 0;
         const totalAssigned = Number(user.totalAssigned) || 0;
         const readyToAssign = totalIncome - totalAssigned;
         const currentBalance = processedAccounts.reduce(
            (sum, account) => sum + account.balance,
            0
         );

         const userData = {
            id: user._id,
            name: user.name,
            email: user.email,
            isAdmin: user.isAdmin || false,
            onboardingComplete: user.onboardingComplete || false,
            accounts: processedAccounts,
            recurringIncomes: user.recurringIncomes || [],
            recurringExpenses: user.recurringExpenses || [],
            mainIncomeId: user.mainIncomeId,
            totalIncome,
            totalAssigned,
            readyToAssign: Number(readyToAssign.toFixed(2)),
            currentBalance: Number(currentBalance.toFixed(2)),
            preferences: user.preferences || { showFutureExpenses: true },
            createdAt: user.createdAt,
            plaidItems: user.plaidItems || [],
            subscription: user.subscription || { planId: "free" },
         };

         console.log("User API: Returning user data");
         return NextResponse.json(userData);
      } catch (timeoutError) {
         console.error("User API: User lookup timed out:", {
            error: timeoutError.message,
            userId: session.user.id,
            timestamp: new Date().toISOString(),
         });
         return NextResponse.json(
            { error: "Request timeout" },
            { status: 408 }
         );
      }
   } catch (error) {
      console.error("User API: Error in user route:", {
         error: error.message,
         stack: error.stack,
         userId: session?.user?.id,
         timestamp: new Date().toISOString(),
      });
      return NextResponse.json(
         { error: "Internal server error" },
         { status: 500 }
      );
   }
}

// Helper function to clean up income fields based on frequency
function cleanupIncomeFields(income) {
   const cleanedIncome = { ...income };

   switch (income.payPeriod) {
      case "weekly":
      case "biweekly":
         // Remove monthly/quarterly/annually fields
         delete cleanedIncome.payDay;

         // Ensure weekly/biweekly fields exist
         if (!cleanedIncome.payDayOfWeek || !cleanedIncome.payWeekDay) {
            cleanedIncome.payDayOfWeek = "1"; // Default to Monday
            cleanedIncome.payWeekDay = "Monday";
         }

         // For biweekly, ensure lastPaymentDate exists
         if (income.payPeriod === "biweekly") {
            if (!cleanedIncome.lastPaymentDate) {
               cleanedIncome.lastPaymentDate = new Date();
            }
         } else {
            // For weekly, remove biweekly fields
            delete cleanedIncome.lastPaymentDate;
         }
         break;

      case "monthly":
         // Remove weekly/biweekly fields
         delete cleanedIncome.payDayOfWeek;
         delete cleanedIncome.payWeekDay;
         delete cleanedIncome.lastPaymentDate;

         // Ensure monthly fields exist
         if (!cleanedIncome.payDay) {
            cleanedIncome.payDay = "1"; // Default to 1st of month
         }
         break;

      case "semimonthly":
         // Remove all specific day fields since it's always 1st and 15th
         delete cleanedIncome.payDay;
         delete cleanedIncome.payDayOfWeek;
         delete cleanedIncome.payWeekDay;
         delete cleanedIncome.lastPaymentDate;
         break;
   }

   return cleanedIncome;
}

export async function PUT(request) {
   try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const body = await request.json();
      await dbConnect();

      // Update user data
      const updatedUser = await User.findByIdAndUpdate(session.user.id, body, {
         new: true,
      });

      if (!updatedUser) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      return NextResponse.json({
         id: updatedUser._id,
         name: updatedUser.name,
         email: updatedUser.email,
         isAdmin: updatedUser.isAdmin,
         onboardingComplete: updatedUser.onboardingComplete,
      });
   } catch (error) {
      console.error("Error updating user:", error);
      return NextResponse.json(
         { error: "Internal server error" },
         { status: 500 }
      );
   }
}

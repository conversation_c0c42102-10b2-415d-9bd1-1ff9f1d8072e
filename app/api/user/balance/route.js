import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Transaction from "../../../lib/mongodb/models/Transaction";
import Expense from "../../../lib/mongodb/models/Expense";
import User from "../../../lib/mongodb/models/User";

// Helper function to ensure all monetary values are rounded to 2 decimal places
const roundToTwo = (num) => Number(Number(num).toFixed(2));

export async function POST(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user first
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Get transactions
      const transactions = await Transaction.find({ userId: user._id });

      let currentBalance = 0;
      let totalIncome = 0;

      // Calculate totals from transactions
      transactions.forEach((transaction) => {
         const amount = roundToTwo(transaction.amount);
         currentBalance = roundToTwo(currentBalance + amount);

         if (
            transaction.type === "Income" &&
            (!transaction.assignedTo || transaction.assignedToType === "Income")
         ) {
            totalIncome = roundToTwo(totalIncome + amount);
         }
      });

      // Get total assigned amount from expenses
      const expenses = await Expense.find({ userId: user._id });
      let totalAssigned = 0;
      expenses.forEach((expense) => {
         totalAssigned = roundToTwo(
            totalAssigned + (expense.amountAssigned || 0)
         );
      });

      // Calculate ready to assign
      const readyToAssign = roundToTwo(totalIncome - totalAssigned);

      // Update user with new balances
      const updatedUser = await User.findByIdAndUpdate(
         user._id,
         {
            $set: {
               currentBalance: roundToTwo(currentBalance),
               totalIncome: roundToTwo(totalIncome),
               totalAssigned: roundToTwo(totalAssigned),
               readyToAssign: readyToAssign,
            },
         },
         { new: true }
      );

      return NextResponse.json({
         currentBalance: updatedUser.currentBalance,
         totalIncome: updatedUser.totalIncome,
         totalAssigned: updatedUser.totalAssigned,
         readyToAssign: updatedUser.readyToAssign,
      });
   } catch (error) {
      console.error("Error calculating balances:", error);
      return NextResponse.json(
         { error: "Failed to calculate balances" },
         { status: 500 }
      );
   }
}

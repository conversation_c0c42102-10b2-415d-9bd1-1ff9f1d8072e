import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import User from "@/app/lib/mongodb/models/User";
import dbConnect from "@/app/lib/mongodb/dbConnect";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const {
         description,
         payAmount,
         payPeriod,
         payDay,
         payDayOfWeek,
         payWeekDay,
         lastPaymentDate,
         enabled = true,
      } = await request.json();

      // Validate required fields
      if (!description || !payAmount || !payPeriod) {
         return NextResponse.json(
            { error: "Missing required fields" },
            { status: 400 }
         );
      }

      // Validate pay period
      const validPayPeriods = ["weekly", "biweekly", "semimonthly", "monthly"];
      if (!validPayPeriods.includes(payPeriod)) {
         return NextResponse.json(
            { error: "Invalid pay period" },
            { status: 400 }
         );
      }

      // Validate pay day based on pay period
      if (payPeriod === "monthly") {
         if (!payDay || payDay < 1 || payDay > 31) {
            return NextResponse.json(
               { error: "Invalid pay day" },
               { status: 400 }
            );
         }
      } else if (payPeriod === "semimonthly") {
         // No additional validation needed for semimonthly as it's fixed to 1st and 15th
      } else {
         if (!payDayOfWeek || !payWeekDay) {
            return NextResponse.json(
               { error: "Missing pay day information" },
               { status: 400 }
            );
         }
      }

      await dbConnect();
      const user = await User.findOne({ email: session.user.email });

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Ensure payWeekDay matches payDayOfWeek for consistency
      let finalPayWeekDay = payWeekDay;
      if (
         payDayOfWeek &&
         (payPeriod === "weekly" || payPeriod === "biweekly")
      ) {
         const DAYS_OF_WEEK = [
            { value: "1", label: "Monday" },
            { value: "2", label: "Tuesday" },
            { value: "3", label: "Wednesday" },
            { value: "4", label: "Thursday" },
            { value: "5", label: "Friday" },
            { value: "6", label: "Saturday" },
            { value: "7", label: "Sunday" },
         ];
         const dayMatch = DAYS_OF_WEEK.find(
            (day) => day.value === payDayOfWeek
         );
         if (dayMatch) {
            finalPayWeekDay = dayMatch.label;
         }
      }

      // Create the new income object
      const newIncome = {
         description,
         payAmount: Number(payAmount),
         payPeriod,
         payDay: payPeriod === "monthly" ? payDay : undefined,
         payDayOfWeek: payPeriod !== "monthly" ? payDayOfWeek : undefined,
         payWeekDay: payPeriod !== "monthly" ? finalPayWeekDay : undefined,
         lastPaymentDate: payPeriod === "biweekly" ? lastPaymentDate : null,
         enabled,
      };

      // Add the new income to the user's recurring incomes
      user.recurringIncomes.push(newIncome);
      await user.save();

      return NextResponse.json({
         message: "Income added successfully",
         currentIncomes: user.recurringIncomes,
      });
   } catch (error) {
      console.error("Error adding income:", error);
      return NextResponse.json(
         { error: "Failed to add income" },
         { status: 500 }
      );
   }
}

export async function PUT(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const updatedIncome = await request.json();

      // Validate required fields
      if (
         !updatedIncome._id ||
         !updatedIncome.description ||
         !updatedIncome.payAmount ||
         !updatedIncome.payPeriod
      ) {
         return NextResponse.json(
            { error: "Missing required fields" },
            { status: 400 }
         );
      }

      // Validate pay period
      const validPayPeriods = ["weekly", "biweekly", "semimonthly", "monthly"];
      if (!validPayPeriods.includes(updatedIncome.payPeriod)) {
         return NextResponse.json(
            { error: "Invalid pay period" },
            { status: 400 }
         );
      }

      // Validate pay day based on pay period
      if (updatedIncome.payPeriod === "monthly") {
         if (
            !updatedIncome.payDay ||
            updatedIncome.payDay < 1 ||
            updatedIncome.payDay > 31
         ) {
            return NextResponse.json(
               { error: "Invalid pay day" },
               { status: 400 }
            );
         }
      } else if (updatedIncome.payPeriod === "semimonthly") {
         // No additional validation needed for semimonthly as it's fixed to 1st and 15th
      } else {
         if (!updatedIncome.payDayOfWeek || !updatedIncome.payWeekDay) {
            return NextResponse.json(
               { error: "Missing pay day information" },
               { status: 400 }
            );
         }
      }

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Find and update the recurring income
      const incomeIndex = user.recurringIncomes.findIndex(
         (income) => String(income._id) === String(updatedIncome._id)
      );

      if (incomeIndex === -1) {
         return NextResponse.json(
            { error: "Recurring income not found" },
            { status: 404 }
         );
      }

      // Ensure payWeekDay matches payDayOfWeek for consistency
      let finalPayWeekDay = updatedIncome.payWeekDay;
      if (
         updatedIncome.payDayOfWeek &&
         (updatedIncome.payPeriod === "weekly" ||
            updatedIncome.payPeriod === "biweekly")
      ) {
         const DAYS_OF_WEEK = [
            { value: "1", label: "Monday" },
            { value: "2", label: "Tuesday" },
            { value: "3", label: "Wednesday" },
            { value: "4", label: "Thursday" },
            { value: "5", label: "Friday" },
            { value: "6", label: "Saturday" },
            { value: "7", label: "Sunday" },
         ];
         const dayMatch = DAYS_OF_WEEK.find(
            (day) => day.value === updatedIncome.payDayOfWeek
         );
         if (dayMatch) {
            finalPayWeekDay = dayMatch.label;
         }
      }

      // Update the income with the new values
      user.recurringIncomes[incomeIndex] = {
         ...user.recurringIncomes[incomeIndex].toObject(),
         description: updatedIncome.description,
         payAmount: Number(updatedIncome.payAmount),
         payPeriod: updatedIncome.payPeriod,
         payDay:
            updatedIncome.payPeriod === "monthly"
               ? updatedIncome.payDay
               : undefined,
         payDayOfWeek:
            updatedIncome.payPeriod !== "monthly"
               ? updatedIncome.payDayOfWeek
               : undefined,
         payWeekDay:
            updatedIncome.payPeriod !== "monthly" ? finalPayWeekDay : undefined,
         lastPaymentDate:
            updatedIncome.payPeriod === "biweekly"
               ? updatedIncome.lastPaymentDate
               : null,
         enabled:
            updatedIncome.enabled ?? user.recurringIncomes[incomeIndex].enabled,
      };

      // Mark the lastPaymentDate field as modified to trigger validation
      user.markModified("recurringIncomes");

      try {
         await user.save();
         return NextResponse.json({
            message: "Income updated successfully",
            currentIncomes: user.recurringIncomes,
         });
      } catch (saveError) {
         console.error("Error saving income:", saveError);
         return NextResponse.json(
            { error: saveError.message || "Failed to update income" },
            { status: 400 }
         );
      }
   } catch (error) {
      console.error("Error updating income:", error);
      return NextResponse.json(
         { error: "Failed to update income" },
         { status: 500 }
      );
   }
}

export async function DELETE(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { _id } = await request.json();

      if (!_id) {
         return NextResponse.json(
            { error: "Missing income ID" },
            { status: 400 }
         );
      }

      await dbConnect();
      const user = await User.findOne({ email: session.user.email });

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const incomeIndex = user.recurringIncomes.findIndex(
         (i) => i._id.toString() === _id
      );
      if (incomeIndex === -1) {
         return NextResponse.json(
            { error: "Income not found" },
            { status: 404 }
         );
      }

      user.recurringIncomes.splice(incomeIndex, 1);
      await user.save();

      return NextResponse.json({
         message: "Income deleted successfully",
         currentIncomes: user.recurringIncomes,
      });
   } catch (error) {
      console.error("Error deleting income:", error);
      return NextResponse.json(
         { error: "Failed to delete income" },
         { status: 500 }
      );
   }
}

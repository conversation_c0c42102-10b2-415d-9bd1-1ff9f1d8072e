import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function PUT(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      const data = await request.json();

      // Get the current user to access existing preferences
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Create an update object that merges new preferences with existing ones
      const currentPreferences = user.preferences || {};
      const updatedPreferences = { ...currentPreferences };

      // Update specific preferences from the request
      if (data.showFutureExpenses !== undefined) {
         updatedPreferences.showFutureExpenses = data.showFutureExpenses;
      }

      if (data.hideInactiveDebts !== undefined) {
         updatedPreferences.hideInactiveDebts = data.hideInactiveDebts;
      }

      // Update cash flow warning preferences
      if (data.cashFlowWarnings !== undefined) {
         // Ensure we have a cashFlowWarnings object
         if (!updatedPreferences.cashFlowWarnings) {
            updatedPreferences.cashFlowWarnings = {
               enabled: true,
               periodsAhead: 4,
            };
         }

         // Update individual cash flow warning settings
         if (data.cashFlowWarnings.enabled !== undefined) {
            updatedPreferences.cashFlowWarnings.enabled =
               data.cashFlowWarnings.enabled;
         }

         if (data.cashFlowWarnings.periodsAhead !== undefined) {
            // Validate periodsAhead is within reasonable limits
            const periodsAhead = parseInt(data.cashFlowWarnings.periodsAhead);
            if (periodsAhead >= 1 && periodsAhead <= 12) {
               updatedPreferences.cashFlowWarnings.periodsAhead = periodsAhead;
            } else {
               return NextResponse.json(
                  { error: "periodsAhead must be between 1 and 12" },
                  { status: 400 }
               );
            }
         }
      }

      // Update user preferences in the database
      const updatedUser = await User.findByIdAndUpdate(
         user._id,
         { $set: { preferences: updatedPreferences } },
         { new: true }
      );

      if (!updatedUser) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      return NextResponse.json({
         success: true,
         message: "Preferences updated successfully",
         preferences: updatedUser.preferences,
      });
   } catch (error) {
      console.error("Error in PUT /api/user/preferences:", error);
      return NextResponse.json(
         { error: "Failed to update preferences", details: error.message },
         { status: 500 }
      );
   }
}

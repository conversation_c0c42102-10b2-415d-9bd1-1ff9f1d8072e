import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function POST(request) {
   try {
      const { name, email, password } = await request.json();

      // Validate input
      if (!name || typeof name !== "string" || name.length < 2) {
         return NextResponse.json(
            { error: "Name must be at least 2 characters long" },
            { status: 400 }
         );
      }

      if (!email || typeof email !== "string" || !email.includes("@")) {
         return NextResponse.json(
            { error: "Please provide a valid email address" },
            { status: 400 }
         );
      }

      if (!password || typeof password !== "string" || password.length < 6) {
         return NextResponse.json(
            { error: "Password must be at least 6 characters long" },
            { status: 400 }
         );
      }

      await dbConnect();

      // Check if user already exists
      const existingUser = await User.findOne({ email: email.toLowerCase() });
      if (existingUser) {
         return NextResponse.json(
            { error: "Email is already registered" },
            { status: 400 }
         );
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const user = await User.create({
         name: name.trim(),
         email: email.toLowerCase().trim(),
         password: hashedPassword,
      });

      // Return success without password
      return NextResponse.json({
         id: user._id,
         name: user.name,
         email: user.email,
      });
   } catch (error) {
      console.error("Registration error:", error);
      return NextResponse.json(
         { error: "Error creating user" },
         { status: 500 }
      );
   }
}

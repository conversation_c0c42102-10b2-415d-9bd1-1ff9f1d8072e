import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import connectDB from "@/app/lib/mongodb/dbConnect";
import User from "@/app/lib/mongodb/models/User";

export const authOptions = {
   providers: [
      CredentialsProvider({
         name: "Credentials",
         credentials: {
            email: { label: "Email", type: "email" },
            password: { label: "Password", type: "password" },
         },
         async authorize(credentials) {
            if (!credentials?.email || !credentials?.password) {
               throw new Error("Please enter an email and password");
            }

            await connectDB();

            const user = await User.findOne({
               email: credentials.email.toLowerCase(),
            });
            if (!user) {
               throw new Error("No user found with this email");
            }

            const isPasswordValid = await bcrypt.compare(
               credentials.password,
               user.password
            );
            if (!isPasswordValid) {
               throw new Error("Invalid password");
            }

            return {
               id: user._id.toString(),
               email: user.email,
               name: user.name,
               isAdmin: user.isAdmin || false,
               onboardingComplete: user.onboardingComplete || false,
            };
         },
      }),
   ],
   callbacks: {
      async jwt({ token, user, trigger, session }) {
         if (user) {
            token.id = user.id;
            token.isAdmin = user.isAdmin || false;
            token.onboardingComplete = user.onboardingComplete || false;
         }
         // Handle user updates
         if (trigger === "update" && session?.name) {
            token.name = session.name;
            token.email = session.email;
         }
         // Handle onboarding completion update
         if (
            trigger === "update" &&
            session?.onboardingComplete !== undefined
         ) {
            token.onboardingComplete = session.onboardingComplete;
         }
         return token;
      },
      async session({ session, token }) {
         if (token) {
            session.user.id = token.id;
            session.user.isAdmin = token.isAdmin || false;
            session.user.onboardingComplete = token.onboardingComplete || false;
         }
         return session;
      },
   },
   pages: {
      signIn: "/auth/login",
      signUp: "/auth/register",
      error: "/auth/login",
   },
   session: {
      strategy: "jwt",
      maxAge: 30 * 24 * 60 * 60, // 30 days
   },
   secret: process.env.NEXTAUTH_SECRET,
   jwt: {
      secret: process.env.NEXTAUTH_SECRET,
      encryption: true,
   },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };

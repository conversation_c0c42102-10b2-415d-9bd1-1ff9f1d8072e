import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Expense from "../../../lib/mongodb/models/Expense";
import User from "../../../lib/mongodb/models/User";
import mongoose from "mongoose";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user first
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Get the date range parameters from the request
      const { searchParams } = new URL(request.url);
      const endDateParam = searchParams.get("end");
      const viewType = searchParams.get("viewType");
      const startDateParam = searchParams.get("start");

      // If no end date is provided, use current date
      let endDate;
      if (endDateParam) {
         endDate = endDateParam;
      } else {
         // Default to current date if no end date specified
         const currentDate = new Date();
         endDate = currentDate.toISOString().split("T")[0]; // YYYY-MM-DD format
      }

      // Get the start date of the period
      let startDate;
      if (startDateParam) {
         startDate = startDateParam;
      } else {
         // Calculate start date based on view type if not provided
         const endDateObj = new Date(endDate);
         if (viewType === "month") {
            // Set to first day of the month
            const startDateObj = new Date(
               endDateObj.getFullYear(),
               endDateObj.getMonth(),
               1
            );
            startDate = startDateObj.toISOString().split("T")[0];
         } else if (viewType === "payPeriod") {
            // Default to 14 days before end for pay periods if not specified
            const startDateObj = new Date(endDateObj);
            startDateObj.setDate(startDateObj.getDate() - 14);
            startDate = startDateObj.toISOString().split("T")[0];
         }
      }

      // Calculate next period start date
      let nextPeriodStartDate;
      const endDateObj = new Date(endDate);

      if (viewType === "month") {
         // Move to the first day of the next month
         const nextPeriodObj = new Date(
            endDateObj.getFullYear(),
            endDateObj.getMonth() + 1,
            1
         );
         nextPeriodStartDate = nextPeriodObj.toISOString().split("T")[0];
      } else if (viewType === "payPeriod") {
         // For pay periods, add a day to the end date to get the start of the next period
         const nextPeriodObj = new Date(endDateObj);
         nextPeriodObj.setDate(nextPeriodObj.getDate() + 1);
         nextPeriodStartDate = nextPeriodObj.toISOString().split("T")[0];
      }

      // Check if we're viewing a past period
      const currentDateStr = new Date().toISOString().split("T")[0];
      const isPastPeriod = endDate < currentDateStr;

      let query;

      if (isPastPeriod) {
         // For past periods, show future expenses created on or before the period end
         // but with a due date after the period end
         query = {
            userId: user._id,
            // Date is beyond the period we're viewing
            date: { $gt: endDate },
            // Created on or before this period's end date (use Date object for createdAt since it's a timestamp)
            createdAt: { $lte: new Date(endDate + "T23:59:59.999Z") },
            // Only get one-off expenses (no frequency or oneoff frequency)
            $or: [
               { frequency: { $exists: false } },
               { frequency: "oneoff" },
               { frequency: "" },
            ],
            // Exclude expenses with start/end dates (they are not one-off)
            $and: [
               {
                  $or: [{ startDate: { $exists: false } }, { startDate: null }],
               },
               {
                  $or: [{ endDate: { $exists: false } }, { endDate: null }],
               },
            ],
         };
      } else {
         // For current/future periods, show future expenses that are due after the period
         query = {
            userId: user._id,
            // Only get expenses from the next period start date
            date: { $gte: nextPeriodStartDate },
            // Only get one-off expenses (no frequency or oneoff frequency)
            $or: [
               { frequency: { $exists: false } },
               { frequency: "oneoff" },
               { frequency: "" },
            ],
            // Exclude expenses with start/end dates (they are not one-off)
            $and: [
               {
                  $or: [{ startDate: { $exists: false } }, { startDate: null }],
               },
               {
                  $or: [{ endDate: { $exists: false } }, { endDate: null }],
               },
            ],
         };
      }

      const expenses = await Expense.find(query).sort({ date: 1 });

      return NextResponse.json({
         expenses,
         meta: {
            viewType,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            nextPeriodStart: nextPeriodStartDate.toISOString(),
            isPastPeriod: isPastPeriod,
            count: expenses.length,
         },
      });
   } catch (error) {
      console.error("Error in GET /api/expenses/future:", error);
      return NextResponse.json(
         { error: "Failed to fetch future expenses" },
         { status: 500 }
      );
   }
}

// Add this PUT endpoint to handle future expense updates
export async function PUT(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const data = await request.json();
      if (!data.expenseId) {
         return NextResponse.json(
            { error: "Missing expense ID" },
            { status: 400 }
         );
      }

      await dbConnect();

      // Get user first
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Start a MongoDB transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Get current expense
         const currentExpense = await Expense.findOne({
            _id: data.expenseId,
            userId: user._id,
         }).session(mongoSession);

         if (!currentExpense) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Future expense not found" },
               { status: 404 }
            );
         }

         const updateData = { ...data };
         delete updateData.expenseId; // Remove ID from the update data
         delete updateData.end; // Remove date range params from update data
         delete updateData.viewType; // Remove view type from update data
         delete updateData.originalAmount; // Remove original amount from update data
         delete updateData.updateUserAssigned; // Remove updateUserAssigned from update data (we'll handle it separately)

         // Preserve future expense properties, but don't force status to "future"
         // Remove hardcoded status as client will determine if it should be "funded" or "future"
         // updateData.status = "future";

         // Update monetary values
         if (data.amountAssigned !== undefined) {
            updateData.amountAssigned = Number(data.amountAssigned.toFixed(2));
         }

         // Update the expense, preserving future-specific properties
         const expense = await Expense.findOneAndUpdate(
            {
               _id: data.expenseId,
               userId: user._id,
            },
            { $set: updateData },
            { new: true, session: mongoSession }
         );

         if (!expense) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Failed to update future expense" },
               { status: 500 }
            );
         }

         // If we need to update the user's total assigned amount
         if (typeof data.updateUserAssigned === "number") {
            const updatedUser = await User.findByIdAndUpdate(
               user._id,
               {
                  $inc: {
                     totalAssigned: Number(data.updateUserAssigned.toFixed(2)),
                  },
               },
               { session: mongoSession, new: true }
            );

            if (!updatedUser) {
               await mongoSession.abortTransaction();
               return NextResponse.json(
                  { error: "User not found" },
                  { status: 404 }
               );
            }
         }

         // Create the dateRange for prorating
         const { searchParams } = new URL(request.url);
         const endDateParam = searchParams.get("end") || data.end;
         const viewType = searchParams.get("viewType") || data.viewType;

         // Get original amount (either from request or use the current amountDue)
         const originalAmount = data.originalAmount || expense.amountDue;

         // Create a properly formatted future expense response
         const formattedResponse = {
            ...expense.toObject(),
            // Don't force status to "future" - client will decide based on amounts
            // status: "future",
            isFutureExpense: true,
            originalAmountDue: originalAmount,
            // Keep the original amountDue value from the expense
            isFutureProrated: true,
         };

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json(formattedResponse);
      } catch (error) {
         // If an error occurred, abort the transaction
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         // End the session
         await mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error in PUT /api/expenses/future:", error);
      return NextResponse.json(
         { error: error.message || "Failed to update future expense" },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Expense from "../../../lib/mongodb/models/Expense";
import User from "../../../lib/mongodb/models/User";
import mongoose from "mongoose";

// Helper function to update balances
async function updateUserBalances(userId, mongoSession) {
   try {
      const user = await User.findById(userId).session(mongoSession);
      if (!user) {
         throw new Error("User not found");
      }

      // Get all expenses for the user
      const expenses = await Expense.find({ userId }).session(mongoSession);

      // Calculate totals
      const totalAssigned = expenses.reduce(
         (sum, expense) => sum + (expense.amountAssigned || 0),
         0
      );
      const totalSpent = expenses.reduce(
         (sum, expense) => sum + (expense.amountSpent || 0),
         0
      );

      // Update user balances
      user.totalAssigned = totalAssigned;
      user.totalSpent = totalSpent;
      user.currentBalance = user.totalIncome - totalAssigned;

      await user.save({ session: mongoSession });
      return user;
   } catch (error) {
      console.error("Error updating user balances:", error);
      throw error;
   }
}

// Helper function to determine initial status
const determineInitialStatus = (expense) => {
   // Convert to absolute values and ensure proper number handling
   const due = Math.abs(Number(expense.amountDue || 0));
   const assigned = Number(expense.amountAssigned || 0);
   const spent = Math.abs(Number(expense.amountSpent || 0));

   // Check if the due date has passed
   const today = new Date();
   today.setHours(0, 0, 0, 0);
   const expenseDate = new Date(expense.date);
   expenseDate.setHours(0, 0, 0, 0);
   const dueDatePassed = expenseDate < today;

   // If spent amount exceeds due amount, it's overpaid (regardless of due date)
   if (spent > due) {
      return "overpaid";
   }

   // If spent amount equals due amount exactly, it's paid
   if (spent === due) {
      return "paid";
   }

   // Handle past due expenses
   if (dueDatePassed) {
      // If there's some spending but less than due, it's underpaid
      if (spent > 0) {
         return "underpaid";
      }
      // If no spending and due date passed, it's late
      return "late";
   }

   // If assigned covers the due amount, it's funded
   if (assigned >= due) {
      return "funded";
   }

   // Default to scheduled
   return "scheduled";
};

export async function POST(req) {
   const mongoSession = await mongoose.startSession();
   mongoSession.startTransaction();

   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId).session(mongoSession);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { expenses } = await req.json();

      if (!Array.isArray(expenses) || expenses.length === 0) {
         return NextResponse.json(
            { error: "No expenses provided" },
            { status: 400 }
         );
      }

      // Add userId and determine initial status for each expense
      const expensesWithUserId = expenses.map((expense) => ({
         ...expense,
         userId: user._id.toString(),
         status: determineInitialStatus(expense),
         // Keep recurringExpenseId if it exists in the expense object
         recurringExpenseId: expense.recurringExpenseId || null,
      }));

      // Create all expenses in a single operation within the transaction
      const createdExpenses = await Expense.insertMany(expensesWithUserId, {
         session: mongoSession,
      });

      // Update user balances
      await updateUserBalances(user._id, mongoSession);

      // Commit the transaction
      await mongoSession.commitTransaction();

      return NextResponse.json({
         message: "Expenses created successfully",
         expenses: createdExpenses,
      });
   } catch (error) {
      // Abort transaction on error
      await mongoSession.abortTransaction();
      console.error("Error in bulk create expenses:", error);
      return NextResponse.json(
         { error: error.message || "Failed to create expenses" },
         { status: 500 }
      );
   } finally {
      // End the session
      await mongoSession.endSession();
   }
}

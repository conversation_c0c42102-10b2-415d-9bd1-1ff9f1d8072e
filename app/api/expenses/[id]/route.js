import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Expense from "../../../lib/mongodb/models/Expense";
import User from "../../../lib/mongodb/models/User";
import mongoose from "mongoose";
import { determineExpenseStatus } from "../../../lib/utils/expenseUtils";

export async function DELETE(request, context) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const params = await context.params;
      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Start a MongoDB transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         const { id } = await context.params;
         console.log("Deleting expense:", {
            expenseId: id,
            userId: user._id,
         });

         const expense = await Expense.findOneAndDelete({
            _id: id,
            userId: user._id,
         }).session(mongoSession);

         if (!expense) {
            throw new Error("Expense not found");
         }

         console.log("Deleted expense:", {
            expenseId: id,
            description: expense.description,
            amountAssigned: expense.amountAssigned,
            amountDue: expense.amountDue,
         });

         // Calculate new total assigned amount
         const expenses = await Expense.find({
            userId: user._id,
         }).session(mongoSession);

         let totalAssigned = 0;
         expenses.forEach((exp) => {
            totalAssigned = Number(
               (totalAssigned + (exp.amountAssigned || 0)).toFixed(2)
            );
         });

         console.log("Calculated new total assigned:", totalAssigned);

         // Calculate readyToAssign amount
         const readyToAssign = Number(
            (user.totalIncome - totalAssigned).toFixed(2)
         );

         // Update user's total assigned amount and readyToAssign
         const updatedUser = await User.findByIdAndUpdate(
            user._id,
            {
               $set: {
                  totalAssigned,
                  readyToAssign,
               },
            },
            { new: true, session: mongoSession }
         );

         if (!updatedUser) {
            throw new Error("User not found");
         }

         console.log("Updated user total assigned:", updatedUser.totalAssigned);

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json({ message: "Expense deleted successfully" });
      } catch (error) {
         // If an error occurred, abort the transaction
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error in DELETE /api/expenses/[id]:", error);
      return NextResponse.json(
         { error: error.message || "Failed to delete expense" },
         { status: 500 }
      );
   }
}

export async function PUT(request, context) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { id } = await context.params;
      if (!id) {
         return NextResponse.json(
            { error: "Missing expense ID" },
            { status: 400 }
         );
      }

      const data = await request.json();

      // Validate required fields
      if (data.amountAssigned !== undefined && isNaN(data.amountAssigned)) {
         return NextResponse.json(
            { error: "Invalid assigned amount" },
            { status: 400 }
         );
      }

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Start a MongoDB transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         console.log("Updating expense:", {
            expenseId: id,
            userId: user._id,
            updateData: data,
         });

         // Get current expense to check amounts
         const currentExpense = await Expense.findOne({
            _id: id,
            userId: user._id,
         }).session(mongoSession);

         if (!currentExpense) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Expense not found" },
               { status: 404 }
            );
         }

         // Determine the new status based on amounts
         let updateData = { ...data };

         // Update status whenever amounts change
         if (
            data.amountDue !== undefined ||
            data.amountAssigned !== undefined ||
            data.amountSpent !== undefined
         ) {
            const newAmountDue = Number(
               (data.amountDue ?? currentExpense.amountDue).toFixed(2)
            );
            const newAmountAssigned = Number(
               (data.amountAssigned ?? currentExpense.amountAssigned).toFixed(2)
            );
            const newAmountSpent = Number(
               Math.abs(data.amountSpent ?? currentExpense.amountSpent).toFixed(
                  2
               )
            );

            // Use the comprehensive determineExpenseStatus function for consistent status determination
            updateData.status = determineExpenseStatus(
               newAmountSpent,
               newAmountAssigned,
               newAmountDue,
               currentExpense.date,
               currentExpense.startDate,
               currentExpense.endDate,
               data.status || currentExpense.status,
               currentExpense.isFutureExpense
            );

            // Update monetary values
            if (data.amountDue !== undefined)
               updateData.amountDue = newAmountDue;
            if (data.amountAssigned !== undefined)
               updateData.amountAssigned = newAmountAssigned;
            if (data.amountSpent !== undefined)
               updateData.amountSpent = newAmountSpent;
         }

         // Add retry logic for write conflicts
         const MAX_RETRIES = 3;
         let retryCount = 0;
         let expense = null;

         while (retryCount < MAX_RETRIES) {
            try {
               // Update the expense
               expense = await Expense.findOneAndUpdate(
                  {
                     _id: id,
                     userId: user._id,
                  },
                  { $set: updateData },
                  { new: true, session: mongoSession }
               );
               break; // If successful, exit the loop
            } catch (error) {
               if (error.code === 112 && retryCount < MAX_RETRIES - 1) {
                  // Write conflict, wait a bit and retry
                  await new Promise((resolve) =>
                     setTimeout(resolve, 100 * Math.pow(2, retryCount))
                  );
                  retryCount++;
                  continue;
               }
               throw error; // If it's not a write conflict or we're out of retries, rethrow
            }
         }

         if (!expense) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Failed to update expense" },
               { status: 500 }
            );
         }

         console.log("Updated expense:", {
            expenseId: expense._id,
            description: expense.description,
            amountAssigned: expense.amountAssigned,
            amountSpent: expense.amountSpent,
            amountAvailable: expense.amountAvailable,
            amountDue: expense.amountDue,
            status: expense.status,
         });

         // If we need to update the user's total assigned amount
         if (typeof data.updateUserAssigned === "number") {
            let updatedUser = null;
            let userRetryCount = 0;

            while (userRetryCount < MAX_RETRIES) {
               try {
                  updatedUser = await User.findByIdAndUpdate(
                     user._id,
                     {
                        $inc: {
                           totalAssigned: Number(
                              data.updateUserAssigned.toFixed(2)
                           ),
                        },
                     },
                     { session: mongoSession, new: true }
                  );
                  break; // If successful, exit the loop
               } catch (error) {
                  if (error.code === 112 && userRetryCount < MAX_RETRIES - 1) {
                     // Write conflict, wait a bit and retry
                     await new Promise((resolve) =>
                        setTimeout(resolve, 100 * Math.pow(2, userRetryCount))
                     );
                     userRetryCount++;
                     continue;
                  }
                  throw error; // If it's not a write conflict or we're out of retries, rethrow
               }
            }

            if (!updatedUser) {
               await mongoSession.abortTransaction();
               return NextResponse.json(
                  { error: "User not found" },
                  { status: 404 }
               );
            }

            console.log("Updated user total assigned:", {
               userId: updatedUser._id,
               totalAssigned: updatedUser.totalAssigned,
               updateAmount: data.updateUserAssigned,
            });
         }

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json(expense);
      } catch (error) {
         // If an error occurred, abort the transaction
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         // End the session
         await mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error in PUT /api/expenses/[id]:", error);
      return NextResponse.json(
         { error: error.message || "Failed to update expense" },
         { status: 500 }
      );
   }
}

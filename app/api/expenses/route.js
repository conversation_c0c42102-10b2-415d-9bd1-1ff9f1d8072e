import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../lib/mongodb/dbConnect";
import Expense from "../../lib/mongodb/models/Expense";
import User from "../../lib/mongodb/models/User";

// Helper function to update balances
async function updateUserBalances(userId) {
   try {
      const user = await User.findById(userId);
      if (!user) {
         throw new Error("User not found");
      }

      // Get all expenses for the user
      const expenses = await Expense.find({ userId });

      // Calculate totals
      const totalAssigned = expenses.reduce(
         (sum, expense) => sum + (expense.amountAssigned || 0),
         0
      );

      // Update user balances
      user.totalAssigned = totalAssigned;
      user.currentBalance = user.totalIncome - totalAssigned;

      await user.save({ validateBeforeSave: false });
      return user;
   } catch (error) {
      console.error("Error updating user balances:", error);
      throw error;
   }
}

// Helper function to determine initial status
const determineInitialStatus = (expense) => {
   // Convert to absolute values and ensure proper number handling
   const due = Math.abs(Number(expense.amountDue || 0));
   const assigned = Number(expense.amountAssigned || 0);
   const spent = Math.abs(Number(expense.amountSpent || 0));

   // Check if the due date has passed
   const today = new Date();
   today.setHours(0, 0, 0, 0);
   const expenseDate = new Date(expense.date);
   expenseDate.setHours(0, 0, 0, 0);
   const dueDatePassed = expenseDate < today;

   // If spent amount exceeds due amount, it's overpaid (regardless of due date)
   if (spent > due) {
      return "overpaid";
   }

   // If spent amount equals due amount exactly, it's paid
   if (spent === due) {
      return "paid";
   }

   // Handle past due expenses
   if (dueDatePassed) {
      // If there's some spending but less than due, it's underpaid
      if (spent > 0) {
         return "underpaid";
      }
      // If no spending and due date passed, it's late
      return "late";
   }

   // If assigned covers the due amount, it's funded
   if (assigned >= due) {
      return "funded";
   }

   // Default to scheduled
   return "scheduled";
};

export async function POST(req) {
   try {
      const { userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return new NextResponse(JSON.stringify({ error: "User not found" }), {
            status: 404,
         });
      }

      const data = await req.json();
      const expense = new Expense({
         ...data,
         userId: user._id,
         status: determineInitialStatus(data),
         recurringExpenseId: data.recurringExpenseId || null,
      });

      await expense.save();

      // Update user balances
      await updateUserBalances(user._id);

      return new NextResponse(JSON.stringify(expense), { status: 201 });
   } catch (error) {
      console.error("Error in POST /api/expenses:", error);
      return new NextResponse(
         JSON.stringify({
            error: "Failed to create expense",
            details: error.message,
         }),
         { status: 500 }
      );
   }
}

export async function GET(request) {
   try {
      const { userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { searchParams } = new URL(request.url);
      const status = searchParams.get("status");
      const start = searchParams.get("start");
      const end = searchParams.get("end");

      // Build query
      const query = { userId: user._id };
      if (status) {
         query.status = status;
      }

      // Add date range to query if provided
      if (start || end) {
         // For expenses, we need to handle both single-date and date range expenses
         const dateQuery = [];

         // Case 1: Single-date expenses
         const singleDateQuery = {};
         if (start) singleDateQuery.date = { $gte: start };
         if (end) singleDateQuery.date = { ...singleDateQuery.date, $lte: end };
         dateQuery.push({
            ...singleDateQuery,
            $or: [
               { startDate: { $exists: false } },
               { endDate: { $exists: false } },
            ],
         });

         // Case 2: Date range expenses (with startDate and endDate)
         if (start && end) {
            dateQuery.push({
               $and: [
                  { startDate: { $exists: true } },
                  { endDate: { $exists: true } },
                  { startDate: { $lte: end } },
                  { endDate: { $gte: start } },
               ],
            });
         }

         query.$or = dateQuery;
      }

      const expenses = await Expense.find(query).sort({ date: -1 });

      return NextResponse.json(expenses);
   } catch (error) {
      console.error("Error in GET /api/expenses:", error);
      return NextResponse.json(
         { error: "Failed to fetch expenses" },
         { status: 500 }
      );
   }
}

export async function PUT(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const data = await request.json();
      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const expense = await Expense.findOneAndUpdate(
         { _id: data._id, userId: user._id },
         { $set: data },
         { new: true }
      );

      if (!expense) {
         return NextResponse.json(
            { error: "Expense not found" },
            { status: 404 }
         );
      }

      // Update balances after updating expense
      await updateUserBalances(user._id);

      return NextResponse.json(expense);
   } catch (error) {
      console.error("Error in PUT /api/expenses:", error);
      return NextResponse.json(
         { error: "Failed to update expense" },
         { status: 500 }
      );
   }
}

export async function DELETE(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { searchParams } = new URL(request.url);
      const id = searchParams.get("id");

      if (!id) {
         return NextResponse.json(
            { error: "Expense ID is required" },
            { status: 400 }
         );
      }

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      console.log("Deleting expense:", {
         expenseId: id,
         userId: user._id,
      });

      const expense = await Expense.findOneAndDelete({
         _id: id,
         userId: user._id,
      });

      if (!expense) {
         return NextResponse.json(
            { error: "Expense not found" },
            { status: 404 }
         );
      }

      console.log("Deleted expense:", {
         expenseId: id,
         description: expense.description,
         amountAssigned: expense.amountAssigned,
         amountDue: expense.amountDue,
      });

      // Update balances after deleting expense
      console.log("Updating balances for user:", user._id);
      await updateUserBalances(user._id);
      console.log("Balances updated successfully");

      return NextResponse.json({ message: "Expense deleted successfully" });
   } catch (error) {
      console.error("Error in DELETE /api/expenses:", error);
      return NextResponse.json(
         { error: "Failed to delete expense" },
         { status: 500 }
      );
   }
}

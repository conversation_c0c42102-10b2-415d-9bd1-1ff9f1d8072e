import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Expense from "../../../lib/mongodb/models/Expense";
import User from "../../../lib/mongodb/models/User";
import mongoose from "mongoose";

// Helper function to update balances
async function updateUserBalances(userId, mongoSession) {
   try {
      const user = await User.findById(userId).session(mongoSession);
      if (!user) {
         throw new Error("User not found");
      }

      // Get all expenses for the user
      const expenses = await Expense.find({ userId }).session(mongoSession);

      // Calculate totals
      const totalAssigned = expenses.reduce(
         (sum, expense) => sum + (expense.amountAssigned || 0),
         0
      );
      const totalSpent = expenses.reduce(
         (sum, expense) => sum + (expense.amountSpent || 0),
         0
      );

      // Update user balances
      user.totalAssigned = totalAssigned;
      user.totalSpent = totalSpent;
      user.readyToAssign = user.totalIncome - totalAssigned;
      user.currentBalance = user.totalIncome - totalAssigned;

      await user.save({ session: mongoSession });
      return user;
   } catch (error) {
      console.error("Error updating user balances:", error);
      throw error;
   }
}

export async function POST(req) {
   const mongoSession = await mongoose.startSession();
   mongoSession.startTransaction();

   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId).session(mongoSession);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { expenseIds } = await req.json();

      if (!Array.isArray(expenseIds) || expenseIds.length === 0) {
         return NextResponse.json(
            { error: "No expense IDs provided" },
            { status: 400 }
         );
      }

      // Delete all expenses in a single operation within the transaction
      const result = await Expense.deleteMany({
         _id: { $in: expenseIds },
         userId: user._id,
      }).session(mongoSession);

      if (result.deletedCount === 0) {
         throw new Error("No expenses were deleted");
      }

      // Update user balances
      await updateUserBalances(user._id, mongoSession);

      // Commit the transaction
      await mongoSession.commitTransaction();

      return NextResponse.json({
         message: "Expenses deleted successfully",
         deletedCount: result.deletedCount,
      });
   } catch (error) {
      // Abort transaction on error
      await mongoSession.abortTransaction();
      console.error("Error in bulk delete expenses:", error);
      return NextResponse.json(
         { error: error.message || "Failed to delete expenses" },
         { status: 500 }
      );
   } finally {
      // End the session
      await mongoSession.endSession();
   }
}

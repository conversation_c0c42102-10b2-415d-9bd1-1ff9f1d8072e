import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import {
   checkPlaidItemStatus,
   checkPlaidAccountStatus,
   updatePlaidItemStatus,
   getConnectionStatusInfo,
} from "../../../lib/utils/plaidUtils";

// Helper function to save user document with retry logic for version conflicts
async function saveUserWithRetry(userId, updateFn, maxRetries = 3) {
   for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
         const user = await User.findById(userId);
         if (!user) {
            throw new Error("User not found");
         }

         // Apply the update function
         await updateFn(user);

         // Try to save
         await user.save();
         return user;
      } catch (error) {
         // Check if it's a version error and we have retries left
         if (error.name === "VersionError" && attempt < maxRetries) {
            console.log(`Version conflict on attempt ${attempt}, retrying...`);
            // Add a small delay before retrying
            await new Promise((resolve) => setTimeout(resolve, 50 * attempt));
            continue;
         }
         // If it's not a version error or we're out of retries, throw the error
         throw error;
      }
   }
}

export async function GET(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Get connection status for all plaid items
      const connectionStatuses = user.plaidItems.map((plaidItem) => {
         const statusInfo = getConnectionStatusInfo(plaidItem);
         return {
            itemId: plaidItem.itemId,
            institutionName: plaidItem.institutionName,
            linkedAccountId: plaidItem.linkedAccountId,
            ...statusInfo,
         };
      });

      return NextResponse.json({
         connections: connectionStatuses,
         hasIssues: connectionStatuses.some((status) => status.needsAction),
      });
   } catch (error) {
      console.error("Error checking connection status:", error);
      return NextResponse.json(
         { error: "Failed to check connection status" },
         { status: 500 }
      );
   }
}

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { itemId, action } = await request.json();

      // itemId is only required for specific item actions, not for check_all
      if (action !== "check_all" && !itemId) {
         return NextResponse.json(
            { error: "Item ID is required" },
            { status: 400 }
         );
      }

      await dbConnect();

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Check if user has required subscription for bank connections
      const { checkUserFeatureAccess } = await import(
         "@/app/lib/stripe/subscriptionUtils"
      );
      const hasAccess = await checkUserFeatureAccess(
         user._id,
         "bank_connections"
      );

      if (!hasAccess) {
         return NextResponse.json(
            {
               error: "Subscription required",
               message:
                  "Bank connections require a Basic or Pro subscription plan.",
               feature: "bank_connections",
               requiresUpgrade: true,
            },
            { status: 403 }
         );
      }

      const plaidItem = user.plaidItems.find((item) => item.itemId === itemId);
      if (!plaidItem) {
         return NextResponse.json(
            { error: "Plaid item not found" },
            { status: 404 }
         );
      }

      if (action === "check_status") {
         // Check both item and account status via Plaid API
         const itemStatusCheck = await checkPlaidItemStatus(
            plaidItem.accessToken
         );
         const accountStatusCheck = await checkPlaidAccountStatus(
            plaidItem.accessToken
         );

         let itemStatus = "error";
         let itemMessage = "Connection error";
         let itemNeedsAction = true;
         let itemError = null;

         if (itemStatusCheck.success) {
            itemStatus = "good";
            itemMessage = "Item connection is healthy";
            itemNeedsAction = false;
            updatePlaidItemStatus(user, itemId, "good");
         } else if (itemStatusCheck.needsReauth) {
            itemStatus = "login_required";
            itemMessage = "Item re-authentication required";
            itemNeedsAction = true;
            itemError = itemStatusCheck.error;
            updatePlaidItemStatus(
               user,
               itemId,
               "login_required",
               itemStatusCheck.error
            );
         } else {
            itemStatus = "error";
            itemMessage = "Item connection error";
            itemNeedsAction = true;
            itemError = itemStatusCheck.error;
            updatePlaidItemStatus(user, itemId, "error", itemStatusCheck.error);
         }

         let accountStatus = "error";
         let accountMessage = "Account connection error";
         let accountNeedsAction = true;
         let accountError = null;

         if (accountStatusCheck.success) {
            accountStatus = "good";
            accountMessage = "Account connection is healthy";
            accountNeedsAction = false;
         } else if (accountStatusCheck.needsReauth) {
            accountStatus = "login_required";
            accountMessage = "Account re-authentication required";
            accountNeedsAction = true;
            accountError = accountStatusCheck.error;
         } else {
            accountStatus = "error";
            accountMessage = "Account connection error";
            accountNeedsAction = true;
            accountError = accountStatusCheck.error;
         }

         // Determine overall status
         const overallNeedsAction = itemNeedsAction || accountNeedsAction;
         const overallStatus = overallNeedsAction ? "error" : "good";
         const overallMessage = overallNeedsAction
            ? `${itemMessage}; ${accountMessage}`
            : "All connections are healthy";

         // Save user with retry logic for version conflicts
         await saveUserWithRetry(userId, async (user) => {
            // Re-apply the status updates in case the document was reloaded
            if (itemStatusCheck.success) {
               updatePlaidItemStatus(user, itemId, "good");
            } else if (itemStatusCheck.needsReauth) {
               updatePlaidItemStatus(
                  user,
                  itemId,
                  "login_required",
                  itemStatusCheck.error
               );
            } else {
               updatePlaidItemStatus(
                  user,
                  itemId,
                  "error",
                  itemStatusCheck.error
               );
            }
         });

         return NextResponse.json({
            status: overallStatus,
            message: overallMessage,
            needsAction: overallNeedsAction,
            itemStatus,
            itemMessage,
            accountStatus,
            accountMessage,
            error: itemError || accountError,
         });
      } else if (action === "check_all") {
         // Check all plaid items and their accounts for this user
         const results = [];

         for (const item of user.plaidItems) {
            try {
               // Check item status first
               const itemStatusCheck = await checkPlaidItemStatus(
                  item.accessToken
               );
               let itemStatus = "error";
               let itemMessage = "Connection error";
               let itemNeedsAction = true;
               let itemError = null;

               if (itemStatusCheck.success) {
                  itemStatus = "good";
                  itemMessage = "Item connection is healthy";
                  itemNeedsAction = false;
                  updatePlaidItemStatus(user, item.itemId, "good");
               } else if (itemStatusCheck.needsReauth) {
                  itemStatus = "login_required";
                  itemMessage = "Item re-authentication required";
                  itemNeedsAction = true;
                  itemError = itemStatusCheck.error;
                  updatePlaidItemStatus(
                     user,
                     item.itemId,
                     "login_required",
                     itemStatusCheck.error
                  );
               } else {
                  itemStatus = "error";
                  itemMessage = "Item connection error";
                  itemNeedsAction = true;
                  itemError = itemStatusCheck.error;
                  updatePlaidItemStatus(
                     user,
                     item.itemId,
                     "error",
                     itemStatusCheck.error
                  );
               }

               // Check account status
               const accountStatusCheck = await checkPlaidAccountStatus(
                  item.accessToken
               );
               let accountStatus = "error";
               let accountMessage = "Account connection error";
               let accountNeedsAction = true;
               let accountError = null;

               if (accountStatusCheck.success) {
                  accountStatus = "good";
                  accountMessage = "Account connection is healthy";
                  accountNeedsAction = false;
               } else if (accountStatusCheck.needsReauth) {
                  accountStatus = "login_required";
                  accountMessage = "Account re-authentication required";
                  accountNeedsAction = true;
                  accountError = accountStatusCheck.error;
               } else {
                  accountStatus = "error";
                  accountMessage = "Account connection error";
                  accountNeedsAction = true;
                  accountError = accountStatusCheck.error;
               }

               // Determine overall status (if either item or account has issues, mark as needing action)
               const overallNeedsAction = itemNeedsAction || accountNeedsAction;
               const overallStatus = overallNeedsAction ? "error" : "good";
               const overallMessage = overallNeedsAction
                  ? `${itemMessage}; ${accountMessage}`
                  : "All connections are healthy";

               results.push({
                  itemId: item.itemId,
                  institutionName: item.institutionName,
                  status: overallStatus,
                  message: overallMessage,
                  needsAction: overallNeedsAction,
                  itemStatus,
                  itemMessage,
                  accountStatus,
                  accountMessage,
                  error: itemError || accountError,
               });
            } catch (error) {
               console.error(`Error checking item ${item.itemId}:`, error);
               results.push({
                  itemId: item.itemId,
                  institutionName: item.institutionName,
                  status: "error",
                  message: "Failed to check status",
                  needsAction: false,
                  itemStatus: "error",
                  itemMessage: "Failed to check item status",
                  accountStatus: "error",
                  accountMessage: "Failed to check account status",
                  error: {
                     errorCode: "CHECK_FAILED",
                     errorMessage: error.message,
                  },
               });
            }
         }

         // Save all updates with retry logic for version conflicts
         await saveUserWithRetry(userId, async (user) => {
            // Re-apply all status updates in case the document was reloaded
            for (const result of results) {
               if (result.itemStatus === "good") {
                  updatePlaidItemStatus(user, result.itemId, "good");
               } else if (result.itemStatus === "login_required") {
                  updatePlaidItemStatus(
                     user,
                     result.itemId,
                     "login_required",
                     result.error
                  );
               } else if (result.itemStatus === "error" && result.error) {
                  updatePlaidItemStatus(
                     user,
                     result.itemId,
                     "error",
                     result.error
                  );
               }
            }
         });

         return NextResponse.json({
            results,
            totalChecked: results.length,
            needsAction: results.some((r) => r.needsAction),
         });
      } else if (action === "create_update_token") {
         // Create a link token for update mode
         const { plaidClient } = await import("../../../lib/plaid/config");

         try {
            const linkTokenResponse = await plaidClient.linkTokenCreate({
               user: {
                  client_user_id: userId,
               },
               client_name: "Alto Budgeting",
               country_codes: ["US"],
               language: "en",
               webhook: process.env.PLAID_WEBHOOK_URL,
               access_token: plaidItem.accessToken,
            });

            return NextResponse.json({
               link_token: linkTokenResponse.data.link_token,
               expiration: linkTokenResponse.data.expiration,
            });
         } catch (error) {
            console.error("Error creating update link token:", error);
            return NextResponse.json(
               { error: "Failed to create update link token" },
               { status: 500 }
            );
         }
      } else {
         return NextResponse.json({ error: "Invalid action" }, { status: 400 });
      }
   } catch (error) {
      console.error("Error in connection status endpoint:", error);
      return NextResponse.json(
         { error: "Internal server error" },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Transaction from "../../../lib/mongodb/models/Transaction";
import mongoose from "mongoose";
import Income from "../../../lib/mongodb/models/Income";
import Expense from "../../../lib/mongodb/models/Expense";
import Debt from "../../../lib/mongodb/models/Debt";
import { plaidClient } from "../../../lib/plaid/config";
import { logInfo, logError, logDebug } from "../../../lib/utils/logger";
import { syncTransactionsForItem } from "../../../lib/plaid/syncTransactions";

// Helper function to round numbers to 2 decimal places
function roundToTwo(num) {
   return Number(Math.round(num + "e+2") + "e-2");
}

// Helper function to update income amounts and status
const updateIncomeAmounts = async (incomeId, amount, mongoSession) => {
   const income = await Income.findById(incomeId).session(mongoSession);
   if (!income) {
      throw new Error("Income not found");
   }

   // Calculate new amounts with proper rounding
   const newReceivedAmount = roundToTwo((income.receivedAmount || 0) + amount);

   // Update income with new amounts
   const updatedIncome = await Income.findByIdAndUpdate(
      incomeId,
      {
         receivedAmount: newReceivedAmount,
      },
      { session: mongoSession, new: true }
   );

   // Update status based on received vs expected amount
   const newStatus =
      roundToTwo(newReceivedAmount) >= roundToTwo(income.expectedAmount)
         ? "received"
         : "scheduled";

   if (updatedIncome.status !== newStatus) {
      await Income.findByIdAndUpdate(
         incomeId,
         { status: newStatus },
         { session: mongoSession }
      );
   }

   return updatedIncome;
};

// Helper function to determine expense status based on amounts
const determineExpenseStatus = (amountDue, amountAssigned, amountSpent) => {
   if (Math.abs(amountSpent) >= Math.abs(amountDue)) return "paid";
   if (Math.abs(amountAssigned) >= Math.abs(amountDue)) return "funded";
   return "scheduled";
};

// Helper function to update debt history when a transaction is assigned to a debt payment expense
const updateDebtHistory = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   const expense = await Expense.findById(expenseId).session(mongoSession);

   // Check if this expense is a debt payment and has a valid debtId
   if (!expense || !expense.isDebtPayment || !expense.debtId) {
      return null;
   }

   try {
      // Find the debt
      const debt = await Debt.findById(expense.debtId).session(mongoSession);
      if (!debt) {
         console.error(`Debt not found with ID: ${expense.debtId}`);
         return null;
      }

      // Calculate the new balance after this payment (payment amount is typically negative)
      const paymentAmount = Math.abs(amount); // Convert to positive for calculation
      const oldBalance = debt.balance;

      // If removing a transaction from a debt payment category, we should add the amount back
      // If adding a transaction to a debt payment category, we should subtract the amount
      const newBalance = isRemoval
         ? roundToTwo(oldBalance + paymentAmount)
         : roundToTwo(oldBalance - paymentAmount);

      // Create a history entry
      const historyEntry = {
         date: new Date(),
         oldBalance: oldBalance,
         newBalance: newBalance,
         oldMinimumPayment: debt.minimumPayment,
         newMinimumPayment: debt.minimumPayment,
         oldAPR: debt.apr,
         newAPR: debt.apr,
         oldDueDate: debt.dueDate,
         newDueDate: debt.dueDate,
         note: isRemoval
            ? `Payment of $${paymentAmount.toFixed(2)} removed`
            : `Payment of $${paymentAmount.toFixed(2)} applied`,
      };

      // Update the debt document with the new balance and history entry
      const updatedDebt = await Debt.findByIdAndUpdate(
         expense.debtId,
         {
            $set: { balance: newBalance },
            $push: { history: historyEntry },
         },
         { session: mongoSession, new: true }
      );

      return updatedDebt;
   } catch (error) {
      console.error("Error updating debt history:", error);
      return null;
   }
};

// Helper function to update expense amounts and status
const updateExpenseAmounts = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   const expense = await Expense.findById(expenseId).session(mongoSession);
   if (!expense) {
      console.warn(
         `Expense with ID ${expenseId} not found - skipping expense update`
      );
      return null; // Gracefully handle missing expenses instead of throwing error
   }

   // Calculate new amounts with proper rounding
   const newAmountSpent = roundToTwo((expense.amountSpent || 0) + amount);

   // Update expense with new amounts
   const updatedExpense = await Expense.findByIdAndUpdate(
      expenseId,
      {
         amountSpent: newAmountSpent,
      },
      { session: mongoSession, new: true }
   );

   // Update status if needed
   const newStatus = determineExpenseStatus(
      expense.amountDue,
      expense.amountAssigned,
      newAmountSpent
   );

   if (updatedExpense.status !== newStatus) {
      await Expense.findByIdAndUpdate(
         expenseId,
         { status: newStatus },
         { session: mongoSession }
      );
   }

   // If this is a debt payment expense, update the debt history
   if (expense.isDebtPayment && expense.debtId) {
      await updateDebtHistory(expenseId, amount, mongoSession, isRemoval);
   }

   return updatedExpense;
};

// Add this function at the top of the file
function normalizePlaidTransaction(plaidTx, userId, linkedAccountId) {
   const normalized = {
      userId,
      // Plaid sends expenses as positive, so we need to flip the logic
      type: plaidTx.amount > 0 ? "Expense" : "Income",
      // For expenses (positive amounts), make them negative
      // For income (negative amounts), make them positive
      amount: plaidTx.amount > 0 ? -plaidTx.amount : Math.abs(plaidTx.amount),
      payee: plaidTx.name || plaidTx.merchant_name,
      date: new Date(plaidTx.date),
      accountId: linkedAccountId || null,
      status: plaidTx.pending ? "pending" : "cleared",
      plaidData: plaidTx,
      plaidTransactionId: plaidTx.transaction_id,
      plaidAccountId: plaidTx.account_id,
      isPlaidTransaction: true,
      pendingPlaidTransactionId: plaidTx.pending_transaction_id || null,
   };

   console.log("Normalized Plaid Transaction:", {
      type: normalized.type,
      amount: normalized.amount,
      payee: normalized.payee,
      date: normalized.date,
      originalAmount: plaidTx.amount,
      pending: plaidTx.pending,
      pendingTransactionId: plaidTx.pending_transaction_id || null,
   });

   return normalized;
}

// Update the normalizeTransactionForComparison function
function normalizeTransactionForComparison(transaction) {
   // Determine if it's a Plaid transaction or regular transaction
   const isPlaidTransaction = Boolean(transaction.plaidData);

   // Get the raw amount
   const rawAmount =
      typeof transaction.amount === "number"
         ? transaction.amount
         : transaction.plaidData?.amount;

   // Normalize the amount based on transaction type
   // Ensure expenses are negative and incomes are positive
   const amount =
      transaction.type === "Income"
         ? Math.abs(rawAmount) // Make income positive
         : -Math.abs(rawAmount); // Make expense negative

   // Normalize date to YYYY-MM-DD string format
   const date =
      transaction.date instanceof Date
         ? transaction.date.toISOString().split("T")[0]
         : new Date(transaction.date).toISOString().split("T")[0];

   return {
      date,
      amount,
      type: transaction.type,
   };
}

// Replace logToFile with database logging
function logTransactionSync(data, userId = null) {
   return logInfo(
      "plaid-sync",
      data.event || "transaction_sync",
      data.message || "Transaction sync log",
      data,
      userId
   );
}

// Replace logPlaidTransactions with database logging
function logPlaidTransactions(
   transactions,
   source,
   additionalInfo = {},
   userId = null
) {
   try {
      const logData = {
         source,
         transactionCount: transactions.length,
         ...additionalInfo,
         transactions: transactions.map((tx) => ({
            transaction_id: tx.transaction_id,
            name: tx.name,
            merchant_name: tx.merchant_name,
            amount: tx.amount,
            date: tx.date,
            pending: tx.pending,
            assignedTo: tx.assignedTo,
            account_id: tx.account_id,
         })),
      };

      return logInfo(
         "plaid-sync",
         "plaid_transactions_received",
         `Received ${transactions.length} transactions from ${source}`,
         logData,
         userId
      );
   } catch (error) {
      console.error("Error logging Plaid transactions:", error);
      logError(
         "plaid-sync",
         "log_error",
         "Error logging Plaid transactions",
         { error: error.message, stack: error.stack },
         userId
      );
   }
}

// Update the findPotentialDuplicates function to use database logging
function findPotentialDuplicates(
   normalizedPlaidTx,
   existingTransactions,
   userId = null
) {
   const plaidComparison = normalizeTransactionForComparison(normalizedPlaidTx);

   const logData = {
      plaidTransaction: {
         payee: normalizedPlaidTx.payee,
         amount: normalizedPlaidTx.amount,
         date: normalizedPlaidTx.date,
         type: normalizedPlaidTx.type,
         plaidTransactionId: normalizedPlaidTx.plaidTransactionId,
         pendingPlaidTransactionId: normalizedPlaidTx.pendingPlaidTransactionId,
      },
   };

   // First check for exact plaidTransactionId match
   const existingPlaidTransaction = existingTransactions.find(
      (tx) => tx.plaidTransactionId === normalizedPlaidTx.plaidTransactionId
   );

   if (existingPlaidTransaction) {
      logData.exactMatch = {
         existingId: existingPlaidTransaction._id,
         plaidTransactionId: existingPlaidTransaction.plaidTransactionId,
      };

      logDebug(
         "plaid-sync",
         "exact_transaction_match",
         "Found exact transaction match by plaidTransactionId",
         logData,
         userId
      );

      // Return empty array since exact plaidTransactionId matches are not duplicates
      // that need user resolution - they're the same transaction
      return [];
   }

   // Check for pending-to-posted relationship
   // If this is a posted transaction with a pending_transaction_id, check if we already have that pending transaction
   if (normalizedPlaidTx.pendingPlaidTransactionId) {
      const pendingTransaction = existingTransactions.find(
         (tx) =>
            tx.plaidTransactionId ===
            normalizedPlaidTx.pendingPlaidTransactionId
      );

      if (pendingTransaction) {
         logData.pendingToPostedMatch = {
            pendingId: pendingTransaction._id,
            pendingPlaidId: pendingTransaction.plaidTransactionId,
            postedPlaidId: normalizedPlaidTx.plaidTransactionId,
         };

         logDebug(
            "plaid-sync",
            "pending_to_posted_match",
            "Found pending transaction that matches this posted transaction",
            logData,
            userId
         );

         // Return the pending transaction as the only "duplicate" to ensure the UI can handle this special case
         return [pendingTransaction];
      }
   }

   logData.existingTransactionsCount = existingTransactions.length;
   const comparisons = [];

   const duplicates = existingTransactions.filter((existingTx) => {
      if (existingTx.isAdjustment) {
         return false;
      }

      const existingComparison = normalizeTransactionForComparison(existingTx);
      const existingDate = new Date(existingComparison.date);
      const dateDiff = Math.abs(existingDate - new Date(plaidComparison.date));
      const twoDaysMs = 2 * 24 * 60 * 60 * 1000;

      const dateMatches = dateDiff <= twoDaysMs;
      const amountMatches =
         Math.abs(existingComparison.amount - plaidComparison.amount) < 0.01;
      const typeMatches = existingComparison.type === plaidComparison.type;
      const payeeMatches = existingTx.isPlaidTransaction
         ? normalizedPlaidTx.payee.toLowerCase().trim() ===
           existingTx.payee.toLowerCase().trim()
         : true;

      if (dateMatches || amountMatches || typeMatches) {
         comparisons.push({
            plaidTransaction: {
               payee: normalizedPlaidTx.payee,
               amount: plaidComparison.amount,
               date: plaidComparison.date,
               type: plaidComparison.type,
               pendingPlaidTransactionId:
                  normalizedPlaidTx.pendingPlaidTransactionId,
            },
            existingTransaction: {
               payee: existingTx.payee,
               amount: existingComparison.amount,
               date: existingComparison.date,
               type: existingComparison.type,
               isPlaid: existingTx.isPlaidTransaction,
               plaidTransactionId: existingTx.plaidTransactionId,
            },
            matchResults: {
               dateMatches,
               amountMatches: {
                  matches: amountMatches,
                  diff: Math.abs(
                     existingComparison.amount - plaidComparison.amount
                  ),
                  plaidAmount: plaidComparison.amount,
                  existingAmount: existingComparison.amount,
               },
               typeMatches: {
                  matches: typeMatches,
                  plaidType: plaidComparison.type,
                  existingType: existingComparison.type,
               },
               payeeMatches: {
                  matches: payeeMatches,
                  plaidPayee: normalizedPlaidTx.payee,
                  existingPayee: existingTx.payee,
               },
               dateDiffDays: Math.round(dateDiff / (24 * 60 * 60 * 1000)),
            },
         });
      }

      return dateMatches && amountMatches && typeMatches && payeeMatches;
   });

   logData.comparisons = comparisons;
   logData.duplicatesFound = duplicates.length;
   if (duplicates.length > 0) {
      logData.duplicateTransactions = duplicates.map((d) => ({
         id: d._id,
         payee: d.payee,
         amount: d.amount,
         date: d.date,
         type: d.type,
         isPlaid: d.isPlaidTransaction,
         plaidTransactionId: d.plaidTransactionId,
      }));
   }

   // At the end, log potential duplicates
   if (duplicates.length > 0) {
      logInfo(
         "plaid-sync",
         "potential_duplicates_found",
         `Found ${duplicates.length} potential duplicate transactions`,
         {
            plaidTransactionId: normalizedPlaidTx.plaidTransactionId,
            payee: normalizedPlaidTx.payee,
            amount: normalizedPlaidTx.amount,
            duplicateCount: duplicates.length,
            duplicates: duplicates.map((d) => ({
               id: d._id.toString(),
               payee: d.payee,
               amount: d.amount,
               date: d.date,
            })),
         },
         userId
      );
   }

   return duplicates;
}

// Update the getTransactionsForDuplicateCheck function
async function getTransactionsForDuplicateCheck(
   userId,
   accountId,
   date,
   mongoSession
) {
   const threeDaysMs = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds
   const searchStartDate = new Date(new Date(date).getTime() - threeDaysMs);
   const searchEndDate = new Date(new Date(date).getTime() + threeDaysMs);

   return await Transaction.find(
      {
         userId,
         accountId,
         date: {
            $gte: searchStartDate,
            $lte: searchEndDate,
         },
      },
      null, // Keep as null to get all fields
      { session: mongoSession }
   );
}

// Add this helper function to calculate total transaction amount
function calculateTransactionsTotal(transactions) {
   return transactions.reduce((sum, tx) => sum + tx.amount, 0);
}

// Add this helper function to update an existing transaction with Plaid data
async function updateExistingTransaction(
   existingTransaction,
   plaidTx,
   userId,
   linkedAccountId,
   mongoSession
) {
   // Normalize the Plaid transaction
   const normalizedTx = normalizePlaidTransaction(
      plaidTx,
      userId,
      linkedAccountId
   );

   // ALWAYS preserve the existing category information
   // Make sure these lines are executed regardless of any other conditions
   normalizedTx.assignedTo = existingTransaction.assignedTo;
   normalizedTx.assignedToType = existingTransaction.assignedToType;

   // Calculate amount difference for account balance update
   const amountDifference = normalizedTx.amount - existingTransaction.amount;

   // Update transaction with a $set that explicitly preserves the category fields
   const updateFields = {
      amount: normalizedTx.amount,
      type: normalizedTx.type,
      payee: normalizedTx.payee,
      date: normalizedTx.date,
      status: normalizedTx.status,
      plaidData: normalizedTx.plaidData,
   };

   // Explicitly set category fields from existing transaction
   updateFields.assignedTo = existingTransaction.assignedTo;
   updateFields.assignedToType = existingTransaction.assignedToType;

   await Transaction.findByIdAndUpdate(
      existingTransaction._id,
      { $set: updateFields },
      { session: mongoSession }
   );

   // If amount changed, update account balance
   if (Math.abs(amountDifference) > 0.01 && existingTransaction.accountId) {
      await User.updateOne(
         {
            _id: userId,
            "accounts._id": existingTransaction.accountId,
         },
         {
            $inc: { "accounts.$.balance": amountDifference },
         },
         { session: mongoSession }
      );
   }

   // Update associated income/expense records if the transaction is categorized
   if (existingTransaction.assignedTo && existingTransaction.assignedToType) {
      if (
         existingTransaction.assignedToType === "Income" &&
         Math.abs(amountDifference) > 0.01
      ) {
         // First remove the old amount
         await updateIncomeAmounts(
            existingTransaction.assignedTo,
            -existingTransaction.amount, // Remove old amount
            mongoSession
         );
         // Then add the new amount
         await updateIncomeAmounts(
            existingTransaction.assignedTo,
            normalizedTx.amount,
            mongoSession
         );
      } else if (
         existingTransaction.assignedToType === "Expense" &&
         Math.abs(amountDifference) > 0.01
      ) {
         // First remove the old amount
         const removeResult = await updateExpenseAmounts(
            existingTransaction.assignedTo,
            -existingTransaction.amount, // Remove old amount
            mongoSession
         );
         // Then add the new amount - only if expense still exists
         if (removeResult) {
            await updateExpenseAmounts(
               existingTransaction.assignedTo,
               normalizedTx.amount,
               mongoSession
            );
         }
      }
   }

   return amountDifference;
}

// Simplified processAddedTransactions function
async function processAddedTransactions(
   addedTransactions,
   userId,
   linkedAccountId,
   mongoSession
) {
   const processedTransactions = [];

   for (const plaidTx of addedTransactions) {
      // Create normalized transaction
      const normalizedTx = normalizePlaidTransaction(
         plaidTx,
         userId,
         linkedAccountId
      );

      // Set category fields to null for new transactions
      normalizedTx.assignedTo = null;
      normalizedTx.assignedToType = null;

      processedTransactions.push(normalizedTx);
   }

   // Insert all new transactions
   if (processedTransactions.length > 0) {
      await Transaction.insertMany(processedTransactions, {
         session: mongoSession,
      });

      // Update account balance
      if (linkedAccountId) {
         const totalAmount = processedTransactions.reduce(
            (sum, tx) => sum + tx.amount,
            0
         );
         await User.updateOne(
            {
               _id: userId,
               "accounts._id": linkedAccountId,
            },
            {
               $inc: { "accounts.$.balance": totalAmount },
            },
            { session: mongoSession }
         );
      }
   }

   return processedTransactions.length;
}

// Simplified processModifiedTransactions function
async function processModifiedTransactions(
   modifiedTransactions,
   userId,
   mongoSession
) {
   let updatedCount = 0;

   for (const modifiedTx of modifiedTransactions) {
      const existingTx = await Transaction.findOne(
         { plaidTransactionId: modifiedTx.transaction_id },
         null,
         { session: mongoSession }
      );

      if (existingTx) {
         const newAmount =
            modifiedTx.amount > 0
               ? -Math.abs(modifiedTx.amount)
               : Math.abs(modifiedTx.amount);
         const amountDifference = newAmount - existingTx.amount;

         // Update associated income/expense records if the transaction is categorized
         if (existingTx.assignedTo && existingTx.assignedToType) {
            if (existingTx.assignedToType === "Income") {
               // First remove the old amount
               await updateIncomeAmounts(
                  existingTx.assignedTo,
                  -existingTx.amount, // Remove old amount
                  mongoSession
               );
               // Then add the new amount
               await updateIncomeAmounts(
                  existingTx.assignedTo,
                  newAmount,
                  mongoSession
               );
            } else if (existingTx.assignedToType === "Expense") {
               // Check if we're dealing with a debt payment expense
               const expense = await Expense.findById(
                  existingTx.assignedTo
               ).session(mongoSession);
               const isDebtPayment =
                  expense && expense.isDebtPayment && expense.debtId;

               // For expense, update the amountSpent
               // First remove the old amount (by adding its negative)
               const removeResult = await updateExpenseAmounts(
                  existingTx.assignedTo,
                  -existingTx.amount, // Remove old amount
                  mongoSession,
                  isDebtPayment // Mark as removal if it's a debt payment
               );
               // Then add the new amount - only if expense still exists
               if (removeResult) {
                  await updateExpenseAmounts(
                     existingTx.assignedTo,
                     newAmount,
                     mongoSession,
                     false // Not a removal but an addition
                  );
               }
            }
         }

         // Update transaction fields while preserving category information
         await Transaction.updateOne(
            { plaidTransactionId: modifiedTx.transaction_id },
            {
               $set: {
                  amount: newAmount,
                  type: modifiedTx.amount > 0 ? "Expense" : "Income",
                  payee: modifiedTx.name || modifiedTx.merchant_name,
                  date: new Date(modifiedTx.date),
                  status: modifiedTx.pending ? "pending" : "cleared",
                  plaidData: modifiedTx,
                  // Preserve existing category information
                  assignedTo: existingTx.assignedTo,
                  assignedToType: existingTx.assignedToType,
               },
            },
            { session: mongoSession }
         );

         // Update account balance if amount changed
         if (amountDifference !== 0 && existingTx.accountId) {
            await User.updateOne(
               {
                  _id: userId,
                  "accounts._id": existingTx.accountId,
               },
               {
                  $inc: { "accounts.$.balance": amountDifference },
               },
               { session: mongoSession }
            );
         }

         updatedCount++;
      }
   }

   return updatedCount;
}

// Updated processRemovedTransactions function with category adjustments
async function processRemovedTransactions(
   removedTransactionIds,
   userId,
   mongoSession
) {
   const removedTransactions = await Transaction.find(
      { plaidTransactionId: { $in: removedTransactionIds } },
      null,
      { session: mongoSession }
   );

   if (removedTransactions.length > 0) {
      // Calculate balance adjustments
      const accountAdjustments = removedTransactions.reduce((acc, tx) => {
         if (tx.accountId) {
            acc[tx.accountId] = (acc[tx.accountId] || 0) - tx.amount;
         }
         return acc;
      }, {});

      // Update account balances
      for (const [accountId, adjustment] of Object.entries(
         accountAdjustments
      )) {
         await User.updateOne(
            {
               _id: userId,
               "accounts._id": accountId,
            },
            {
               $inc: { "accounts.$.balance": adjustment },
            },
            { session: mongoSession }
         );
      }

      // Handle category adjustments for removed transactions
      for (const tx of removedTransactions) {
         if (tx.assignedTo && tx.assignedToType) {
            if (tx.assignedToType === "Income") {
               // For income, decrease the receivedAmount
               await updateIncomeAmounts(
                  tx.assignedTo,
                  -Math.abs(tx.amount), // Remove the amount from received
                  mongoSession
               );
            } else if (tx.assignedToType === "Expense") {
               // Check if we're dealing with a debt payment expense
               const expense = await Expense.findById(tx.assignedTo).session(
                  mongoSession
               );
               const isDebtPayment =
                  expense && expense.isDebtPayment && expense.debtId;

               // For expense, decrease the amountSpent
               const updateResult = await updateExpenseAmounts(
                  tx.assignedTo,
                  -tx.amount, // Remove the amount from spent
                  mongoSession,
                  isDebtPayment // Mark as removal if it's a debt payment
               );
               // updateResult will be null if expense doesn't exist - that's ok for removal
            }
         }
      }

      // Delete removed transactions
      await Transaction.deleteMany(
         { plaidTransactionId: { $in: removedTransactionIds } },
         { session: mongoSession }
      );
   }

   return removedTransactions.length;
}

export async function POST(request) {
   try {
      // Parse the request body
      const { itemId, _userId } = await request.json();

      if (!itemId) {
         return NextResponse.json(
            { error: "Item ID is required" },
            { status: 400 }
         );
      }

      // Check if this is a server-to-server webhook call
      const isWebhookCall = !!_userId;

      // Log sync request received
      await logInfo(
         "plaid-sync",
         "sync_request",
         `Received sync request for item: ${itemId}`,
         {
            itemId,
            isWebhook: isWebhookCall,
            userId: isWebhookCall ? _userId : undefined,
         }
      );

      // If this is a webhook call, we use the provided user ID
      // Otherwise, we get the user from the session
      let mongoUserId;

      if (!isWebhookCall) {
         // Get the user from Next Auth
         const {
            session: authSession,
            userId: clerkUserId,
            error,
         } = await getSessionForAPI();
         if (error) return error;

         // Get MongoDB user
         await dbConnect();
         const user = await User.findById(clerkUserId);
         if (!user) {
            return NextResponse.json(
               { error: "User not found" },
               { status: 404 }
            );
         }
         mongoUserId = user._id;
      } else {
         mongoUserId = _userId;
      }

      // Connect to the database
      await dbConnect();

      // Rest of your existing code for handling the sync

      try {
         // Log that we're about to call syncTransactionsForItem
         await logInfo(
            "plaid-sync",
            "sync_process_start",
            `Starting transaction sync process for item: ${itemId}`,
            {
               itemId,
               userId: mongoUserId,
               isWebhook: isWebhookCall,
            }
         );

         // Call sync with a flag indicating if it's from a webhook
         const syncResult = await syncTransactionsForItem(
            itemId,
            mongoUserId,
            isWebhookCall
         );

         // Log sync success
         await logInfo(
            "plaid-sync",
            "sync_process_complete",
            `Completed transaction sync for item: ${itemId}`,
            {
               itemId,
               userId: mongoUserId,
               isWebhook: isWebhookCall,
               ...syncResult,
            }
         );

         return NextResponse.json(syncResult);
      } catch (syncError) {
         // Log sync failure
         await logError(
            "plaid-sync",
            "sync_process_error",
            `Error in transaction sync for item: ${itemId}`,
            {
               itemId,
               userId: mongoUserId,
               isWebhook: isWebhookCall,
               error: syncError.message,
               stack: syncError.stack,
            }
         );

         throw syncError;
      }
   } catch (error) {
      console.error("Sync account error:", error);

      // Log the error
      await logError(
         "plaid-sync",
         "sync_api_error",
         `API error in sync-account endpoint`,
         {
            error: error.message,
            stack: error.stack,
         }
      );

      return NextResponse.json({ error: error.message }, { status: 500 });
   }
}

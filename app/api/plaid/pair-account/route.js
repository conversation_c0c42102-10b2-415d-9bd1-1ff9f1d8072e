import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import { plaidClient } from "../../../lib/plaid/config";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import mongoose from "mongoose";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const {
         itemId,
         accountId,
         syncStartDate = "creation",
      } = await request.json();

      // Handle accountId - it can be a string or number
      const linkedAccountId = accountId === "none" ? null : accountId;

      // First find the user to verify they exist
      const user = await User.findById(userId);
      if (!user) {
         console.error("User not found:", userId);
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Check if user has required subscription for bank connections
      const { checkUserFeatureAccess } = await import(
         "@/app/lib/stripe/subscriptionUtils"
      );
      const hasAccess = await checkUserFeatureAccess(
         user._id,
         "bank_connections"
      );

      if (!hasAccess) {
         return NextResponse.json(
            {
               error: "Subscription required",
               message:
                  "Bank connections require a Basic or Pro subscription plan.",
               feature: "bank_connections",
            },
            { status: 403 }
         );
      }

      // Find the plaidItem to verify it exists
      const plaidItem = user.plaidItems.find((item) => item.itemId === itemId);
      if (!plaidItem) {
         console.error("Plaid item not found:", itemId);
         return NextResponse.json(
            { error: "Plaid item not found" },
            { status: 404 }
         );
      }

      // Verify the account exists if we're linking to one
      if (linkedAccountId !== null) {
         const accountExists = user.accounts.some(
            (account) => String(account._id) === String(linkedAccountId)
         );
         if (!accountExists) {
            console.error("Account not found:", linkedAccountId);
            return NextResponse.json(
               { error: "Account not found" },
               { status: 404 }
            );
         }
      }

      // Clear only the specific connections that need to be updated
      // 1. Clear any existing plaidItemId from the target account
      // 2. Clear any existing linkedAccountId from the target plaidItem
      // 3. Clear any other account that might be using this plaidItem
      await User.updateOne(
         { _id: user._id },
         {
            $set: {
               // Clear the target plaidItem's linkedAccountId
               "plaidItems.$[plaidItem].linkedAccountId": null,
               // Clear plaidItemId from any account that was using this plaidItem
               "accounts.$[account].plaidItemId": null,
            },
         },
         {
            arrayFilters: [
               { "plaidItem.itemId": itemId },
               { "account.plaidItemId": itemId },
            ],
         }
      );

      // Calculate start date based on preference
      let startDate;
      if (syncStartDate === "today") {
         startDate = new Date(); // Start from today
      } else if (syncStartDate === "creation") {
         // Default to start of creation month
         const userCreationDate = user._id.getTimestamp();
         startDate = new Date(
            userCreationDate.getFullYear(),
            userCreationDate.getMonth(),
            1
         );
      } else {
         // Handle custom date
         startDate = new Date(syncStartDate);

         // Validate that the custom date is within allowed range
         const creationDate = user._id.getTimestamp();
         const minDate = new Date(
            creationDate.getFullYear(),
            creationDate.getMonth(),
            1
         );
         const maxDate = new Date();

         if (startDate < minDate || startDate > maxDate) {
            return NextResponse.json(
               {
                  error: "Custom date must be between account creation month and today",
               },
               { status: 400 }
            );
         }
      }

      // Log the pairing attempt
      console.log("Attempting to pair account:", {
         userId: user._id.toString(),
         itemId,
         linkedAccountId,
         startDate,
         timestamp: new Date().toISOString(),
      });

      // Update both the plaidItem's linkedAccountId and the account's plaidItemId
      const updateResult = await User.findOneAndUpdate(
         {
            _id: user._id,
            "plaidItems.itemId": itemId,
         },
         {
            $set: {
               "plaidItems.$.linkedAccountId": linkedAccountId,
               "accounts.$[account].plaidItemId": linkedAccountId
                  ? itemId
                  : null,
               "plaidItems.$.startDate": startDate,
            },
         },
         {
            new: true,
            runValidators: true,
            arrayFilters: [{ "account._id": linkedAccountId }],
         }
      );

      if (!updateResult) {
         console.error("Update failed. User:", userId, "ItemId:", itemId);
         return NextResponse.json(
            { error: "Failed to update account pairing" },
            { status: 400 }
         );
      }

      // Verify both updates were successful
      const updatedItem = updateResult.plaidItems.find(
         (item) => item.itemId === itemId
      );
      const updatedAccount = updateResult.accounts.find(
         (account) => String(account._id) === String(linkedAccountId)
      );

      console.log("Update successful:", {
         itemId,
         linkedAccountId: updatedItem?.linkedAccountId,
         accountPlaidItemId: updatedAccount?.plaidItemId,
         updatedItemData: updatedItem,
         updatedAccountData: updatedAccount,
         userIdUsed: user._id.toString(),
         mongoObjectId: user._id,
      });

      return NextResponse.json({
         success: true,
         message: "Account pairing updated successfully",
         data: {
            itemId,
            linkedAccountId: updatedItem?.linkedAccountId,
         },
      });
   } catch (error) {
      console.error("Error updating account pairing:", {
         error,
         stack: error.stack,
         message: error.message,
      });
      return NextResponse.json(
         {
            error: "Failed to update account pairing",
            details: error.message,
            stack:
               process.env.NODE_ENV === "development" ? error.stack : undefined,
         },
         { status: 500 }
      );
   }
}

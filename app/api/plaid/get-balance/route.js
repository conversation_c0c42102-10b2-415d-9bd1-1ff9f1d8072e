import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import { plaidClient } from "../../../lib/plaid/config";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { itemId, accountId } = await request.json();
      if (!itemId) {
         return NextResponse.json(
            { error: "Item ID is required" },
            { status: 400 }
         );
      }

      // Get the access token for the item
      const plaidItem = user.plaidItems.find((item) => item.itemId === itemId);

      if (!plaidItem) {
         return NextResponse.json(
            { error: "Plaid item not found" },
            { status: 404 }
         );
      }

      // Get account balances from Plaid using accounts/balance/get for real-time data
      const balanceResponse = await plaidClient.accountsBalanceGet({
         access_token: plaidItem.accessToken,
      });

      // Find the specific account if accountId is provided
      let balance = null;
      if (accountId) {
         const account = balanceResponse.data.accounts.find(
            (acc) => acc.account_id === accountId
         );
         if (account) {
            // Use available balance if present, fall back to current balance
            balance = {
               available: account.balances.available,
               current: account.balances.current,
               // Use available balance (includes pending) if it exists, otherwise use current
               balance: account.balances.available ?? account.balances.current,
            };
         }
      } else {
         // Return all account balances
         balance = balanceResponse.data.accounts.map((account) => ({
            account_id: account.account_id,
            available: account.balances.available,
            current: account.balances.current,
            // Use available balance (includes pending) if it exists, otherwise use current
            balance: account.balances.available ?? account.balances.current,
            name: account.name,
            type: account.type,
            subtype: account.subtype,
         }));
      }

      return NextResponse.json({ balance });
   } catch (error) {
      console.error("Error getting Plaid balance:", error);
      return NextResponse.json(
         { error: "Failed to get balance", details: error.message },
         { status: 500 }
      );
   }
}

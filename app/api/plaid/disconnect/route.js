import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import { plaidClient } from "../../../lib/plaid/config";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Transaction from "../../../lib/mongodb/models/Transaction";
import mongoose from "mongoose";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { itemId } = await request.json();
      if (!itemId) {
         return NextResponse.json(
            { error: "Item ID is required" },
            { status: 400 }
         );
      }

      // Start a MongoDB transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Find the user and get the access token for the item
         const user = await User.findById(userId);
         const plaidItem = user.plaidItems.find(
            (item) => item.itemId === itemId
         );

         if (!plaidItem) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Plaid item not found" },
               { status: 404 }
            );
         }

         // Remove item from Plaid
         try {
            await plaidClient.itemRemove({
               access_token: plaidItem.accessToken,
            });
         } catch (plaidError) {
            console.error("Error removing item from Plaid:", plaidError);
            // Continue with local cleanup even if Plaid removal fails
         }

         // Remove the Plaid item from the user's plaidItems array
         const result = await User.findByIdAndUpdate(
            user._id,
            {
               $pull: {
                  plaidItems: { itemId: itemId },
               },
               // Also clear any accounts linked to this Plaid item
               $set: {
                  "accounts.$[account].plaidItemId": null,
               },
            },
            {
               session: mongoSession,
               new: true,
               arrayFilters: [{ "account.plaidItemId": itemId }],
            }
         );

         if (!result) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Failed to disconnect account" },
               { status: 400 }
            );
         }

         await mongoSession.commitTransaction();

         return NextResponse.json({
            success: true,
            message: "Account disconnected successfully",
         });
      } catch (error) {
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error disconnecting Plaid account:", error);
      return NextResponse.json(
         {
            error: "Failed to disconnect account",
            details: error.message,
         },
         { status: 500 }
      );
   }
}

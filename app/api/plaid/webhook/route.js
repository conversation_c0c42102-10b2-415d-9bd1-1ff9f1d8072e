import { NextResponse } from "next/server";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import { plaidClient } from "../../../lib/plaid/config";
import { logInfo, logError } from "../../../lib/utils/logger";
import mongoose from "mongoose";
import { syncTransactionsForItem } from "../../../lib/plaid/syncTransactions";
import { updatePlaidItemStatus } from "../../../lib/utils/plaidUtils";

// Helper function to sync transactions for an item
async function webhookSyncTransactions(itemId, userId) {
   try {
      // Log start of sync attempt with additional details
      await logInfo(
         "plaid-webhook",
         "sync_attempt",
         `Starting sync attempt for item ${itemId}`,
         {
            itemId,
            userId,
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || "unknown",
         }
      );

      // In serverless functions, we need to use absolute URLs with the correct domain
      // The request object in webhooks doesn't have the same context as regular API routes
      const domain =
         process.env.NODE_ENV === "production"
            ? process.env.NEXTAUTH_URL || "https://app.altobudgeting.com"
            : "http://localhost:3000";

      const apiUrl = `${domain}/api/plaid/sync-account`;

      console.log(`Calling sync API at: ${apiUrl}`, {
         itemId,
         userId,
         timestamp: new Date().toISOString(),
      });

      // Use a more direct approach with the Plaid sync utility
      try {
         // Skip the API call and directly use the sync utility
         // This avoids potential issues with nested API calls in serverless environments
         console.log(`Using direct Plaid sync for item ${itemId}`, {
            itemId,
            userId,
            timestamp: new Date().toISOString(),
         });

         // Connect to database
         await dbConnect();

         // Use the shared utility to perform the sync directly
         const syncResult = await syncTransactionsForItem(itemId, userId, true);

         // Log direct sync result
         await logInfo(
            "plaid-webhook",
            "direct_sync_success",
            `Successfully completed direct sync for item ${itemId}`,
            {
               itemId,
               userId,
               syncResult,
               timestamp: new Date().toISOString(),
            }
         );

         return syncResult;
      } catch (syncError) {
         // Log any error during the direct sync approach
         console.error(
            `Error in direct Plaid sync for item ${itemId}:`,
            syncError
         );
         await logError(
            "plaid-webhook",
            "direct_sync_error",
            `Error in direct Plaid sync for item ${itemId}`,
            {
               error: syncError.message,
               stack: syncError.stack,
               itemId,
               userId,
               timestamp: new Date().toISOString(),
            }
         );
         throw syncError;
      }
   } catch (error) {
      console.error(`Error syncing transactions for item ${itemId}:`, error);
      await logError(
         "plaid-webhook",
         "sync_transactions_error",
         `Error syncing transactions for item ${itemId}`,
         {
            error: error.message,
            stack: error.stack,
            itemId,
            userId,
            timestamp: new Date().toISOString(),
         }
      );
      throw error;
   }
}

// Helper function to find the user associated with an item
async function findUserForItem(itemId, retryCount = 0, maxRetries = 3) {
   await dbConnect();
   const user = await User.findOne({ "plaidItems.itemId": itemId });

   if (!user && retryCount < maxRetries) {
      // If no user found and we haven't exhausted retries, wait and try again
      // This handles race conditions where webhook arrives before database write is committed
      const delayMs = (retryCount + 1) * 1000; // 1s, 2s, 3s delays

      console.log(
         `No user found for Plaid item ${itemId}, retrying in ${delayMs}ms (attempt ${
            retryCount + 1
         }/${maxRetries + 1})`
      );

      await logInfo(
         "plaid-webhook",
         "user_lookup_retry",
         `Retrying user lookup for Plaid item ${itemId} after ${delayMs}ms delay`,
         {
            itemId,
            retryAttempt: retryCount + 1,
            maxRetries: maxRetries + 1,
            delayMs,
            reason: "race_condition_handling",
            timestamp: new Date().toISOString(),
         }
      );

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, delayMs));

      // Recursive retry
      return findUserForItem(itemId, retryCount + 1, maxRetries);
   }

   if (!user) {
      console.error(
         `No user found for Plaid item ID: ${itemId} after ${
            maxRetries + 1
         } attempts`
      );
      await logError(
         "plaid-webhook",
         "item_not_found",
         `No user found for Plaid item ID: ${itemId} after ${
            maxRetries + 1
         } attempts`,
         {
            itemId,
            totalAttempts: maxRetries + 1,
            finalAttempt: true,
         }
      );
      return null;
   }

   if (retryCount > 0) {
      // Log successful retry
      await logInfo(
         "plaid-webhook",
         "user_lookup_retry_success",
         `Successfully found user for Plaid item ${itemId} on retry attempt ${
            retryCount + 1
         }`,
         {
            itemId,
            retryAttempt: retryCount + 1,
            userId: user._id.toString(),
            timestamp: new Date().toISOString(),
         }
      );
   }

   return user;
}

// Process a transaction webhook by triggering a sync
async function processTransactionWebhook(webhookData) {
   const { webhook_code, item_id } = webhookData;

   // Log start of transaction webhook processing
   await logInfo(
      "plaid-webhook",
      "transaction_webhook_start",
      `Starting to process ${webhook_code} webhook for item ${item_id}`,
      {
         itemId: item_id,
         webhookCode: webhook_code,
         webhookData: {
            initial_update_complete: webhookData.initial_update_complete,
            historical_update_complete: webhookData.historical_update_complete,
         },
         timestamp: new Date().toISOString(),
      }
   );

   // Find the user who owns this item
   let user;
   try {
      user = await findUserForItem(item_id);
      if (!user) {
         await logError(
            "plaid-webhook",
            "user_not_found",
            `No user found for Plaid item ID: ${item_id}`,
            {
               itemId: item_id,
               timestamp: new Date().toISOString(),
            }
         );
         return;
      }
   } catch (findError) {
      await logError(
         "plaid-webhook",
         "find_user_error",
         `Error finding user for item: ${item_id}`,
         {
            error: findError.message,
            stack: findError.stack,
            itemId: item_id,
            timestamp: new Date().toISOString(),
         }
      );
      return;
   }

   // Log the webhook processing
   await logInfo(
      "plaid-webhook",
      "processing_transaction_webhook",
      `Processing ${webhook_code} webhook for user ${user._id.toString()}`,
      {
         itemId: item_id,
         userId: user._id.toString(),
         webhookCode: webhook_code,
         initialUpdateComplete: webhookData.initial_update_complete,
         historicalUpdateComplete: webhookData.historical_update_complete,
         timestamp: new Date().toISOString(),
      },
      user._id.toString()
   );

   try {
      // Verify item is in a synchable state
      const plaidItem = user.plaidItems.find((item) => item.itemId === item_id);
      if (!plaidItem) {
         await logError(
            "plaid-webhook",
            "item_not_found_in_user",
            `Plaid item ${item_id} not found in user document`,
            {
               itemId: item_id,
               userId: user._id.toString(),
               webhookCode: webhook_code,
               timestamp: new Date().toISOString(),
            },
            user._id.toString()
         );
         return;
      }

      // Skip processing if item is suspended (user on free tier)
      if (plaidItem.status === "suspended") {
         await logInfo(
            "plaid-webhook",
            "item_suspended_skip_sync",
            `Skipping sync for suspended Plaid item ${item_id} (user on free tier)`,
            {
               itemId: item_id,
               userId: user._id.toString(),
               webhookCode: webhook_code,
               itemStatus: plaidItem.status,
               suspendedAt: plaidItem.suspendedAt,
               timestamp: new Date().toISOString(),
            },
            user._id.toString()
         );
         return;
      }

      // Update item status to good if sync is successful
      updatePlaidItemStatus(user, item_id, "good");
      await user.save();

      // Trigger transaction sync process
      const syncResult = await webhookSyncTransactions(
         item_id,
         user._id.toString()
      );

      // Log successful sync
      await logInfo(
         "plaid-webhook",
         "sync_complete",
         `Sync completed for ${webhook_code} webhook for user ${user._id.toString()}`,
         {
            itemId: item_id,
            userId: user._id.toString(),
            syncResult,
            webhookCode: webhook_code,
            timestamp: new Date().toISOString(),
         },
         user._id.toString()
      );
   } catch (syncError) {
      // Log sync failure
      await logError(
         "plaid-webhook",
         "sync_failed",
         `Sync failed for ${webhook_code} webhook for user ${user._id.toString()}`,
         {
            itemId: item_id,
            userId: user._id.toString(),
            error: syncError.message,
            stack: syncError.stack,
            webhookCode: webhook_code,
            timestamp: new Date().toISOString(),
         },
         user._id.toString()
      );
   }
}

// Process item error webhooks
async function processItemErrorWebhook(webhookData) {
   const { webhook_code, item_id, error } = webhookData;

   await logError(
      "plaid-webhook",
      "item_error_webhook_received",
      `Item error webhook received for item ${item_id}`,
      {
         itemId: item_id,
         webhookCode: webhook_code,
         error: error,
         timestamp: new Date().toISOString(),
      }
   );

   // Find the user who owns this item
   let user;
   try {
      user = await findUserForItem(item_id);
      if (!user) {
         await logError(
            "plaid-webhook",
            "user_not_found_for_error",
            `No user found for Plaid item ID: ${item_id} in error webhook`,
            {
               itemId: item_id,
               timestamp: new Date().toISOString(),
            }
         );
         return;
      }
   } catch (findError) {
      await logError(
         "plaid-webhook",
         "find_user_error_webhook",
         `Error finding user for item in error webhook: ${item_id}`,
         {
            error: findError.message,
            stack: findError.stack,
            itemId: item_id,
            timestamp: new Date().toISOString(),
         }
      );
      return;
   }

   // Update plaid item status based on error code
   let newStatus = "error";
   if (error?.error_code === "ITEM_LOGIN_REQUIRED") {
      newStatus = "login_required";
   }

   updatePlaidItemStatus(user, item_id, newStatus, {
      errorCode: error?.error_code || "UNKNOWN_ERROR",
      errorMessage: error?.error_message || "Unknown error occurred",
      errorType: error?.error_type || "ITEM_ERROR",
   });

   try {
      await user.save();
      await logInfo(
         "plaid-webhook",
         "item_status_updated",
         `Updated item status to ${newStatus} for item ${item_id}`,
         {
            itemId: item_id,
            userId: user._id.toString(),
            newStatus,
            errorCode: error?.error_code,
            timestamp: new Date().toISOString(),
         }
      );
   } catch (saveError) {
      await logError(
         "plaid-webhook",
         "save_error_webhook",
         `Error saving user after item error webhook: ${item_id}`,
         {
            error: saveError.message,
            stack: saveError.stack,
            itemId: item_id,
            userId: user._id.toString(),
            timestamp: new Date().toISOString(),
         }
      );
   }
}

// Process item webhooks for connection status changes
async function processItemWebhook(webhookData) {
   const { webhook_code, item_id } = webhookData;

   await logInfo(
      "plaid-webhook",
      "item_webhook_received",
      `Item webhook received: ${webhook_code} for item ${item_id}`,
      {
         itemId: item_id,
         webhookCode: webhook_code,
         timestamp: new Date().toISOString(),
      }
   );

   // Find the user who owns this item
   let user;
   try {
      user = await findUserForItem(item_id);
      if (!user) {
         await logError(
            "plaid-webhook",
            "user_not_found_for_item_webhook",
            `No user found for Plaid item ID: ${item_id} in item webhook`,
            {
               itemId: item_id,
               webhookCode: webhook_code,
               timestamp: new Date().toISOString(),
            }
         );
         return;
      }
   } catch (findError) {
      await logError(
         "plaid-webhook",
         "find_user_error_item_webhook",
         `Error finding user for item in item webhook: ${item_id}`,
         {
            error: findError.message,
            stack: findError.stack,
            itemId: item_id,
            webhookCode: webhook_code,
            timestamp: new Date().toISOString(),
         }
      );
      return;
   }

   // Update plaid item status based on webhook code
   let newStatus = "good";
   switch (webhook_code) {
      case "PENDING_EXPIRATION":
         newStatus = "pending_expiration";
         break;
      case "PENDING_DISCONNECT":
         newStatus = "pending_disconnect";
         break;
      case "LOGIN_REPAIRED":
         newStatus = "good";
         break;
      default:
         // For other item webhooks, keep current status
         return;
   }

   updatePlaidItemStatus(user, item_id, newStatus);

   try {
      await user.save();
      await logInfo(
         "plaid-webhook",
         "item_status_updated_webhook",
         `Updated item status to ${newStatus} for item ${item_id} via ${webhook_code} webhook`,
         {
            itemId: item_id,
            userId: user._id.toString(),
            newStatus,
            webhookCode: webhook_code,
            timestamp: new Date().toISOString(),
         }
      );
   } catch (saveError) {
      await logError(
         "plaid-webhook",
         "save_error_item_webhook",
         `Error saving user after item webhook: ${item_id}`,
         {
            error: saveError.message,
            stack: saveError.stack,
            itemId: item_id,
            userId: user._id.toString(),
            webhookCode: webhook_code,
            timestamp: new Date().toISOString(),
         }
      );
   }
}

// Process webhooks asynchronously without blocking the response
async function processWebhookAsync(webhookData) {
   try {
      // We now log the start of processing before this function is called
      // Only log start of processing for non-SYNC webhook types
      if (
         !(
            webhookData.webhook_type === "TRANSACTIONS" &&
            webhookData.webhook_code === "SYNC_UPDATES_AVAILABLE"
         )
      ) {
         await logInfo(
            "plaid-webhook",
            "async_processing_started",
            `Started async processing of ${webhookData.webhook_type}:${webhookData.webhook_code} webhook`,
            {
               webhookType: webhookData.webhook_type,
               webhookCode: webhookData.webhook_code,
               itemId: webhookData.item_id,
            }
         );
      }

      if (webhookData.webhook_type === "TRANSACTIONS" && webhookData.item_id) {
         if (webhookData.webhook_code === "SYNC_UPDATES_AVAILABLE") {
            // Process only SYNC_UPDATES_AVAILABLE webhooks
            await logInfo(
               "plaid-webhook",
               "processing_sync_webhook",
               `Processing SYNC_UPDATES_AVAILABLE webhook for item ${webhookData.item_id}`,
               {
                  itemId: webhookData.item_id,
                  webhookCode: webhookData.webhook_code,
                  timestamp: new Date().toISOString(),
               }
            );

            // Process the webhook - this will use the plaidItem's startDate for filtering
            // Only transactions from the connection date/time forward will be synced
            await processTransactionWebhook(webhookData);
         } else if (
            webhookData.webhook_code === "INITIAL_UPDATE" ||
            webhookData.webhook_code === "HISTORICAL_UPDATE"
         ) {
            // Process INITIAL_UPDATE and HISTORICAL_UPDATE webhooks for new bank connections
            // These webhooks are also subject to connection-based date filtering
            await logInfo(
               "plaid-webhook",
               "processing_historical_webhook",
               `Processing ${webhookData.webhook_code} webhook for new bank connection`,
               {
                  itemId: webhookData.item_id,
                  webhookCode: webhookData.webhook_code,
                  note: "Will respect plaidItem.startDate for filtering transactions",
                  timestamp: new Date().toISOString(),
               }
            );

            // Process the webhook to sync historical transactions
            // Note: This will still respect the plaidItem.startDate filtering
            await processTransactionWebhook(webhookData);
         } else {
            // Log legacy webhooks but don't process them (DEFAULT_UPDATE, TRANSACTIONS_REMOVED)
            await logInfo(
               "plaid-webhook",
               "legacy_webhook_ignored",
               `Received legacy webhook ${webhookData.webhook_code} for the /transactions/get API, ignoring as we use /transactions/sync`,
               {
                  itemId: webhookData.item_id,
                  webhookCode: webhookData.webhook_code,
               }
            );
         }
      } else if (
         webhookData.webhook_type === "ITEM" &&
         webhookData.webhook_code === "ERROR"
      ) {
         // Handle item error webhooks
         await processItemErrorWebhook(webhookData);
      } else if (webhookData.webhook_type === "ITEM") {
         // Handle item webhooks for connection status changes
         await processItemWebhook(webhookData);
      }
   } catch (error) {
      // Comprehensive error logging
      await logError(
         "plaid-webhook",
         "async_processing_error",
         "Error in async webhook processing",
         {
            error: error.message,
            stack: error.stack,
            webhookData: {
               type: webhookData.webhook_type,
               code: webhookData.webhook_code,
               itemId: webhookData.item_id,
            },
         }
      );

      console.error("Error in async webhook processing:", error);
   }
}

export async function POST(request) {
   // Define JSON headers once to be reused
   const jsonHeaders = {
      "Content-Type": "application/json",
   };

   console.log("Webhook POST handler started", {
      timestamp: new Date().toISOString(),
      url: request.url,
   });

   try {
      // Parse the webhook data
      let webhookData;
      try {
         webhookData = await request.json();
         console.log("Successfully parsed webhook data", {
            webhook_type: webhookData.webhook_type,
            webhook_code: webhookData.webhook_code,
            timestamp: new Date().toISOString(),
         });
      } catch (parseError) {
         // Handle case where request body is not valid JSON
         const body = await request.text();
         console.error("Failed to parse webhook payload", {
            error: parseError.message,
            bodyPreview: body.substring(0, 100),
         });
         await logError(
            "plaid-webhook",
            "invalid_json",
            "Failed to parse webhook payload as JSON",
            {
               error: parseError.message,
               bodyPreview: body.substring(0, 200), // Log first 200 chars of body
               contentType: request.headers.get("content-type"),
               timestamp: new Date().toISOString(),
            }
         );
         // Always return 200 to Plaid, even for parse errors
         return NextResponse.json(
            {
               status: "acknowledged",
               message: "Invalid JSON payload, but webhook received",
            },
            { status: 200, headers: jsonHeaders }
         );
      }

      // Extract webhook type and code
      const { webhook_type, webhook_code, item_id } = webhookData;

      // Log the raw webhook payload to database
      await logInfo(
         "plaid-webhook",
         "webhook_received",
         `Received ${webhook_type}:${webhook_code} webhook`,
         webhookData
      );

      // For SYNC_UPDATES_AVAILABLE webhooks, process without blocking response
      if (
         webhook_type === "TRANSACTIONS" &&
         webhook_code === "SYNC_UPDATES_AVAILABLE"
      ) {
         // Log start of processing
         await logInfo(
            "plaid-webhook",
            "sync_processing_started",
            `Processing ${webhook_type}:${webhook_code} webhook synchronously`,
            {
               webhookType: webhook_type,
               webhookCode: webhook_code,
               itemId: item_id,
               timestamp: new Date().toISOString(),
            }
         );

         // Process the webhook synchronously rather than in the background
         // This ensures the process completes before the serverless function terminates
         try {
            console.log(
               "Starting SYNC_UPDATES_AVAILABLE webhook processing synchronously",
               {
                  itemId: item_id,
                  timestamp: new Date().toISOString(),
               }
            );

            // Process webhook synchronously - await the result
            // to ensure it completes before responding
            await processTransactionWebhook(webhookData);

            console.log(
               "Successfully completed SYNC_UPDATES_AVAILABLE webhook processing",
               {
                  itemId: item_id,
                  timestamp: new Date().toISOString(),
               }
            );

            // Log completion
            await logInfo(
               "plaid-webhook",
               "sync_processing_completed",
               `Completed processing ${webhook_type}:${webhook_code} webhook synchronously`,
               {
                  webhookType: webhook_type,
                  webhookCode: webhook_code,
                  itemId: item_id,
                  timestamp: new Date().toISOString(),
               }
            );
         } catch (processingError) {
            // Log any error that occurs during processing
            console.error("Error processing webhook:", processingError);
            await logError(
               "plaid-webhook",
               "sync_processing_error",
               "Error processing webhook synchronously",
               {
                  error: processingError.message,
                  stack: processingError.stack,
                  webhookType: webhook_type,
                  webhookCode: webhook_code,
                  itemId: item_id,
                  timestamp: new Date().toISOString(),
               }
            );
         }
      } else if (webhook_type === "TRANSACTIONS") {
         // Process INITIAL_UPDATE and HISTORICAL_UPDATE webhooks for new bank connections
         if (
            webhook_code === "INITIAL_UPDATE" ||
            webhook_code === "HISTORICAL_UPDATE"
         ) {
            await logInfo(
               "plaid-webhook",
               "processing_historical_webhook_main",
               `Processing ${webhook_code} webhook for new bank connection synchronously`,
               {
                  itemId: item_id,
                  webhookCode: webhook_code,
                  timestamp: new Date().toISOString(),
               }
            );

            try {
               // Process the webhook synchronously
               await processTransactionWebhook(webhookData);

               await logInfo(
                  "plaid-webhook",
                  "historical_webhook_completed",
                  `Completed processing ${webhook_code} webhook synchronously`,
                  {
                     itemId: item_id,
                     webhookCode: webhook_code,
                     timestamp: new Date().toISOString(),
                  }
               );
            } catch (processingError) {
               await logError(
                  "plaid-webhook",
                  "historical_webhook_error",
                  `Error processing ${webhook_code} webhook synchronously`,
                  {
                     error: processingError.message,
                     stack: processingError.stack,
                     itemId: item_id,
                     webhookCode: webhook_code,
                     timestamp: new Date().toISOString(),
                  }
               );
            }
         } else {
            // For other transaction webhook types (like DEFAULT_UPDATE), just log they're ignored but don't process them
            await logInfo(
               "plaid-webhook",
               "legacy_webhook_ignored",
               `Received legacy webhook ${webhook_code} for the /transactions/get API, ignoring as we use /transactions/sync`,
               {
                  itemId: item_id,
                  webhookCode: webhook_code,
                  timestamp: new Date().toISOString(),
               }
            );
         }
      } else if (webhook_type === "ITEM" && webhook_code === "ERROR") {
         // For item error webhooks, log but don't process
         await processItemErrorWebhook(webhookData);
      } else if (webhook_type === "ITEM") {
         // For item webhooks, log but don't process
         await processItemWebhook(webhookData);
      }

      console.log("Webhook processing initiated, sending response", {
         webhook_type,
         webhook_code,
         item_id,
         timestamp: new Date().toISOString(),
      });

      // Immediately respond with 200 status code
      return NextResponse.json(
         {
            status: "acknowledged",
            message: `Received ${webhook_type}:${webhook_code} webhook`,
         },
         { status: 200, headers: jsonHeaders }
      );
   } catch (error) {
      console.error("Error processing webhook:", error);

      // Log error to database
      await logError(
         "plaid-webhook",
         "webhook_error",
         "Failed to process webhook, but acknowledged",
         {
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
         }
      );

      // Always return 200 to Plaid, even for critical errors
      return NextResponse.json(
         {
            status: "acknowledged",
            message: "Error processing webhook, but acknowledged",
         },
         { status: 200, headers: jsonHeaders }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import { plaidClient } from "../../../lib/plaid/config";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { itemId } = await request.json();

      if (!itemId) {
         return NextResponse.json(
            { error: "Item ID is required" },
            { status: 400 }
         );
      }

      await dbConnect();

      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const plaidItem = user.plaidItems.find((item) => item.itemId === itemId);
      if (!plaidItem) {
         return NextResponse.json(
            { error: "Plaid item not found" },
            { status: 404 }
         );
      }

      // Try to get item info
      let itemInfo = null;
      let itemError = null;

      try {
         const itemResponse = await plaidClient.itemGet({
            access_token: plaidItem.accessToken,
         });
         itemInfo = itemResponse.data;
      } catch (error) {
         itemError = {
            error_code: error.error_code,
            error_message: error.error_message,
            error_type: error.error_type,
         };
      }

      // Try to get accounts info
      let accountsInfo = null;
      let accountsError = null;

      try {
         const accountsResponse = await plaidClient.accountsGet({
            access_token: plaidItem.accessToken,
         });
         accountsInfo = accountsResponse.data;
      } catch (error) {
         accountsError = {
            error_code: error.error_code,
            error_message: error.error_message,
            error_type: error.error_type,
         };
      }

      return NextResponse.json({
         itemId,
         institutionName: plaidItem.institutionName,
         currentStatus: plaidItem.status,
         itemInfo,
         itemError,
         accountsInfo,
         accountsError,
         needsReauth:
            itemError?.error_code === "ITEM_LOGIN_REQUIRED" ||
            accountsError?.error_code === "ITEM_LOGIN_REQUIRED",
      });
   } catch (error) {
      console.error("Error in debug connection endpoint:", error);
      return NextResponse.json(
         { error: "Internal server error" },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import { plaidClient } from "../../../lib/plaid/config";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function POST(request) {
   try {
      // Get the authenticated user's session
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      // Parse request body first to get accountId and syncStartDate
      const body = await request.json().catch(() => ({}));
      const { accountId, syncStartDate } = body;

      // Connect to database and fetch user first
      await dbConnect();
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Check if user has required subscription for bank connections
      // Exception: Allow reauthorization for existing connections even on free tier
      const { checkUserFeatureAccess } = await import(
         "@/app/lib/stripe/subscriptionUtils"
      );

      let hasAccess = await checkUserFeatureAccess(
         user._id,
         "bank_connections"
      );

      // If user doesn't have subscription access, check if this is a reauthorization
      if (!hasAccess && accountId) {
         // Find the account to check if it has an existing Plaid connection
         const account = user.accounts.find(
            (acc) => String(acc._id) === String(accountId)
         );

         if (account && account.plaidItemId) {
            // This is a reauthorization of an existing connection - allow it
            hasAccess = true;
            console.log(
               "Allowing reauthorization for existing Plaid connection:",
               {
                  accountId,
                  accountName: account.name,
                  plaidItemId: account.plaidItemId,
                  userEmail: user.email,
               }
            );
         }
      }

      if (!hasAccess) {
         return NextResponse.json(
            {
               error: "Subscription required",
               message:
                  "Bank connections require a Basic or Pro subscription plan.",
               feature: "bank_connections",
               requiresUpgrade: true,
            },
            { status: 403 }
         );
      }

      console.log("Create link token request:", {
         userId,
         accountId,
         accountIdType: typeof accountId,
         syncStartDate,
         onboardingComplete: user.onboardingComplete,
         existingAccountsCount: user.accounts?.length || 0,
         timestamp: new Date().toISOString(),
      });

      // If accountId is provided, verify it exists
      if (accountId) {
         const accountExists = user.accounts.some(
            (account) => String(account._id) === String(accountId)
         );

         console.log("Account verification for link token:", {
            accountId,
            accountExists,
            userAccountIds: user.accounts.map((acc) => ({
               id: acc._id,
               stringId: String(acc._id),
               name: acc.name,
            })),
         });

         if (!accountExists) {
            console.error("Account not found during link token creation:", {
               accountId,
               availableAccountIds: user.accounts.map((acc) => String(acc._id)),
            });
            return NextResponse.json(
               { error: "Account not found" },
               { status: 404 }
            );
         }
      }

      // Check if webhook URL is configured
      if (!process.env.PLAID_WEBHOOK_URL) {
         console.warn(
            "PLAID_WEBHOOK_URL is not configured in environment variables. Webhooks won't work correctly."
         );
      }

      // Calculate days from start of current month to today
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const daysFromMonthStart =
         Math.ceil((now - startOfMonth) / (1000 * 60 * 60 * 24)) + 1;

      // Log the calculated days for debugging
      console.log("Link token creation:", {
         userId: user._id.toString(),
         currentDate: now.toISOString().split("T")[0],
         startOfMonth: startOfMonth.toISOString().split("T")[0],
         daysFromMonthStart,
         daysRequested: Math.max(daysFromMonthStart, 30),
         note: "Transaction sync will use plaidItem.startDate for actual filtering",
      });

      const plaidRequest = {
         user: {
            client_user_id: user._id.toString(),
         },
         client_name: "Hand Made Budget",
         products: ["transactions"],
         country_codes: ["US"],
         language: "en",
         webhook: process.env.PLAID_WEBHOOK_URL,
         transactions: {
            days_requested: Math.max(daysFromMonthStart, 30), // Request from start of current month, minimum 30 days
            // Note: Actual filtering is done by plaidItem.startDate in syncTransactions
         },
      };

      const createTokenResponse = await plaidClient.linkTokenCreate(
         plaidRequest
      );

      console.log("Link token created successfully:", {
         userId: user._id.toString(),
         accountId,
         linkTokenExpiration: createTokenResponse.data.expiration,
         timestamp: new Date().toISOString(),
      });

      return NextResponse.json({
         linkToken: createTokenResponse.data.link_token,
         expiration: createTokenResponse.data.expiration,
         // Include account ID and syncStartDate in response for frontend to pass to exchange-token
         accountId: accountId || null,
         syncStartDate: syncStartDate || null,
      });
   } catch (error) {
      console.error("Error creating Plaid link token:", error);
      return NextResponse.json(
         { error: "Failed to create link token" },
         { status: 500 }
      );
   }
}

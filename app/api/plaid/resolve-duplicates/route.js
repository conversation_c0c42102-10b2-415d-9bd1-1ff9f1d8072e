import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Transaction from "../../../lib/mongodb/models/Transaction";
import mongoose from "mongoose";
import User from "../../../lib/mongodb/models/User";

// Helper function to log resolution actions
function logResolution(userId, resolution) {
   console.log(`Resolution for user ${userId}:`, {
      plaidTransactionId: resolution.plaidTransaction.plaidTransactionId,
      action: resolution.action,
      selectedTransactionId: resolution.selectedTransactionId || "none",
      pendingToPosted: resolution.pendingToPosted || false,
   });
}

export async function POST(request) {
   let mongoSession;
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();
      mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      const { resolutions } = await request.json();
      const results = { skipped: 0, merged: 0, added: 0, pendingToPosted: 0 };

      for (const [plaidTransactionId, resolution] of Object.entries(
         resolutions
      )) {
         const { action, selectedTransactionId, plaidTransaction } = resolution;

         // Log the resolution
         logResolution(user._id, resolution);

         switch (action) {
            case "skip":
               results.skipped++;
               break;

            case "merge":
               const existingTransaction = await Transaction.findById(
                  selectedTransactionId
               ).session(mongoSession);

               if (!existingTransaction) {
                  console.error(
                     `Transaction with ID ${selectedTransactionId} not found`
                  );
                  continue;
               }

               // Create update object with base fields
               const updateFields = {
                  plaidTransactionId,
                  plaidAccountId: plaidTransaction.plaidAccountId,
                  plaidData: plaidTransaction.plaidData,
                  isPlaidTransaction: true,
                  payee: plaidTransaction.payee,
                  status: plaidTransaction.status,
               };

               // Check if this is a pending-to-posted transition
               if (
                  plaidTransaction.pendingPlaidTransactionId &&
                  existingTransaction.plaidTransactionId ===
                     plaidTransaction.pendingPlaidTransactionId
               ) {
                  // This is a pending transaction being updated to a posted transaction
                  updateFields.pendingPlaidTransactionId =
                     plaidTransaction.pendingPlaidTransactionId;
                  results.pendingToPosted++;

                  console.log(
                     `Pending transaction ${existingTransaction.plaidTransactionId} updated to posted transaction ${plaidTransactionId}`
                  );
               }

               // ALWAYS preserve assignedTo information from existing transaction
               // regardless of whether it's null or defined
               updateFields.assignedTo = existingTransaction.assignedTo;
               updateFields.assignedToType = existingTransaction.assignedToType;

               await Transaction.findByIdAndUpdate(
                  selectedTransactionId,
                  { $set: updateFields },
                  { session: mongoSession }
               );
               results.merged++;
               break;

            case "add":
               // Create new transaction with all required fields
               await Transaction.create(
                  [
                     {
                        userId: user._id,
                        payee: plaidTransaction.payee,
                        amount: plaidTransaction.amount,
                        type: plaidTransaction.type,
                        date: plaidTransaction.date,
                        accountId: plaidTransaction.accountId,
                        status: plaidTransaction.status,
                        plaidTransactionId: plaidTransaction.plaidTransactionId,
                        plaidAccountId: plaidTransaction.plaidAccountId,
                        plaidData: plaidTransaction.plaidData,
                        isPlaidTransaction: true,
                        pendingPlaidTransactionId:
                           plaidTransaction.pendingPlaidTransactionId || null,
                     },
                  ],
                  { session: mongoSession }
               );

               // Update account balance if accountId exists
               if (plaidTransaction.accountId) {
                  await User.updateOne(
                     {
                        _id: user._id,
                        "accounts._id": plaidTransaction.accountId,
                     },
                     {
                        $inc: { "accounts.$.balance": plaidTransaction.amount },
                     },
                     { session: mongoSession }
                  );
               }
               results.added++;
               break;
         }
      }

      await mongoSession.commitTransaction();

      return NextResponse.json({
         success: true,
         results,
         message: `Processed ${Object.keys(resolutions).length} transactions: ${
            results.skipped
         } skipped, ${results.merged} merged (including ${
            results.pendingToPosted
         } pending-to-posted updates), ${results.added} added.`,
      });
   } catch (error) {
      if (mongoSession) {
         await mongoSession.abortTransaction();
      }
      console.error("Error resolving duplicates:", error);
      return NextResponse.json(
         { error: "Failed to resolve duplicates", details: error.message },
         { status: 500 }
      );
   } finally {
      if (mongoSession) {
         await mongoSession.endSession();
      }
   }
}

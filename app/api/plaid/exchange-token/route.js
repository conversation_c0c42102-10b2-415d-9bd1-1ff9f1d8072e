import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import { plaidClient } from "../../../lib/plaid/config";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      // Connect to database and fetch user first
      await dbConnect();
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Check if user has required subscription for bank connections
      const { checkUserFeatureAccess } = await import(
         "@/app/lib/stripe/subscriptionUtils"
      );
      const hasAccess = await checkUserFeatureAccess(
         user._id,
         "bank_connections"
      );

      if (!hasAccess) {
         return NextResponse.json(
            {
               error: "Subscription required",
               message:
                  "Bank connections require a Basic or Pro subscription plan.",
               feature: "bank_connections",
            },
            { status: 403 }
         );
      }

      const {
         public_token,
         institution,
         syncStartDate = "creation",
         accountId = null, // Optional account ID for automatic linking
      } = await request.json();

      console.log("Exchange token request received:", {
         userId,
         institution: institution?.name,
         accountId,
         syncStartDate,
         timestamp: new Date().toISOString(),
      });

      // Exchange public token for access token
      const response = await plaidClient.itemPublicTokenExchange({
         public_token,
      });

      const accessToken = response.data.access_token;
      const itemId = response.data.item_id;

      // Get account details
      const accountsResponse = await plaidClient.accountsGet({
         access_token: accessToken,
      });

      // Check if this bank is already connected
      const existingItem = await User.findOne({
         "plaidItems.itemId": itemId,
      });

      if (existingItem) {
         return NextResponse.json(
            { error: "This bank account is already connected" },
            { status: 400 }
         );
      }

      // User already found above

      console.log("User found:", {
         userId: user._id.toString(),
         onboardingComplete: user.onboardingComplete,
         existingAccountsCount: user.accounts?.length || 0,
         existingPlaidItemsCount: user.plaidItems?.length || 0,
      });

      // Calculate start date based on preference and onboarding status
      let startDate;

      if (syncStartDate && syncStartDate !== "creation") {
         // Use the user-selected sync start date
         if (syncStartDate === "today") {
            startDate = new Date();
         } else if (syncStartDate === "current_month") {
            const now = new Date();
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
         } else if (syncStartDate === "1_month") {
            startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
         } else if (syncStartDate === "3_months") {
            startDate = new Date();
            startDate.setDate(startDate.getDate() - 90);
         } else if (syncStartDate === "6_months") {
            startDate = new Date();
            startDate.setDate(startDate.getDate() - 180);
         } else {
            // Assume it's a custom date in YYYY-MM-DD format
            // Parse as local timezone to avoid date shifts
            const [year, month, day] = syncStartDate.split("-").map(Number);
            startDate = new Date(year, month - 1, day);
         }

         console.log("Using user-selected sync start date:", {
            userId: user._id.toString(),
            startDate: startDate.toISOString(),
            reason: "user_selected_date",
            originalSyncStartDate: syncStartDate,
         });
      } else if (!user.onboardingComplete) {
         // During onboarding, use current connection time if no date selected
         startDate = new Date();
         console.log("Setting startDate to connection time for onboarding:", {
            userId: user._id.toString(),
            startDate: startDate.toISOString(),
            reason: "onboarding_mode_default",
         });
      } else {
         // For settings page connections without date selection, use current connection time
         startDate = new Date();
         console.log(
            "Setting startDate to connection time for settings page:",
            {
               userId: user._id.toString(),
               startDate: startDate.toISOString(),
               reason: "settings_page_default",
            }
         );
      }

      // Create Plaid items with all required fields
      const plaidItems = accountsResponse.data.accounts.map((account) => ({
         itemId: itemId,
         accessToken,
         institutionId: institution.institution_id,
         institutionName: institution.name,
         status: "good",
         accountType: account.type || "other",
         accountSubtype: account.subtype || "other",
         plaidAccountId: account.account_id,
         name: account.name || institution.name,
         linkedAccountId: null,
         startDate: startDate,
      }));

      console.log("Created plaidItems:", {
         itemId,
         plaidItemsCount: plaidItems.length,
         plaidItems: plaidItems.map((item) => ({
            plaidAccountId: item.plaidAccountId,
            name: item.name,
            accountType: item.accountType,
            linkedAccountId: item.linkedAccountId,
         })),
      });

      // Check if user is in onboarding mode (accounts need to be created)
      const isOnboarding = !user.onboardingComplete;
      let createdAccounts = [];

      if (isOnboarding) {
         // During onboarding, create temporary accounts for each Plaid account
         // Get the next available account ID
         let nextAccountId = 1;
         if (user.accounts && user.accounts.length > 0) {
            nextAccountId =
               Math.max(...user.accounts.map((acc) => acc._id)) + 1;
         }

         // Create temporary accounts for each Plaid account
         createdAccounts = accountsResponse.data.accounts.map(
            (plaidAccount, index) => ({
               _id: nextAccountId + index,
               name: plaidAccount.name || `${institution.name} Account`,
               bank: institution.name,
               accountType: mapPlaidAccountType(
                  plaidAccount.type,
                  plaidAccount.subtype
               ),
               balance: plaidAccount.balances.current || 0,
               active: true,
               plaidItemId: itemId,
               // Set default values for credit/loan fields
               dueDate: "",
               minimumPayment: 0,
               interestRate: 0,
            })
         );

         // Update plaidItems to link to the created accounts
         plaidItems.forEach((plaidItem, index) => {
            plaidItem.linkedAccountId = createdAccounts[index]._id;
         });

         // Add both the plaidItems and the temporary accounts to the user
         await User.findByIdAndUpdate(
            user._id,
            {
               $push: {
                  plaidItems: {
                     $each: plaidItems,
                  },
                  accounts: {
                     $each: createdAccounts,
                  },
               },
            },
            { new: true }
         );

         // Verify the plaidItems and accounts were actually added (helps prevent race conditions with webhooks)
         const verificationUser = await User.findById(user._id);
         const addedPlaidItem = verificationUser.plaidItems.find(
            (item) => item.itemId === itemId
         );

         if (!addedPlaidItem) {
            console.error(
               "Failed to verify plaidItem was added during onboarding:",
               {
                  userId: user._id.toString(),
                  itemId,
                  timestamp: new Date().toISOString(),
               }
            );
            throw new Error("Onboarding database write verification failed");
         }

         console.log(
            "Verified plaidItem and accounts were successfully added during onboarding:",
            {
               userId: user._id.toString(),
               itemId,
               accountCount: createdAccounts.length,
               timestamp: new Date().toISOString(),
            }
         );

         console.log("Created temporary accounts during onboarding:", {
            userId: user._id.toString(),
            itemId,
            institutionName: institution.name,
            accountCount: createdAccounts.length,
            createdAccounts: createdAccounts.map((acc) => ({
               id: acc._id,
               name: acc.name,
               type: acc.accountType,
               balance: acc.balance,
            })),
            primaryBalance:
               createdAccounts.length > 0 ? createdAccounts[0].balance : 0,
            balanceBreakdown: accountsResponse.data.accounts.map((acc) => ({
               plaidAccountId: acc.account_id,
               name: acc.name,
               availableBalance: acc.balances.available,
               currentBalance: acc.balances.current,
               usedBalance: acc.balances.current || 0,
            })),
         });

         return NextResponse.json({
            success: true,
            institutionName: institution.name,
            accountCount: plaidItems.length,
            plaidItems: plaidItems,
            autoLinked: true,
            onboardingMode: true,
            createdAccounts: createdAccounts,
            // Add balance information for easy access by frontend
            balances: createdAccounts.map((acc) => ({
               accountId: acc._id,
               name: acc.name,
               balance: acc.balance,
               accountType: acc.accountType,
            })),
            // For single account connections, provide the first account's balance
            primaryBalance:
               createdAccounts.length > 0 ? createdAccounts[0].balance : 0,
         });
      } else {
         console.log(
            "Settings page flow - adding plaidItems to existing user:",
            {
               userId: user._id.toString(),
               itemId,
               accountId,
               accountIdType: typeof accountId,
            }
         );

         // Regular flow for settings page - just add plaidItems
         await User.findByIdAndUpdate(
            user._id,
            {
               $push: {
                  plaidItems: {
                     $each: plaidItems,
                  },
               },
            },
            { new: true }
         );

         console.log("Added plaidItems to user successfully");

         // Verify the plaidItems were actually added (helps prevent race conditions with webhooks)
         const verificationUser = await User.findById(user._id);
         const addedPlaidItem = verificationUser.plaidItems.find(
            (item) => item.itemId === itemId
         );

         if (!addedPlaidItem) {
            console.error("Failed to verify plaidItem was added to user:", {
               userId: user._id.toString(),
               itemId,
               timestamp: new Date().toISOString(),
            });
            throw new Error("Database write verification failed");
         }

         console.log("Verified plaidItem was successfully added to user:", {
            userId: user._id.toString(),
            itemId,
            plaidItemId: addedPlaidItem._id,
            timestamp: new Date().toISOString(),
         });

         // If accountId is provided, automatically link the first Plaid item to the account
         if (accountId && plaidItems.length > 0) {
            const firstPlaidItem = plaidItems[0];

            console.log("Attempting to link account to Plaid item:", {
               accountId,
               accountIdType: typeof accountId,
               itemId: firstPlaidItem.itemId,
               institutionName: institution.name,
            });

            // Verify the account exists
            const accountExists = user.accounts.some(
               (account) => String(account._id) === String(accountId)
            );

            console.log("Account existence check:", {
               accountExists,
               accountId,
               userAccountIds: user.accounts.map((acc) => ({
                  id: acc._id,
                  stringId: String(acc._id),
                  name: acc.name,
               })),
            });

            if (accountExists) {
               console.log("Account exists, proceeding with linking...");

               // Clear any existing connections for this account and plaid item
               const clearResult = await User.updateOne(
                  { _id: user._id },
                  {
                     $set: {
                        // Clear any existing plaidItemId from accounts using this item
                        "accounts.$[account].plaidItemId": null,
                        // Clear any existing linkedAccountId from plaid items
                        "plaidItems.$[plaidItem].linkedAccountId": null,
                     },
                  },
                  {
                     arrayFilters: [
                        { "account.plaidItemId": firstPlaidItem.itemId },
                        { "plaidItem.itemId": firstPlaidItem.itemId },
                     ],
                  }
               );

               console.log("Cleared existing connections:", {
                  modifiedCount: clearResult.modifiedCount,
                  matchedCount: clearResult.matchedCount,
               });

               // Now link the account to the plaid item
               const linkResult = await User.findOneAndUpdate(
                  {
                     _id: user._id,
                     "plaidItems.itemId": firstPlaidItem.itemId,
                  },
                  {
                     $set: {
                        "plaidItems.$.linkedAccountId": accountId,
                        "accounts.$[account].plaidItemId":
                           firstPlaidItem.itemId,
                     },
                  },
                  {
                     new: true,
                     runValidators: true,
                     arrayFilters: [{ "account._id": accountId }],
                  }
               );

               console.log("Link operation result:", {
                  linkResultExists: !!linkResult,
                  updatedPlaidItem: linkResult?.plaidItems.find(
                     (item) => item.itemId === firstPlaidItem.itemId
                  ),
                  updatedAccount: linkResult?.accounts.find(
                     (account) => String(account._id) === String(accountId)
                  ),
               });

               if (linkResult) {
                  console.log(
                     "Successfully auto-linked account to Plaid item:",
                     {
                        accountId,
                        itemId: firstPlaidItem.itemId,
                        institutionName: institution.name,
                     }
                  );

                  // Verify the linking was successful by checking both directions
                  const finalVerificationUser = await User.findById(user._id);
                  const linkedPlaidItem = finalVerificationUser.plaidItems.find(
                     (item) => item.itemId === firstPlaidItem.itemId
                  );
                  const linkedAccount = finalVerificationUser.accounts.find(
                     (account) => String(account._id) === String(accountId)
                  );

                  if (
                     linkedPlaidItem?.linkedAccountId === accountId &&
                     linkedAccount?.plaidItemId === firstPlaidItem.itemId
                  ) {
                     console.log(
                        "Verified bidirectional account-plaid linking is complete:",
                        {
                           accountId,
                           itemId: firstPlaidItem.itemId,
                           plaidItemLinkedAccountId:
                              linkedPlaidItem.linkedAccountId,
                           accountPlaidItemId: linkedAccount.plaidItemId,
                           timestamp: new Date().toISOString(),
                        }
                     );
                  } else {
                     console.error("Account linking verification failed:", {
                        accountId,
                        itemId: firstPlaidItem.itemId,
                        plaidItemLinkedAccountId:
                           linkedPlaidItem?.linkedAccountId,
                        accountPlaidItemId: linkedAccount?.plaidItemId,
                        expectedBidirectionalLink: true,
                        timestamp: new Date().toISOString(),
                     });
                  }
               } else {
                  console.error("Failed to link account to Plaid item:", {
                     accountId,
                     itemId: firstPlaidItem.itemId,
                     userId: user._id.toString(),
                  });
               }
            } else {
               console.error("Account not found for linking:", {
                  accountId,
                  accountIdType: typeof accountId,
                  availableAccountIds: user.accounts.map((acc) =>
                     String(acc._id)
                  ),
               });
            }
         } else {
            console.log(
               "No account ID provided for linking, skipping auto-link"
            );
         }

         return NextResponse.json({
            success: true,
            institutionName: institution.name,
            accountCount: plaidItems.length,
            plaidItems: plaidItems,
            autoLinked: !!accountId, // Indicate if auto-linking was attempted
            onboardingMode: false,
         });
      }
   } catch (error) {
      console.error("Error in exchange-token:", error);
      return NextResponse.json(
         { error: "Failed to exchange token" },
         { status: 500 }
      );
   }
}

// Helper function to map Plaid account types to our internal account types
function mapPlaidAccountType(plaidType, plaidSubtype) {
   switch (plaidType) {
      case "depository":
         if (plaidSubtype === "checking") return "checking";
         if (plaidSubtype === "savings") return "savings";
         return "checking"; // default for depository
      case "credit":
         return "credit";
      case "loan":
         return "loan";
      default:
         return "checking"; // default fallback
   }
}

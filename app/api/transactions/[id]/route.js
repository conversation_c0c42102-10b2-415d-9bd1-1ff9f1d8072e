import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import mongoose from "mongoose";
import Transaction from "../../../lib/mongodb/models/Transaction";
import Expense from "../../../lib/mongodb/models/Expense";
import Income from "../../../lib/mongodb/models/Income";
import User from "../../../lib/mongodb/models/User";
import Debt from "../../../lib/mongodb/models/Debt";
import { ObjectId } from "mongodb";
import { determineExpenseStatus } from "../../../lib/utils/expenseUtils";

// Pre-save middleware to ensure all monetary values are rounded to 2 decimal places
const roundToTwo = (num) => Number(Number(num).toFixed(2));

// Helper function to update income amounts and status
const updateIncomeAmounts = async (incomeId, amount, mongoSession) => {
   const income = await Income.findById(incomeId).session(mongoSession);
   if (!income) {
      throw new Error("Income not found");
   }

   // Calculate new amounts with proper rounding
   const newReceivedAmount = roundToTwo((income.receivedAmount || 0) + amount);

   // Update income with new amounts
   const updatedIncome = await Income.findByIdAndUpdate(
      incomeId,
      {
         receivedAmount: newReceivedAmount,
      },
      { session: mongoSession, new: true }
   );

   // Update status based on received vs expected amount
   const newStatus =
      roundToTwo(newReceivedAmount) >= roundToTwo(income.expectedAmount)
         ? "received"
         : "scheduled";

   if (updatedIncome.status !== newStatus) {
      await Income.findByIdAndUpdate(
         incomeId,
         { status: newStatus },
         { session: mongoSession }
      );
   }

   return updatedIncome;
};

// Helper function to update debt history when a transaction is assigned to a debt payment expense
const updateDebtHistory = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   const expense = await Expense.findById(expenseId).session(mongoSession);

   // Check if this expense is a debt payment and has a valid debtId
   if (!expense || !expense.isDebtPayment || !expense.debtId) {
      return null;
   }

   try {
      // Find the debt
      const debt = await Debt.findById(expense.debtId).session(mongoSession);
      if (!debt) {
         console.error(`Debt not found with ID: ${expense.debtId}`);
         return null;
      }

      // Calculate the new balance after this payment (payment amount is typically negative)
      const paymentAmount = Math.abs(amount); // Convert to positive for calculation
      const oldBalance = debt.balance;

      // If removing a transaction from a debt payment category, we should add the amount back
      // If adding a transaction to a debt payment category, we should subtract the amount
      const newBalance = isRemoval
         ? roundToTwo(oldBalance + paymentAmount)
         : roundToTwo(oldBalance - paymentAmount);

      // Create a history entry
      const historyEntry = {
         date: new Date(),
         oldBalance: oldBalance,
         newBalance: newBalance,
         oldMinimumPayment: debt.minimumPayment,
         newMinimumPayment: debt.minimumPayment,
         oldAPR: debt.apr,
         newAPR: debt.apr,
         oldDueDate: debt.dueDate,
         newDueDate: debt.dueDate,
         note: isRemoval
            ? `Payment of $${paymentAmount.toFixed(2)} removed`
            : `Payment of $${paymentAmount.toFixed(2)} applied`,
      };

      // Update the debt document with the new balance and history entry
      const updatedDebt = await Debt.findByIdAndUpdate(
         expense.debtId,
         {
            $set: { balance: newBalance },
            $push: { history: historyEntry },
         },
         { session: mongoSession, new: true }
      );

      return updatedDebt;
   } catch (error) {
      console.error("Error updating debt history:", error);
      return null;
   }
};

// Helper function to update expense amounts and status
const updateExpenseAmounts = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   const expense = await Expense.findById(expenseId).session(mongoSession);
   if (!expense) {
      console.warn(
         `Expense with ID ${expenseId} not found - skipping expense update`
      );
      return null; // Gracefully handle missing expenses instead of throwing error
   }

   // Calculate new amounts with proper rounding
   // For expenses, we want to keep the negative sign
   const newAmountSpent = roundToTwo((expense.amountSpent || 0) + amount);

   // Update expense with new amounts
   const updatedExpense = await Expense.findByIdAndUpdate(
      expenseId,
      {
         amountSpent: newAmountSpent,
      },
      { session: mongoSession, new: true }
   );

   // Update status using the comprehensive determination function
   const newStatus = determineExpenseStatus(
      newAmountSpent,
      expense.amountAssigned,
      expense.amountDue,
      expense.date,
      expense.startDate,
      expense.endDate,
      expense.status
   );

   if (updatedExpense.status !== newStatus) {
      await Expense.findByIdAndUpdate(
         expenseId,
         { status: newStatus },
         { session: mongoSession }
      );
   }

   // If this is a debt payment expense, update the debt history
   if (expense.isDebtPayment && expense.debtId) {
      await updateDebtHistory(expenseId, amount, mongoSession, isRemoval);
   }

   return updatedExpense;
};

const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 100;

// Helper function for delay
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to handle the transaction with retries
const executeWithRetry = async (operation) => {
   let lastError;

   for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      const session = await mongoose.startSession();
      try {
         session.startTransaction();
         const result = await operation(session);
         await session.commitTransaction();
         return result;
      } catch (error) {
         await session.abortTransaction();
         lastError = error;

         // Only retry on write conflicts
         if (error.code === 112 && attempt < MAX_RETRIES) {
            await delay(RETRY_DELAY_MS * attempt);
            continue;
         }
         throw error;
      } finally {
         await session.endSession();
      }
   }
   throw lastError;
};

export async function PATCH(request, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { id } = await params;
      const data = await request.json();

      await dbConnect();

      const result = await executeWithRetry(async (mongoSession) => {
         // Get user from MongoDB
         const user = await User.findById(userId).session(mongoSession);
         if (!user) {
            throw new Error("User not found");
         }

         // Get the original transaction
         const originalTransaction = await Transaction.findOne({
            _id: id,
            userId: user._id,
         }).session(mongoSession);

         if (!originalTransaction) {
            throw new Error("Transaction not found");
         }

         // Handle assignedTo changes
         if (
            data.assignedTo !== undefined ||
            data.assignedToType !== undefined
         ) {
            // Remove amounts from old assignedTo if it exists
            if (
               originalTransaction.assignedTo &&
               originalTransaction.assignedToType
            ) {
               if (originalTransaction.assignedToType === "Income") {
                  // Remove amount from old Income assignedTo
                  await updateIncomeAmounts(
                     originalTransaction.assignedTo,
                     -Math.abs(originalTransaction.amount), // Remove the amount from received
                     mongoSession
                  );
               } else if (originalTransaction.assignedToType === "Expense") {
                  // Check if we're removing from a debt payment expense
                  const originalExpense = await Expense.findById(
                     originalTransaction.assignedTo
                  ).session(mongoSession);
                  const isDebtPayment =
                     originalExpense &&
                     originalExpense.isDebtPayment &&
                     originalExpense.debtId;

                  // Remove amount from old Expense assignedTo
                  const updateResult = await updateExpenseAmounts(
                     originalTransaction.assignedTo,
                     -originalTransaction.amount, // Reverse the original transaction amount
                     mongoSession,
                     isDebtPayment // Pass true if this is removing from a debt payment expense
                  );
                  // updateResult will be null if expense doesn't exist - that's ok, we'll continue
               }
            }

            // Add amounts to new assignedTo if provided
            if (data.assignedTo && data.assignedToType) {
               if (data.assignedToType === "Income") {
                  // Add amount to new Income assignedTo
                  await updateIncomeAmounts(
                     data.assignedTo,
                     Math.abs(originalTransaction.amount),
                     mongoSession
                  );
               } else if (data.assignedToType === "Expense") {
                  // Add amount to new Expense assignedTo
                  const updateResult = await updateExpenseAmounts(
                     data.assignedTo,
                     originalTransaction.amount,
                     mongoSession,
                     false // Not a removal but adding to a new category
                  );
                  // If expense doesn't exist, this will fail the transaction appropriately
                  if (!updateResult) {
                     throw new Error(
                        "Cannot assign transaction to non-existent expense category"
                     );
                  }
               }
            }
         }

         // Update the transaction
         const updatedTransaction = await Transaction.findByIdAndUpdate(
            id,
            data,
            { new: true, runValidators: true, session: mongoSession }
         ).session(mongoSession);

         // If amount changed, update account balance
         if (
            data.amount !== undefined &&
            data.amount !== originalTransaction.amount
         ) {
            const user = await User.findById(userId).session(mongoSession);
            if (!user) {
               throw new Error("User not found");
            }

            const accountIndex = user.accounts.findIndex(
               (a) => a._id === originalTransaction.accountId
            );
            if (accountIndex !== -1) {
               // Remove the old amount and add the new amount
               user.accounts[accountIndex].balance = roundToTwo(
                  user.accounts[accountIndex].balance -
                     originalTransaction.amount +
                     data.amount
               );
               await user.save({ session: mongoSession });
            }
         }

         return { transaction: updatedTransaction };
      });

      return NextResponse.json(result);
   } catch (error) {
      console.error("Error in PATCH /api/transactions/[id]:", error);
      return NextResponse.json(
         { error: error.message || "Failed to update transaction" },
         { status: 500 }
      );
   }
}

export async function DELETE(request, { params }) {
   try {
      const { id: transactionId } = await params;
      const transaction = await Transaction.findById(transactionId);

      if (!transaction) {
         return NextResponse.json(
            { success: true, message: "Transaction already deleted" },
            { status: 200 }
         );
      }

      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const result = await executeWithRetry(async (mongoSession) => {
         // Find the transaction
         const transaction = await Transaction.findOne({
            _id: transactionId,
            userId: user._id,
         }).session(mongoSession);

         if (!transaction) {
            throw new Error("Transaction not found");
         }

         // Delete the transaction
         await Transaction.deleteOne({
            _id: transactionId,
            userId: user._id,
         }).session(mongoSession);

         // Handle account updates
         const userForUpdate = await User.findById(userId).session(
            mongoSession
         );
         if (!userForUpdate) {
            throw new Error("User not found");
         }

         // Update account balance
         if (transaction.accountId) {
            const accountIndex = userForUpdate.accounts.findIndex(
               (a) => a._id === transaction.accountId
            );
            if (accountIndex !== -1) {
               userForUpdate.accounts[accountIndex].balance = roundToTwo(
                  userForUpdate.accounts[accountIndex].balance -
                     transaction.amount
               );
            }
         }

         // Update assignedTo if it exists
         if (transaction.assignedTo && transaction.assignedToType) {
            if (transaction.assignedToType === "Income") {
               // For income, decrease the receivedAmount
               await updateIncomeAmounts(
                  transaction.assignedTo,
                  -Math.abs(transaction.amount), // Remove the amount from received
                  mongoSession
               );
            } else if (transaction.assignedToType === "Expense") {
               // Check if we're removing from a debt payment expense
               const originalExpense = await Expense.findById(
                  transaction.assignedTo
               ).session(mongoSession);
               const isDebtPayment =
                  originalExpense &&
                  originalExpense.isDebtPayment &&
                  originalExpense.debtId;

               // Use updateExpenseAmounts with negative amount to reverse the transaction
               const updateResult = await updateExpenseAmounts(
                  transaction.assignedTo,
                  -transaction.amount, // Reverse the original transaction amount
                  mongoSession,
                  isDebtPayment // Pass true if this is removing from a debt payment expense
               );
               // updateResult will be null if expense doesn't exist - that's ok for deletion
            }
         }

         await userForUpdate.save({ session: mongoSession });
         return { message: "Transaction deleted successfully" };
      });

      return NextResponse.json(result);
   } catch (error) {
      console.error("Error deleting transaction:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
   }
}

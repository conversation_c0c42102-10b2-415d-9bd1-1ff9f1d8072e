import Transaction from "../../../lib/mongodb/models/Transaction";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import User from "../../../lib/mongodb/models/User";
import Expense from "../../../lib/mongodb/models/Expense";
import Income from "../../../lib/mongodb/models/Income";

export async function GET() {
   const { session: authSession, userId, error } = await getSessionForAPI();
   if (error) return error;

   try {
      // First get the user to access their accounts
      const user = await User.findById(userId).lean();
      if (!user) return new Response("User not found", { status: 404 });

      // Create a map of account IDs to names for quick lookup
      const accountMap = new Map();
      user.accounts.forEach((account) => {
         accountMap.set(account._id, account.name);
      });

      // Get all transactions
      const allTransactions = await Transaction.find({
         userId: user._id,
      }).lean();

      // Separate transactions by assignedTo type
      const expenseCategories = allTransactions.filter(
         (t) => t.assignedToType === "Expense"
      );
      const incomeCategories = allTransactions.filter(
         (t) => t.assignedToType === "Income"
      );
      const uncategorized = allTransactions.filter((t) => !t.assignedToType);

      // Get all unique category IDs
      const expenseCategoryIds = [
         ...new Set(expenseCategories.map((t) => t.category).filter(Boolean)),
      ];
      const incomeCategoryIds = [
         ...new Set(incomeCategories.map((t) => t.category).filter(Boolean)),
      ];

      // Fetch all categories in bulk
      const [expenses, incomes] = await Promise.all([
         Expense.find({ _id: { $in: expenseCategoryIds } })
            .select("description")
            .lean(),
         Income.find({ _id: { $in: incomeCategoryIds } })
            .select("description")
            .lean(),
      ]);

      // Create category maps for quick lookup
      const expenseMap = new Map(
         expenses.map((e) => [e._id.toString(), e.description])
      );
      const incomeMap = new Map(
         incomes.map((i) => [i._id.toString(), i.description])
      );

      // Combine and format all transactions
      const formatted = [
         ...expenseCategories,
         ...incomeCategories,
         ...uncategorized,
      ]
         .sort((a, b) => new Date(b.date) - new Date(a.date))
         .flatMap((t) => {
            if (t.type === "Transfer" && t.transferDetails) {
               // Create two entries for transfers
               return [
                  {
                     ...t,
                     amount: -Math.abs(t.amount),
                     category: "",
                     accountId:
                        accountMap.get(t.transferDetails.fromAccountId) ||
                        "Unassigned",
                     description: `Transfer to ${
                        accountMap.get(t.transferDetails.toAccountId) ||
                        "Unassigned"
                     }`,
                  },
                  {
                     ...t,
                     amount: Math.abs(t.amount),
                     category: "",
                     accountId:
                        accountMap.get(t.transferDetails.toAccountId) ||
                        "Unassigned",
                     description: `Transfer from ${
                        accountMap.get(t.transferDetails.fromAccountId) ||
                        "Unassigned"
                     }`,
                  },
               ];
            }

            // Get assignedTo description based on assignedTo type
            let assignedToDescription = "";
            if (t.assignedTo) {
               if (t.assignedToType === "Expense") {
                  assignedToDescription =
                     expenseMap.get(t.assignedTo.toString()) || "";
               } else if (t.assignedToType === "Income") {
                  assignedToDescription =
                     incomeMap.get(t.assignedTo.toString()) || "";
               }
            }

            // Regular transaction
            return {
               ...t,
               amount:
                  t.type === "Expense"
                     ? -Math.abs(t.amount)
                     : Math.abs(t.amount),
               category: categoryDescription,
               accountId: t.accountId
                  ? accountMap.get(t.accountId)
                  : "Unassigned",
            };
         });

      return new Response(
         JSON.stringify({
            transactions: formatted,
         }),
         {
            status: 200,
            headers: {
               "Content-Type": "application/json",
            },
         }
      );
   } catch (error) {
      console.error("Export error:", error);
      return new Response("Error exporting transactions", { status: 500 });
   }
}

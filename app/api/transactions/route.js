import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../lib/mongodb/dbConnect";
import mongoose from "mongoose";
import Transaction from "../../lib/mongodb/models/Transaction";
import Expense from "../../lib/mongodb/models/Expense";
import Income from "../../lib/mongodb/models/Income";
import User from "../../lib/mongodb/models/User";
import Debt from "../../lib/mongodb/models/Debt";
import { ObjectId } from "mongodb";

// Helper function to ensure all monetary values are rounded to 2 decimal places
const roundToTwo = (num) => Number(Number(num).toFixed(2));

// Helper function to determine expense status based on amounts
const determineExpenseStatus = (amountDue, amountAssigned, amountSpent) => {
   // Convert to absolute values and ensure proper number handling
   const due = Math.abs(Number(amountDue || 0));
   const assigned = Number(amountAssigned || 0);
   const spent = Math.abs(Number(amountSpent || 0));

   // If spent exceeds due, it's overpaid (regardless of due date)
   if (spent > due) {
      return "overpaid";
   }

   // If spent exactly matches due, it's paid
   if (spent === due) {
      return "paid";
   }

   // Check if expense is funded
   if (assigned >= due) {
      return "funded";
   }

   // Default to scheduled
   return "scheduled";
};

// Helper function to update debt history when a transaction is assigned to a debt payment expense
const updateDebtHistory = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   const expense = await Expense.findById(expenseId).session(mongoSession);

   // Check if this expense is a debt payment and has a valid debtId
   if (!expense || !expense.isDebtPayment || !expense.debtId) {
      return null;
   }

   try {
      // Find the debt
      const debt = await Debt.findById(expense.debtId).session(mongoSession);
      if (!debt) {
         console.error(`Debt not found with ID: ${expense.debtId}`);
         return null;
      }

      // Calculate the new balance after this payment (payment amount is typically negative)
      const paymentAmount = Math.abs(amount); // Convert to positive for calculation
      const oldBalance = debt.balance;

      // If removing a transaction from a debt payment assignedTo, we should add the amount back
      // If adding a transaction to a debt payment assignedTo, we should subtract the amount
      const newBalance = isRemoval
         ? roundToTwo(oldBalance + paymentAmount)
         : roundToTwo(oldBalance - paymentAmount);

      // Create a history entry
      const historyEntry = {
         date: new Date(),
         oldBalance: oldBalance,
         newBalance: newBalance,
         oldMinimumPayment: debt.minimumPayment,
         newMinimumPayment: debt.minimumPayment,
         oldAPR: debt.apr,
         newAPR: debt.apr,
         oldDueDate: debt.dueDate,
         newDueDate: debt.dueDate,
         note: isRemoval
            ? `Payment of $${paymentAmount.toFixed(2)} removed`
            : `Payment of $${paymentAmount.toFixed(2)} applied`,
      };

      // Update the debt document with the new balance and history entry
      const updatedDebt = await Debt.findByIdAndUpdate(
         expense.debtId,
         {
            $set: { balance: newBalance },
            $push: { history: historyEntry },
         },
         { session: mongoSession, new: true }
      );

      return updatedDebt;
   } catch (error) {
      console.error("Error updating debt history:", error);
      return null;
   }
};

// Helper function to update expense amounts and status
const updateExpenseAmounts = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   const expense = await Expense.findById(expenseId).session(mongoSession);
   if (!expense) {
      console.warn(
         `Expense with ID ${expenseId} not found - skipping expense update`
      );
      return null; // Gracefully handle missing expenses instead of throwing error
   }

   // Calculate new amounts with proper rounding
   const newAmountSpent = roundToTwo((expense.amountSpent || 0) + amount);

   // Update expense with new amounts
   const updatedExpense = await Expense.findByIdAndUpdate(
      expenseId,
      {
         amountSpent: newAmountSpent,
      },
      { session: mongoSession, new: true }
   );

   // Update status if needed
   const newStatus = determineExpenseStatus(
      newAmountSpent,
      expense.amountAssigned,
      expense.amountDue,
      expense.date,
      expense.startDate,
      expense.endDate,
      expense.status,
      expense.isFutureExpense
   );

   if (updatedExpense.status !== newStatus) {
      await Expense.findByIdAndUpdate(
         expenseId,
         { status: newStatus },
         { session: mongoSession }
      );
   }

   // If this is a debt payment expense, update the debt history
   if (expense.isDebtPayment && expense.debtId) {
      await updateDebtHistory(expenseId, amount, mongoSession, isRemoval);
   }

   return updatedExpense;
};

// Helper function to update expense amount spent
const updateExpenseAmountSpent = async (
   expenseId,
   amount,
   mongoSession,
   isRemoval = false
) => {
   // First get the current expense to ensure proper rounding
   const currentExpense = await Expense.findById(expenseId).session(
      mongoSession
   );
   if (!currentExpense) {
      console.warn(
         `Expense with ID ${expenseId} not found - skipping expense update`
      );
      return null; // Gracefully handle missing expenses instead of throwing error
   }

   console.log("updateExpenseAmountSpent - Current state:", {
      expenseId,
      currentAmountSpent: currentExpense.amountSpent,
      amountToAdd: amount,
   });

   // Calculate the new amount spent with proper rounding
   const currentAmountSpent = roundToTwo(currentExpense.amountSpent || 0);
   const amountToAdd = roundToTwo(amount);
   const newAmountSpent = roundToTwo(currentAmountSpent + amountToAdd);

   console.log("updateExpenseAmountSpent - New state:", {
      expenseId,
      currentAmountSpent,
      amountToAdd,
      newAmountSpent,
   });

   // Update the expense with the properly rounded amount
   const expense = await Expense.findByIdAndUpdate(
      expenseId,
      {
         $set: { amountSpent: newAmountSpent },
      },
      { session: mongoSession, new: true }
   );

   // Update status if needed
   const newStatus = determineExpenseStatus(
      newAmountSpent,
      currentExpense.amountAssigned,
      currentExpense.amountDue,
      currentExpense.date,
      currentExpense.startDate,
      currentExpense.endDate,
      currentExpense.status,
      currentExpense.isFutureExpense
   );

   if (expense.status !== newStatus) {
      await Expense.findByIdAndUpdate(
         expenseId,
         { status: newStatus },
         { session: mongoSession }
      );
   }

   // If this is a debt payment expense, update the debt history
   if (currentExpense.isDebtPayment && currentExpense.debtId) {
      await updateDebtHistory(expenseId, amountToAdd, mongoSession, isRemoval);
   }

   return expense;
};

// Helper function to update balances
async function updateUserBalances(userId) {
   const response = await fetch(
      `${process.env.NEXTAUTH_URL}/api/user/balance`,
      {
         method: "POST",
         headers: {
            "Content-Type": "application/json",
         },
         body: JSON.stringify({ userId }),
      }
   );

   if (!response.ok) {
      console.error("Failed to update balances:", await response.text());
   }
}

// GET all transactions for the authenticated user
export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Get query parameters
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get("page")) || 1;
      const limit = parseInt(searchParams.get("limit")) || 50;
      const search = searchParams.get("search") || "";
      const accountId = searchParams.get("accountId");

      // Get user's accounts for transfer descriptions
      const accounts = user.accounts || [];

      console.log("Account ID:", accountId);

      // Build the query
      const query = {
         userId: user._id,
      };

      // Add account filter if specified
      if (accountId && accountId !== "all") {
         const parsedAccountId = parseInt(accountId);
         // Only add the account filter if we have a valid number
         if (!isNaN(parsedAccountId)) {
            query.accountId = parsedAccountId;
         }
      }

      // Add search filter if specified
      if (search) {
         const searchRegex = new RegExp(search, "i");

         // First, find all categories (both expenses and incomes) that match the search
         const [matchingExpenses, matchingIncomes] = await Promise.all([
            Expense.find({ description: searchRegex }, "_id").lean(),
            Income.find({ description: searchRegex }, "_id").lean(),
         ]);

         const matchingAssignedToIds = [
            ...matchingExpenses.map((e) => e._id),
            ...matchingIncomes.map((i) => i._id),
         ];

         const searchQuery = [{ payee: searchRegex }, { status: searchRegex }];

         // Add amount search if the search term can be converted to a number
         const searchNumber = parseFloat(search.replace(/[^0-9.-]/g, ""));
         if (!isNaN(searchNumber)) {
            // Search for both positive and negative versions of the number
            searchQuery.push(
               { amount: searchNumber },
               { amount: -searchNumber }
            );
         }

         // Only add assignedTo search if we found matching assignedTo items
         if (matchingAssignedToIds.length > 0) {
            searchQuery.push({ assignedTo: { $in: matchingAssignedToIds } });
         }

         query.$or = searchQuery;
      }

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await Transaction.countDocuments(query);

      // Fetch transactions with initial sort (we'll sort again after assignedTo processing)
      const transactions = await Transaction.find(query)
         .sort({ date: -1, _id: -1 })
         .skip(skip)
         .limit(limit)
         .lean();

      // For each transaction, fetch its assignedTo details if it has one
      const transactionsWithCategories = await Promise.all(
         transactions.map(async (transaction) => {
            if (transaction.assignedTo && transaction.assignedToType) {
               try {
                  const Model =
                     transaction.assignedToType === "Income" ? Income : Expense;
                  const assignedTo = await Model.findById(
                     transaction.assignedTo
                  );
                  if (assignedTo) {
                     return {
                        ...transaction,
                        assignedTo: assignedTo.description,
                        assignedToDetails: {
                           frequency: assignedTo.frequency || "oneoff",
                           date: assignedTo.date,
                           startDate: assignedTo.startDate,
                           endDate: assignedTo.endDate,
                           weeklyChargeType: assignedTo.weeklyChargeType,
                        },
                        isAssigned: true,
                     };
                  } else {
                     // AssignedTo not found, return transaction with "Unassigned"
                     return {
                        ...transaction,
                        assignedTo: "Unassigned",
                        assignedToType: null,
                        isAssigned: false,
                     };
                  }
               } catch (error) {
                  console.error("Error fetching assignedTo:", error);
                  // On error, return transaction with "Unassigned"
                  return {
                     ...transaction,
                     assignedTo: "Unassigned",
                     assignedToType: null,
                     isAssigned: false,
                  };
               }
            }
            return {
               ...transaction,
               assignedTo: transaction.assignedTo || "Unassigned",
               isAssigned: false,
            };
         })
      );

      // Sort transactions with unassigned at the top, then by date
      const sortedTransactions = transactionsWithCategories.sort((a, b) => {
         // First sort by assignment status (unassigned first)
         if (a.isAssigned !== b.isAssigned) {
            return a.isAssigned ? 1 : -1; // false (unassigned) comes first
         }

         // Then sort by date (newest first)
         const dateA = new Date(a.date);
         const dateB = new Date(b.date);
         if (dateA !== dateB) {
            return dateB - dateA; // Newest first
         }

         // Finally sort by ID for consistency
         return b._id.toString().localeCompare(a._id.toString());
      });

      // Remove the isAssigned property before returning
      const finalTransactions = sortedTransactions.map(
         ({ isAssigned, ...transaction }) => transaction
      );

      // Return the response with pagination data
      return NextResponse.json({
         transactions: finalTransactions,
         pagination: {
            total: totalCount,
            totalPages: Math.ceil(totalCount / limit),
            page: page,
            limit: limit,
         },
      });
   } catch (error) {
      console.error("Error in GET /api/transactions:", error);
      return NextResponse.json(
         { error: "Failed to fetch transactions" },
         { status: 500 }
      );
   }
}

// POST create a new transaction
export async function POST(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const data = await request.json();

      // Convert assignedTo to ObjectId if it's a string and not null/undefined
      if (data.assignedTo && typeof data.assignedTo === "string") {
         try {
            data.assignedTo = new ObjectId(data.assignedTo);
         } catch (error) {
            console.error("Invalid assignedTo ID format:", error);
            return NextResponse.json(
               { error: "Invalid assignedTo ID format" },
               { status: 400 }
            );
         }
      }

      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Ensure amount is properly rounded
         const roundedAmount = roundToTwo(data.amount);
         console.log("Rounded amount:", roundedAmount);

         // Get user first for all transaction types
         const user = await User.findById(userId).session(mongoSession);
         if (!user) {
            throw new Error("User not found");
         }

         // Create the transaction(s)
         let createdTransactions;
         if (data.type === "Transfer") {
            // For transfers, create two transactions
            const fromAccount = user.accounts.find(
               (a) => a._id === data.transferDetails.fromAccountId
            );
            const toAccount = user.accounts.find(
               (a) => a._id === data.transferDetails.toAccountId
            );

            const baseTransactionData = {
               userId: user._id,
               type: "Transfer",
               date: data.date,
               status: data.status,
               isAdjustment: true,
               notes: data.notes,
            };

            // Create descriptive payee fields for both transactions
            const fromPayee = `Transfer to: ${
               toAccount?.name || "Unknown Account"
            }`;
            const toPayee = `Transfer from: ${
               fromAccount?.name || "Unknown Account"
            }`;

            createdTransactions = await Transaction.create(
               [
                  {
                     ...baseTransactionData,
                     accountId: data.transferDetails.fromAccountId,
                     amount: -Math.abs(roundedAmount),
                     payee: fromPayee,
                  },
                  {
                     ...baseTransactionData,
                     accountId: data.transferDetails.toAccountId,
                     amount: Math.abs(roundedAmount),
                     payee: toPayee,
                  },
               ],
               { session: mongoSession }
            );

            // Update source account (subtract the amount)
            const fromAccountIndex = user.accounts.findIndex(
               (a) => a._id === data.transferDetails.fromAccountId
            );
            if (fromAccountIndex !== -1) {
               user.accounts[fromAccountIndex].balance = roundToTwo(
                  user.accounts[fromAccountIndex].balance -
                     Math.abs(roundedAmount)
               );
            }

            // Update destination account (add the amount)
            const toAccountIndex = user.accounts.findIndex(
               (a) => a._id === data.transferDetails.toAccountId
            );
            if (toAccountIndex !== -1) {
               user.accounts[toAccountIndex].balance = roundToTwo(
                  user.accounts[toAccountIndex].balance +
                     Math.abs(roundedAmount)
               );
            }

            await user.save({ session: mongoSession });
         } else {
            // For non-transfer transactions, create a single transaction
            createdTransactions = await Transaction.create(
               [
                  {
                     ...data,
                     amount: roundedAmount,
                     userId: user._id,
                  },
               ],
               { session: mongoSession }
            );

            // Handle non-transfer account updates
            if (data.accountId) {
               const accountIndex = user.accounts.findIndex(
                  (a) => a._id === parseInt(data.accountId)
               );
               if (accountIndex !== -1) {
                  user.accounts[accountIndex].balance = roundToTwo(
                     user.accounts[accountIndex].balance + roundedAmount
                  );
                  await user.save({ session: mongoSession });
               }
            }

            // Update user's balance and totals based on transaction type
            if (data.type === "Expense") {
               await User.findByIdAndUpdate(
                  user._id,
                  {
                     $inc: {
                        currentBalance: roundedAmount,
                        totalSpent: roundedAmount,
                     },
                  },
                  { session: mongoSession }
               );
            } else if (data.type === "Income") {
               await User.findByIdAndUpdate(
                  user._id,
                  {
                     $inc: {
                        currentBalance: roundedAmount,
                        // Only increment totalIncome if the transaction is not assigned to an expense assignedTo
                        totalIncome:
                           !data.assignedTo || data.assignedToType === "Income"
                              ? roundedAmount
                              : 0,
                     },
                  },
                  { session: mongoSession }
               );
            }

            // Handle assignedTo updates based on assignedToType, not transaction type
            if (data.assignedTo) {
               if (data.assignedToType === "Income") {
                  const income = await Income.findByIdAndUpdate(
                     data.assignedTo,
                     {
                        $inc: {
                           receivedAmount: roundedAmount,
                        },
                     },
                     { session: mongoSession, new: true }
                  );

                  if (!income) {
                     throw new Error("Income assignedTo not found");
                  }

                  // Update status if received amount meets or exceeds expected amount
                  if (
                     roundToTwo(income.receivedAmount) >=
                     roundToTwo(income.expectedAmount)
                  ) {
                     await Income.findByIdAndUpdate(
                        data.assignedTo,
                        { status: "received" },
                        { session: mongoSession }
                     );
                  } else {
                     await Income.findByIdAndUpdate(
                        data.assignedTo,
                        { status: "scheduled" },
                        { session: mongoSession }
                     );
                  }
               } else if (data.assignedToType === "Expense") {
                  // Use the updateExpenseAmounts helper function
                  const updateResult = await updateExpenseAmounts(
                     data.assignedTo,
                     roundedAmount,
                     mongoSession
                  );
                  // For new transactions, we require the expense to exist
                  if (!updateResult) {
                     throw new Error(
                        "Cannot assign transaction to non-existent expense assignedTo"
                     );
                  }
               }
            }
         }

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json(createdTransactions[0], { status: 201 });
      } catch (error) {
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error in POST /api/transactions:", error);
      return NextResponse.json(
         { error: error.message || "Failed to create transaction" },
         { status: 500 }
      );
   }
}

// PUT update a transaction
export async function PUT(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const data = await request.json();
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Find the current transaction first
         const currentTransaction = await Transaction.findById(
            data._id
         ).session(mongoSession);

         if (!currentTransaction) {
            throw new Error("Transaction not found");
         }

         // Update the transaction
         const transaction = await Transaction.findByIdAndUpdate(
            data._id,
            data,
            { new: true, session: mongoSession }
         );

         // If this is an expense transaction and has a assignedTo, update the expense status
         if (transaction.type === "Expense" && transaction.assignedTo) {
            // Use the updateExpenseAmounts helper function
            const updateResult = await updateExpenseAmounts(
               transaction.assignedTo,
               roundToTwo(transaction.amount - currentTransaction.amount),
               mongoSession
            );
            // updateResult will be null if expense doesn't exist - that's ok for updates
         }

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json(transaction);
      } catch (error) {
         // If an error occurred, abort the transaction
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         // End the session
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error updating transaction:", error);
      return NextResponse.json(
         { error: "Error updating transaction" },
         { status: 500 }
      );
   }
}

// DELETE a transaction
export async function DELETE(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const data = await request.json();
      const { id } = data;

      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Find and delete the transaction
         const transaction = await Transaction.findOneAndDelete({
            _id: id,
            userId: user._id,
         }).session(mongoSession);

         if (!transaction) {
            throw new Error("Transaction not found");
         }

         // Update assignedTo if it exists
         if (transaction.assignedTo && transaction.assignedToType) {
            if (transaction.assignedToType === "Income") {
               // For income, decrease the receivedAmount
               await Income.findByIdAndUpdate(
                  transaction.assignedTo,
                  {
                     $inc: {
                        receivedAmount: -Math.abs(transaction.amount), // Remove the amount from received
                     },
                  },
                  { session: mongoSession }
               );
            } else if (transaction.assignedToType === "Expense") {
               // Use updateExpenseAmounts with negative amount to reverse the transaction
               const updateResult = await updateExpenseAmounts(
                  transaction.assignedTo,
                  -transaction.amount, // Reverse the original transaction amount
                  mongoSession
               );
               // updateResult will be null if expense doesn't exist - that's ok for deletion
            }
         }

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json({ success: true });
      } catch (error) {
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error deleting transaction:", error);
      return NextResponse.json(
         { error: "Error deleting transaction" },
         { status: 500 }
      );
   }
}

// PATCH update transactions when assignedTo is deleted
export async function PATCH(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }
      const data = await request.json();
      const mongoUserId = user._id;

      // Check if this is a bulk update for deleted assignedTo
      if (data.assignedToId && data.type) {
         // This is a bulk update to unassign all transactions from a deleted assignedTo
         const { assignedToId, type } = data;

         // Start a transaction
         const mongoSession = await mongoose.startSession();
         mongoSession.startTransaction();

         try {
            // Find all transactions assigned to this assignedTo
            const transactions = await Transaction.find({
               userId: mongoUserId,
               assignedTo: assignedToId,
               assignedToType: type,
            }).session(mongoSession);

            // Update all transactions to be unassigned
            await Transaction.updateMany(
               {
                  userId: mongoUserId,
                  assignedTo: assignedToId,
                  assignedToType: type,
               },
               {
                  $set: {
                     assignedTo: null,
                     assignedToType: null,
                  },
               },
               { session: mongoSession }
            );

            // Commit the transaction
            await mongoSession.commitTransaction();
            return NextResponse.json({
               success: true,
               message: `Unassigned ${
                  transactions.length
               } transactions from deleted ${type.toLowerCase()}`,
            });
         } catch (error) {
            await mongoSession.abortTransaction();
            throw error;
         } finally {
            mongoSession.endSession();
         }
      }

      // Original single transaction update logic
      const { transactionId, newAssignedToId, newAssignedToType } = data;

      // Start a transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Get the transaction
         const transaction = await Transaction.findOne({
            _id: transactionId,
            userId: mongoUserId,
         }).session(mongoSession);

         if (!transaction) {
            await mongoSession.abortTransaction();
            return NextResponse.json(
               { error: "Transaction not found" },
               { status: 404 }
            );
         }

         // Save original values
         const originalAssignedToId = transaction.assignedTo;
         const originalAssignedToType = transaction.assignedToType;
         const amount = transaction.amount;

         // Step 1: Remove the transaction amount from the original assignedTo
         if (originalAssignedToId && originalAssignedToType) {
            if (originalAssignedToType === "Income") {
               await Income.findByIdAndUpdate(
                  originalAssignedToId,
                  {
                     $inc: { receivedAmount: -amount },
                  },
                  { session: mongoSession }
               );

               // Update the status
               const updatedIncome = await Income.findById(
                  originalAssignedToId
               ).session(mongoSession);
               if (updatedIncome) {
                  let newStatus = "scheduled";
                  if (
                     roundToTwo(updatedIncome.receivedAmount) >=
                     roundToTwo(updatedIncome.expectedAmount)
                  ) {
                     newStatus = "received";
                  }

                  if (updatedIncome.status !== newStatus) {
                     await Income.findByIdAndUpdate(
                        originalAssignedToId,
                        { status: newStatus },
                        { session: mongoSession }
                     );
                  }
               }
            } else if (originalAssignedToType === "Expense") {
               // Check if we're removing from a debt payment expense
               const originalExpense = await Expense.findById(
                  originalAssignedToId
               ).session(mongoSession);
               const isDebtPayment =
                  originalExpense &&
                  originalExpense.isDebtPayment &&
                  originalExpense.debtId;

               // Update the expense's amountSpent and status
               // We need to mark it as removal if it's a debt payment
               const updateResult = await updateExpenseAmounts(
                  originalAssignedToId,
                  -amount, // Reverse the original transaction's effect
                  mongoSession,
                  isDebtPayment // Pass true if this is removing from a debt payment expense
               );
               // updateResult will be null if expense doesn't exist - that's ok when removing
            }
         }

         // Step 2: Update the transaction with the new assignedTo
         await Transaction.findByIdAndUpdate(
            transactionId,
            {
               assignedTo: newAssignedToId || null,
               assignedToType: newAssignedToType || null,
            },
            { session: mongoSession }
         );

         // Step 3: Add the transaction amount to the new assignedTo
         if (newAssignedToId && newAssignedToType) {
            if (newAssignedToType === "Income") {
               await Income.findByIdAndUpdate(
                  newAssignedToId,
                  {
                     $inc: { receivedAmount: amount },
                  },
                  { session: mongoSession }
               );

               // Update the status
               const updatedIncome = await Income.findById(
                  newAssignedToId
               ).session(mongoSession);
               if (updatedIncome) {
                  let newStatus = "scheduled";
                  if (
                     roundToTwo(updatedIncome.receivedAmount) >=
                     roundToTwo(updatedIncome.expectedAmount)
                  ) {
                     newStatus = "received";
                  }

                  if (updatedIncome.status !== newStatus) {
                     await Income.findByIdAndUpdate(
                        newAssignedToId,
                        { status: newStatus },
                        { session: mongoSession }
                     );
                  }
               }
            } else if (newAssignedToType === "Expense") {
               // Update the expense's amountSpent and status
               const updateResult = await updateExpenseAmounts(
                  newAssignedToId,
                  amount, // Add the transaction amount to the new assignedTo
                  mongoSession,
                  false // Not removing but adding
               );
               // For assigning to a new assignedTo, we require the expense to exist
               if (!updateResult) {
                  throw new Error(
                     "Cannot assign transaction to non-existent expense assignedTo"
                  );
               }
            }
         }

         // Commit the transaction
         await mongoSession.commitTransaction();
         return NextResponse.json({ success: true });
      } catch (error) {
         await mongoSession.abortTransaction();
         throw error;
      }
   } catch (error) {
      console.error("Error updating transaction assignedTo:", error);
      return NextResponse.json(
         { error: "Failed to update transaction assignedTo" },
         { status: 500 }
      );
   }
}

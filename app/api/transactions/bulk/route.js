import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import mongoose from "mongoose";
import Transaction from "../../../lib/mongodb/models/Transaction";
import User from "../../../lib/mongodb/models/User";

export async function POST(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      const { transactions } = await request.json();
      await dbConnect();

      // Start a MongoDB transaction
      const mongoSession = await mongoose.startSession();
      mongoSession.startTransaction();

      try {
         // Calculate total amounts for user updates
         let totalIncome = 0;
         let totalSpent = 0;
         let totalBalance = 0;

         // Group transactions by account for batch updates
         const accountUpdates = new Map();

         // Add userId to each transaction and calculate totals
         const transactionsToInsert = transactions.map((transaction) => {
            const amount = transaction.amount;
            if (transaction.type === "Income") {
               // Only add to totalIncome if the transaction is not assigned to an expense assignedTo
               if (
                  !transaction.assignedTo ||
                  transaction.assignedToType === "Income"
               ) {
                  totalIncome += amount;
               }
               totalBalance += amount;
            } else {
               totalSpent += Math.abs(amount);
               totalBalance += amount; // amount is already negative for expenses
            }

            // Track account balance updates if accountId is present
            if (transaction.accountId) {
               const currentAmount =
                  accountUpdates.get(transaction.accountId) || 0;
               accountUpdates.set(
                  transaction.accountId,
                  currentAmount + amount
               );
            }

            return {
               ...transaction,
               userId: user._id,
            };
         });

         // Insert all transactions
         await Transaction.insertMany(transactionsToInsert, {
            session: mongoSession,
         });

         // Update user totals and account balances
         const user = await User.findById(userId);
         if (!user) {
            throw new Error("User not found");
         }

         // Update account balances
         if (accountUpdates.size > 0) {
            for (const [accountId, amount] of accountUpdates) {
               const accountIndex = user.accounts.findIndex(
                  (acc) => acc._id.toString() === accountId.toString()
               );
               if (accountIndex !== -1) {
                  user.accounts[accountIndex].balance = Number(
                     (user.accounts[accountIndex].balance + amount).toFixed(2)
                  );
               }
            }
         }

         // Update user totals
         user.currentBalance = Number(
            (user.currentBalance + totalBalance).toFixed(2)
         );
         user.totalIncome = Number((user.totalIncome + totalIncome).toFixed(2));
         user.totalSpent = Number((user.totalSpent + totalSpent).toFixed(2));

         // Save all changes
         await user.save({ session: mongoSession });

         // Commit the transaction
         await mongoSession.commitTransaction();

         return NextResponse.json({
            message: "Transactions imported successfully",
            accounts: user.accounts,
         });
      } catch (error) {
         // If an error occurred, abort the transaction
         await mongoSession.abortTransaction();
         throw error;
      } finally {
         // End the session
         mongoSession.endSession();
      }
   } catch (error) {
      console.error("Error in POST /api/transactions/bulk:", error);
      return NextResponse.json(
         { error: "Failed to import transactions" },
         { status: 500 }
      );
   }
}

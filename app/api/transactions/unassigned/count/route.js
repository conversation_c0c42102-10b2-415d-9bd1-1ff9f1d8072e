import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import Transaction from "@/app/lib/mongodb/models/Transaction";
import User from "@/app/lib/mongodb/models/User";

export async function GET() {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user first
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const count = await Transaction.countDocuments({
         userId: user._id,
         assignedTo: null, // Updated to use assignedTo instead of category
         type: { $ne: "Transfer" },
         isAdjustment: { $ne: true }, // Exclude adjustment transactions
         $or: [
            { transferDetails: { $exists: false } },
            { transferDetails: null },
         ],
      });

      return NextResponse.json({ count });
   } catch (error) {
      console.error("Error in GET /api/transactions/unassigned/count:", error);
      return NextResponse.json(
         { error: "Failed to fetch unassigned count" },
         { status: 500 }
      );
   }
}

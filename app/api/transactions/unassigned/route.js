import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import Transaction from "@/app/lib/mongodb/models/Transaction";
import User from "@/app/lib/mongodb/models/User";

export async function GET(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user first
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Get query parameters
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get("page")) || 1;
      const limit = parseInt(searchParams.get("limit")) || 50;
      const startDate = searchParams.get("startDate");
      const endDate = searchParams.get("endDate");
      const accountId = searchParams.get("accountId");

      // Build query
      const query = {
         userId: user._id,
         assignedTo: null, // Updated to use assignedTo instead of category
         type: { $ne: "Transfer" }, // Exclude transfer transactions
         isAdjustment: { $ne: true }, // Exclude adjustment transactions
         $or: [
            { transferDetails: { $exists: false } },
            { transferDetails: null },
         ],
      };

      // Add account filter if provided
      if (accountId && accountId !== "all") {
         query.accountId = parseInt(accountId);
      }

      // Add date range to query if provided
      if (startDate || endDate) {
         query.date = {};
         if (startDate) query.date.$gte = new Date(startDate);
         if (endDate) query.date.$lte = new Date(endDate);
      }

      // Get total count for pagination
      const total = await Transaction.countDocuments(query);

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Fetch transactions with pagination
      const transactions = await Transaction.find(query)
         .sort({ date: -1 }) // Sort by date descending
         .skip(skip)
         .limit(limit)
         .lean();

      return NextResponse.json({
         transactions,
         pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
         },
      });
   } catch (error) {
      console.error("Error in GET /api/transactions/unassigned:", error);
      return NextResponse.json(
         { error: "Failed to fetch unassigned transactions" },
         { status: 500 }
      );
   }
}

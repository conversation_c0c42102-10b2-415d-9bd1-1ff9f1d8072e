import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "@/app/lib/mongodb/dbConnect";
import Transaction from "@/app/lib/mongodb/models/Transaction";
import Expense from "@/app/lib/mongodb/models/Expense";
import User from "@/app/lib/mongodb/models/User";

export async function GET(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get MongoDB user by user ID
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Get query parameters
      const { searchParams } = new URL(request.url);
      const categoryId = searchParams.get("id");
      const page = parseInt(searchParams.get("page")) || 1;
      const limit = parseInt(searchParams.get("limit")) || 50;

      if (!categoryId) {
         return NextResponse.json(
            { error: "Category ID is required" },
            { status: 400 }
         );
      }

      // Build query
      const query = {
         userId: user._id,
         assignedTo: categoryId,
         assignedToType: "Expense",
      };

      // Get total count for pagination
      const total = await Transaction.countDocuments(query);

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Fetch transactions with pagination
      const transactions = await Transaction.find(query)
         .sort({ date: -1 }) // Sort by date descending
         .skip(skip)
         .limit(limit)
         .lean();

      // Find the expense to add its details
      const expense = await Expense.findById(categoryId).lean();

      return NextResponse.json({
         transactions,
         expense: expense || null,
         pagination: {
            total,
            totalPages: Math.ceil(total / limit),
            page,
            limit,
         },
      });
   } catch (error) {
      console.error("Error fetching transactions by category:", error);
      return NextResponse.json(
         { error: "Failed to fetch transactions" },
         { status: 500 }
      );
   }
}

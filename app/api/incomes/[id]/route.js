import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Income from "../../../lib/mongodb/models/Income";
import User from "../../../lib/mongodb/models/User";

export async function DELETE(request, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { id } = await params;
      const income = await Income.findOneAndDelete({
         _id: id,
         userId: user._id,
      });

      if (!income) {
         return NextResponse.json(
            { error: "Income not found" },
            { status: 404 }
         );
      }

      return NextResponse.json({ message: "Income deleted successfully" });
   } catch (error) {
      console.error("Error in DELETE /api/incomes/[id]:", error);
      return NextResponse.json(
         { error: "Failed to delete income" },
         { status: 500 }
      );
   }
}

export async function PUT(request, { params }) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      const { id } = await params;
      const updates = await request.json();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Handle date conversion if needed
      if (updates.date) {
         if (updates.date instanceof Date) {
            updates.date = updates.date.toISOString().split("T")[0];
         } else if (
            typeof updates.date === "string" &&
            updates.date.includes("T")
         ) {
            // Handle ISO string format
            updates.date = new Date(updates.date).toISOString().split("T")[0];
         }
      }

      const income = await Income.findOneAndUpdate(
         {
            _id: id,
            userId: user._id,
         },
         {
            $set: updates,
         },
         { new: true }
      );

      if (!income) {
         return NextResponse.json(
            { error: "Income not found" },
            { status: 404 }
         );
      }

      return NextResponse.json(income);
   } catch (error) {
      console.error("Error updating income:", error);
      return NextResponse.json(
         { error: "Failed to update income" },
         { status: 500 }
      );
   }
}

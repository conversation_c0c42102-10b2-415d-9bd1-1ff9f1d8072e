import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../lib/mongodb/dbConnect";
import Income from "../../lib/mongodb/models/Income";
import User from "../../lib/mongodb/models/User";

export async function GET(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      const { searchParams } = new URL(request.url);
      const status = searchParams.get("status");
      const start = searchParams.get("start");
      const end = searchParams.get("end");

      // Build query with optional date range
      const query = { userId: user._id };
      if (status) {
         query.status = status;
      }

      if (start || end) {
         query.date = {};
         if (start) query.date.$gte = start;
         if (end) query.date.$lte = end;
      }

      const incomes = await Income.find(query).sort({ date: -1 });

      return NextResponse.json(incomes);
   } catch (error) {
      console.error("Error in GET /api/incomes:", error);
      return NextResponse.json(
         { error: "Failed to fetch incomes" },
         { status: 500 }
      );
   }
}

export async function POST(request) {
   try {
      const { session, userId, error } = await getSessionForAPI();
      if (error) return error;

      const data = await request.json();
      await dbConnect();

      // Get user from MongoDB
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Ensure date is in YYYY-MM-DD format
      let dateString = data.date;
      if (data.date instanceof Date) {
         dateString = data.date.toISOString().split("T")[0];
      } else if (typeof data.date === "string" && data.date.includes("T")) {
         // Handle ISO string format
         dateString = new Date(data.date).toISOString().split("T")[0];
      }

      const incomeData = {
         userId: user._id,
         date: dateString,
         description: data.description,
         expectedAmount: data.expectedAmount,
         receivedAmount: data.receivedAmount,
         category: data.category || "Salary",
         status: data.status || "scheduled",
         notes: data.notes,
      };

      const income = await Income.create(incomeData);
      return NextResponse.json(income, { status: 201 });
   } catch (error) {
      console.error("Error in POST /api/incomes:", error);
      return NextResponse.json(
         { error: "Failed to create income", details: error.message },
         { status: 500 }
      );
   }
}

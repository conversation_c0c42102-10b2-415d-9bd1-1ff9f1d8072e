import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";

export async function GET(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Find all users who have debts
      const users = await User.find({ "debts.0": { $exists: true } });
      let migratedCount = 0;

      for (const user of users) {
         let modified = false;

         // Process each debt
         user.debts.forEach((debt) => {
            if (!debt.history || !debt.history.length) return;

            // Check each history entry and add missing fields
            debt.history.forEach((entry) => {
               if (!entry.oldDueDate) {
                  entry.oldDueDate = debt.dueDate || "1";
                  modified = true;
               }
               if (!entry.newDueDate) {
                  entry.newDueDate = debt.dueDate || "1";
                  modified = true;
               }
            });
         });

         // Save if any modifications were made
         if (modified) {
            await user.save();
            migratedCount++;
         }
      }

      return NextResponse.json({
         success: true,
         message: `Migration completed successfully. Updated ${migratedCount} users.`,
      });
   } catch (error) {
      console.error("Error in debt migration:", error);
      return NextResponse.json(
         { error: "Failed to migrate debt data", details: error.message },
         { status: 500 }
      );
   }
}

import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Debt from "../../../lib/mongodb/models/Debt";

export async function GET(request) {
   try {
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB to check admin status
      const user = await User.findById(userId);
      if (!user || !user.isAdmin) {
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      await dbConnect();

      // Find all users who have debts
      const users = await User.find({ "debts.0": { $exists: true } });

      let migratedUsers = 0;
      let migratedDebts = 0;

      for (const user of users) {
         let userDebts = [];

         // Process each debt from user document
         for (const debt of user.debts) {
            // Create a new Debt document for each debt in the user
            const newDebt = new Debt({
               userId: user._id,
               debtType: debt.debtType,
               lender: debt.lender,
               balance: debt.balance,
               apr: debt.apr,
               minimumPayment: debt.minimumPayment,
               dueDate: debt.dueDate,
               active: debt.active,
               history: debt.history || [],
            });

            // Save the debt
            await newDebt.save();
            userDebts.push(newDebt);
            migratedDebts++;
         }

         if (userDebts.length > 0) {
            migratedUsers++;
         }
      }

      return NextResponse.json({
         success: true,
         message: `Migration completed successfully. Migrated ${migratedDebts} debts from ${migratedUsers} users.`,
      });
   } catch (error) {
      console.error("Error in debt migration:", error);
      return NextResponse.json(
         { error: "Failed to migrate debt data", details: error.message },
         { status: 500 }
      );
   }
}

// For a specific user
export async function POST(request) {
   try {
      const {
         session: authSession,
         userId: clerkUserId,
         error,
      } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB to check admin status
      const adminUser = await User.findById(clerkUserId);
      if (!adminUser || !adminUser.isAdmin) {
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Get the target user ID from request body
      const { userId } = await request.json();
      const user = await User.findById(userId);

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      let migratedDebts = 0;

      // Process each debt from user document
      for (const debt of user.debts) {
         // Create a new Debt document for each debt in the user
         const newDebt = new Debt({
            userId: user._id,
            debtType: debt.debtType,
            lender: debt.lender,
            balance: debt.balance,
            apr: debt.apr,
            minimumPayment: debt.minimumPayment,
            dueDate: debt.dueDate,
            active: debt.active,
            history: debt.history || [],
         });

         // Save the debt
         await newDebt.save();
         migratedDebts++;
      }

      // Optionally, clear the debts array from the user document
      // Uncomment this once you confirm the migration is working correctly
      // user.debts = [];
      // await user.save();

      return NextResponse.json({
         success: true,
         message: `Migration completed successfully. Migrated ${migratedDebts} debts.`,
      });
   } catch (error) {
      console.error("Error in debt migration:", error);
      return NextResponse.json(
         { error: "Failed to migrate debt data", details: error.message },
         { status: 500 }
      );
   }
}

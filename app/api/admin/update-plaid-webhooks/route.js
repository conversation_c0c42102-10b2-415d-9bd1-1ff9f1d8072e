import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import { plaidClient } from "../../../lib/plaid/config";
import fs from "fs";
import path from "path";

// Helper function to log the migration results
function logMigrationResults(data) {
   try {
      const logDir = path.join(process.cwd(), "logs");
      if (!fs.existsSync(logDir)) {
         fs.mkdirSync(logDir, { recursive: true });
      }

      const logPath = path.join(logDir, "plaid-webhook-migration.json");
      const timestamp = new Date().toISOString();

      // Create log entry with timestamp
      const logEntry = {
         timestamp,
         ...data,
      };

      // Write to file
      fs.writeFileSync(logPath, JSON.stringify(logEntry, null, 2));
   } catch (error) {
      console.error("Error writing to migration log file:", error);
   }
}

export async function POST(request) {
   try {
      // Get request data and check for API key
      const requestData = await request.json();
      const { apiKey } = requestData;

      // Check API key first
      const hasValidApiKey = apiKey && apiKey === process.env.ADMIN_API_KEY;

      // If no valid API key, check admin session
      let isAdmin = false;
      if (!hasValidApiKey) {
         const {
            session: authSession,
            userId,
            error,
         } = await getSessionForAPI();
         if (error) return error;

         isAdmin =
            authSession?.user?.isAdmin ||
            authSession?.user?.email === process.env.ADMIN_EMAIL;
      }

      if (!isAdmin && !hasValidApiKey) {
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Check if webhook URL is configured
      if (!process.env.PLAID_WEBHOOK_URL) {
         return NextResponse.json(
            { error: "PLAID_WEBHOOK_URL environment variable is not set" },
            { status: 400 }
         );
      }

      await dbConnect();

      // Find all users with Plaid Items
      const users = await User.find({ "plaidItems.0": { $exists: true } });

      if (users.length === 0) {
         return NextResponse.json({
            message: "No users with Plaid Items found",
            usersProcessed: 0,
            itemsProcessed: 0,
            succeeded: 0,
            failed: 0,
         });
      }

      // Track results
      const results = {
         usersProcessed: users.length,
         itemsProcessed: 0,
         succeeded: 0,
         failed: 0,
         errors: [],
         successfulItems: [],
      };

      // Process each user and their Plaid Items
      for (const user of users) {
         for (const plaidItem of user.plaidItems) {
            results.itemsProcessed++;

            try {
               // Call Plaid's API to update the webhook URL
               const response = await plaidClient.itemWebhookUpdate({
                  access_token: plaidItem.accessToken,
                  webhook: process.env.PLAID_WEBHOOK_URL,
               });

               results.succeeded++;
               results.successfulItems.push({
                  itemId: plaidItem.itemId,
                  userId: user._id.toString(),
                  institutionName: plaidItem.institutionName || "unknown",
               });

               console.log(
                  `Successfully updated webhook for Item ${plaidItem.itemId}`
               );
            } catch (error) {
               results.failed++;
               results.errors.push({
                  itemId: plaidItem.itemId,
                  userId: user._id.toString(),
                  error: error.message,
                  errorCode: error.response?.data?.error_code,
               });

               console.error(
                  `Error updating webhook for Item ${plaidItem.itemId}:`,
                  error.message
               );
            }
         }
      }

      // Log the results
      logMigrationResults(results);

      return NextResponse.json({
         message: "Plaid webhook migration completed",
         usersProcessed: results.usersProcessed,
         itemsProcessed: results.itemsProcessed,
         succeeded: results.succeeded,
         failed: results.failed,
         errors: results.errors.length > 0 ? results.errors : undefined,
      });
   } catch (error) {
      console.error("Error during Plaid webhook migration:", error);
      return NextResponse.json(
         { error: "Failed to update Plaid webhooks", details: error.message },
         { status: 500 }
      );
   }
}

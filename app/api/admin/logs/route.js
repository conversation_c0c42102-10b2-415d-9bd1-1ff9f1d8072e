import { NextResponse } from "next/server";
import { getSessionForAPI } from "@/app/lib/utils/userUtils";
import dbConnect from "../../../lib/mongodb/dbConnect";
import User from "../../../lib/mongodb/models/User";
import Log from "../../../lib/mongodb/models/Log";

export const runtime = "nodejs";

export async function GET(request) {
   try {
      // Check authentication and admin status
      const { session: authSession, userId, error } = await getSessionForAPI();
      if (error) return error;

      await dbConnect();

      // Get user from MongoDB and check admin status
      const user = await User.findById(userId);
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Only allow admins to access logs
      if (!user.isAdmin) {
         return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }

      // Parse query parameters
      const { searchParams } = new URL(request.url);
      const limit = Number(searchParams.get("limit")) || 100;
      const offset = Number(searchParams.get("offset")) || 0;
      const source = searchParams.get("source");
      const level = searchParams.get("level");
      const event = searchParams.get("event");
      const filterUserId = searchParams.get("userId");
      const startDate = searchParams.get("startDate");
      const endDate = searchParams.get("endDate");

      // Build query filters
      const query = {};

      if (source) query.source = source;
      if (level) query.level = level;
      if (event) query.event = event;
      if (filterUserId) query.userId = filterUserId;

      if (startDate || endDate) {
         query.timestamp = {};
         if (startDate) query.timestamp.$gte = new Date(startDate);
         if (endDate) query.timestamp.$lte = new Date(endDate);
      }

      // Execute the query
      const logs = await Log.find(query)
         .sort({ timestamp: -1 })
         .skip(offset)
         .limit(limit);

      // Get total count for pagination
      const total = await Log.countDocuments(query);

      // Get available sources, levels, and events for filtering
      const sources = await Log.distinct("source");
      const levels = await Log.distinct("level");
      const events = await Log.distinct("event");

      return NextResponse.json({
         logs,
         pagination: {
            total,
            offset,
            limit,
            hasMore: total > offset + limit,
         },
         filters: {
            sources,
            levels,
            events,
         },
      });
   } catch (error) {
      console.error("Error fetching logs:", error);
      return NextResponse.json(
         { error: "Failed to fetch logs", details: error.message },
         { status: 500 }
      );
   }
}

"use client";

import { useState, useCallback } from "react";
import {
   TransactionsUtils,
   TransactionHelpers,
} from "../lib/utils/transactionsUtils";

export const useTransactions = (initialFilter) => {
   const [transactions, setTransactions] = useState([]);
   const [loading, setLoading] = useState(false);
   const [error, setError] = useState(null);
   const [pagination, setPagination] = useState({
      page: 1,
      limit: 50,
      total: 0,
      totalPages: 0,
   });

   const fetchTransactions = useCallback(
      async (filter = initialFilter) => {
         try {
            setLoading(true);
            const {
               transactions: transactionsData,
               pagination: paginationData,
            } = await TransactionsUtils.fetchTransactions(filter);

            setTransactions(transactionsData);
            setPagination(paginationData);
         } catch (error) {
            setError(error.message || "Failed to load transactions");
         } finally {
            setLoading(false);
         }
      },
      [initialFilter]
   );

   const handleSearch = useCallback(
      async (searchQuery) => {
         try {
            setLoading(true);
            const newState = TransactionHelpers.handleSearchInput(searchQuery, {
               pagination,
            });

            const {
               transactions: transactionsData,
               pagination: paginationData,
            } = await TransactionsUtils.fetchTransactions({
               searchQuery,
               page: newState.pagination.page,
               limit: newState.pagination.limit,
            });

            setTransactions(transactionsData);
            setPagination(paginationData);
         } catch (error) {
            setError(error.message || "Failed to search transactions");
         } finally {
            setLoading(false);
         }
      },
      [pagination]
   );

   const handleDelete = useCallback(
      async (transactionId) => {
         try {
            setLoading(true);
            await TransactionsUtils.handleDeleteTransaction(
               transactionId,
               transactions
            );
            await fetchTransactions();
         } catch (error) {
            setError(error.message || "Failed to delete transaction");
         } finally {
            setLoading(false);
         }
      },
      [transactions, fetchTransactions]
   );

   return {
      transactions,
      loading,
      error,
      fetchTransactions,
      handleDelete,
      setTransactions,
      handleSearch,
   };
};

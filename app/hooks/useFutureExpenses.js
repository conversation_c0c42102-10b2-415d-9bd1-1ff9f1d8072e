import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { isSameDay } from "date-fns";
import { calculateProratedAmount } from "../lib/utils/expenseUtils";

export function useFutureExpenses() {
   const [futureExpenses, setFutureExpenses] = useState([]);
   const [showFutureExpenses, setShowFutureExpenses] = useState(true);

   // Create a ref to track the previous date range
   const prevDateRangeRef = useRef(null);
   // Create a ref to track the previous preference value
   const prevPreferenceRef = useRef(showFutureExpenses);

   // Define fetchFutureExpenses with useCallback
   const fetchFutureExpenses = useCallback(
      async (dateRange, userExpenseSettings) => {
         if (!dateRange?.end) return;

         // Check if the date range has changed
         const hasDateRangeChanged =
            !prevDateRangeRef.current ||
            !isSameDay(prevDateRangeRef.current.start, dateRange.start) ||
            !isSameDay(prevDateRangeRef.current.end, dateRange.end) ||
            prevDateRangeRef.current.viewType !== dateRange.viewType;

         // Only fetch if the date range has changed
         if (!hasDateRangeChanged) return;

         // Update the previous date range ref
         prevDateRangeRef.current = { ...dateRange };

         try {
            // Build URL with date range parameters
            const url = new URL("/api/expenses/future", window.location.origin);

            // Handle date object or date string
            const endDateIso =
               dateRange.end instanceof Date
                  ? dateRange.end.toISOString()
                  : typeof dateRange.end === "string" &&
                    dateRange.end.includes("T")
                  ? dateRange.end
                  : new Date(dateRange.end).toISOString();

            url.searchParams.append("end", endDateIso);

            // Include start date if available
            if (dateRange.start) {
               const startDateIso =
                  dateRange.start instanceof Date
                     ? dateRange.start.toISOString()
                     : typeof dateRange.start === "string" &&
                       dateRange.start.includes("T")
                     ? dateRange.start
                     : new Date(dateRange.start).toISOString();
               url.searchParams.append("start", startDateIso);
            }

            if (dateRange.viewType) {
               url.searchParams.append("viewType", dateRange.viewType);
            }

            const response = await fetch(url);
            if (response.ok) {
               const data = await response.json();

               // Create a dateRange object that includes userPaySettings for future expense calculation
               const dateRangeWithSettings = {
                  ...dateRange,
                  userPaySettings: userExpenseSettings,
               };

               // Map the future expenses to include the proper status
               const mappedFutureExpenses = data.expenses.map((expense) => {
                  // Calculate prorated amount
                  const proratedAmountDue = calculateProratedAmount(
                     expense,
                     dateRangeWithSettings
                  );

                  // Check if the expense is funded but keep status as "future"
                  const isFutureFunded =
                     expense.amountAssigned >= proratedAmountDue;

                  return {
                     ...expense,
                     status: "future", // Always set as "future"
                     isFutureFunded: isFutureFunded, // Add this property
                     isFutureExpense: true,
                     originalAmountDue: expense.amountDue,
                     amountDue: proratedAmountDue,
                  };
               });

               setFutureExpenses(mappedFutureExpenses);
            }
         } catch (error) {
            console.error("Error fetching future expenses:", error);
         }
      },
      []
   );

   // Load showFutureExpenses preference from user settings
   const loadPreference = useCallback((userExpenseSettings) => {
      const newPreference =
         userExpenseSettings?.preferences?.showFutureExpenses;

      // Only update if the preference exists and has changed from the previous value
      if (
         newPreference !== undefined &&
         newPreference !== prevPreferenceRef.current
      ) {
         setShowFutureExpenses(newPreference);
         prevPreferenceRef.current = newPreference;
      }
   }, []);

   const handleToggleFutureExpenses = useCallback(
      async (userExpenseSettings, expenses, onExpensesChange) => {
         // Toggle the state locally first for immediate feedback
         const newValue = !showFutureExpenses;
         setShowFutureExpenses(newValue);

         // Create updated settings object immediately for a smoother UI update
         if (userExpenseSettings) {
            // Create a new preferences object with the updated value
            const updatedPreferences = {
               ...(userExpenseSettings.preferences || {}),
               showFutureExpenses: newValue,
            };

            // Create a new userExpenseSettings object with the updated preferences
            const updatedSettings = {
               ...userExpenseSettings,
               preferences: updatedPreferences,
            };

            // Notify the parent component about the updated settings immediately
            // This ensures the UI updates before the API call completes
            if (onExpensesChange) {
               onExpensesChange(expenses, updatedSettings);
            }
         }

         // Save the preference to the user's document in the background
         // We don't need to wait for this to complete before updating the UI
         fetch("/api/user/preferences", {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               showFutureExpenses: newValue,
            }),
         }).catch((error) => {
            console.error("Error saving preference:", error);
            // Only revert if there's an error
            setShowFutureExpenses(!newValue);

            // Also revert the parent component's state if there was an error
            if (userExpenseSettings && onExpensesChange) {
               const revertedPreferences = {
                  ...(userExpenseSettings.preferences || {}),
                  showFutureExpenses: !newValue,
               };

               const revertedSettings = {
                  ...userExpenseSettings,
                  preferences: revertedPreferences,
               };

               onExpensesChange(expenses, revertedSettings);
            }
         });
      },
      [showFutureExpenses]
   );

   // Update future expenses when the expenses array changes from a parent update
   const syncWithExpenses = useCallback((expenses) => {
      if (expenses.length) {
         setFutureExpenses((prevFutureExpenses) => {
            // First create a map of the existing future expenses by ID
            const futureExpenseMap = new Map(
               prevFutureExpenses.map((exp) => [exp._id, exp])
            );

            // Check for updated future expenses in the expenses array
            expenses.forEach((expense) => {
               if (
                  (expense.isFutureExpense || expense.status === "future") &&
                  futureExpenseMap.has(expense._id)
               ) {
                  // Get the current future expense to access its prorated amountDue
                  const currentFutureExp = futureExpenseMap.get(expense._id);
                  const proratedAmountDue = currentFutureExp.amountDue;

                  // Check if expense is properly funded but keep status as "future"
                  const isFutureFunded =
                     expense.amountAssigned >= proratedAmountDue;

                  // Update the future expense with any changes from the expenses array
                  futureExpenseMap.set(expense._id, {
                     ...futureExpenseMap.get(expense._id),
                     ...expense,
                     // Ensure future expense properties are preserved
                     isFutureExpense: true,
                     // Always keep status as "future"
                     status: "future",
                     // Set funding property
                     isFutureFunded: isFutureFunded,
                     // Make sure the prorated amountDue is maintained
                     amountDue: proratedAmountDue,
                     // Ensure original amount is preserved
                     originalAmountDue: currentFutureExp.originalAmountDue,
                  });
               }
            });

            // Return the updated future expenses array
            return Array.from(futureExpenseMap.values());
         });
      }
   }, []);

   // Create a memoized version of showFutureExpenses to prevent unnecessary recalculations
   const memoizedShowFutureExpenses = useMemo(
      () => showFutureExpenses,
      [showFutureExpenses]
   );

   return {
      futureExpenses,
      setFutureExpenses,
      showFutureExpenses: memoizedShowFutureExpenses,
      setShowFutureExpenses,
      fetchFutureExpenses,
      loadPreference,
      handleToggleFutureExpenses,
      syncWithExpenses,
   };
}

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { format, parseISO } from "date-fns";

export function useHiddenProjections() {
   const { data: session } = useSession();
   const [hiddenProjections, setHiddenProjections] = useState([]);
   const [isHiddenSectionExpanded, setIsHiddenSectionExpanded] =
      useState(false);

   // Load hidden projections from localStorage on component mount
   useEffect(() => {
      if (session?.user?.id) {
         const stored =
            localStorage.getItem(
               `skipped-expense-projections-${session?.user?.id}`
            ) ||
            localStorage.getItem(
               `hidden-expense-projections-${session?.user?.id}`
            );
         if (stored) {
            try {
               setHiddenProjections(JSON.parse(stored));
            } catch (error) {
               console.error("Error parsing hidden projections:", error);
               setHiddenProjections([]);
            }
         }
      }
   }, [session]);

   const isProjectionHidden = useCallback(
      (expense) => {
         if (!session?.user?.id) return false;

         // Since dates are now YYYY-MM-DD strings, use them directly
         const expenseDate =
            expense.date instanceof Date
               ? format(expense.date, "yyyy-MM-dd")
               : expense.date;

         return hiddenProjections.some(
            (hidden) =>
               hidden.description.toLowerCase() ===
                  expense.description.toLowerCase() &&
               hidden.date === expenseDate
         );
      },
      [hiddenProjections, session?.user]
   );

   const handleHideProjection = (expense, e) => {
      e.stopPropagation();
      if (!session?.user?.id) return;

      const expenseDate =
         expense.date instanceof Date
            ? format(expense.date, "yyyy-MM-dd")
            : expense.date;

      let newHiddenProjections;
      if (expense.isAggregated && expense.expenses) {
         // Hide all weeks in the group
         newHiddenProjections = [
            ...hiddenProjections,
            ...expense.expenses.map((weekExp) => ({
               description: weekExp.description,
               date:
                  weekExp.date instanceof Date
                     ? format(weekExp.date, "yyyy-MM-dd")
                     : weekExp.date,
            })),
         ];
      } else {
         // Hide single expense
         newHiddenProjections = [
            ...hiddenProjections,
            {
               description: expense.description,
               date: expenseDate,
            },
         ];
      }

      setHiddenProjections(newHiddenProjections);
      localStorage.setItem(
         `skipped-expense-projections-${session?.user?.id}`,
         JSON.stringify(newHiddenProjections)
      );
   };

   const handleUnhideProjection = (expense, e) => {
      e.stopPropagation();
      if (!session?.user?.id) return;

      let newHiddenProjections;
      if (expense.isAggregated && expense.expenses) {
         // Unhide all weeks in the group
         newHiddenProjections = hiddenProjections.filter(
            (hidden) =>
               !expense.expenses.some(
                  (weekExp) =>
                     weekExp.description.toLowerCase() ===
                        hidden.description.toLowerCase() &&
                     (weekExp.date instanceof Date
                        ? format(weekExp.date, "yyyy-MM-dd")
                        : weekExp.date) === hidden.date
               )
         );
      } else {
         // Unhide single expense
         const expenseDate =
            expense.date instanceof Date
               ? format(expense.date, "yyyy-MM-dd")
               : expense.date;
         newHiddenProjections = hiddenProjections.filter(
            (hidden) =>
               !(
                  hidden.description.toLowerCase() ===
                     expense.description.toLowerCase() &&
                  hidden.date === expenseDate
               )
         );
      }

      setHiddenProjections(newHiddenProjections);
      localStorage.setItem(
         `skipped-expense-projections-${session?.user?.id}`,
         JSON.stringify(newHiddenProjections)
      );
   };

   return {
      hiddenProjections,
      isHiddenSectionExpanded,
      setIsHiddenSectionExpanded,
      isProjectionHidden,
      handleHideProjection,
      handleUnhideProjection,
   };
}

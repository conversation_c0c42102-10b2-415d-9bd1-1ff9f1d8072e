import { useState, useCallback, useRef, useEffect } from "react";
import { format } from "date-fns";
import { determineExpenseStatus } from "../lib/utils/expenseUtils";

// Available status options
const STATUS_OPTIONS = [
   "all",
   "projected",
   "scheduled",
   "funded",
   "paid",
   "late",
   "overpaid",
   "underpaid",
   "future",
];

// Available filter options
const AVAILABLE_FILTER_OPTIONS = [
   { value: "all", label: "All" },
   { value: "available", label: "Available" },
   { value: "negative", label: "Negative" },
];

export function useExpenseFiltering() {
   const [showStatusFilter, setShowStatusFilter] = useState(false);
   const [selectedStatuses, setSelectedStatuses] = useState([
      "all",
      "projected",
      "scheduled",
      "funded",
      "paid",
      "late",
      "overpaid",
      "underpaid",
      "future",
   ]);
   const [showAvailableFilter, setShowAvailableFilter] = useState(false);
   const [selectedAvailableFilter, setSelectedAvailableFilter] =
      useState("all");

   const statusFilterRef = useRef(null);
   const availableFilterRef = useRef(null);

   // Close filter dropdowns when clicking outside
   useEffect(() => {
      const handleClickOutside = (event) => {
         if (
            statusFilterRef.current &&
            !statusFilterRef.current.contains(event.target)
         ) {
            setShowStatusFilter(false);
         }
         if (
            availableFilterRef.current &&
            !availableFilterRef.current.contains(event.target)
         ) {
            setShowAvailableFilter(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
         document.removeEventListener("mousedown", handleClickOutside);
   }, []);

   // Filter expenses by status
   const filterExpensesByStatus = useCallback(
      (expensesToFilter) => {
         // If "all" is selected, return all expenses
         if (selectedStatuses.includes("all")) {
            return expensesToFilter;
         }

         return expensesToFilter.filter((expense) => {
            // If the expense is already paid, use that status directly
            if (expense.status === "paid") {
               const passesStatusFilter = selectedStatuses.includes("paid");
               const available = expense.amountAvailable || 0;
               const passesAvailableFilter =
                  selectedAvailableFilter === "all" ||
                  (selectedAvailableFilter === "available" && available > 0) ||
                  (selectedAvailableFilter === "negative" && available < 0);
               return passesStatusFilter && passesAvailableFilter;
            }

            const calculatedStatus = determineExpenseStatus(
               expense.amountSpent,
               expense.amountAssigned,
               expense.amountDue,
               expense.date,
               expense.startDate,
               expense.endDate,
               expense.status,
               expense.isFutureExpense
            );

            // For aggregated weekly expenses
            if (expense.isAggregated) {
               return expense.expenses.some((subExpense) => {
                  // If the sub-expense is already paid, use that status directly
                  if (subExpense.status === "paid") {
                     const passesStatusFilter =
                        selectedStatuses.includes("paid");
                     const available = subExpense.amountAvailable || 0;
                     const passesAvailableFilter =
                        selectedAvailableFilter === "all" ||
                        (selectedAvailableFilter === "available" &&
                           available > 0) ||
                        (selectedAvailableFilter === "negative" &&
                           available < 0);
                     return passesStatusFilter && passesAvailableFilter;
                  }

                  const subStatus = determineExpenseStatus(
                     subExpense.amountSpent,
                     subExpense.amountAssigned,
                     subExpense.amountDue,
                     subExpense.date,
                     subExpense.startDate,
                     subExpense.endDate,
                     subExpense.status,
                     subExpense.isFutureExpense
                  );

                  const available = subExpense.amountAvailable || 0;
                  const passesAvailableFilter =
                     selectedAvailableFilter === "all" ||
                     (selectedAvailableFilter === "available" &&
                        available > 0) ||
                     (selectedAvailableFilter === "negative" && available < 0);

                  // Check both the calculated status and the expense's status property
                  const passesStatusFilter =
                     selectedStatuses.includes(subStatus.toLowerCase()) ||
                     (selectedStatuses.includes("projected") &&
                        (subExpense.status === "projected" ||
                           subStatus === "projected"));

                  return passesStatusFilter && passesAvailableFilter;
               });
            }

            // For regular expenses
            const available = expense.amountAvailable || 0;
            const passesAvailableFilter =
               selectedAvailableFilter === "all" ||
               (selectedAvailableFilter === "available" && available > 0) ||
               (selectedAvailableFilter === "negative" && available < 0);

            // Check both the calculated status and the expense's status property
            const passesStatusFilter =
               selectedStatuses.includes(calculatedStatus.toLowerCase()) ||
               (selectedStatuses.includes("projected") &&
                  (expense.status === "projected" ||
                     calculatedStatus === "projected"));

            return passesStatusFilter && passesAvailableFilter;
         });
      },
      [selectedStatuses, selectedAvailableFilter]
   );

   const filterExpensesByDate = useCallback((expensesToFilter, dateRange) => {
      if (!dateRange?.start || !dateRange?.end) return expensesToFilter;

      return expensesToFilter.filter((expense) => {
         // Skip date filtering for expenses already identified as future
         if (expense.status === "future" || expense.isFutureExpense) {
            return true;
         }

         // For spread expenses (weekly, biweekly, etc), check if any part of the expense duration overlaps with the period
         if (expense.startDate && expense.endDate) {
            const rangeStartStr = format(dateRange.start, "yyyy-MM-dd");
            const rangeEndStr = format(dateRange.end, "yyyy-MM-dd");

            // Check if the expense period overlaps with the target period (string comparison)
            return (
               expense.startDate <= rangeEndStr &&
               expense.endDate >= rangeStartStr
            );
         }

         // For non-spread expenses, check if the date falls within the range (string comparison)
         const rangeStartStr = format(dateRange.start, "yyyy-MM-dd");
         const rangeEndStr = format(dateRange.end, "yyyy-MM-dd");
         return expense.date >= rangeStartStr && expense.date <= rangeEndStr;
      });
   }, []);

   // Filter projected expenses by date (current month forward)
   const filterProjectedExpensesByDate = useCallback(
      (expensesToFilter, dateRange) => {
         // If no expenses or no date range, return as is
         if (!expensesToFilter.length || !dateRange?.start || !dateRange?.end)
            return expensesToFilter;

         // Get current date as YYYY-MM-DD string
         const currentDateStr = format(new Date(), "yyyy-MM-dd");
         // Get current month start as YYYY-MM-DD string
         const currentMonthStart = new Date();
         currentMonthStart.setDate(1);
         const currentMonthStartStr = format(currentMonthStart, "yyyy-MM-dd");

         return expensesToFilter.filter((expense) => {
            // Don't filter future expenses - always keep them
            if (expense.status === "future" || expense.isFutureExpense) {
               return true;
            }

            // Skip if not a projected expense
            if (expense.status !== "projected") return true;

            // For aggregated expenses, filter children
            if (expense.isAggregated && expense.expenses) {
               // Filter the children expenses to only keep current month forward
               const filteredExpenses = expense.expenses.filter((weekExp) => {
                  // Don't filter future expenses
                  if (weekExp.status === "future" || weekExp.isFutureExpense) {
                     return true;
                  }

                  if (weekExp.status !== "projected") return true;

                  // Keep only if expense date is in current month or future (string comparison)
                  return weekExp.date >= currentMonthStartStr;
               });

               // If we have filtered expenses, update the expense object
               if (filteredExpenses.length > 0) {
                  expense.expenses = filteredExpenses;
                  // Update aggregated values
                  expense.amountDue = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountDue,
                     0
                  );
                  expense.amountAssigned = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountAssigned,
                     0
                  );
                  expense.amountSpent = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountSpent,
                     0
                  );
                  expense.amountAvailable = filteredExpenses.reduce(
                     (sum, week) => sum + week.amountAvailable,
                     0
                  );

                  // Keep this aggregated expense since we have valid children
                  return true;
               }

               // If no children expenses remain, filter out this aggregated expense
               return false;
            }

            // For non-aggregated expenses
            // Keep only if expense date is in current month or future (string comparison)
            return expense.date >= currentMonthStartStr;
         });
      },
      []
   );

   return {
      // State
      showStatusFilter,
      setShowStatusFilter,
      selectedStatuses,
      setSelectedStatuses,
      showAvailableFilter,
      setShowAvailableFilter,
      selectedAvailableFilter,
      setSelectedAvailableFilter,

      // Refs
      statusFilterRef,
      availableFilterRef,

      // Functions
      filterExpensesByStatus,
      filterExpensesByDate,
      filterProjectedExpensesByDate,

      // Constants
      STATUS_OPTIONS,
      AVAILABLE_FILTER_OPTIONS,
   };
}

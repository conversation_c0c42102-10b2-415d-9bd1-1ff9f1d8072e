import { useState, useEffect, useCallback } from "react";
import {
   addDays,
   addWeeks,
   addMonths,
   addYears,
   isBefore,
   startOfDay,
   subDays,
   subMonths,
   format,
} from "date-fns";

export function useExpectedExpenses({ dateRange, userExpenseSettings }) {
   const [expectedExpenses, setExpectedExpenses] = useState([]);

   const generateExpectedExpenses = useCallback(() => {
      if (!dateRange || !userExpenseSettings) {
         setExpectedExpenses([]);
         return;
      }

      const expected = [];

      // Get current date at the start of the day (midnight)
      const currentDate = startOfDay(new Date());
      // Get current month (start of current month)
      const currentMonthStart = new Date(currentDate);
      currentMonthStart.setDate(1);
      currentMonthStart.setHours(0, 0, 0, 0);
      const currentMonthStartStr = format(currentMonthStart, "yyyy-MM-dd");

      // For pay period view, look back one period to catch any expenses that might overlap
      let lookbackStart = dateRange.start;
      if (dateRange.viewType === "payPeriod") {
         if (userExpenseSettings.payPeriod === "weekly") {
            lookbackStart = subDays(dateRange.start, 7);
         } else if (userExpenseSettings.payPeriod === "biweekly") {
            lookbackStart = subDays(dateRange.start, 14);
         } else if (userExpenseSettings.payPeriod === "semimonthly") {
            lookbackStart = subDays(dateRange.start, 15);
         } else if (userExpenseSettings.payPeriod === "monthly") {
            lookbackStart = subDays(dateRange.start, 31);
         }
      }

      // Ensure lookbackStart is not before current month start for projected expenses
      if (lookbackStart < currentMonthStart) {
         lookbackStart = currentMonthStart;
      }

      // Generate expenses from recurring expenses
      const { recurringExpenses = [] } = userExpenseSettings;
      if (recurringExpenses && recurringExpenses.length > 0) {
         recurringExpenses.forEach((expense) => {
            if (!expense.enabled) return;

            let startingDate;
            if (expense.frequency === "weekly") {
               // For weekly expenses, look back one week from the start date
               startingDate = subDays(lookbackStart, 7);
               // Don't look before current month for projected expenses
               if (startingDate < currentMonthStart) {
                  startingDate = new Date(currentMonthStart);
               }
            } else if (dateRange.viewType === "payPeriod") {
               // For pay period view, start from our lookback date
               startingDate = new Date(lookbackStart);
            } else {
               // For other views, use the regular start date, but not before current month
               startingDate = new Date(dateRange.start);
               if (startingDate < currentMonthStart) {
                  startingDate = new Date(currentMonthStart);
               }
            }

            let curDate = new Date(startingDate);
            while (curDate <= dateRange.end) {
               let nextExpenseDate;
               let startDate = null;
               let endDate = null;

               if (expense.frequency === "weekly") {
                  // If current day is not the due day, find next occurrence
                  while (curDate.getDay() !== parseInt(expense.dueDay)) {
                     curDate = addDays(curDate, 1);
                  }
                  nextExpenseDate = curDate;
                  // Set start date to the due date
                  startDate = new Date(curDate);
                  // Set end date to 6 days after start date to create a full week
                  endDate = addDays(startDate, 6);

                  // Check if any part of the weekly period overlaps with our target period
                  const isOverlapping =
                     (startDate <= dateRange.end &&
                        endDate >= dateRange.start) ||
                     (dateRange.start <= endDate && dateRange.end >= startDate);

                  // For pay period view, also check if the expense date falls within the actual pay period
                  const isInPayPeriod =
                     dateRange.viewType === "payPeriod"
                        ? nextExpenseDate >= dateRange.start &&
                          nextExpenseDate <= dateRange.end
                        : true;

                  // Only add if expense date is in current month or later
                  const nextExpenseDateStr = format(
                     nextExpenseDate,
                     "yyyy-MM-dd"
                  );
                  const isInCurrentMonthOrFuture =
                     nextExpenseDateStr >= currentMonthStartStr;

                  if (
                     isOverlapping &&
                     isInPayPeriod &&
                     isInCurrentMonthOrFuture
                  ) {
                     expected.push({
                        date: format(nextExpenseDate, "yyyy-MM-dd"),
                        startDate:
                           expense.weeklyChargeType === "spread"
                              ? format(startDate, "yyyy-MM-dd")
                              : null,
                        endDate:
                           expense.weeklyChargeType === "spread"
                              ? format(endDate, "yyyy-MM-dd")
                              : null,
                        amountDue: expense.amount,
                        amountAssigned: 0,
                        amountSpent: 0,
                        amountAvailable: 0,
                        status: "projected",
                        description: expense.name,
                        category: expense.category || "Other",
                        frequency: expense.frequency,
                        weeklyChargeType:
                           expense.weeklyChargeType || "one-time",
                        recurringExpenseId: expense.id, // Add the recurring expense ID reference
                     });
                  }

                  curDate = addWeeks(curDate, 1);
               } else {
                  if (expense.frequency === "biweekly") {
                     while (curDate.getDay() !== parseInt(expense.dueDay)) {
                        curDate = addDays(curDate, 1);
                     }
                     nextExpenseDate = curDate;
                     curDate = addWeeks(curDate, 2);
                  } else if (expense.frequency === "monthly") {
                     const dueDayNum = parseInt(expense.dueDay);
                     // Get the last day of the current month
                     const lastDayOfMonth = new Date(
                        curDate.getFullYear(),
                        curDate.getMonth() + 1,
                        0
                     ).getDate();
                     // Use the last day of the month if dueDayNum exceeds it
                     const adjustedDueDay = Math.min(dueDayNum, lastDayOfMonth);
                     nextExpenseDate = new Date(
                        curDate.getFullYear(),
                        curDate.getMonth(),
                        adjustedDueDay
                     );
                     if (nextExpenseDate < curDate) {
                        // Get the last day of next month
                        const lastDayOfNextMonth = new Date(
                           curDate.getFullYear(),
                           curDate.getMonth() + 2,
                           0
                        ).getDate();
                        // Use the last day of next month if dueDayNum exceeds it
                        const adjustedDueDay = Math.min(
                           dueDayNum,
                           lastDayOfNextMonth
                        );
                        nextExpenseDate = new Date(
                           curDate.getFullYear(),
                           curDate.getMonth() + 1,
                           adjustedDueDay
                        );
                     }
                     curDate = addMonths(curDate, 1);
                  } else if (expense.frequency === "quarterly") {
                     const dueDayNum = parseInt(expense.dueDay);
                     // Get the last day of the current month
                     const lastDayOfMonth = new Date(
                        curDate.getFullYear(),
                        curDate.getMonth() + 1,
                        0
                     ).getDate();
                     // Use the last day of the month if dueDayNum exceeds it
                     const adjustedDueDay = Math.min(dueDayNum, lastDayOfMonth);
                     nextExpenseDate = new Date(
                        curDate.getFullYear(),
                        curDate.getMonth(),
                        adjustedDueDay
                     );
                     if (nextExpenseDate < curDate) {
                        // Get the last day of next quarter (3 months ahead)
                        const nextQuarterMonth = curDate.getMonth() + 3;
                        const nextQuarterYear =
                           nextQuarterMonth > 11
                              ? curDate.getFullYear() + 1
                              : curDate.getFullYear();
                        const adjustedQuarterMonth = nextQuarterMonth % 12;
                        const lastDayOfNextQuarter = new Date(
                           nextQuarterYear,
                           adjustedQuarterMonth + 1,
                           0
                        ).getDate();
                        const adjustedDueDay = Math.min(
                           dueDayNum,
                           lastDayOfNextQuarter
                        );
                        nextExpenseDate = new Date(
                           nextQuarterYear,
                           adjustedQuarterMonth,
                           adjustedDueDay
                        );
                     }
                     curDate = addMonths(curDate, 3);
                  } else if (expense.frequency === "annually") {
                     const dueMonthNum = parseInt(expense.dueDay); // Month (1-12)
                     const dueDayNum = parseInt(expense.dueMonth || "1"); // Day (1-31)

                     // Create date for this year
                     const thisYearDate = new Date(
                        curDate.getFullYear(),
                        dueMonthNum - 1, // JavaScript months are 0-indexed
                        dueDayNum
                     );

                     // Check if the date is valid (handles Feb 29 on non-leap years, etc.)
                     if (thisYearDate.getMonth() !== dueMonthNum - 1) {
                        // Date rolled over (e.g., Feb 29 became Mar 1), use last day of intended month
                        const lastDayOfMonth = new Date(
                           curDate.getFullYear(),
                           dueMonthNum,
                           0
                        ).getDate();
                        nextExpenseDate = new Date(
                           curDate.getFullYear(),
                           dueMonthNum - 1,
                           Math.min(dueDayNum, lastDayOfMonth)
                        );
                     } else {
                        nextExpenseDate = thisYearDate;
                     }

                     // If the date has already passed this year, use next year
                     if (nextExpenseDate < curDate) {
                        const nextYearDate = new Date(
                           curDate.getFullYear() + 1,
                           dueMonthNum - 1,
                           dueDayNum
                        );

                        // Check if the date is valid for next year
                        if (nextYearDate.getMonth() !== dueMonthNum - 1) {
                           const lastDayOfMonth = new Date(
                              curDate.getFullYear() + 1,
                              dueMonthNum,
                              0
                           ).getDate();
                           nextExpenseDate = new Date(
                              curDate.getFullYear() + 1,
                              dueMonthNum - 1,
                              Math.min(dueDayNum, lastDayOfMonth)
                           );
                        } else {
                           nextExpenseDate = nextYearDate;
                        }
                     }

                     // Move to next year for the next iteration
                     curDate = addYears(curDate, 1);
                  }

                  // For pay period view, only add if the expense falls within the actual pay period
                  const isInPayPeriod =
                     dateRange.viewType === "payPeriod"
                        ? nextExpenseDate >= dateRange.start &&
                          nextExpenseDate <= dateRange.end
                        : nextExpenseDate <= dateRange.end;

                  // Only add if expense date is in current month or later
                  const nextExpenseDateStr = format(
                     nextExpenseDate,
                     "yyyy-MM-dd"
                  );
                  const isInCurrentMonthOrFuture =
                     nextExpenseDateStr >= currentMonthStartStr;

                  if (isInPayPeriod && isInCurrentMonthOrFuture) {
                     expected.push({
                        date: format(nextExpenseDate, "yyyy-MM-dd"),
                        startDate: startDate
                           ? format(startDate, "yyyy-MM-dd")
                           : null,
                        endDate: endDate ? format(endDate, "yyyy-MM-dd") : null,
                        amountDue: expense.amount,
                        amountAssigned: 0,
                        amountSpent: 0,
                        amountAvailable: 0,
                        status: "projected",
                        description: expense.name,
                        category: expense.category,
                        frequency: expense.frequency || "weekly",
                        recurringExpenseId: expense.id, // Add the recurring expense ID reference
                     });
                  }
               }
            }
         });
      }

      // Generate expenses from debts
      if (userExpenseSettings.debts && userExpenseSettings.debts.length > 0) {
         userExpenseSettings.debts.forEach((debt) => {
            // Skip inactive debts
            if (debt.active === false) return;

            // Skip debts with zero or negative balance
            if (debt.balance <= 0) return;

            // Get due date for the current month
            const dueDayNum = parseInt(debt.dueDate);
            if (isNaN(dueDayNum)) return;

            // Get the current month for reference
            const currentMonth = currentDate.getMonth();
            const currentYear = currentDate.getFullYear();

            // Create a date for this month's due date
            const lastDayOfMonth = new Date(
               currentYear,
               currentMonth + 1,
               0
            ).getDate();
            const adjustedDueDay = Math.min(dueDayNum, lastDayOfMonth);
            let nextDueDate = new Date(
               currentYear,
               currentMonth,
               adjustedDueDay
            );

            // If the due date is before the current month start, use next month
            // This ensures we show debt payments from the current month forward
            if (nextDueDate < currentMonthStart) {
               const nextMonth = currentMonth + 1;
               const nextMonthYear =
                  nextMonth > 11 ? currentYear + 1 : currentYear;
               const lastDayOfNextMonth = new Date(
                  nextMonthYear,
                  (nextMonth % 12) + 1,
                  0
               ).getDate();
               const nextMonthAdjustedDueDay = Math.min(
                  dueDayNum,
                  lastDayOfNextMonth
               );
               nextDueDate = new Date(
                  nextMonthYear,
                  nextMonth % 12,
                  nextMonthAdjustedDueDay
               );
            }

            // Check if we need to go further into the future for the date range
            let debtDueDate = nextDueDate;

            // Keep adding months until we're within our date range or go past the end
            while (debtDueDate < dateRange.start) {
               const nextMonth = debtDueDate.getMonth() + 1;
               const nextMonthYear =
                  nextMonth > 11
                     ? debtDueDate.getFullYear() + 1
                     : debtDueDate.getFullYear();
               const lastDayOfNextMonth = new Date(
                  nextMonthYear,
                  (nextMonth % 12) + 1,
                  0
               ).getDate();
               const nextMonthAdjustedDueDay = Math.min(
                  dueDayNum,
                  lastDayOfNextMonth
               );
               debtDueDate = new Date(
                  nextMonthYear,
                  nextMonth % 12,
                  nextMonthAdjustedDueDay
               );
            }

            // Add monthly debt payments that fall within our date range
            while (debtDueDate <= dateRange.end) {
               // For pay period view, only add if the expense falls within the actual pay period
               const isInPayPeriod =
                  dateRange.viewType === "payPeriod"
                     ? debtDueDate >= dateRange.start &&
                       debtDueDate <= dateRange.end
                     : debtDueDate <= dateRange.end;

               // Only add if expense date is in current month or later
               const debtDueDateStr = format(debtDueDate, "yyyy-MM-dd");
               const isInCurrentMonthOrFuture =
                  debtDueDateStr >= currentMonthStartStr;

               if (isInPayPeriod && isInCurrentMonthOrFuture) {
                  expected.push({
                     date: format(debtDueDate, "yyyy-MM-dd"),
                     amountDue: debt.minimumPayment,
                     amountAssigned: 0,
                     amountSpent: 0,
                     amountAvailable: 0,
                     status: "projected",
                     description: `${debt.lender} - Payment`,
                     category: "Debt Payment",
                     frequency: "monthly",
                     // Use a special identifier for debt payments
                     debtId: debt._id,
                     isDebtPayment: true,
                  });
               }

               // Move to the next month
               const nextMonth = debtDueDate.getMonth() + 1;
               const nextMonthYear =
                  nextMonth > 11
                     ? debtDueDate.getFullYear() + 1
                     : debtDueDate.getFullYear();
               const lastDayOfNextMonth = new Date(
                  nextMonthYear,
                  (nextMonth % 12) + 1,
                  0
               ).getDate();
               const nextMonthAdjustedDueDay = Math.min(
                  dueDayNum,
                  lastDayOfNextMonth
               );
               debtDueDate = new Date(
                  nextMonthYear,
                  nextMonth % 12,
                  nextMonthAdjustedDueDay
               );
            }
         });
      }

      // Set the expected expenses in state
      setExpectedExpenses(expected);
   }, [dateRange, userExpenseSettings]);

   // Effect to generate expected expenses when settings or date range changes
   useEffect(() => {
      if (dateRange && userExpenseSettings) {
         generateExpectedExpenses();
      }
   }, [dateRange, userExpenseSettings, generateExpectedExpenses]);

   return {
      expectedExpenses,
      generateExpectedExpenses,
   };
}

import { useState } from "react";
import {
   evaluateExpression,
   determineExpenseStatus,
} from "../lib/utils/expenseUtils";
import useBalanceStore from "../lib/stores/balanceStore";

export function useExpenseEditing() {
   const [editingAssigned, setEditingAssigned] = useState(null);
   const [editingValue, setEditingValue] = useState("");
   const [editingDue, setEditingDue] = useState(null);
   const [editingDueValue, setEditingDueValue] = useState("");
   const [isProcessing, setIsProcessing] = useState(false);

   const handleAssignedClick = (expense) => {
      // Allow editing for scheduled, funded, paid, late, and overpaid expenses
      if (
         expense.status !== "scheduled" &&
         expense.status !== "funded" &&
         expense.status !== "paid" &&
         expense.status !== "late" &&
         expense.status !== "overpaid" &&
         expense.status !== "underbudget"
      )
         return;

      setEditingAssigned(expense._id);
      // Convert to string and remove any formatting
      setEditingValue(
         expense.amountAssigned.toString().replace(/[^0-9.+-]/g, "")
      );
   };

   const handleAssignedChange = (e, updatedExpense = null) => {
      // Check if e is an event or a numeric difference
      if (e && e.target && e.target.value !== undefined) {
         // It's an event object
         const value = e.target.value.replace(/[^0-9.+-]/g, "");
         setEditingValue(value);
      } else if (typeof e === "number") {
         // Store updates are handled directly in individual components
      }
   };

   const handleAssignedBlur = async (
      expense,
      onExpensesChange,
      setFutureExpenses,
      setShowAvailableDropdown
   ) => {
      if (!editingAssigned || editingValue === "") {
         setEditingAssigned(null);
         setEditingValue("");
         return;
      }

      try {
         // Evaluate the expression
         const newValue = evaluateExpression(
            editingValue,
            expense.amountAssigned || 0
         );

         // Make sure newValue is a valid number and not negative
         if (isNaN(newValue) || newValue < 0) {
            alert("Please enter a valid amount");
            setEditingAssigned(null);
            setEditingValue("");
            return;
         }

         // Calculate the difference for updating the balance
         const oldValue = expense.amountAssigned || 0;
         const difference = Number((newValue - oldValue).toFixed(2));

         // Create updated expense object with new amounts
         const updatedExpense = {
            ...expense,
            amountAssigned: newValue,
            amountAvailable: Number(
               (newValue + (expense.amountSpent || 0)).toFixed(2)
            ),
            status: expense.isFutureExpense
               ? "future"
               : determineExpenseStatus(
                    expense.amountSpent,
                    newValue,
                    expense.amountDue
                 ),
         };

         // If it's a future expense, update the futureExpenses state
         if (
            updatedExpense.isFutureExpense ||
            updatedExpense.status === "future"
         ) {
            setFutureExpenses((prevFutureExpenses) =>
               prevFutureExpenses.map((exp) =>
                  exp._id === updatedExpense._id ? updatedExpense : exp
               )
            );
         }

         // Optimistically update the local state
         onExpensesChange((prevExpenses) =>
            prevExpenses.map((e) =>
               e._id === expense._id ? updatedExpense : e
            )
         );

         // Close the dropdown
         setShowAvailableDropdown(null);

         // Update store with the assigned amount change
         const { updateAssignedAmount } = useBalanceStore.getState();
         updateAssignedAmount(difference);

         console.log("Updating expense:", {
            expenseId: expense._id,
            currentAssigned: oldValue,
            newAssigned: newValue,
            difference,
            newAvailable: updatedExpense.amountAvailable,
         });

         // Make the API call
         const response = await fetch(`/api/expenses/${expense._id}`, {
            method: "PUT",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({
               amountAssigned: newValue,
               amountAvailable: updatedExpense.amountAvailable,
               updateUserAssigned: difference,
               status: updatedExpense.status,
            }),
         });

         const responseData = await response.json();

         if (!response.ok) {
            console.error("Error updating expense:", {
               status: response.status,
               statusText: response.statusText,
               data: responseData,
            });
            throw new Error(responseData.error || "Failed to update expense");
         }

         console.log("Expense updated successfully:", responseData);

         // Get the server response but don't update state again unless there's a difference
         if (JSON.stringify(responseData) !== JSON.stringify(updatedExpense)) {
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((e) =>
                  e._id === expense._id ? responseData : e
               )
            );
         }
      } catch (error) {
         console.error("Error in handleAssignedBlur:", error);
         // Revert the optimistic update on error
         onExpensesChange((prevExpenses) =>
            prevExpenses.map((e) => (e._id === expense._id ? expense : e))
         );

         // Note: We intentionally do NOT revert the Zustand store
         // The store updates represent the user's intent and should persist
         // even if the database update fails (user can retry later)

         alert(error.message || "Failed to update expense. Please try again.");
      } finally {
         setEditingAssigned(null);
         setEditingValue("");
      }
   };

   const handleAssignedKeyDown = (
      e,
      expense,
      onExpensesChange,
      onRefreshBalances,
      setFutureExpenses,
      setShowAvailableDropdown
   ) => {
      if (e.key === "Enter") {
         e.preventDefault();
         handleAssignedBlur(
            expense,
            onExpensesChange,
            onRefreshBalances,
            setFutureExpenses,
            setShowAvailableDropdown
         );
      } else if (e.key === "Escape") {
         setEditingAssigned(null);
         setEditingValue("");
      }
   };

   const handleDueClick = (expense) => {
      // Allow editing for scheduled, funded, paid, late, and overpaid expenses
      if (
         expense.status !== "scheduled" &&
         expense.status !== "funded" &&
         expense.status !== "paid" &&
         expense.status !== "late" &&
         expense.status !== "overpaid" &&
         expense.status !== "underbudget"
      )
         return;

      setEditingDue(expense._id);
      setEditingDueValue(expense.amountDue.toString());
   };

   const handleDueChange = (e) => {
      setEditingDueValue(e.target.value);
   };

   const handleDueKeyDown = (e, expense, onExpensesChange, setError) => {
      if (e.key === "Enter") {
         e.preventDefault();
         handleDueBlur(expense, onExpensesChange, setError);
      } else if (e.key === "Escape") {
         e.preventDefault();
         setEditingDue(null);
         setEditingDueValue("");
      }
   };

   const handleDueBlur = async (expense, onExpensesChange, setError) => {
      // Don't close if there's no value yet
      if (!editingDueValue) {
         return;
      }

      try {
         const newAmount = parseFloat(editingDueValue);
         if (isNaN(newAmount) || newAmount < 0) {
            setError("Please enter a valid amount");
            return;
         }

         const oldAmount = expense.amountDue || 0;
         // Only save if the amount has changed
         if (newAmount !== oldAmount) {
            // Update the single expense
            const response = await fetch(`/api/expenses/${expense._id}`, {
               method: "PUT",
               headers: {
                  "Content-Type": "application/json",
               },
               body: JSON.stringify({
                  amountDue: Number(newAmount.toFixed(2)),
               }),
            });

            if (!response.ok) {
               throw new Error("Failed to update amount due");
            }

            // Get the updated expense and update local state
            const updatedExpense = await response.json();
            onExpensesChange((prevExpenses) =>
               prevExpenses.map((e) =>
                  e._id === expense._id ? updatedExpense : e
               )
            );

            // Only clear the editing state after successful update
            setEditingDue(null);
            setEditingDueValue("");
         } else {
            // If no change, just clear the editing state
            setEditingDue(null);
            setEditingDueValue("");
         }
      } catch (error) {
         console.error("Error updating amount due:", error);
         setError("Failed to update amount due");
      }
   };

   return {
      editingAssigned,
      editingValue,
      editingDue,
      editingDueValue,
      isProcessing,
      setIsProcessing,
      handleAssignedClick,
      handleAssignedChange,
      handleAssignedBlur,
      handleAssignedKeyDown,
      handleDueClick,
      handleDueChange,
      handleDueKeyDown,
      handleDueBlur,
   };
}

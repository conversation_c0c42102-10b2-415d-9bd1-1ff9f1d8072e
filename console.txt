SERVER] Processing expense data: {expenses: 43, expectedExpenses: 0, futureExpenses: 0, weeklyExpected: 0}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 0, filtered: 0, weekly: 0}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 43, weeklyExpenses: 14, weeklyProjected: 0}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 5}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 0}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 4, childStatuses: Array(4), hasProjected: false}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 5, childStatuses: Array(5), hasProjected: false}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 5, childStatuses: Array(5), hasProjected: false}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 4, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 4, visibleChildren: 4, hiddenChildren: 0, visibleChildStatuses: Array(4), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 4}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 5, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 5, visibleChildren: 5, hiddenChildren: 0, visibleChildStatuses: Array(5), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 5}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 5, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 5, visibleChildren: 5, hiddenChildren: 0, visibleChildStatuses: Array(5), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 5}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 32, hiddenExpenses: 0, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 26, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 0, futureExpenses: 0, weeklyExpected: 0}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 0, filtered: 0, weekly: 0}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 43, weeklyExpenses: 14, weeklyProjected: 0}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 5}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 0}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 4, childStatuses: Array(4), hasProjected: false}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 5, childStatuses: Array(5), hasProjected: false}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 5, childStatuses: Array(5), hasProjected: false}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 4, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 4, visibleChildren: 4, hiddenChildren: 0, visibleChildStatuses: Array(4), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 4}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 5, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 5, visibleChildren: 5, hiddenChildren: 0, visibleChildStatuses: Array(5), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 5}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 5, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 5, visibleChildren: 5, hiddenChildren: 0, visibleChildStatuses: Array(5), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 5}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 32, hiddenExpenses: 0, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 26, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 31, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 31, filtered: 31, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 74, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 50, hiddenExpenses: 1, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 44, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 31, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 31, filtered: 31, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 74, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 50, hiddenExpenses: 1, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 44, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 31, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 31, filtered: 31, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 74, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 50, hiddenExpenses: 1, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 44, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 31, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 31, filtered: 31, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 74, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 50, hiddenExpenses: 1, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 44, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 31, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 31, filtered: 31, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 74, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 50, hiddenExpenses: 1, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 44, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 31, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 31, filtered: 31, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 74, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 50, hiddenExpenses: 1, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 44, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
expenseDataProcessing.js:15 🔍 [SERVER] Processing expense data: {expenses: 43, expectedExpenses: 37, futureExpenses: 0, weeklyExpected: 12}
expenseDataProcessing.js:29 📋 [SERVER] Sample weekly expected: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Water', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Eating Out', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Spotify', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Internet', frequency: 'monthly', status: 'projected', date: '2025-07-05'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Electric', frequency: 'monthly', status: 'projected', date: '2025-07-25'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Netflix', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gym', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Car Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-27'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gymnastics', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Natural Gas', frequency: 'monthly', status: 'projected', date: '2025-07-29'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Groceries', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Justin Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Siobhan Fun', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-07'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-14'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Gas', frequency: 'weekly', status: 'projected', date: '2025-07-28'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Kids Misc', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Pets', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Google Drive', frequency: 'monthly', status: 'projected', date: '2025-07-21'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Health Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Dental Insurance', frequency: 'monthly', status: 'projected', date: '2025-07-15'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Medication', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Misc Home', frequency: 'monthly', status: 'projected', date: '2025-07-30'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Nintendo', frequency: 'monthly', status: 'projected', date: '2025-07-06'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Flex Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-13'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Discover - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Sofi - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-20'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Polygon Digital - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-11'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Apple Card - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-31'}
expenseDataProcessing.js:51 🧪 [SERVER] Testing projected expense: {description: 'Guild Mortgage - Payment', frequency: 'monthly', status: 'projected', date: '2025-07-01'}
expenseDataProcessing.js:111 ✅ [SERVER] Filtered projected expenses: {original: 37, filtered: 37, weekly: 12}
expenseDataProcessing.js:137 📊 [SERVER] Before aggregation: {totalExpenses: 80, weeklyExpenses: 26, weeklyProjected: 12}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'underpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: gas
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'underpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: eating out
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:173 🆕 [SERVER] Created new weekly group: groceries
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 1}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 2}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 3}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'overpaid', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'underpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'underpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'overpaid', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'overpaid', groupExpenseCount: 4}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 5}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'funded', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'funded', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'funded', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'overpaid', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'overpaid', groupExpenseCount: 6}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 7}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'scheduled', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'scheduled', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'scheduled', groupExpenseCount: 8}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Eating Out', status: 'projected', frequency: 'weekly', key: 'eating out'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'eating out', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Groceries', status: 'projected', frequency: 'weekly', key: 'groceries'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'groceries', expenseStatus: 'projected', groupExpenseCount: 9}
expenseDataProcessing.js:151 🔧 [SERVER] Processing weekly expense for aggregation: {description: 'Gas', status: 'projected', frequency: 'weekly', key: 'gas'}
expenseDataProcessing.js:179 ➕ [SERVER] Added expense to group: {groupKey: 'gas', expenseStatus: 'projected', groupExpenseCount: 8}
expenseDataProcessing.js:207 🔄 [SERVER] After aggregation: {weeklyGroups: 3, groupsWithProjected: 3}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Gas', status: 'funded', childCount: 8, childStatuses: Array(8), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Eating Out', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:216 📋 [SERVER] Aggregated group details: {description: 'Groceries', status: 'scheduled', childCount: 9, childStatuses: Array(9), hasProjected: true}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Gas', childCount: 8, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Gas', totalChildren: 8, visibleChildren: 8, hiddenChildren: 0, visibleChildStatuses: Array(8), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Gas', visibleChildCount: 8}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Eating Out', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Eating Out', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Eating Out', visibleChildCount: 9}
expenseDataProcessing.js:234 👀 [SERVER] Processing aggregated expense for visibility: {description: 'Groceries', childCount: 9, isProjectionHiddenFunction: 'function'}
expenseDataProcessing.js:251 🔍 [SERVER] Visibility check results: {description: 'Groceries', totalChildren: 9, visibleChildren: 9, hiddenChildren: 0, visibleChildStatuses: Array(9), …}
expenseDataProcessing.js:285 ✅ [SERVER] Added visible group: {description: 'Groceries', visibleChildCount: 9}
expenseDataProcessing.js:315 👁️ [SERVER] Final visibility: {visibleExpenses: 55, hiddenExpenses: 2, visibleWeekly: 3}
expenseDataProcessing.js:334 📦 [SERVER] Final organized: {oneoff: 3, weekly: 3, monthly: 49, annual: 0}
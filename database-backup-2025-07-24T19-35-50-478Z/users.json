[{"_id": "6764aeeccfd30a63a4a45566", "name": "<PERSON>", "email": "<EMAIL>", "createdAt": "2024-12-19T23:40:28.661Z", "__v": 387, "recurringExpenses": [{"id": 1734662038389, "name": "Rent", "amount": 2824.81, "frequency": "monthly", "dueDay": "1", "enabled": false, "weeklyChargeType": "one-time", "_id": "68388165f61cf7ee0b96c32a"}, {"id": 1734662534886, "name": "Water", "amount": 146, "frequency": "monthly", "dueDay": "27", "dueMonth": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "6772dcdc793842c671ac7876"}, {"id": 1734662544797, "name": "Eating Out", "amount": 50, "frequency": "weekly", "dueDay": "1", "enabled": true, "weeklyChargeType": "spread", "_id": "6764d99046edc0a25c646937"}, {"id": 1734836492078, "name": "Spotify", "amount": 18.39, "frequency": "monthly", "dueDay": "6", "enabled": true, "weeklyChargeType": "one-time", "_id": "681a1058c6b0e0e12bf496a6"}, {"id": 1734836506748, "name": "Renters Insurance", "amount": 22.92, "frequency": "monthly", "dueDay": "11", "enabled": false, "weeklyChargeType": "one-time", "_id": "6767817c373df8d462d05cb8"}, {"id": 1734836530312, "name": "Internet", "amount": 105.16, "frequency": "monthly", "dueDay": "5", "enabled": true, "weeklyChargeType": "one-time", "_id": "6876d24d6132a1cf22238c31"}, {"id": 1734836541484, "name": "Electric", "amount": 400, "frequency": "monthly", "dueDay": "25", "enabled": true, "weeklyChargeType": "one-time", "_id": "6880e725b33c41fd569014e0"}, {"id": 1734836554715, "name": "Netflix", "amount": 27.05, "frequency": "monthly", "dueDay": "11", "enabled": true, "weeklyChargeType": "one-time", "_id": "6767817c373df8d462d05cbc"}, {"id": 1734836565858, "name": "Gym", "amount": 43.25, "frequency": "monthly", "dueDay": "21", "enabled": true, "weeklyChargeType": "one-time", "_id": "680fc01f0576723bec31b0db"}, {"id": 1734836589455, "name": "Car Insurance", "amount": 107.28, "frequency": "monthly", "dueDay": "27", "enabled": true, "weeklyChargeType": "one-time", "_id": "6859cb1e171b6adbc9cd5d2b"}, {"id": 1734836603983, "name": "Gymnastics", "amount": 240, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "67c3a3fb539603509c42e8d8"}, {"id": 1734836730046, "name": "Natural Gas", "amount": 132.32, "frequency": "monthly", "dueDay": "29", "enabled": true, "weeklyChargeType": "one-time", "_id": "680fc0b2d91980207bbedf5b"}, {"id": 1734836742711, "name": "Daycare", "amount": 525, "frequency": "monthly", "dueDay": "28", "enabled": false, "weeklyChargeType": "one-time", "_id": "67678206373df8d462d05d4c"}, {"id": 1734836840323, "name": "Groceries", "amount": 250, "frequency": "weekly", "dueDay": "1", "enabled": true, "weeklyChargeType": "spread", "_id": "67ffafb43db91ed9c5ffee92"}, {"id": 1734841555906, "name": "ROTH", "amount": 60, "frequency": "weekly", "dueDay": "2", "enabled": false, "weeklyChargeType": "one-time", "_id": "676794fc6ca4be3da946b7eb"}, {"id": 1734841565038, "name": "Crypto", "amount": 10, "frequency": "weekly", "dueDay": "5", "enabled": false, "weeklyChargeType": "one-time", "_id": "676794fc6ca4be3da946b7ec"}, {"id": 1734841581594, "name": "<PERSON> 429", "amount": 20, "frequency": "weekly", "dueDay": "2", "enabled": false, "weeklyChargeType": "one-time", "_id": "676794fc6ca4be3da946b7ed"}, {"id": 1734841596157, "name": "Chandler 429", "amount": 20, "frequency": "weekly", "dueDay": "2", "enabled": false, "weeklyChargeType": "one-time", "_id": "676794fc6ca4be3da946b7ee"}, {"id": 1734841926686, "name": "<PERSON>", "amount": 0, "frequency": "monthly", "dueDay": "31", "enabled": true, "weeklyChargeType": "one-time", "_id": "683881ce745dacd41c816f4b"}, {"id": 1734841935993, "name": "<PERSON><PERSON><PERSON>", "amount": 0, "frequency": "monthly", "dueDay": "31", "enabled": true, "weeklyChargeType": "one-time", "_id": "683881c7745dacd41c816ef8"}, {"id": 1734842589091, "name": "Charging", "amount": 15, "frequency": "weekly", "dueDay": "1", "enabled": false, "weeklyChargeType": "spread", "_id": "67fd70d0c21e7a1f12dc85e7"}, {"id": 1734842711824, "name": "Gas", "amount": 80, "frequency": "weekly", "dueDay": "1", "enabled": true, "weeklyChargeType": "spread", "_id": "68826cdbf86f4d0cfc5bcbe0"}, {"id": 1735016086168, "name": "Kids Misc", "amount": 0, "frequency": "monthly", "dueDay": "31", "enabled": true, "weeklyChargeType": "one-time", "_id": "6876ea3f5812b6c5b3d3cd36"}, {"id": 1735308415957, "name": "Tolls", "amount": 40, "frequency": "weekly", "dueDay": "1", "enabled": false, "weeklyChargeType": "spread", "_id": "676eb47ff01ddb05b4ab31a9"}, {"id": 1735308454260, "name": "Home Improvement", "amount": 100, "frequency": "monthly", "dueDay": "31", "enabled": false, "weeklyChargeType": "one-time", "_id": "676eb4a6f01ddb05b4ab32d8"}, {"id": 1735308966101, "name": "Pets", "amount": 100, "frequency": "monthly", "dueDay": "31", "enabled": true, "weeklyChargeType": "one-time", "_id": "676eb6a6f01ddb05b4ab439e"}, {"id": 1735309223518, "name": "Google Drive", "amount": 21.31, "frequency": "monthly", "dueDay": "21", "enabled": true, "weeklyChargeType": "one-time", "_id": "685b05c1bc3430743cfce978"}, {"id": 1735309463068, "name": "Bestbuy CC", "amount": 100, "frequency": "monthly", "dueDay": "23", "enabled": false, "weeklyChargeType": "one-time", "_id": "676eb897f01ddb05b4ab4dcf"}, {"id": 1735309636101, "name": "Piano", "amount": 100, "frequency": "monthly", "dueDay": "1", "enabled": false, "weeklyChargeType": "one-time", "_id": "676eb944f01ddb05b4ab5024"}, {"id": 1735752127638, "name": "Health Insurance", "amount": 114.66, "frequency": "monthly", "dueDay": "31", "enabled": true, "weeklyChargeType": "one-time", "_id": "67d83b2d48edb1291bc01b1e"}, {"id": 1735752141968, "name": "Dental Insurance", "amount": 47.96, "frequency": "monthly", "dueDay": "15", "enabled": true, "weeklyChargeType": "one-time", "_id": "67ffac04f3fbb71c9ef16aaf"}, {"id": 1735757426773, "name": "Homeschool", "amount": 75, "frequency": "monthly", "dueDay": "31", "enabled": false, "weeklyChargeType": "one-time", "_id": "683881b5745dacd41c816e78"}, {"id": 1737000894823, "name": "Prime Payment", "amount": 0, "frequency": "monthly", "dueDay": "7", "enabled": false, "weeklyChargeType": "one-time", "_id": "683b85503a6de50053eb08ae"}, {"id": 1737039834652, "name": "Apple Card Payment", "amount": 0, "frequency": "monthly", "dueDay": "31", "enabled": false, "weeklyChargeType": "one-time", "_id": "678920013b535b9c9ea607ea"}, {"id": 1741784849608, "name": "Medication", "amount": 22, "frequency": "monthly", "dueDay": "11", "enabled": true, "weeklyChargeType": "one-time", "_id": "67d187112fffe5614238d872"}, {"id": 1747475768916, "name": "Misc Home", "amount": 0, "frequency": "monthly", "dueDay": "30", "enabled": true, "weeklyChargeType": "one-time", "_id": "682f20996c76b5160a8a37bb"}, {"id": 1750863147684, "name": "Amazon Prime", "amount": 150.47, "frequency": "annually", "dueDay": "6", "dueMonth": "27", "enabled": true, "weeklyChargeType": "one-time", "_id": "685c0d2b872d7b640babeab8"}, {"id": 1751993668153, "name": "Nintendo", "amount": 4.24, "frequency": "monthly", "dueDay": "6", "enabled": true, "weeklyChargeType": "one-time", "_id": "686d4d44d5e54213f25c5a75"}], "updatedAt": "2025-07-24T19:32:13.224Z", "totalAssigned": 83159.14, "totalIncome": 84881.48, "recurringIncomes": [{"description": "Premiere Roofing", "payAmount": 1816.56, "payPeriod": "weekly", "payDayOfWeek": "1", "payWeekDay": "Monday", "enabled": true, "_id": "676d74401bc7561cf2f6f0ac", "isMainIncome": false}], "mainIncomeId": "676d74401bc7561cf2f6f0ac", "accounts": [{"_id": *************, "name": "Checking", "bank": "Chase", "accountType": "cash", "balance": 2577.2, "active": true, "plaidItemId": "Bad6dLRb3KCBNZBPaVawTXk1QJmBOrt9Xdw3n"}], "preferences": {"showFutureExpenses": false, "cashFlowWarnings": {"enabled": true, "periodsAhead": 4}, "hideInactiveDebts": true}, "isAdmin": true, "onboardingComplete": true, "readyToAssign": 1722.34, "plaidConnectionsDisconnectedAt": "2025-07-14T23:09:31.622Z", "alertVisibility": "negative", "stripeCustomerId": "cus_ShmxvLjqPhhQua", "subscription": {"cancelAtPeriodEnd": false, "currentPeriodEnd": "2025-08-19T02:49:22.000Z", "currentPeriodStart": "2025-07-19T02:49:22.000Z", "id": "sub_1RmQpyBd5i4YsObuDRs060L1", "nextPlanId": null, "planId": "basic", "status": "active"}, "password": "$2a$12$1ru4c9rW8mszgd0gMp7NHuw/4vL4Ff/WJu.L6u/ZwDj5yTmgJJ8qC", "plaidItems": [{"itemId": "Bad6dLRb3KCBNZBPaVawTXk1QJmBOrt9Xdw3n", "accessToken": "access-production-48ec79e1-569d-4cc0-aa8b-0311e94b892a", "institutionId": "ins_56", "institutionName": "Chase", "status": "good", "linkedAccountId": *************, "accountType": "depository", "accountSubtype": "checking", "plaidAccountId": "3DbgbjeK0mHoeqoOB7BJib53XP5yyVIz41vM4", "name": "CHASE SECURE BANKING", "startDate": "2025-07-18T00:00:00.000Z", "_id": "687b07f8d21925eb1c0fd232", "cursor": "CAESJVlZcTNxUDBhTXZVQUJZQUtnamdvY0xPa3pQODZETkl6YU84T2IaDAj0k4fEBhDQ5+PoAiIMCPSTh8QGENDn4+gCKgwI9JOHxAYQ0Ofj6AI=", "lastSync": "2025-07-24T05:51:52.725Z"}]}, {"_id": "687b08d1111b41ebb6c81ce7", "name": "<PERSON>", "email": "<EMAIL>", "password": "$2a$10$tIhngdqTmXiySzY0A6x5r.akrELYGKESzSxoP3OgTkY2ItbTP6IFi", "isAdmin": true, "onboardingComplete": true, "mainIncomeId": "687b1871a6b7ca864f7ff4b7", "totalAssigned": 1931.33, "totalIncome": 16143.32, "readyToAssign": 14211.99, "preferences": {"showFutureExpenses": true, "cashFlowWarnings": {"enabled": true, "periodsAhead": 4}}, "subscription": {"planId": "basic", "cancelAtPeriodEnd": false, "currentPeriodEnd": "2025-08-19T05:06:25.000Z", "currentPeriodStart": "2025-07-19T05:06:25.000Z", "id": "sub_1RmSycBd5i4YsObu8up5gVgy", "status": "active", "nextPlanId": null}, "recurringExpenses": [{"id": 1, "name": "Mortgage", "amount": 1936, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b1871a6b7ca864f7ff4ba"}, {"id": 2, "name": "Gymnastics", "amount": 115, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b1871a6b7ca864f7ff4bb"}, {"id": 3, "name": "Dragonfly Martial Arts", "amount": 105, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b1871a6b7ca864f7ff4bc"}, {"id": 1752903272621, "name": "Spotify", "amount": 18.39, "frequency": "monthly", "dueDay": "15", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b2e68ed52f4c5df720b00"}, {"id": 1752903414859, "name": "Scoop Soldiers", "amount": 23.8, "frequency": "weekly", "dueDay": "4", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b2ef62fc2c97276d6a99d"}, {"id": 1752903596942, "name": "Daycare", "amount": 245, "frequency": "weekly", "dueDay": "2", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b2fac536b1910ebef6dde"}, {"id": 1752903632858, "name": "Internet", "amount": 101.31, "frequency": "monthly", "dueDay": "14", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b2fd0b660f8e9aecc81d2"}, {"id": 1752903706381, "name": "Sling TV", "amount": 85.51, "frequency": "monthly", "dueDay": "13", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b301aed52f4c5df720c21"}, {"id": 1752903943209, "name": "Hagerty Car Insurance", "amount": 103.79, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b310716c9faeadcbd1d3c"}, {"id": 1752903963970, "name": "Water Bill", "amount": 90.41, "frequency": "monthly", "dueDay": "9", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b311b16c9faeadcbd1d6b"}, {"id": 1752904185269, "name": "Health Insurance", "amount": 431.85, "frequency": "monthly", "dueDay": "7", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b31f9ed52f4c5df720f84"}, {"id": 1752904295656, "name": "Gym Station", "amount": 63, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b326716c9faeadcbd200a"}, {"id": 1752904435401, "name": "Electricity Bill", "amount": 173.03, "frequency": "monthly", "dueDay": "3", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b32f32fc2c97276d6ad68"}, {"id": 1752904461281, "name": "Dakboard", "amount": 10, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b330d2fc2c97276d6addb"}, {"id": *************, "name": "Subaru Car Payment", "amount": 500.8, "frequency": "monthly", "dueDay": "20", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b352c0365ebebce35675a"}, {"id": *************, "name": "Subaru Car Payment", "amount": 500.6, "frequency": "monthly", "dueDay": "20", "enabled": true, "weeklyChargeType": "one-time", "_id": "687b355aa1ab991d129b1b3d"}], "recurringIncomes": [{"description": "Accounting", "payAmount": 2845, "payPeriod": "semimonthly", "lastPaymentDate": null, "enabled": true, "_id": "687b1871a6b7ca864f7ff4b7"}], "createdAt": "2025-07-19T02:54:09.818Z", "accounts": [{"_id": 3, "name": "Personal Chase", "bank": "Chase", "accountType": "cash", "balance": 201.78, "active": true, "plaidItemId": "YdkxgQygYkIRy10X1Oo8T5X5NgLNMvhQAM3RP"}, {"_id": 4, "name": "Joint Checking", "bank": "Chase", "accountType": "cash", "balance": 14.***************, "active": true, "plaidItemId": "7d8A3BJneNT31Y5Rgq0vuKQk6wNqensQaJXNb"}], "updatedAt": "2025-07-24T08:18:29.546Z", "__v": 20, "stripeCustomerId": "cus_Shr6KCjj2WGjTc", "plaidItems": [{"itemId": "YdkxgQygYkIRy10X1Oo8T5X5NgLNMvhQAM3RP", "accessToken": "access-production-7f74f54a-7ce8-4bef-b81d-1496f829c2e3", "institutionId": "ins_56", "institutionName": "Chase", "status": "login_required", "linkedAccountId": 3, "accountType": "depository", "accountSubtype": "checking", "plaidAccountId": "77vn6Vr6Bvu43xgExykbCKDJ4rE00pSDaMjeB", "name": "PERSONAL CHECKING", "startDate": "2025-07-01T00:00:00.000Z", "_id": "687b285689316ed1e2e9d828", "cursor": "CAESJURlZ3Ywb1YwSmdVYTBNWWtNdzdWU09Lcm1tRUpEUlR6WTg5SjkaDAjZ0OzDBhD404yPAyIMCNnQ7MMGEPjTjI8DKgwI2dDswwYQ+NOMjwM=", "lastSync": "2025-07-19T05:08:44.257Z", "error": {"errorCode": "ITEM_LOGIN_REQUIRED", "errorMessage": "the login details of this item have changed (credentials, MFA, or required user action) and a user login is required to update this information. use Link's update mode to restore the item to a good state", "errorType": "ITEM_ERROR", "lastErrorDate": "2025-07-22T05:13:32.907Z"}}, {"itemId": "7d8A3BJneNT31Y5Rgq0vuKQk6wNqensQaJXNb", "accessToken": "access-production-13220b6c-99d1-4068-bff9-19eb71110dc3", "institutionId": "ins_56", "institutionName": "Chase", "status": "good", "linkedAccountId": 4, "accountType": "depository", "accountSubtype": "checking", "plaidAccountId": "EvygX8wAz1hp1JVwLrPNTYzOp6xrjMSJmqX95", "name": "JOINT CHECKING", "startDate": "2025-07-01T00:00:00.000Z", "_id": "687b2a1fa8e891e000ee09f5", "cursor": "CAESJVl2RVY0WUp4ejloeXhlcVYwTXpxY2I1YXpNTWpLWVV3ZHZubnEaDAjS2IfEBhDg9bW8ASIMCNLYh8QGEOD1tbwBKgwI0tiHxAYQ4PW1vAE=", "lastSync": "2025-07-24T08:18:29.545Z"}]}, {"_id": "687b0b9535ac0651fd8dffbb", "name": "Test Test", "email": "<EMAIL>", "password": "$2a$10$1PwchgcGctD1VhCkN58V5uiC.u6uxmKkSwZMttbvCLNf3tYk5Aj7C", "isAdmin": false, "onboardingComplete": false, "mainIncomeId": null, "totalAssigned": 0, "totalIncome": 0, "readyToAssign": 0, "preferences": {"showFutureExpenses": true, "cashFlowWarnings": {"enabled": true, "periodsAhead": 4}}, "subscription": {"planId": "free", "cancelAtPeriodEnd": false}, "recurringExpenses": [], "recurringIncomes": [], "createdAt": "2025-07-19T03:05:57.401Z", "accounts": [], "plaidItems": [], "updatedAt": "2025-07-19T03:05:57.401Z", "__v": 0}, {"_id": "687d0df81aac89c8c167f77f", "name": "<PERSON>", "email": "<EMAIL>", "password": "$2a$10$M0DZShAZjtcHYlNJTBFGW.dT0jwDm1/YQhjla/Srz7suyuOnQ8mdC", "isAdmin": false, "onboardingComplete": true, "totalAssigned": 0, "totalIncome": 1500, "readyToAssign": 1500, "preferences": {"showFutureExpenses": true, "cashFlowWarnings": {"enabled": true, "periodsAhead": 4}}, "subscription": {"planId": "free", "cancelAtPeriodEnd": false}, "recurringExpenses": [{"id": 1, "name": "Rent", "amount": 1200, "frequency": "monthly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687d2ee31aac89c8c16800d0"}, {"id": 2, "name": "Internet", "amount": 80, "frequency": "monthly", "dueDay": "15", "enabled": true, "weeklyChargeType": "one-time", "_id": "687d2ee31aac89c8c16800d1"}, {"id": 3, "name": "Gym Membership", "amount": 50, "frequency": "monthly", "dueDay": "25", "enabled": true, "weeklyChargeType": "one-time", "_id": "687d2ee31aac89c8c16800d2"}, {"id": 4, "name": "Phone Bill", "amount": 75, "frequency": "monthly", "dueDay": "5", "enabled": true, "weeklyChargeType": "one-time", "_id": "687d2ee31aac89c8c16800d3"}, {"id": 5, "name": "Groceries (One-time)", "amount": 150, "frequency": "weekly", "dueDay": "1", "enabled": true, "weeklyChargeType": "one-time", "_id": "687d2ee31aac89c8c16800d4"}, {"id": 6, "name": "Gas (Spread)", "amount": 200, "frequency": "weekly", "dueDay": "1", "enabled": true, "weeklyChargeType": "spread", "_id": "687d2ee31aac89c8c16800d5"}, {"id": 7, "name": "Insurance", "amount": 300, "frequency": "annually", "dueDay": "1", "dueMonth": "15", "enabled": true, "weeklyChargeType": "one-time", "_id": "687d2ee31aac89c8c16800d6"}], "recurringIncomes": [{"description": "Main", "payAmount": 1000, "payPeriod": "weekly", "payDayOfWeek": "1", "payWeekDay": "Monday", "lastPaymentDate": null, "enabled": true, "isMainIncome": true, "_id": "687d13141aac89c8c167f913"}, {"description": "Second", "payAmount": 500, "payPeriod": "monthly", "payDay": "1", "lastPaymentDate": null, "enabled": true, "isMainIncome": false, "_id": "687d15851aac89c8c167fa31"}], "createdAt": "2025-07-20T15:40:40.644Z", "accounts": [{"_id": 1, "name": "Checking", "bank": "Chase", "accountType": "cash", "balance": 1000, "active": true, "plaidItemId": null}, {"_id": 2, "name": "Savings", "bank": "Chase", "accountType": "cash", "balance": 500, "active": true, "plaidItemId": null}], "plaidItems": [], "updatedAt": "2025-07-21T14:01:51.379Z", "__v": 13, "mainIncomeId": "687d13141aac89c8c167f913"}]
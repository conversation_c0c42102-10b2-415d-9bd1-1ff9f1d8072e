[{"_id": "676dae811bc7561cf2f7f764", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-01T06:00:00.000Z", "description": "Rent", "amountDue": 1500, "amountAssigned": 1500, "amountSpent": -1500, "status": "paid", "createdAt": "2024-12-26T19:29:05.538Z", "updatedAt": "2025-07-23T15:04:36.064Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae811bc7561cf2f7f7ff", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-04T06:00:00.000Z", "description": "Spotify", "amountDue": 14.06, "amountAssigned": 18.39, "amountSpent": -18.39, "status": "overpaid", "createdAt": "2024-12-26T19:29:05.564Z", "updatedAt": "2025-07-23T15:10:34.776Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae811bc7561cf2f7f7a2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-13T06:00:00.000Z", "description": "Chase Flex", "amountDue": 609.42, "amountAssigned": 609.42, "amountSpent": -609.42, "status": "paid", "createdAt": "2024-12-26T19:29:05.551Z", "updatedAt": "2025-07-23T15:13:32.960Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae811bc7561cf2f7f7e0", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-01T06:00:00.000Z", "description": "Water", "amountDue": 161.77, "amountAssigned": 336.77, "amountSpent": -336.77, "status": "paid", "createdAt": "2024-12-26T19:29:05.560Z", "updatedAt": "2025-07-24T02:43:37.273Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae811bc7561cf2f7f7c1", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-07T06:00:00.000Z", "description": "Renters Insurance", "amountDue": 22.92, "amountAssigned": 22.92, "amountSpent": -22.92, "status": "paid", "createdAt": "2024-12-26T19:29:05.555Z", "updatedAt": "2025-07-23T15:09:22.694Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae821bc7561cf2f7f903", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-15T06:00:00.000Z", "description": "Internet", "amountDue": 105.16, "amountAssigned": 105.16, "amountSpent": -105.16, "status": "paid", "createdAt": "2024-12-26T19:29:06.374Z", "updatedAt": "2025-07-23T15:09:17.525Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae821bc7561cf2f7f941", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-17T06:00:00.000Z", "description": "Electric", "amountDue": 215.97, "amountAssigned": 215.97, "amountSpent": -215.97, "status": "paid", "createdAt": "2024-12-26T19:29:06.382Z", "updatedAt": "2025-07-24T01:26:40.835Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae821bc7561cf2f7f960", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "description": "Netflix", "amountDue": 21.64, "amountAssigned": 24.89, "amountSpent": -24.89, "status": "overpaid", "createdAt": "2024-12-26T19:29:06.388Z", "updatedAt": "2025-07-23T15:09:42.604Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae821bc7561cf2f7f922", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-21T06:00:00.000Z", "description": "Gym", "amountDue": 43.25, "amountAssigned": 54.08, "amountSpent": -54.08, "status": "overpaid", "createdAt": "2024-12-26T19:29:06.378Z", "updatedAt": "2025-07-24T02:43:38.939Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae821bc7561cf2f7f97f", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-25T06:00:00.000Z", "description": "Tesla", "amountDue": 584.84, "amountAssigned": 584.84, "amountSpent": -584.84, "status": "paid", "createdAt": "2024-12-26T19:29:06.391Z", "updatedAt": "2025-07-23T15:13:18.819Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7fa83", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-27T06:00:00.000Z", "description": "Natural Gas", "amountDue": 132.32, "amountAssigned": 132.32, "amountSpent": -132.32, "status": "paid", "createdAt": "2024-12-26T19:29:07.071Z", "updatedAt": "2025-07-23T15:41:37.262Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7faa2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "<PERSON>", "amountDue": 50, "amountAssigned": 489.92, "amountSpent": -489.92, "status": "overpaid", "createdAt": "2024-12-26T19:29:07.079Z", "updatedAt": "2025-07-24T02:43:45.533Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7fa64", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-27T06:00:00.000Z", "description": "Car Insurance", "amountDue": 259.11, "amountAssigned": 259.11, "amountSpent": -259.11, "status": "paid", "createdAt": "2024-12-26T19:29:07.067Z", "updatedAt": "2025-07-23T15:41:43.304Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7fac1", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-28T06:00:00.000Z", "description": "Gymnastics", "amountDue": 613, "amountAssigned": 1264.75, "amountSpent": -1264.75, "status": "overpaid", "createdAt": "2024-12-26T19:29:07.083Z", "updatedAt": "2025-07-24T02:43:41.621Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7fae0", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-28T06:00:00.000Z", "description": "Daycare", "amountDue": 525, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2024-12-26T19:29:07.090Z", "updatedAt": "2025-07-24T02:43:40.802Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7faff", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "amountDue": 50, "amountAssigned": 257.59, "amountSpent": -257.59, "status": "overpaid", "createdAt": "2024-12-26T19:29:07.096Z", "updatedAt": "2025-07-24T02:43:44.633Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae831bc7561cf2f7fbe4", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "Kids Misc", "amountDue": 100, "amountAssigned": 41.51, "amountSpent": -41.51, "status": "underpaid", "createdAt": "2024-12-26T19:29:07.791Z", "updatedAt": "2025-07-24T02:43:43.724Z", "__v": 0, "amountAvailable": 0, "frequency": "monthly"}, {"_id": "676dae841bc7561cf2f7fd61", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-03T06:00:00.000Z", "startDate": "2024-12-03T06:00:00.000Z", "endDate": "2024-12-09T06:00:00.000Z", "description": "Chandler 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "createdAt": "2024-12-26T19:29:08.351Z", "updatedAt": "2025-07-23T15:13:54.749Z", "__v": 0, "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676dae841bc7561cf2f7fd9f", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-06T06:00:00.000Z", "startDate": "2024-12-06T06:00:00.000Z", "endDate": "2024-12-12T06:00:00.000Z", "description": "Crypto", "amountDue": 10, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "createdAt": "2024-12-26T19:29:08.373Z", "updatedAt": "2025-07-24T02:43:33.176Z", "__v": 0, "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db3b91bc7561cf2f81e87", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-03T06:00:00.000Z", "startDate": "2024-12-03T06:00:00.000Z", "endDate": "2024-12-09T06:00:00.000Z", "description": "<PERSON> 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:51:21.987Z", "updatedAt": "2025-07-23T15:04:13.351Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db3b91bc7561cf2f81e88", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-10T06:00:00.000Z", "startDate": "2024-12-10T06:00:00.000Z", "endDate": "2024-12-16T06:00:00.000Z", "description": "<PERSON> 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:51:21.987Z", "updatedAt": "2025-07-23T15:09:59.236Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db3b91bc7561cf2f81e89", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-17T06:00:00.000Z", "startDate": "2024-12-17T06:00:00.000Z", "endDate": "2024-12-23T06:00:00.000Z", "description": "<PERSON> 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:51:21.987Z", "updatedAt": "2025-07-23T15:13:59.112Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db3b91bc7561cf2f81e8a", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-24T06:00:00.000Z", "startDate": "2024-12-24T06:00:00.000Z", "endDate": "2024-12-30T06:00:00.000Z", "description": "<PERSON> 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:51:21.988Z", "updatedAt": "2025-07-24T02:43:28.844Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db4c11bc7561cf2f825a2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-10T06:00:00.000Z", "startDate": "2024-12-10T06:00:00.000Z", "endDate": "2024-12-16T06:00:00.000Z", "description": "Chandler 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:55:45.035Z", "updatedAt": "2025-07-23T15:10:05.751Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db4c11bc7561cf2f825a3", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-17T06:00:00.000Z", "startDate": "2024-12-17T06:00:00.000Z", "endDate": "2024-12-23T06:00:00.000Z", "description": "Chandler 429", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:55:45.035Z", "updatedAt": "2025-07-23T15:13:54.956Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db4c11bc7561cf2f825a6", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-13T06:00:00.000Z", "startDate": "2024-12-13T06:00:00.000Z", "endDate": "2024-12-19T06:00:00.000Z", "description": "Crypto", "amountDue": 10, "amountAssigned": 10, "amountSpent": -10, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:55:45.035Z", "updatedAt": "2025-07-23T15:15:32.111Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676db4c11bc7561cf2f825a7", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-20T06:00:00.000Z", "startDate": "2024-12-20T06:00:00.000Z", "endDate": "2024-12-26T06:00:00.000Z", "description": "Crypto", "amountDue": 10, "amountAssigned": 10, "amountSpent": -10, "status": "paid", "__v": 0, "createdAt": "2024-12-26T19:55:45.035Z", "updatedAt": "2025-07-23T15:41:49.370Z", "amountAvailable": 0, "frequency": "weekly"}, {"_id": "676eb4a5f01ddb05b4ab3296", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "Home Improvement", "amountDue": 29.96, "amountAssigned": 29.96, "amountSpent": -29.96, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T14:07:33.833Z", "updatedAt": "2025-07-23T15:41:01.149Z", "__v": 0, "frequency": "monthly", "weeklyChargeType": "one-time"}, {"_id": "676eb4e7f01ddb05b4ab353d", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-25T00:00:00.000Z", "description": "Christmas", "amountDue": 869.43, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2024-12-27T14:08:39.661Z", "updatedAt": "2025-07-24T02:43:09.972Z", "__v": 0, "frequency": "oneoff"}, {"_id": "676eb6a5f01ddb05b4ab435a", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "Pets", "amountDue": 63.09, "amountAssigned": 63.09, "amountSpent": -63.09, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T14:16:05.710Z", "updatedAt": "2025-07-23T15:14:54.104Z", "__v": 0, "frequency": "monthly"}, {"_id": "676eb7a7f01ddb05b4ab4af1", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-26T06:00:00.000Z", "description": "Google Drive", "amountDue": 10.65, "amountAssigned": 10.65, "amountSpent": -10.65, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T14:20:23.065Z", "updatedAt": "2025-07-23T15:42:01.029Z", "__v": 0, "frequency": "monthly"}, {"_id": "676eb896f01ddb05b4ab4d87", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "description": "Bestbuy CC", "amountDue": 377.79, "amountAssigned": 377.79, "amountSpent": -377.79, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T14:24:22.671Z", "updatedAt": "2025-07-24T01:30:39.812Z", "__v": 0, "frequency": "monthly"}, {"_id": "676eb943f01ddb05b4ab4fda", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-01T06:00:00.000Z", "description": "Piano", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2024-12-27T14:27:15.672Z", "updatedAt": "2025-07-24T02:43:36.542Z", "__v": 0, "frequency": "monthly"}, {"_id": "676ec11df01ddb05b4ab8fb2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-10T06:00:00.000Z", "startDate": "2024-12-10T06:00:00.000Z", "endDate": "2024-12-16T06:00:00.000Z", "description": "ROTH", "amountDue": 60, "amountAssigned": 60, "amountSpent": -60, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:00:45.412Z", "updatedAt": "2025-07-23T15:09:32.531Z", "frequency": "weekly"}, {"_id": "676ec11df01ddb05b4ab8fb3", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-17T06:00:00.000Z", "startDate": "2024-12-17T06:00:00.000Z", "endDate": "2024-12-23T06:00:00.000Z", "description": "ROTH", "amountDue": 60, "amountAssigned": 60, "amountSpent": -60, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:00:45.412Z", "updatedAt": "2025-07-23T15:13:24.231Z", "frequency": "weekly"}, {"_id": "676ec11df01ddb05b4ab8fb4", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-24T06:00:00.000Z", "startDate": "2024-12-24T06:00:00.000Z", "endDate": "2024-12-30T06:00:00.000Z", "description": "ROTH", "amountDue": 60, "amountAssigned": 60, "amountSpent": -60, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:00:45.412Z", "updatedAt": "2025-07-23T15:42:07.033Z", "frequency": "weekly"}, {"_id": "676ec2fff01ddb05b4ab97f4", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-03T06:00:00.000Z", "startDate": "2024-12-03T06:00:00.000Z", "endDate": "2024-12-09T06:00:00.000Z", "description": "ROTH", "amountDue": 60, "amountAssigned": 60, "amountSpent": -60, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T15:08:47.259Z", "updatedAt": "2025-07-23T15:05:03.189Z", "__v": 0, "frequency": "weekly"}, {"_id": "676ec975f01ddb05b4abbba9", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-01T06:00:00.000Z", "description": "Rent", "amountDue": 1500, "amountAssigned": 1500, "amountSpent": -1500, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:30:04.681Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbaa", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-01T06:00:00.000Z", "description": "Water", "amountDue": 118.09, "amountAssigned": 118.09, "amountSpent": -118.09, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:30:01.985Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbab", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-01T06:00:00.000Z", "description": "Piano", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:25.055Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbad", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-07T06:00:00.000Z", "description": "Renters Insurance", "amountDue": 22.92, "amountAssigned": 22.92, "amountSpent": -22.92, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:35:53.669Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbaf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-15T06:00:00.000Z", "description": "<PERSON><PERSON>", "amountDue": 1212.55, "amountAssigned": 1212.55, "amountSpent": -1212.55, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:34:29.914Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbb0", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-15T06:00:00.000Z", "description": "Internet", "amountDue": 105.16, "amountAssigned": 105.16, "amountSpent": -105.16, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:34:42.269Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbb1", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-17T06:00:00.000Z", "description": "Electric", "amountDue": 217.53, "amountAssigned": 217.53, "amountSpent": -217.53, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:34:48.741Z", "frequency": "monthly", "weeklyChargeType": "one-time"}, {"_id": "676ec975f01ddb05b4abbbb2", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-21T06:00:00.000Z", "description": "Gym", "amountDue": 51, "amountAssigned": 96.29, "amountSpent": -96.29, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:37:39.376Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbb5", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-25T06:00:00.000Z", "description": "Tesla", "amountDue": 584.84, "amountAssigned": 600, "amountSpent": -600, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:38:50.732Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbb6", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-26T06:00:00.000Z", "description": "Google Drive", "amountDue": 10.65, "amountAssigned": 10.65, "amountSpent": -10.65, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:45:31.776Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbb7", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "description": "Car Insurance", "amountDue": 259.11, "amountAssigned": 259.11, "amountSpent": -259.11, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T01:37:20.578Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbb8", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "description": "Natural Gas", "amountDue": 220.35, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:29.329Z", "frequency": "monthly", "weeklyChargeType": "one-time"}, {"_id": "676ec975f01ddb05b4abbbb9", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-28T06:00:00.000Z", "description": "Gymnastics", "amountDue": 72.53, "amountAssigned": 309, "amountSpent": -309, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:30.325Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbbb", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "<PERSON>", "amountDue": 4.24, "amountAssigned": 188.43, "amountSpent": -188.43, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:38.551Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbbc", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "amountDue": 0, "amountAssigned": 156.53, "amountSpent": -156.53, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:37.543Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbbd", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "Kids Misc", "amountDue": 15.48, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:36.233Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbbe", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "Home Improvement", "amountDue": 19.38, "amountAssigned": 125, "amountSpent": -125, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:35.173Z", "frequency": "monthly"}, {"_id": "676ec975f01ddb05b4abbbbf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "Pets", "amountDue": 60, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "__v": 0, "createdAt": "2024-12-27T15:36:21.969Z", "updatedAt": "2025-07-24T02:44:33.916Z", "frequency": "monthly"}, {"_id": "676ecb09f01ddb05b4abc5b4", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-04T06:00:00.000Z", "description": "Spotify", "amountDue": 18.39, "amountAssigned": 36.78, "amountSpent": -36.78, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T15:43:05.333Z", "updatedAt": "2025-07-24T02:44:25.858Z", "__v": 0, "frequency": "monthly"}, {"_id": "676ecd9ff01ddb05b4abd6e5", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-15T06:00:00.000Z", "description": "<PERSON><PERSON>", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T15:54:07.801Z", "updatedAt": "2025-01-02T15:16:57.208Z", "__v": 0, "frequency": "monthly"}, {"_id": "676ed333f01ddb05b4abf7f5", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "Apple CC", "amountDue": 0, "amountAssigned": 84, "amountSpent": -84, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-27T16:17:55.146Z", "updatedAt": "2025-07-24T02:43:42.341Z", "__v": 0, "frequency": "monthly"}, {"_id": "676f4b51fe68f6c6a5394009", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-04T00:00:00.000Z", "description": "Birthday Party", "amountDue": 22.57, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2024-12-28T00:50:25.344Z", "updatedAt": "2025-07-24T02:44:04.085Z", "__v": 0, "frequency": "oneoff"}, {"_id": "676f4b84fe68f6c6a5394179", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-18T00:00:00.000Z", "description": "White Elephant Gymnastics", "amountDue": 40, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2024-12-28T00:51:16.151Z", "updatedAt": "2025-07-24T02:44:05.842Z", "__v": 0, "frequency": "oneoff"}, {"_id": "6772c86f793842c671ac2c3a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-07T06:00:00.000Z", "description": "Amazon CC", "amountDue": 35, "amountAssigned": 35, "amountSpent": -35, "amountAvailable": 0, "status": "paid", "createdAt": "2024-12-30T16:21:03.277Z", "updatedAt": "2025-07-24T01:28:56.058Z", "__v": 0, "frequency": "monthly"}, {"_id": "67730f99793842c671acf0fc", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-31T06:00:00.000Z", "description": "Date Night", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2024-12-30T21:24:41.305Z", "updatedAt": "2025-07-24T02:43:12.696Z", "__v": 0, "frequency": "oneoff"}, {"_id": "677365e612c46bd1379e8c00", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-24T06:00:00.000Z", "startDate": "2024-12-24T06:00:00.000Z", "endDate": "2024-12-30T06:00:00.000Z", "description": "Chandler 429", "amountDue": 20, "amountAssigned": 70, "amountSpent": -70, "amountAvailable": 0, "status": "overpaid", "createdAt": "2024-12-31T03:32:54.807Z", "updatedAt": "2025-07-24T02:43:31.593Z", "__v": 0, "frequency": "weekly"}, {"_id": "677572901853bf5980b17860", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-01T06:00:00.000Z", "description": "Water", "amountDue": 126.47, "amountAssigned": 126.47, "amountSpent": -126.47, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2025-01-01T16:51:28.159Z", "updatedAt": "2025-07-24T01:43:18.841Z", "frequency": "monthly"}, {"_id": "677572901853bf5980b17873", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-01T06:00:00.000Z", "description": "Piano", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "__v": 0, "createdAt": "2025-01-01T16:51:28.159Z", "updatedAt": "2025-07-24T02:45:07.948Z", "frequency": "monthly"}, {"_id": "677579d81853bf5980b19922", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "description": "Health Insurance", "amountDue": 114.66, "amountAssigned": 114.66, "amountSpent": -114.66, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-01T17:22:32.174Z", "updatedAt": "2025-07-24T01:40:10.523Z", "__v": 0, "frequency": "monthly"}, {"_id": "677579da1853bf5980b1999c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "description": "Dental Insurance", "amountDue": 47.96, "amountAssigned": 95.92, "amountSpent": -95.92, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-01T17:22:34.909Z", "updatedAt": "2025-07-24T02:44:27.362Z", "__v": 0, "frequency": "monthly", "weeklyChargeType": "one-time"}, {"_id": "67757d9e1853bf5980b1a4d6", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-04T00:00:00.000Z", "description": "Garage Fix", "amountDue": 125, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-01T17:38:38.225Z", "updatedAt": "2025-07-24T02:44:03.306Z", "__v": 0, "frequency": "oneoff"}, {"_id": "6775837f1853bf5980b1b4e2", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-21T06:00:00.000Z", "description": "Gymnastics Entry Fee", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 50, "status": "late", "createdAt": "2025-01-01T18:03:43.466Z", "updatedAt": "2025-07-24T02:44:47.698Z", "__v": 0, "frequency": "oneoff"}, {"_id": "67758c8d1853bf5980b1bc86", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-04-05T17:00:00.000Z", "description": "Salem Flights", "amountDue": 332.46, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-01T18:42:21.398Z", "updatedAt": "2025-07-24T02:46:20.500Z", "__v": 0, "frequency": "oneoff"}, {"_id": "67758ccf1853bf5980b1bdca", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-09-01T17:00:00.000Z", "description": "Salem Housing", "amountDue": 157, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "createdAt": "2025-01-01T18:43:27.983Z", "updatedAt": "2025-04-03T10:17:12.147Z", "__v": 0, "frequency": "oneoff"}, {"_id": "67758d061853bf5980b1bf0e", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-09-11T05:00:00.000Z", "description": "Salem Trip Fund", "amountDue": 1000, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "createdAt": "2025-01-01T18:44:22.351Z", "updatedAt": "2025-04-23T14:17:40.311Z", "__v": 0, "frequency": "oneoff"}, {"_id": "67758e7a1853bf5980b1cc7e", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "Homeschool", "amountDue": 10, "amountAssigned": 15, "amountSpent": -15, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-01T18:50:34.438Z", "updatedAt": "2025-07-24T02:44:33.073Z", "__v": 0, "frequency": "monthly"}, {"_id": "677698181853bf5980b1eee3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-28T06:00:00.000Z", "description": "Daycare", "amountDue": 535, "amountAssigned": 535, "amountSpent": -535, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-02T13:43:52.414Z", "updatedAt": "2025-07-24T01:29:32.230Z", "__v": 0, "frequency": "monthly"}, {"_id": "6776d5b21183623332e13234", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-18T06:00:00.000Z", "description": "Brunch Date", "frequency": "oneoff", "amountDue": 80, "amountAssigned": 173, "amountSpent": -173, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-02T18:06:42.859Z", "updatedAt": "2025-07-24T02:44:06.667Z", "__v": 0, "weeklyChargeType": "one-time"}, {"_id": "677d55ef15613817139a019a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-06T06:00:00.000Z", "description": "Sheets", "frequency": "oneoff", "amountDue": 138.54, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-07T16:27:27.199Z", "updatedAt": "2025-07-24T02:44:04.842Z", "__v": 0}, {"_id": "67817c40e8c2a7db3ee7a815", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-19T06:00:00.000Z", "description": "Babysitter", "frequency": "oneoff", "amountDue": 60, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-10T20:00:00.281Z", "updatedAt": "2025-07-24T02:44:07.433Z", "__v": 0}, {"_id": "6782b0e1f7fcb816a123d07a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-06T06:00:00.000Z", "startDate": "2025-01-06T06:00:00.000Z", "endDate": "2025-01-12T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T17:56:49.561Z", "updatedAt": "2025-01-22T17:14:35.199Z", "__v": 0}, {"_id": "6782b0e5f7fcb816a123d124", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "startDate": "2025-01-20T06:00:00.000Z", "endDate": "2025-01-26T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 60, "amountAssigned": 4.09, "amountSpent": -4.09, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-11T17:56:53.124Z", "updatedAt": "2025-07-24T01:39:05.507Z", "__v": 0}, {"_id": "6782b0f1f7fcb816a123d2a4", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "startDate": "2025-01-20T06:00:00.000Z", "endDate": "2025-01-26T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 334.11, "amountSpent": -334.11, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T17:57:05.890Z", "updatedAt": "2025-07-24T02:44:20.318Z", "__v": 0}, {"_id": "6782b0fcf7fcb816a123d3cf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-13T06:00:00.000Z", "startDate": "2025-01-13T06:00:00.000Z", "endDate": "2025-01-19T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 76.08, "amountAssigned": 76.08, "amountSpent": -76.08, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T17:57:16.130Z", "updatedAt": "2025-07-24T01:34:38.106Z", "__v": 0}, {"_id": "6782b0fdf7fcb816a123d424", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "startDate": "2025-01-20T06:00:00.000Z", "endDate": "2025-01-26T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 86.97, "amountSpent": -86.97, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-11T17:57:17.472Z", "updatedAt": "2025-07-24T01:39:30.225Z", "__v": 0}, {"_id": "6782b116f7fcb816a123d57c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "startDate": "2025-01-20T06:00:00.000Z", "endDate": "2025-01-26T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 628.09, "amountSpent": -628.09, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T17:57:42.392Z", "updatedAt": "2025-07-24T02:44:15.572Z"}, {"_id": "6782b116f7fcb816a123d57b", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-13T06:00:00.000Z", "startDate": "2025-01-13T06:00:00.000Z", "endDate": "2025-01-19T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 197.47, "amountSpent": -197.47, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T17:57:42.392Z", "updatedAt": "2025-07-24T01:40:33.925Z"}, {"_id": "6782b116f7fcb816a123d57a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-06T06:00:00.000Z", "startDate": "2025-01-06T06:00:00.000Z", "endDate": "2025-01-12T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 30.91, "amountAssigned": 136.49, "amountSpent": -136.49, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T17:57:42.392Z", "updatedAt": "2025-07-24T02:44:14.570Z"}, {"_id": "6782b126f7fcb816a123d680", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "startDate": "2025-01-20T06:00:00.000Z", "endDate": "2025-01-26T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2025-01-11T17:57:58.872Z", "updatedAt": "2025-01-25T15:41:32.117Z"}, {"_id": "6782b126f7fcb816a123d67f", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-13T06:00:00.000Z", "startDate": "2025-01-13T06:00:00.000Z", "endDate": "2025-01-19T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2025-01-11T17:57:58.872Z", "updatedAt": "2025-01-20T21:34:13.890Z"}, {"_id": "6782b320f7fcb816a123e7e3", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-30T06:00:00.000Z", "startDate": "2024-12-30T06:00:00.000Z", "endDate": "2025-01-05T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 65.03, "amountAssigned": 65.03, "amountSpent": -65.03, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T18:06:24.007Z", "updatedAt": "2025-07-24T01:29:13.717Z", "__v": 0}, {"_id": "6782b345f7fcb816a123ec9d", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-11-25T06:00:00.000Z", "startDate": "2024-11-25T06:00:00.000Z", "endDate": "2024-12-01T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T18:07:01.373Z", "updatedAt": "2025-01-11T18:19:06.359Z", "__v": 0}, {"_id": "6782b347f7fcb816a123ecf2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-02T06:00:00.000Z", "startDate": "2024-12-02T06:00:00.000Z", "endDate": "2024-12-08T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 29.78, "amountAssigned": 29.78, "amountSpent": -29.78, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T18:07:03.330Z", "updatedAt": "2025-07-23T15:12:00.662Z", "__v": 0}, {"_id": "6782b349f7fcb816a123ed47", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-09T06:00:00.000Z", "startDate": "2024-12-09T06:00:00.000Z", "endDate": "2024-12-15T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 29.67, "amountAssigned": 29.67, "amountSpent": -29.67, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T18:07:05.417Z", "updatedAt": "2025-07-23T15:15:16.295Z", "__v": 0}, {"_id": "6782b34af7fcb816a123ed9c", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-16T06:00:00.000Z", "startDate": "2024-12-16T06:00:00.000Z", "endDate": "2024-12-22T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T18:07:06.591Z", "updatedAt": "2025-01-11T18:19:19.182Z", "__v": 0}, {"_id": "6782b34cf7fcb816a123edf1", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "startDate": "2024-12-23T06:00:00.000Z", "endDate": "2024-12-29T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 21.69, "amountAssigned": 21.69, "amountSpent": -21.69, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-11T18:07:08.274Z", "updatedAt": "2025-07-24T01:27:42.579Z", "__v": 0}, {"_id": "6782b5c6f7fcb816a123f9be", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-06T06:00:00.000Z", "startDate": "2025-01-06T06:00:00.000Z", "endDate": "2025-01-12T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 80.91, "amountAssigned": 41.59, "amountSpent": -41.59, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-11T18:17:42.999Z", "updatedAt": "2025-07-24T02:44:10.357Z", "__v": 0}, {"_id": "6782b5d0f7fcb816a123fa3e", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-06T06:00:00.000Z", "startDate": "2025-01-06T06:00:00.000Z", "endDate": "2025-01-12T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 211.85, "amountAssigned": 402.2, "amountSpent": -402.2, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T18:17:52.679Z", "updatedAt": "2025-07-24T02:44:17.773Z", "__v": 0}, {"_id": "6782e3b8f7fcb816a1241af1", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-11-25T06:00:00.000Z", "startDate": "2024-11-25T06:00:00.000Z", "endDate": "2024-12-01T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "createdAt": "2025-01-11T21:33:44.947Z", "updatedAt": "2025-01-11T21:33:44.947Z", "__v": 0}, {"_id": "6782e3d8f7fcb816a1241cf8", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "startDate": "2024-12-23T06:00:00.000Z", "endDate": "2024-12-29T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 504.45, "amountSpent": -504.45, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T21:34:16.996Z", "updatedAt": "2025-07-24T01:27:23.863Z"}, {"_id": "6782e3d8f7fcb816a1241cf4", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-11-25T06:00:00.000Z", "startDate": "2024-11-25T06:00:00.000Z", "endDate": "2024-12-01T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:16.996Z", "updatedAt": "2025-01-11T21:34:16.996Z"}, {"_id": "6782e3d8f7fcb816a1241cf7", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-16T06:00:00.000Z", "startDate": "2024-12-16T06:00:00.000Z", "endDate": "2024-12-22T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 798.9, "amountSpent": -798.9, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T21:34:16.996Z", "updatedAt": "2025-07-24T02:43:18.742Z"}, {"_id": "6782e3d8f7fcb816a1241cf6", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-09T06:00:00.000Z", "startDate": "2024-12-09T06:00:00.000Z", "endDate": "2024-12-15T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 553.31, "amountSpent": -553.31, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T21:34:16.996Z", "updatedAt": "2025-07-24T02:43:17.954Z"}, {"_id": "6782e3d8f7fcb816a1241cf5", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-02T06:00:00.000Z", "startDate": "2024-12-02T06:00:00.000Z", "endDate": "2024-12-08T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 888.76, "amountSpent": -888.76, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T21:34:16.996Z", "updatedAt": "2025-07-24T02:43:17.034Z"}, {"_id": "6782e3d8f7fcb816a1241cf9", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-30T06:00:00.000Z", "startDate": "2024-12-30T06:00:00.000Z", "endDate": "2025-01-05T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 297.22, "amountSpent": -297.22, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T21:34:16.996Z", "updatedAt": "2025-07-24T02:43:21.940Z"}, {"_id": "6782e3dcf7fcb816a1241d4e", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-02T06:00:00.000Z", "startDate": "2024-12-02T06:00:00.000Z", "endDate": "2024-12-08T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 392.26, "amountSpent": -392.26, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T21:34:20.164Z", "updatedAt": "2025-07-24T02:43:24.457Z", "__v": 0}, {"_id": "6782e3ddf7fcb816a1241da3", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-09T06:00:00.000Z", "startDate": "2024-12-09T06:00:00.000Z", "endDate": "2024-12-15T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 338.1, "amountSpent": -338.1, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T21:34:21.558Z", "updatedAt": "2025-07-24T01:26:44.149Z", "__v": 0}, {"_id": "6782e3dff7fcb816a1241df8", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-16T06:00:00.000Z", "startDate": "2024-12-16T06:00:00.000Z", "endDate": "2024-12-22T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 183.97, "amountSpent": -183.97, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T21:34:23.048Z", "updatedAt": "2025-07-23T15:14:50.947Z", "__v": 0}, {"_id": "6782e3e0f7fcb816a1241e4d", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "startDate": "2024-12-23T06:00:00.000Z", "endDate": "2024-12-29T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 154.27, "amountSpent": -154.27, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T21:34:24.374Z", "updatedAt": "2025-07-24T01:31:57.879Z", "__v": 0}, {"_id": "6782e3e1f7fcb816a1241ea2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-30T06:00:00.000Z", "startDate": "2024-12-30T06:00:00.000Z", "endDate": "2025-01-05T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 298.46, "amountSpent": -298.46, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-11T21:34:25.619Z", "updatedAt": "2025-07-24T02:43:25.401Z", "__v": 0}, {"_id": "6782e3f0f7fcb816a1241fd3", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-30T06:00:00.000Z", "startDate": "2024-12-30T06:00:00.000Z", "endDate": "2025-01-05T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 82.34, "amountAssigned": 82.34, "amountSpent": -82.34, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2025-01-11T21:34:40.337Z", "updatedAt": "2025-07-24T01:31:16.560Z"}, {"_id": "6782e3f0f7fcb816a1241fce", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-11-25T06:00:00.000Z", "startDate": "2024-11-25T06:00:00.000Z", "endDate": "2024-12-01T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:40.337Z", "updatedAt": "2025-01-16T02:53:05.083Z"}, {"_id": "6782e3f0f7fcb816a1241fd0", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-09T06:00:00.000Z", "startDate": "2024-12-09T06:00:00.000Z", "endDate": "2024-12-15T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 41.83, "amountSpent": -41.83, "amountAvailable": 0, "status": "underpaid", "__v": 0, "createdAt": "2025-01-11T21:34:40.337Z", "updatedAt": "2025-07-23T15:10:15.445Z"}, {"_id": "6782e3f0f7fcb816a1241fd1", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-16T06:00:00.000Z", "startDate": "2024-12-16T06:00:00.000Z", "endDate": "2024-12-22T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 87.99, "amountSpent": -87.99, "amountAvailable": 0, "status": "underpaid", "__v": 0, "createdAt": "2025-01-11T21:34:40.337Z", "updatedAt": "2025-07-23T15:14:48.702Z"}, {"_id": "6782e3f0f7fcb816a1241fcf", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-02T06:00:00.000Z", "startDate": "2024-12-02T06:00:00.000Z", "endDate": "2024-12-08T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 82.96, "amountSpent": -82.96, "amountAvailable": 0, "status": "underpaid", "__v": 0, "createdAt": "2025-01-11T21:34:40.337Z", "updatedAt": "2025-07-23T15:12:08.165Z"}, {"_id": "6782e3f0f7fcb816a1241fd2", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "startDate": "2024-12-23T06:00:00.000Z", "endDate": "2024-12-29T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:40.337Z", "updatedAt": "2025-01-11T21:34:40.337Z"}, {"_id": "6782e3fef7fcb816a12420ff", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-11-25T06:00:00.000Z", "startDate": "2024-11-25T06:00:00.000Z", "endDate": "2024-12-01T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 40, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:54.362Z", "updatedAt": "2025-01-11T21:34:54.362Z"}, {"_id": "6782e3fef7fcb816a1242101", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-09T06:00:00.000Z", "startDate": "2024-12-09T06:00:00.000Z", "endDate": "2024-12-15T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 40, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:54.362Z", "updatedAt": "2025-01-11T21:34:54.362Z"}, {"_id": "6782e3fef7fcb816a1242100", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-02T06:00:00.000Z", "startDate": "2024-12-02T06:00:00.000Z", "endDate": "2024-12-08T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 40, "amountAssigned": 20, "amountSpent": -20, "amountAvailable": 0, "status": "underpaid", "__v": 0, "createdAt": "2025-01-11T21:34:54.362Z", "updatedAt": "2025-07-23T15:03:37.592Z"}, {"_id": "6782e3fef7fcb816a1242102", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-16T06:00:00.000Z", "startDate": "2024-12-16T06:00:00.000Z", "endDate": "2024-12-22T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 40, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:54.362Z", "updatedAt": "2025-01-11T21:34:54.362Z"}, {"_id": "6782e3fef7fcb816a1242104", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-30T06:00:00.000Z", "startDate": "2024-12-30T06:00:00.000Z", "endDate": "2025-01-05T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 40, "amountAssigned": 61.3, "amountSpent": -61.3, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-11T21:34:54.362Z", "updatedAt": "2025-07-24T01:30:26.668Z"}, {"_id": "6782e3fef7fcb816a1242103", "userId": "6764aeeccfd30a63a4a45566", "date": "2024-12-23T06:00:00.000Z", "startDate": "2024-12-23T06:00:00.000Z", "endDate": "2024-12-29T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 40, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-01-11T21:34:54.362Z", "updatedAt": "2025-01-11T21:34:54.362Z"}, {"_id": "6785463bded9beb8e0769f81", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-13T06:00:00.000Z", "startDate": "2025-01-13T06:00:00.000Z", "endDate": "2025-01-19T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 249.52, "amountAssigned": 289.52, "amountSpent": -289.52, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-13T16:58:35.027Z", "updatedAt": "2025-07-24T02:44:19.478Z", "__v": 0}, {"_id": "67856703d0e25e9a885d30d3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-13T06:00:00.000Z", "startDate": "2025-01-13T06:00:00.000Z", "endDate": "2025-01-19T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-13T19:18:27.394Z", "updatedAt": "2025-01-20T19:17:04.271Z", "__v": 0}, {"_id": "678568a7d0e25e9a885d43bd", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-11T06:00:00.000Z", "description": "Netflix", "frequency": "monthly", "weeklyChargeType": "one-time", "amountDue": 24.89, "amountAssigned": 24.89, "amountSpent": -24.89, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-13T19:25:27.386Z", "updatedAt": "2025-07-24T01:34:59.209Z", "__v": 0}, {"_id": "67857db6d9504d2f8bf8b3db", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-17T06:00:00.000Z", "startDate": "2025-02-17T06:00:00.000Z", "endDate": "2025-02-23T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 446.67, "amountSpent": -446.67, "amountAvailable": 44.84, "status": "overpaid", "__v": 0, "createdAt": "2025-01-13T20:55:18.775Z", "updatedAt": "2025-07-24T02:45:04.740Z"}, {"_id": "67857db6d9504d2f8bf8b3da", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-17T06:00:00.000Z", "startDate": "2025-02-17T06:00:00.000Z", "endDate": "2025-02-23T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 372.6, "amountSpent": -372.6, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-13T20:55:18.774Z", "updatedAt": "2025-07-24T02:44:58.790Z"}, {"_id": "67857db6d9504d2f8bf8b3dc", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-17T06:00:00.000Z", "startDate": "2025-02-17T06:00:00.000Z", "endDate": "2025-02-23T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 60, "status": "paid", "__v": 0, "createdAt": "2025-01-13T20:55:18.775Z", "updatedAt": "2025-02-24T14:52:28.086Z"}, {"_id": "67857db6d9504d2f8bf8b3dd", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-17T06:00:00.000Z", "startDate": "2025-02-17T06:00:00.000Z", "endDate": "2025-02-23T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 127.3, "amountSpent": -127.3, "amountAvailable": 12.77, "status": "overpaid", "__v": 0, "createdAt": "2025-01-13T20:55:18.775Z", "updatedAt": "2025-07-24T01:53:21.887Z"}, {"_id": "67857db6d9504d2f8bf8b3de", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-17T06:00:00.000Z", "startDate": "2025-02-17T06:00:00.000Z", "endDate": "2025-02-23T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 40, "status": "paid", "__v": 0, "createdAt": "2025-01-13T20:55:18.775Z", "updatedAt": "2025-02-24T14:52:25.111Z"}, {"_id": "67857dbcd9504d2f8bf8b466", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-10T06:00:00.000Z", "startDate": "2025-02-10T06:00:00.000Z", "endDate": "2025-02-16T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 60, "amountAssigned": 47, "amountSpent": -47, "amountAvailable": 0, "status": "underpaid", "__v": 0, "createdAt": "2025-01-13T20:55:24.353Z", "updatedAt": "2025-07-24T02:44:51.346Z"}, {"_id": "67857dbcd9504d2f8bf8b465", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-10T06:00:00.000Z", "startDate": "2025-02-10T06:00:00.000Z", "endDate": "2025-02-16T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 410.06, "amountSpent": -410.06, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-13T20:55:24.353Z", "updatedAt": "2025-07-24T02:45:03.942Z"}, {"_id": "67857dbcd9504d2f8bf8b467", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-10T06:00:00.000Z", "startDate": "2025-02-10T06:00:00.000Z", "endDate": "2025-02-16T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 82.45, "amountSpent": -82.45, "amountAvailable": 0, "status": "underpaid", "__v": 0, "createdAt": "2025-01-13T20:55:24.353Z", "updatedAt": "2025-07-24T01:46:41.611Z"}, {"_id": "67857dbcd9504d2f8bf8b464", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-10T06:00:00.000Z", "startDate": "2025-02-10T06:00:00.000Z", "endDate": "2025-02-16T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 161.62, "amountSpent": -161.62, "amountAvailable": 0, "status": "overpaid", "__v": 0, "createdAt": "2025-01-13T20:55:24.353Z", "updatedAt": "2025-07-24T02:44:57.818Z"}, {"_id": "67857dbcd9504d2f8bf8b468", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-10T06:00:00.000Z", "startDate": "2025-02-10T06:00:00.000Z", "endDate": "2025-02-16T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "__v": 0, "createdAt": "2025-01-13T20:55:24.353Z", "updatedAt": "2025-02-17T16:18:54.459Z"}, {"_id": "67889c11d31815bba57cca9d", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-13T06:00:00.000Z", "description": "Flex Payment", "frequency": "monthly", "amountDue": 953.42, "amountAssigned": 1878.68, "amountSpent": -1878.68, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-16T05:41:37.110Z", "updatedAt": "2025-07-24T01:38:10.550Z", "__v": 0}, {"_id": "678917d4c4172aa029bcf124", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-07T06:00:00.000Z", "description": "Prime Payment", "frequency": "monthly", "amountDue": 35, "amountAssigned": 544.37, "amountSpent": -544.37, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-16T14:29:40.278Z", "updatedAt": "2025-07-24T01:33:58.004Z", "__v": 0}, {"_id": "678919147b77c8f92606ed48", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-31T06:00:00.000Z", "description": "Apple Card Payment", "frequency": "monthly", "amountDue": 25, "amountAssigned": 1332.67, "amountSpent": -1332.67, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-16T14:35:00.922Z", "updatedAt": "2025-07-24T02:44:32.223Z", "__v": 0}, {"_id": "678a997c87160d1bf2fbb381", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-17T06:00:00.000Z", "description": "Tesla Registration", "frequency": "oneoff", "weeklyChargeType": "one-time", "amountDue": 273, "amountAssigned": 273, "amountSpent": -273, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-17T17:55:08.932Z", "updatedAt": "2025-07-24T01:33:09.737Z", "__v": 0}, {"_id": "678a9bd487160d1bf2fbc888", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "startDate": "2025-01-27T06:00:00.000Z", "endDate": "2025-02-02T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 307.9, "amountSpent": -307.9, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-17T18:05:08.319Z", "updatedAt": "2025-07-24T02:44:13.180Z", "__v": 0}, {"_id": "678a9d0d87160d1bf2fbd170", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "startDate": "2025-01-27T06:00:00.000Z", "endDate": "2025-02-02T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-17T18:10:21.395Z", "updatedAt": "2025-02-12T16:05:20.101Z", "__v": 0}, {"_id": "678a9f5487160d1bf2fbdebd", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "startDate": "2025-01-27T06:00:00.000Z", "endDate": "2025-02-02T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 288.06, "amountSpent": -288.06, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-17T18:20:04.393Z", "updatedAt": "2025-07-24T02:44:21.334Z", "__v": 0}, {"_id": "678aa1a987160d1bf2fbe726", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "startDate": "2025-01-27T06:00:00.000Z", "endDate": "2025-02-02T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 256.03, "amountSpent": -256.03, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-17T18:30:01.763Z", "updatedAt": "2025-07-24T01:45:35.736Z", "__v": 0}, {"_id": "678aa1b787160d1bf2fbe8fd", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-27T06:00:00.000Z", "startDate": "2025-01-27T06:00:00.000Z", "endDate": "2025-02-02T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 17.43, "amountAssigned": 17.43, "amountSpent": -17.43, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-17T18:30:15.607Z", "updatedAt": "2025-07-24T01:44:24.551Z", "__v": 0}, {"_id": "678ac7319c5f95faa1f0359b", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-06T06:00:00.000Z", "startDate": "2025-01-06T06:00:00.000Z", "endDate": "2025-01-12T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-17T21:10:09.014Z", "updatedAt": "2025-01-20T21:32:55.758Z", "__v": 0}, {"_id": "678e878374bc460070bc6e7c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-01-20T06:00:00.000Z", "description": "<PERSON> Dentist Appointment", "frequency": "oneoff", "weeklyChargeType": "one-time", "amountDue": 99, "amountAssigned": 146.96, "amountSpent": -146.96, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-20T17:27:31.845Z", "updatedAt": "2025-07-24T02:44:08.386Z", "__v": 0}, {"_id": "678f1ed19618aa1edb42a592", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-01T06:00:00.000Z", "description": "Rent", "frequency": "monthly", "amountDue": 1500, "amountAssigned": 3000, "amountSpent": -3000, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-21T04:13:05.828Z", "updatedAt": "2025-07-24T01:50:55.495Z", "__v": 0}, {"_id": "678f206e9618aa1edb42aac3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-04T06:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 18.39, "amountSpent": -18.39, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-21T04:19:58.827Z", "updatedAt": "2025-07-24T01:49:00.102Z", "__v": 0}, {"_id": "678f20c69618aa1edb42af79", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-07T06:00:00.000Z", "description": "Renters Insurance", "frequency": "monthly", "amountDue": 22.92, "amountAssigned": 22.92, "amountSpent": -22.92, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-21T04:21:26.156Z", "updatedAt": "2025-07-24T01:46:15.795Z", "__v": 0}, {"_id": "678f2bf8476ddef3142b8980", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-11T06:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 24.89, "amountAssigned": 24.89, "amountSpent": -24.89, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-21T05:09:12.876Z", "updatedAt": "2025-07-24T01:47:06.797Z", "__v": 0}, {"_id": "678f2c02476ddef3142b8b27", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-03T06:00:00.000Z", "startDate": "2025-02-03T06:00:00.000Z", "endDate": "2025-02-09T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 60, "amountAssigned": 19.32, "amountSpent": -19.32, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-21T05:09:22.636Z", "updatedAt": "2025-07-24T01:48:27.598Z", "__v": 0}, {"_id": "679105009c2a941efae8d7b0", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-03T06:00:00.000Z", "startDate": "2025-02-03T06:00:00.000Z", "endDate": "2025-02-09T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 383.4, "amountSpent": -383.4, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-22T14:47:28.463Z", "updatedAt": "2025-07-24T02:44:55.725Z", "__v": 0}, {"_id": "679502dd628cb49f4173903b", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-02T06:00:00.000Z", "description": "Oil Change", "frequency": "oneoff", "amountDue": 60, "amountAssigned": 102.59, "amountSpent": -102.59, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-25T15:27:25.818Z", "updatedAt": "2025-07-24T01:53:27.909Z", "__v": 0}, {"_id": "67950699d6c19602f10dc3d4", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-22T06:00:00.000Z", "description": "Babysitter", "frequency": "oneoff", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 100, "status": "late", "createdAt": "2025-01-25T15:43:21.212Z", "updatedAt": "2025-07-24T02:44:48.610Z", "__v": 0}, {"_id": "67950aa01baf1ca11c682bdc", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-07T06:00:00.000Z", "description": "<PERSON>", "frequency": "oneoff", "amountDue": 400, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-25T16:00:32.780Z", "updatedAt": "2025-07-24T02:44:44.269Z", "__v": 0}, {"_id": "67950b2e1baf1ca11c682cb3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-24T06:00:00.000Z", "startDate": "2025-02-24T06:00:00.000Z", "endDate": "2025-03-02T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 298.43, "amountSpent": -298.43, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-25T16:02:54.206Z", "updatedAt": "2025-07-24T02:44:59.742Z", "__v": 0}, {"_id": "67950b321baf1ca11c682d31", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-24T06:00:00.000Z", "startDate": "2025-02-24T06:00:00.000Z", "endDate": "2025-03-02T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 236.82, "amountSpent": -236.82, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-25T16:02:58.645Z", "updatedAt": "2025-07-24T02:45:05.661Z", "__v": 0}, {"_id": "67950b331baf1ca11c682daf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-03T06:00:00.000Z", "startDate": "2025-02-03T06:00:00.000Z", "endDate": "2025-02-09T06:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 368, "amountSpent": -368, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-25T16:02:59.521Z", "updatedAt": "2025-07-24T02:45:02.879Z", "__v": 0}, {"_id": "67950b3a1baf1ca11c682e2d", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-03T06:00:00.000Z", "startDate": "2025-02-03T06:00:00.000Z", "endDate": "2025-02-09T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 20, "amountAssigned": 20, "amountSpent": -20, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-25T16:03:06.281Z", "updatedAt": "2025-07-24T01:48:35.883Z", "__v": 0}, {"_id": "67950b3c1baf1ca11c682eab", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-24T06:00:00.000Z", "startDate": "2025-02-24T06:00:00.000Z", "endDate": "2025-03-02T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-25T16:03:08.922Z", "updatedAt": "2025-03-01T12:24:55.807Z", "__v": 0}, {"_id": "67950b401baf1ca11c682f29", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-03T06:00:00.000Z", "startDate": "2025-02-03T06:00:00.000Z", "endDate": "2025-02-09T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 93.26, "amountSpent": -93.26, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-25T16:03:12.456Z", "updatedAt": "2025-07-24T01:43:00.657Z", "__v": 0}, {"_id": "67950b411baf1ca11c682fa7", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-24T06:00:00.000Z", "startDate": "2025-02-24T06:00:00.000Z", "endDate": "2025-03-02T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 49.92, "amountSpent": -49.92, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-25T16:03:13.586Z", "updatedAt": "2025-07-24T01:57:01.816Z", "__v": 0}, {"_id": "679514df1a3f6d2d3ceac1f3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-11T06:00:00.000Z", "description": "Topgolf", "frequency": "oneoff", "amountDue": 40, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-25T16:44:15.477Z", "updatedAt": "2025-07-24T02:44:46.362Z", "__v": 0}, {"_id": "6797a50e5212b1c965239c94", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-13T06:00:00.000Z", "description": "Flex Payment", "frequency": "monthly", "amountDue": 0, "amountAssigned": 33, "amountSpent": -33, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-27T15:23:58.857Z", "updatedAt": "2025-07-24T02:45:10.828Z", "__v": 0}, {"_id": "6797a51a5212b1c965239d14", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-15T06:00:00.000Z", "description": "<PERSON><PERSON>", "frequency": "monthly", "amountDue": 1209.83, "amountAssigned": 1212.55, "amountSpent": -1212.55, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-27T15:24:10.407Z", "updatedAt": "2025-07-24T01:54:13.927Z", "__v": 0}, {"_id": "6797a51aa0ae901c1b0aedce", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-15T06:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 105.16, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-27T15:24:10.804Z", "updatedAt": "2025-07-24T02:45:12.389Z", "__v": 0}, {"_id": "6797b6148a0fd149c283a3cf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-25T06:00:00.000Z", "description": "Tesla", "frequency": "monthly", "amountDue": 584.84, "amountAssigned": 620, "amountSpent": -620, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-27T16:36:36.259Z", "updatedAt": "2025-07-24T01:49:32.730Z", "__v": 0}, {"_id": "6797b6198a0fd149c283a44d", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-26T06:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 10.65, "amountAssigned": 10.65, "amountSpent": -10.65, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-27T16:36:41.096Z", "updatedAt": "2025-07-24T01:57:13.024Z", "__v": 0}, {"_id": "6797b6198a0fd149c283a4cb", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-27T06:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 259.11, "amountAssigned": 254.08, "amountSpent": -254.08, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-27T16:36:41.699Z", "updatedAt": "2025-07-24T01:56:45.333Z", "__v": 0}, {"_id": "6797b61a8a0fd149c283a549", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-27T06:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 220, "amountAssigned": 124.12, "amountSpent": -124.12, "amountAvailable": 123.73, "status": "underpaid", "createdAt": "2025-01-27T16:36:42.200Z", "updatedAt": "2025-07-24T01:51:07.543Z", "__v": 0}, {"_id": "6797b61a8a0fd149c283a5c7", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-28T06:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 240, "amountSpent": -240, "amountAvailable": 0, "status": "paid", "createdAt": "2025-01-27T16:36:42.658Z", "updatedAt": "2025-07-24T01:44:06.536Z", "__v": 0}, {"_id": "6797b61b8a0fd149c283a645", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-28T06:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 100, "amountAssigned": 65.62, "amountSpent": -65.62, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-27T16:36:43.753Z", "updatedAt": "2025-07-24T02:45:18.210Z", "__v": 0}, {"_id": "6797b61d8a0fd149c283a6c3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-28T06:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 100, "amountAssigned": 90.98, "amountSpent": -90.98, "amountAvailable": 0, "status": "underpaid", "createdAt": "2025-01-27T16:36:45.289Z", "updatedAt": "2025-07-24T02:45:17.441Z", "__v": 0}, {"_id": "6797b61f8a0fd149c283a7bf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-28T06:00:00.000Z", "description": "Apple Card Payment", "frequency": "monthly", "amountDue": 300, "amountAssigned": 755.16, "amountSpent": -755.16, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-27T16:36:47.181Z", "updatedAt": "2025-07-24T02:45:16.586Z", "__v": 0}, {"_id": "6797b6208a0fd149c283a8bb", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-01T06:00:00.000Z", "description": "Water", "frequency": "monthly", "amountDue": 150, "amountAssigned": 252.84, "amountSpent": -252.84, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-01-27T16:36:48.291Z", "updatedAt": "2025-07-24T02:45:51.219Z", "__v": 0}, {"_id": "6797b6208a0fd149c283a939", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-01T06:00:00.000Z", "description": "Piano", "frequency": "monthly", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-01-27T16:36:48.870Z", "updatedAt": "2025-07-24T02:45:50.046Z", "__v": 0}, {"_id": "67a373ca9ce1eb7ea4467324", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-04T06:00:00.000Z", "description": "Explorer Registration", "frequency": "oneoff", "amountDue": 78, "amountAssigned": 78, "amountSpent": -78, "amountAvailable": 0, "status": "paid", "createdAt": "2025-02-05T14:20:58.734Z", "updatedAt": "2025-07-24T01:42:30.620Z", "__v": 0}, {"_id": "67a3786caedb77698002615e", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-17T06:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 237.89, "amountAssigned": 237.89, "amountSpent": -237.89, "amountAvailable": 0, "status": "paid", "createdAt": "2025-02-05T14:40:44.538Z", "updatedAt": "2025-07-24T01:52:23.814Z", "__v": 0}, {"_id": "67a3786daedb7769800261e8", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-26T18:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 114.66, "status": "paid", "createdAt": "2025-02-05T14:40:45.526Z", "updatedAt": "2025-03-04T15:21:59.596Z", "__v": 0, "recurringExpenseId": 1735752127638}, {"_id": "67a3786eaedb7769800262cf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-21T06:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 51, "amountAssigned": 43.25, "amountSpent": -43.25, "amountAvailable": 51, "status": "underpaid", "createdAt": "2025-02-05T14:40:46.977Z", "updatedAt": "2025-07-24T01:52:45.499Z", "__v": 0}, {"_id": "67a379e3aedb776980026c17", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-28T06:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 50, "amountAssigned": 770.36, "amountSpent": -770.36, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-02-05T14:46:59.432Z", "updatedAt": "2025-07-24T02:45:15.216Z", "__v": 0}, {"_id": "67a379e4aedb776980026ca1", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-28T06:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 50, "amountAssigned": 512.17, "amountSpent": -512.17, "amountAvailable": 0, "status": "overpaid", "createdAt": "2025-02-05T14:47:00.960Z", "updatedAt": "2025-07-24T02:45:14.174Z", "__v": 0}, {"_id": "67a38bfdaedb77698002efaf", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-07T06:00:00.000Z", "description": "Prime Payment", "frequency": "monthly", "amountDue": 33, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-02-05T16:04:13.309Z", "updatedAt": "2025-07-24T02:45:09.697Z", "__v": 0}, {"_id": "67a3dc50aedb77698003d468", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-24T06:00:00.000Z", "startDate": "2025-02-24T06:00:00.000Z", "endDate": "2025-03-02T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "paid", "createdAt": "2025-02-05T21:46:56.869Z", "updatedAt": "2025-03-12T15:18:33.057Z", "__v": 0}, {"_id": "67ab7324fed1ab63843340bd", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-02-11T06:00:00.000Z", "description": "Mint Mobile Phone Plan 3 Month", "frequency": "oneoff", "amountDue": 129.47, "amountAssigned": 129.47, "amountSpent": -129.47, "amountAvailable": 0, "status": "paid", "createdAt": "2025-02-11T15:56:20.239Z", "updatedAt": "2025-07-24T01:47:21.349Z", "__v": 0}, {"_id": "67ad075c389ee58bbd285f9b", "userId": "67ad0702389ee58bbd285f8c", "date": "2025-02-12T20:41:00.134Z", "description": "Rent", "frequency": "monthly", "amountDue": 1000, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-02-12T20:41:00.134Z", "updatedAt": "2025-02-12T20:41:00.134Z"}, {"_id": "67b7669c1d713bd488bf78df", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-01T06:00:00.000Z", "description": "Dental Insurance", "frequency": "monthly", "amountDue": 47.96, "amountAssigned": 0, "amountSpent": 0, "amountAvailable": 0, "status": "late", "createdAt": "2025-02-20T17:30:04.405Z", "updatedAt": "2025-07-24T02:45:49.329Z", "__v": 0}, {"_id": "67bc8f9c632f4cab8fcfee1c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-04T06:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 18.39, "amountSpent": -18.39, "status": "paid", "createdAt": "2025-02-24T15:26:20.507Z", "updatedAt": "2025-07-24T01:54:16.921Z", "__v": 0}, {"_id": "67bc8f9d632f4cab8fcfee77", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-11T17:00:00.000Z", "description": "Renters Insurance", "frequency": "monthly", "amountDue": 22.92, "amountAssigned": 22.92, "amountSpent": -22.92, "status": "paid", "createdAt": "2025-02-24T15:26:21.588Z", "updatedAt": "2025-07-24T02:00:49.026Z", "__v": 0}, {"_id": "67bc8f9e632f4cab8fcfeef9", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-07T06:00:00.000Z", "description": "Prime Payment", "frequency": "monthly", "amountDue": 33, "amountAssigned": 456.17, "amountSpent": -456.17, "status": "overpaid", "createdAt": "2025-02-24T15:26:22.299Z", "updatedAt": "2025-07-24T02:45:52.392Z", "__v": 0}, {"_id": "67bc8f9e632f4cab8fcfef7d", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-11T05:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 24.89, "amountAssigned": 27.05, "amountSpent": -27.05, "status": "overpaid", "createdAt": "2025-02-24T15:26:22.817Z", "updatedAt": "2025-07-24T02:00:52.475Z", "__v": 0}, {"_id": "67bc8f9f632f4cab8fcff000", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-13T05:00:00.000Z", "description": "Flex Payment", "frequency": "monthly", "amountDue": 639.27, "amountAssigned": 689.27, "amountSpent": -689.27, "status": "overpaid", "createdAt": "2025-02-24T15:26:23.320Z", "updatedAt": "2025-07-24T02:45:54.606Z", "__v": 0}, {"_id": "67bc8f9f632f4cab8fcff059", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-15T05:00:00.000Z", "description": "<PERSON><PERSON>", "frequency": "monthly", "amountDue": 1209.83, "amountAssigned": 1212.55, "amountSpent": -1212.55, "status": "overpaid", "createdAt": "2025-02-24T15:26:23.782Z", "updatedAt": "2025-07-24T01:54:14.232Z", "__v": 0}, {"_id": "67bc8fa0632f4cab8fcff0de", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-15T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 105.16, "amountAssigned": 105.16, "amountSpent": -105.16, "status": "paid", "createdAt": "2025-02-24T15:26:24.290Z", "updatedAt": "2025-07-24T02:01:02.124Z", "__v": 0}, {"_id": "67bc8fa2632f4cab8fcff1b8", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-17T05:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 239.56, "amountAssigned": 239.56, "amountSpent": -239.56, "status": "paid", "createdAt": "2025-02-24T15:26:26.899Z", "updatedAt": "2025-07-24T02:12:04.115Z", "__v": 0}, {"_id": "67bc8fa3632f4cab8fcff26a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-21T05:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 51, "amountAssigned": 43.25, "amountSpent": -43.25, "status": "underpaid", "createdAt": "2025-02-24T15:26:27.683Z", "updatedAt": "2025-07-24T02:08:38.782Z", "__v": 0}, {"_id": "67bc8fa4632f4cab8fcff2ee", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-25T05:00:00.000Z", "description": "Tesla", "frequency": "monthly", "amountDue": 584.84, "amountAssigned": 600, "amountSpent": -600, "status": "overpaid", "createdAt": "2025-02-24T15:26:28.146Z", "updatedAt": "2025-07-24T02:05:18.900Z", "__v": 0}, {"_id": "67bc8fa5632f4cab8fcff3c8", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-26T05:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 10.65, "amountAssigned": 10.65, "amountSpent": -10.65, "status": "paid", "createdAt": "2025-02-24T15:26:29.001Z", "updatedAt": "2025-07-24T02:03:20.420Z", "__v": 0}, {"_id": "67bc8fa6632f4cab8fcff44c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-27T05:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 267, "amountAssigned": 267, "amountSpent": -267, "status": "paid", "createdAt": "2025-02-24T15:26:30.625Z", "updatedAt": "2025-07-24T02:03:10.017Z", "__v": 0}, {"_id": "67bc8fa7632f4cab8fcff4a6", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-31T17:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 321.91, "amountAssigned": 123.73, "amountSpent": -123.73, "status": "underpaid", "createdAt": "2025-02-24T15:26:31.527Z", "updatedAt": "2025-07-24T02:46:01.024Z", "__v": 0}, {"_id": "67bc8fa8632f4cab8fcff5ad", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-31T05:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 50, "amountAssigned": 656.34, "amountSpent": -656.34, "status": "overpaid", "createdAt": "2025-02-24T15:26:32.914Z", "updatedAt": "2025-07-24T02:45:59.318Z", "__v": 0}, {"_id": "67bc8fab632f4cab8fcff65c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-31T05:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 50.19, "amountAssigned": 68.42, "amountSpent": -68.42, "status": "overpaid", "createdAt": "2025-02-24T15:26:35.098Z", "updatedAt": "2025-07-24T02:45:58.515Z", "__v": 0}, {"_id": "67bc8fab632f4cab8fcff6b4", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-31T05:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 100, "amountAssigned": 140.61, "amountSpent": -140.61, "status": "overpaid", "createdAt": "2025-02-24T15:26:35.602Z", "updatedAt": "2025-07-24T02:45:57.590Z", "__v": 0}, {"_id": "67bc8fac632f4cab8fcff792", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-31T05:00:00.000Z", "description": "Apple Card Payment", "frequency": "monthly", "amountDue": 300, "amountAssigned": 400, "amountSpent": -400, "status": "overpaid", "createdAt": "2025-02-24T15:26:36.654Z", "updatedAt": "2025-07-24T01:57:56.355Z", "__v": 0}, {"_id": "67bc8fb3632f4cab8fcff86c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-31T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 50, "amountAssigned": 385.74, "amountSpent": -385.74, "status": "overpaid", "createdAt": "2025-02-24T15:26:43.803Z", "updatedAt": "2025-07-24T02:45:56.613Z", "__v": 0}, {"_id": "67bc9e9f632f4cab8fd00af7", "userId": "67ae323f9060b328d058fb60", "date": "2025-02-01T06:00:00.000Z", "description": "Rent", "frequency": "monthly", "amountDue": 1500, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "createdAt": "2025-02-24T16:30:23.769Z", "updatedAt": "2025-02-24T16:30:23.769Z", "__v": 0}, {"_id": "67bcd42dd791c1e1a6d04d68", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-03T06:00:00.000Z", "startDate": "2025-03-03T06:00:00.000Z", "endDate": "2025-03-09T06:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 115.35, "amountSpent": -115.35, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:18:53.628Z", "updatedAt": "2025-07-24T01:54:45.885Z"}, {"_id": "67bcd42dd791c1e1a6d04d69", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-10T05:00:00.000Z", "startDate": "2025-03-10T17:00:00.000Z", "endDate": "2025-03-16T17:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 204.39, "amountSpent": -204.39, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:18:53.628Z", "updatedAt": "2025-07-24T02:45:42.910Z", "recurringExpenseId": null}, {"_id": "67bcd42dd791c1e1a6d04d6a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-17T05:00:00.000Z", "startDate": "2025-03-17T05:00:00.000Z", "endDate": "2025-03-23T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 217.79, "amountSpent": -217.79, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:18:53.628Z", "updatedAt": "2025-07-24T02:11:58.493Z"}, {"_id": "67bcd42dd791c1e1a6d04d6b", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-24T05:00:00.000Z", "startDate": "2025-03-24T05:00:00.000Z", "endDate": "2025-03-30T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 371.49, "amountSpent": -371.49, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:18:53.628Z", "updatedAt": "2025-07-24T02:45:43.812Z"}, {"_id": "67bcd6e2d791c1e1a6d05241", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-05T05:00:00.000Z", "startDate": "2025-05-05T05:00:00.000Z", "endDate": "2025-05-11T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 273.68, "amountSpent": -273.68, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-24T02:47:03.925Z"}, {"_id": "67bcd6e2d791c1e1a6d05242", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-12T05:00:00.000Z", "startDate": "2025-05-12T05:00:00.000Z", "endDate": "2025-05-18T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 194.41, "amountSpent": -194.41, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-24T02:36:05.458Z"}, {"_id": "67bcd6e2d791c1e1a6d05243", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-19T05:00:00.000Z", "startDate": "2025-05-19T05:00:00.000Z", "endDate": "2025-05-25T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 80.68, "amountSpent": -80.68, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-23T20:51:11.304Z"}, {"_id": "67bcd6e2d791c1e1a6d05244", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-26T05:00:00.000Z", "startDate": "2025-05-26T05:00:00.000Z", "endDate": "2025-06-01T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 407.69, "amountSpent": -407.69, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-24T02:47:05.153Z"}, {"_id": "67bcd6e2d791c1e1a6d05249", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-05T05:00:00.000Z", "startDate": "2025-05-05T05:00:00.000Z", "endDate": "2025-05-11T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 89.19, "amountSpent": -89.19, "status": "underpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-24T02:47:00.177Z"}, {"_id": "67bcd6e2d791c1e1a6d0524a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-12T05:00:00.000Z", "startDate": "2025-05-12T05:00:00.000Z", "endDate": "2025-05-18T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 51.79, "amountSpent": -51.79, "status": "underpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-23T20:39:45.374Z"}, {"_id": "67bcd6e2d791c1e1a6d0524b", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-19T05:00:00.000Z", "startDate": "2025-05-19T05:00:00.000Z", "endDate": "2025-05-25T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 80.04, "amountSpent": -80.04, "status": "underpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-23T20:50:18.880Z"}, {"_id": "67bcd6e2d791c1e1a6d0524c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-26T05:00:00.000Z", "startDate": "2025-05-26T05:00:00.000Z", "endDate": "2025-06-01T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 216.87, "amountSpent": -216.87, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:30:26.722Z", "updatedAt": "2025-07-24T02:47:01.120Z"}, {"_id": "67bcd73fd791c1e1a6d05598", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-03T06:00:00.000Z", "startDate": "2025-03-03T06:00:00.000Z", "endDate": "2025-03-09T06:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-03-02T00:16:11.527Z"}, {"_id": "67bcd73fd791c1e1a6d05599", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-10T05:00:00.000Z", "startDate": "2025-03-10T05:00:00.000Z", "endDate": "2025-03-16T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 98.18, "amountSpent": -98.18, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-07-24T01:58:57.346Z"}, {"_id": "67bcd73fd791c1e1a6d0559a", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-17T05:00:00.000Z", "startDate": "2025-03-17T05:00:00.000Z", "endDate": "2025-03-23T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-03-02T00:16:14.600Z"}, {"_id": "67bcd73fd791c1e1a6d0559b", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-24T05:00:00.000Z", "startDate": "2025-03-24T05:00:00.000Z", "endDate": "2025-03-30T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 8.24, "amountSpent": -8.24, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-07-24T02:19:32.623Z"}, {"_id": "67bcd73fd791c1e1a6d0559c", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-03T06:00:00.000Z", "startDate": "2025-03-03T06:00:00.000Z", "endDate": "2025-03-09T06:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "status": "late", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-07-24T02:45:32.113Z"}, {"_id": "67bcd73fd791c1e1a6d0559d", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-10T05:00:00.000Z", "startDate": "2025-03-10T05:00:00.000Z", "endDate": "2025-03-16T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 161.67, "amountSpent": -161.67, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-07-24T02:45:33.581Z"}, {"_id": "67bcd73fd791c1e1a6d0559e", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-17T05:00:00.000Z", "startDate": "2025-03-17T05:00:00.000Z", "endDate": "2025-03-23T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 47.01, "amountSpent": -47.01, "status": "underpaid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-07-24T02:09:24.593Z"}, {"_id": "67bcd73fd791c1e1a6d0559f", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-24T05:00:00.000Z", "startDate": "2025-03-24T05:00:00.000Z", "endDate": "2025-03-30T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 86.34, "amountSpent": -86.34, "status": "underpaid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-07-24T02:19:49.977Z"}, {"_id": "67bcd73fd791c1e1a6d055a0", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-03T06:00:00.000Z", "startDate": "2025-03-03T06:00:00.000Z", "endDate": "2025-03-09T06:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-03-02T00:16:27.404Z"}, {"_id": "67bcd73fd791c1e1a6d055a1", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-10T05:00:00.000Z", "startDate": "2025-03-10T05:00:00.000Z", "endDate": "2025-03-16T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-03-22T13:16:16.866Z"}, {"_id": "67bcd73fd791c1e1a6d055a2", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-17T05:00:00.000Z", "startDate": "2025-03-17T05:00:00.000Z", "endDate": "2025-03-23T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-03-02T00:16:29.941Z"}, {"_id": "67bcd73fd791c1e1a6d055a3", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-03-24T05:00:00.000Z", "startDate": "2025-03-24T05:00:00.000Z", "endDate": "2025-03-30T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-02-24T20:31:59.007Z", "updatedAt": "2025-03-02T00:16:31.358Z"}, {"_id": "67bcd9acd791c1e1a6d05986", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-05T05:00:00.000Z", "startDate": "2025-05-05T05:00:00.000Z", "endDate": "2025-05-11T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 345.74, "amountSpent": -345.74, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:42:20.943Z", "updatedAt": "2025-07-24T02:47:08.211Z"}, {"_id": "67bcd9acd791c1e1a6d05987", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-12T05:00:00.000Z", "startDate": "2025-05-12T05:00:00.000Z", "endDate": "2025-05-18T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 388.94, "amountSpent": -388.94, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:42:20.943Z", "updatedAt": "2025-07-24T02:47:09.075Z"}, {"_id": "67bcd9acd791c1e1a6d05988", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-19T05:00:00.000Z", "startDate": "2025-05-19T05:00:00.000Z", "endDate": "2025-05-25T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 81.2, "amountSpent": -81.2, "status": "underpaid", "__v": 0, "createdAt": "2025-02-24T20:42:20.943Z", "updatedAt": "2025-07-23T20:51:09.723Z"}, {"_id": "67bcd9acd791c1e1a6d05989", "userId": "6764aeeccfd30a63a4a45566", "date": "2025-05-26T05:00:00.000Z", "startDate": "2025-05-26T05:00:00.000Z", "endDate": "2025-06-01T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 305.82, "amountSpent": -305.82, "status": "overpaid", "__v": 0, "createdAt": "2025-02-24T20:42:20.943Z", "updatedAt": "2025-07-24T02:47:09.880Z"}, {"_id": "67c0fa08f924d09cea98f8aa", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662038389, "date": "2025-03-31T17:00:00.000Z", "description": "Rent", "frequency": "monthly", "amountDue": 1500, "amountAssigned": 1500, "amountSpent": -1500, "status": "paid", "createdAt": "2025-02-27T23:49:28.561Z", "updatedAt": "2025-07-24T02:15:30.134Z", "__v": 0}, {"_id": "67c31738539603509c42a167", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-03-09T06:00:00.000Z", "description": "Haircut", "frequency": "oneoff", "amountDue": 170, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-03-01T14:18:32.400Z", "updatedAt": "2025-07-24T02:45:24.171Z", "__v": 0}, {"_id": "67c31ad7539603509c42a34b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-03-07T06:00:00.000Z", "description": "Little Farmers", "frequency": "oneoff", "amountDue": 30, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-03-01T14:33:59.768Z", "updatedAt": "2025-07-24T02:45:23.229Z", "__v": 0}, {"_id": "67c31b7c539603509c42a4c2", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-03-14T05:00:00.000Z", "description": "Florida Trip", "frequency": "oneoff", "amountDue": 63.2, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-03-01T14:36:44.370Z", "updatedAt": "2025-07-24T02:45:27.038Z", "__v": 0}, {"_id": "67c31cf1539603509c42a952", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752127638, "date": "2025-03-31T17:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 229.32, "amountAssigned": 229.32, "amountSpent": -229.32, "status": "paid", "__v": 0, "createdAt": "2025-03-01T14:42:57.323Z", "updatedAt": "2025-07-24T02:17:55.062Z"}, {"_id": "67c31d1e539603509c42ab56", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-03-03T06:00:00.000Z", "startDate": "2025-03-03T18:00:00.000Z", "endDate": "2025-03-09T17:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 479.77, "amountSpent": -479.77, "status": "overpaid", "createdAt": "2025-03-01T14:43:42.634Z", "updatedAt": "2025-07-24T02:45:37.748Z", "__v": 0}, {"_id": "67c32790539603509c42b52d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-03-10T05:00:00.000Z", "startDate": "2025-03-10T05:00:00.000Z", "endDate": "2025-03-16T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 362.31, "amountSpent": -362.31, "status": "overpaid", "createdAt": "2025-03-01T15:28:16.851Z", "updatedAt": "2025-07-24T02:45:38.678Z", "__v": 0}, {"_id": "67c32792539603509c42b5b9", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-03-17T05:00:00.000Z", "startDate": "2025-03-17T05:00:00.000Z", "endDate": "2025-03-23T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 500.33, "amountSpent": -500.33, "status": "overpaid", "createdAt": "2025-03-01T15:28:18.345Z", "updatedAt": "2025-07-24T02:11:56.371Z", "__v": 0}, {"_id": "67c32794539603509c42b63d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-03-24T05:00:00.000Z", "startDate": "2025-03-24T05:00:00.000Z", "endDate": "2025-03-30T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 95.71, "amountSpent": -95.71, "status": "underpaid", "createdAt": "2025-03-01T15:28:20.002Z", "updatedAt": "2025-07-24T02:45:39.468Z", "__v": 0}, {"_id": "67c3508b539603509c42bd97", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735757426773, "date": "2025-03-31T05:00:00.000Z", "description": "Homeschool", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "createdAt": "2025-03-01T18:23:07.735Z", "updatedAt": "2025-07-24T02:06:47.689Z", "__v": 0}, {"_id": "67c3925b539603509c42c7be", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-03-31T05:00:00.000Z", "startDate": "2025-03-31T05:00:00.000Z", "endDate": "2025-04-06T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 107.56, "amountSpent": -107.56, "status": "overpaid", "createdAt": "2025-03-01T23:03:55.543Z", "updatedAt": "2025-07-24T02:45:44.653Z", "__v": 0}, {"_id": "67c3925e539603509c42c842", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842589091, "date": "2025-03-31T05:00:00.000Z", "startDate": "2025-03-31T05:00:00.000Z", "endDate": "2025-04-06T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 11.52, "amountAssigned": 11.52, "amountSpent": -11.52, "status": "paid", "__v": 0, "createdAt": "2025-03-01T23:03:58.235Z", "updatedAt": "2025-07-24T02:13:59.610Z"}, {"_id": "67c3925e539603509c42c843", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-03-31T05:00:00.000Z", "startDate": "2025-03-31T05:00:00.000Z", "endDate": "2025-04-06T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 35.23, "amountSpent": -35.23, "status": "underpaid", "__v": 0, "createdAt": "2025-03-01T23:03:58.235Z", "updatedAt": "2025-07-24T02:14:33.617Z"}, {"_id": "67c3925e539603509c42c844", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308415957, "date": "2025-03-31T05:00:00.000Z", "startDate": "2025-03-31T05:00:00.000Z", "endDate": "2025-04-06T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-01T23:03:58.235Z", "updatedAt": "2025-04-15T16:15:35.653Z"}, {"_id": "67c3925e539603509c42c845", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-03-31T05:00:00.000Z", "startDate": "2025-03-31T05:00:00.000Z", "endDate": "2025-04-06T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 398.16, "amountSpent": -398.16, "status": "overpaid", "__v": 0, "createdAt": "2025-03-01T23:03:58.235Z", "updatedAt": "2025-07-24T02:17:59.102Z"}, {"_id": "67c3a3f0539603509c42e821", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836603983, "date": "2025-03-01T18:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 240, "amountSpent": -240, "status": "paid", "createdAt": "2025-03-02T00:18:56.211Z", "updatedAt": "2025-07-24T01:56:37.203Z", "__v": 0}, {"_id": "67cc5bc5ce3de484fe5acee6", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-03-14T05:00:00.000Z", "description": "Salem Deposit", "frequency": "oneoff", "amountDue": 128, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-03-08T15:01:25.473Z", "updatedAt": "2025-07-24T02:45:26.259Z", "__v": 0}, {"_id": "67d03a8350726158db0e1622", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-03-14T05:00:00.000Z", "description": "Houston Trip", "frequency": "oneoff", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-03-11T13:28:35.041Z", "updatedAt": "2025-07-24T02:45:25.237Z", "__v": 0}, {"_id": "67d1873f2fffe5614238dd5f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1741784849608, "date": "2025-03-11T05:00:00.000Z", "description": "Medication", "frequency": "monthly", "amountDue": 22, "amountAssigned": 194.98, "amountSpent": -194.98, "status": "overpaid", "createdAt": "2025-03-12T13:08:15.732Z", "updatedAt": "2025-07-24T02:45:53.387Z", "__v": 0}, {"_id": "67d19d2b2fffe56142390320", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308415957, "date": "2025-04-07T05:00:00.000Z", "startDate": "2025-04-07T05:00:00.000Z", "endDate": "2025-04-13T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-15T16:15:39.119Z"}, {"_id": "67d19d2b2fffe56142390326", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-04-21T05:00:00.000Z", "startDate": "2025-04-21T05:00:00.000Z", "endDate": "2025-04-27T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 103.75, "amountSpent": -103.75, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:32:19.531Z"}, {"_id": "67d19d2b2fffe5614239033b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836530312, "date": "2025-04-15T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 105.16, "amountAssigned": 105.16, "amountSpent": -105.16, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:23:39.130Z"}, {"_id": "67d19d2b2fffe56142390325", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-04-14T05:00:00.000Z", "startDate": "2025-04-14T05:00:00.000Z", "endDate": "2025-04-20T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 43.45, "amountSpent": -43.45, "status": "underpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:23:28.084Z"}, {"_id": "67d19d2b2fffe56142390338", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1741784849608, "date": "2025-04-11T05:00:00.000Z", "description": "Medication", "frequency": "monthly", "amountDue": 22, "amountAssigned": 156.06, "amountSpent": -156.06, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:31:12.733Z"}, {"_id": "67d19d2b2fffe5614239033f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735309223518, "date": "2025-04-26T05:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 10.65, "amountAssigned": 10.65, "amountSpent": -10.65, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:23:24.116Z"}, {"_id": "67d19d2b2fffe5614239031c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842589091, "date": "2025-04-07T05:00:00.000Z", "startDate": "2025-04-07T05:00:00.000Z", "endDate": "2025-04-13T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-14T13:53:57.848Z"}, {"_id": "67d19d2b2fffe56142390328", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-04-07T05:00:00.000Z", "startDate": "2025-04-07T05:00:00.000Z", "endDate": "2025-04-13T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 300, "amountAssigned": 421.66, "amountSpent": -421.66, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:26:48.469Z"}, {"_id": "67d19d2b2fffe56142390336", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836506748, "date": "2025-04-11T05:00:00.000Z", "description": "Renters Insurance", "frequency": "monthly", "amountDue": 22.92, "amountAssigned": 22.92, "amountSpent": -22.92, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:26:31.904Z"}, {"_id": "67d19d2b2fffe56142390343", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662038389, "date": "2025-04-30T05:00:00.000Z", "description": "Rent", "frequency": "monthly", "amountDue": 1500, "amountAssigned": 1500, "amountSpent": -1500, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:29:12.524Z"}, {"_id": "67d19d2b2fffe5614239032d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-04-14T05:00:00.000Z", "startDate": "2025-04-14T05:00:00.000Z", "endDate": "2025-04-20T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 232.61, "amountSpent": -232.61, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:31.008Z"}, {"_id": "67d19d2b2fffe5614239033c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836541484, "date": "2025-04-17T05:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 244.37, "amountAssigned": 442.55, "amountSpent": -442.55, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:36.485Z"}, {"_id": "67d19d2b2fffe5614239032b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-04-28T05:00:00.000Z", "startDate": "2025-04-28T05:00:00.000Z", "endDate": "2025-05-04T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 209.37, "amountSpent": -209.37, "status": "underpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:23.810Z"}, {"_id": "67d19d2b2fffe5614239032c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-04-07T05:00:00.000Z", "startDate": "2025-04-07T05:00:00.000Z", "endDate": "2025-04-13T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 447.58, "amountSpent": -447.58, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:29.373Z"}, {"_id": "67d19d2b2fffe5614239032f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-04-28T05:00:00.000Z", "startDate": "2025-04-28T05:00:00.000Z", "endDate": "2025-05-04T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 304.86, "amountSpent": -304.86, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:32.652Z"}, {"_id": "67d19d2b2fffe5614239033d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836565858, "date": "2025-04-21T05:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 51, "amountAssigned": 43.25, "amountSpent": -43.25, "status": "underpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:30:47.422Z"}, {"_id": "67d19d2b2fffe56142390345", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841935993, "date": "2025-04-30T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 50, "amountAssigned": 381.09, "amountSpent": -381.09, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:44.681Z"}, {"_id": "67d19d2b2fffe56142390348", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735757426773, "date": "2025-04-30T05:00:00.000Z", "description": "Homeschool", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-28T18:00:10.469Z"}, {"_id": "67d19d2b2fffe56142390337", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836554715, "date": "2025-04-11T05:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 27.05, "amountAssigned": 27.05, "amountSpent": -27.05, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:25:29.303Z"}, {"_id": "67d19d2b2fffe5614239031d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842589091, "date": "2025-04-14T05:00:00.000Z", "startDate": "2025-04-14T05:00:00.000Z", "endDate": "2025-04-20T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-15T13:39:52.303Z"}, {"_id": "67d19d2b2fffe56142390330", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662534886, "date": "2025-04-01T05:00:00.000Z", "description": "Water", "frequency": "monthly", "amountDue": 177.84, "amountAssigned": 177.84, "amountSpent": -177.84, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:15:23.254Z"}, {"_id": "67d19d2b2fffe56142390333", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752141968, "date": "2025-04-15T17:00:00.000Z", "description": "Dental Insurance", "frequency": "monthly", "amountDue": 47.96, "amountAssigned": 47.96, "amountSpent": -47.96, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:22:57.127Z"}, {"_id": "67d19d2b2fffe56142390331", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836603983, "date": "2025-04-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 240, "amountSpent": -240, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:16:53.181Z"}, {"_id": "67d19d2b2fffe56142390332", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735309636101, "date": "2025-04-01T05:00:00.000Z", "description": "Piano", "frequency": "monthly", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "status": "late", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:35.315Z"}, {"_id": "67d19d2b2fffe56142390321", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308415957, "date": "2025-04-14T05:00:00.000Z", "startDate": "2025-04-14T05:00:00.000Z", "endDate": "2025-04-20T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-15T16:15:43.580Z"}, {"_id": "67d19d2b2fffe56142390342", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836730046, "date": "2025-04-27T05:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 86.15, "amountAssigned": 0, "amountSpent": 0, "status": "late", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:38.064Z"}, {"_id": "67d19d2b2fffe56142390349", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1737039834652, "date": "2025-04-30T05:00:00.000Z", "description": "Apple Card Payment", "frequency": "monthly", "amountDue": 180, "amountAssigned": 390.97, "amountSpent": -390.97, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:43.672Z"}, {"_id": "67d19d2b2fffe56142390324", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-04-07T05:00:00.000Z", "startDate": "2025-04-07T05:00:00.000Z", "endDate": "2025-04-13T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 90.07, "amountSpent": -90.07, "status": "underpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:24:21.938Z"}, {"_id": "67d19d2b2fffe5614239032a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-04-21T05:00:00.000Z", "startDate": "2025-04-21T05:00:00.000Z", "endDate": "2025-04-27T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 308.83, "amountSpent": -308.83, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:31:17.558Z"}, {"_id": "67d19d2b2fffe56142390344", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841926686, "date": "2025-04-30T05:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 50.04, "amountAssigned": 255.91, "amountSpent": -255.91, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:42.440Z"}, {"_id": "67d19d2b2fffe56142390346", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735016086168, "date": "2025-04-30T05:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 100, "amountAssigned": 265.83, "amountSpent": -265.83, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:41.466Z"}, {"_id": "67d19d2b2fffe56142390347", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308966101, "date": "2025-04-30T05:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 89.65, "amountAssigned": 0, "amountSpent": 0, "status": "late", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:40.099Z"}, {"_id": "67d19d2b2fffe5614239031e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842589091, "date": "2025-04-21T05:00:00.000Z", "startDate": "2025-04-21T05:00:00.000Z", "endDate": "2025-04-27T05:00:00.000Z", "description": "Charging", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 10, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-15T15:59:48.985Z"}, {"_id": "67d19d2b2fffe56142390322", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308415957, "date": "2025-04-21T05:00:00.000Z", "startDate": "2025-04-21T05:00:00.000Z", "endDate": "2025-04-27T05:00:00.000Z", "description": "Tolls", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-04-15T16:15:47.282Z"}, {"_id": "67d19d2b2fffe5614239032e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-04-21T05:00:00.000Z", "startDate": "2025-04-21T05:00:00.000Z", "endDate": "2025-04-27T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 103.23, "amountSpent": -103.23, "status": "overpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:46:31.839Z"}, {"_id": "67d19d2b2fffe56142390334", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836492078, "date": "2025-04-04T05:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 18.39, "amountSpent": -18.39, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:14:07.732Z"}, {"_id": "67d19d2b2fffe56142390341", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836589455, "date": "2025-04-27T05:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 257.12, "amountAssigned": 257.12, "amountSpent": -257.12, "status": "paid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:28:00.660Z"}, {"_id": "67d19d2b2fffe56142390329", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-04-14T05:00:00.000Z", "startDate": "2025-04-14T05:00:00.000Z", "endDate": "2025-04-20T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 161.46, "amountSpent": -161.46, "status": "underpaid", "__v": 0, "createdAt": "2025-03-12T14:41:47.493Z", "updatedAt": "2025-07-24T02:23:34.801Z"}, {"_id": "67d94bfa48edb1291bc02e34", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-03-30T05:00:00.000Z", "description": "Gymnastics Comp", "frequency": "oneoff", "amountDue": 30, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "createdAt": "2025-03-18T10:33:30.775Z", "updatedAt": "2025-03-31T22:21:46.447Z", "__v": 0}, {"_id": "67e0a5f88cac3d31da4375a8", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752127638, "date": "2025-04-30T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 114.66, "amountAssigned": 114.66, "amountSpent": -114.66, "status": "paid", "createdAt": "2025-03-24T00:23:20.269Z", "updatedAt": "2025-07-24T02:27:54.035Z", "__v": 0}, {"_id": "67eb11bf8f770f7f35b4f0f0", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-04-23T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "oneoff", "amountDue": 100, "amountAssigned": 100, "amountSpent": -100, "status": "paid", "createdAt": "2025-03-31T22:05:51.536Z", "updatedAt": "2025-07-24T02:46:21.276Z", "__v": 0}, {"_id": "67eb11e18f770f7f35b4f1f1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-10T17:00:00.000Z", "description": "<PERSON> Play Place", "frequency": "oneoff", "amountDue": 150, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-03-31T22:06:25.156Z", "updatedAt": "2025-07-24T02:46:55.905Z", "__v": 0}, {"_id": "67f3cdad959853f939a03f8a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-04-28T05:00:00.000Z", "startDate": "2025-04-28T05:00:00.000Z", "endDate": "2025-05-04T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 134.27, "amountSpent": -134.27, "status": "overpaid", "createdAt": "2025-04-07T13:05:49.065Z", "updatedAt": "2025-07-24T02:46:27.234Z", "__v": 0}, {"_id": "67f4010b2681081cecf8a331", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662534886, "date": "2025-05-01T05:00:00.000Z", "description": "Water", "frequency": "monthly", "amountDue": 189.91, "amountAssigned": 189.91, "amountSpent": -189.91, "status": "paid", "createdAt": "2025-04-07T16:44:59.492Z", "updatedAt": "2025-07-24T02:29:08.397Z", "__v": 0}, {"_id": "67f4010c2681081cecf8a3b8", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836603983, "date": "2025-05-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 240, "amountSpent": -240, "status": "paid", "createdAt": "2025-04-07T16:45:00.564Z", "updatedAt": "2025-07-24T02:27:31.541Z", "__v": 0}, {"_id": "67f4010e2681081cecf8a4c7", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752141968, "date": "2025-05-15T17:00:00.000Z", "description": "Dental Insurance", "frequency": "monthly", "amountDue": 47.96, "amountAssigned": 47.96, "amountSpent": -47.96, "status": "paid", "createdAt": "2025-04-07T16:45:02.474Z", "updatedAt": "2025-07-24T02:35:15.086Z", "__v": 0}, {"_id": "67f4010f2681081cecf8a54d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836492078, "date": "2025-05-06T17:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 0, "amountSpent": 0, "status": "late", "createdAt": "2025-04-07T16:45:03.481Z", "updatedAt": "2025-07-24T02:47:46.126Z", "__v": 0}, {"_id": "67fd134016569ef7e1f2ec55", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1738279102930, "date": "2025-04-07T05:00:00.000Z", "description": "Prime Payment", "frequency": "monthly", "amountDue": 137.4, "amountAssigned": 137.4, "amountSpent": -137.4, "status": "paid", "createdAt": "2025-04-14T13:53:04.945Z", "updatedAt": "2025-07-24T02:25:18.690Z", "__v": 0}, {"_id": "67fd59789b30c5db9898bcb7", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-04-20T05:00:00.000Z", "description": "Sofi - Payment", "frequency": "monthly", "amountDue": 851.84, "amountAssigned": 851.84, "amountSpent": -851.84, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b54", "createdAt": "2025-04-14T18:52:40.739Z", "updatedAt": "2025-07-24T02:23:55.076Z", "__v": 0}, {"_id": "67fd5aa09b30c5db9898c3c1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-04-25T05:00:00.000Z", "description": "Tesla - Payment", "frequency": "monthly", "amountDue": 585, "amountAssigned": 600, "amountSpent": -600, "status": "overpaid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b58", "createdAt": "2025-04-14T18:57:36.359Z", "updatedAt": "2025-07-24T02:30:58.386Z", "__v": 0}, {"_id": "680e2d1fae30ba5fd41d3ded", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-20T17:00:00.000Z", "description": "Practice Leotard", "frequency": "oneoff", "amountDue": 116.81, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-04-27T13:11:59.409Z", "updatedAt": "2025-07-24T02:46:57.072Z", "__v": 0}, {"_id": "680fc40bd5fde75b1d8ca9f7", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-10T05:00:00.000Z", "description": "Discover - Payment", "frequency": "monthly", "amountDue": 185, "amountAssigned": 185, "amountSpent": -185, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b52", "createdAt": "2025-04-28T18:08:11.897Z", "updatedAt": "2025-07-24T02:37:06.899Z", "__v": 0}, {"_id": "680fc40cd5fde75b1d8caa8f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836506748, "date": "2025-05-11T05:00:00.000Z", "description": "Renters Insurance", "frequency": "monthly", "amountDue": 22.92, "amountAssigned": -7.08, "amountSpent": 7.08, "status": "underpaid", "isDebtPayment": false, "createdAt": "2025-04-28T18:08:12.688Z", "updatedAt": "2025-07-24T02:47:49.455Z", "__v": 0}, {"_id": "680fc40dd5fde75b1d8caae3", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836554715, "date": "2025-05-11T05:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 27.05, "amountAssigned": 27.05, "amountSpent": -27.05, "status": "paid", "isDebtPayment": false, "createdAt": "2025-04-28T18:08:13.198Z", "updatedAt": "2025-07-23T20:40:25.153Z", "__v": 0}, {"_id": "680fc40fd5fde75b1d8cab87", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1741784849608, "date": "2025-05-11T05:00:00.000Z", "description": "Medication", "frequency": "monthly", "amountDue": 22, "amountAssigned": 85.64, "amountSpent": -85.64, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-04-28T18:08:15.069Z", "updatedAt": "2025-07-24T02:47:49.009Z", "__v": 0}, {"_id": "6810d3401d656f75ca22891b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1737000894823, "date": "2025-05-07T05:00:00.000Z", "description": "Prime Payment", "frequency": "monthly", "amountDue": 66.25, "amountAssigned": 66.25, "amountSpent": -66.25, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.296Z", "updatedAt": "2025-07-24T02:36:54.573Z"}, {"_id": "6810d3401d656f75ca22891c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-13T05:00:00.000Z", "description": "Chase - Flex - Payment", "frequency": "monthly", "amountDue": 400, "amountAssigned": 400, "amountSpent": -400, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b4c", "__v": 0, "createdAt": "2025-04-29T13:25:20.296Z", "updatedAt": "2025-07-24T02:35:48.648Z"}, {"_id": "6810d3401d656f75ca22891d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836530312, "date": "2025-05-15T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 105.16, "amountAssigned": 107.66, "amountSpent": -107.66, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.296Z", "updatedAt": "2025-07-23T20:38:00.583Z"}, {"_id": "6810d3401d656f75ca22891e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836541484, "date": "2025-05-17T05:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 278.97, "amountAssigned": 278.97, "amountSpent": -278.97, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.296Z", "updatedAt": "2025-07-24T02:35:44.406Z"}, {"_id": "6810d3401d656f75ca228920", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836565858, "date": "2025-05-21T05:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 43.25, "amountAssigned": 43.25, "amountSpent": -43.25, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.296Z", "updatedAt": "2025-07-24T02:42:23.362Z"}, {"_id": "6810d3401d656f75ca228921", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-25T05:00:00.000Z", "description": "Tesla - Payment", "frequency": "monthly", "amountDue": 585, "amountAssigned": 584.84, "amountSpent": -584.84, "status": "underpaid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b58", "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-23T20:50:07.074Z"}, {"_id": "6810d3401d656f75ca228922", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735309223518, "date": "2025-05-21T17:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 10.65, "amountAssigned": 10.65, "amountSpent": -10.65, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-23T20:50:50.010Z"}, {"_id": "6810d3401d656f75ca228923", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836589455, "date": "2025-05-27T05:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 257.12, "amountAssigned": 22.92, "amountSpent": -22.92, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-24T02:47:51.173Z"}, {"_id": "6810d3401d656f75ca228924", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836730046, "date": "2025-05-29T05:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 65.38, "amountAssigned": 65.38, "amountSpent": -65.38, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-23T19:37:54.071Z"}, {"_id": "6810d3401d656f75ca228926", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841926686, "date": "2025-05-31T05:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 130.17, "amountSpent": -130.17, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-24T02:47:58.443Z"}, {"_id": "6810d3401d656f75ca228927", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841935993, "date": "2025-05-31T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 50, "amountAssigned": 415.86, "amountSpent": -415.86, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-24T02:47:57.253Z"}, {"_id": "6810d3401d656f75ca228928", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735016086168, "date": "2025-05-31T05:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 262.18, "amountAssigned": 51.74, "amountSpent": -51.74, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-24T02:47:55.417Z"}, {"_id": "6810d3401d656f75ca228929", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308966101, "date": "2025-05-31T05:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 0, "amountAssigned": 87.36, "amountSpent": -87.36, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-23T20:50:23.753Z"}, {"_id": "6810d3401d656f75ca22892a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752127638, "date": "2025-05-31T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 114.66, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-24T02:47:54.365Z"}, {"_id": "6810d3401d656f75ca22892b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735757426773, "date": "2025-05-31T05:00:00.000Z", "description": "Homeschool", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-05-22T12:56:45.682Z"}, {"_id": "6810d3401d656f75ca22892c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1737039834652, "date": "2025-05-31T05:00:00.000Z", "description": "Apple Card Payment", "frequency": "monthly", "amountDue": 0, "amountAssigned": 120, "amountSpent": -120, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-04-29T13:25:20.297Z", "updatedAt": "2025-07-24T02:47:53.155Z"}, {"_id": "68138c6d952af3fcde61a794", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-20T05:00:00.000Z", "description": "Sofi - Payment", "frequency": "monthly", "amountDue": 855.99, "amountAssigned": 855.99, "amountSpent": -855.99, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b54", "createdAt": "2025-05-01T14:59:57.995Z", "updatedAt": "2025-07-23T20:50:29.184Z", "__v": 0}, {"_id": "681a187cf26796f5598415b2", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662534886, "date": "2025-06-01T05:00:00.000Z", "description": "Water", "frequency": "monthly", "amountDue": 195.07, "amountAssigned": 195.07, "amountSpent": -195.07, "status": "paid", "isDebtPayment": false, "createdAt": "2025-05-06T14:11:08.340Z", "updatedAt": "2025-07-24T02:41:59.607Z", "__v": 0}, {"_id": "681a187ef26796f55984160b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836603983, "date": "2025-06-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 329.75, "amountSpent": -329.75, "status": "paid", "isDebtPayment": false, "createdAt": "2025-05-06T14:11:10.224Z", "updatedAt": "2025-07-23T20:35:47.066Z", "__v": 0}, {"_id": "681f7f1b7510a13374fd1ba0", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-06-30T05:00:00.000Z", "startDate": "2025-06-30T05:00:00.000Z", "endDate": "2025-07-06T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 38.77, "amountSpent": -38.77, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:08.634Z"}, {"_id": "681f7f1b7510a13374fd1ba1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-06-02T05:00:00.000Z", "startDate": "2025-06-02T05:00:00.000Z", "endDate": "2025-06-08T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 47.44, "amountSpent": -47.44, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:41:37.500Z"}, {"_id": "681f7f1b7510a13374fd1bb5", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836492078, "date": "2025-06-06T05:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 18.39, "amountSpent": -18.39, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-23T20:35:55.902Z"}, {"_id": "681f7f1b7510a13374fd1ba3", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-06-16T05:00:00.000Z", "startDate": "2025-06-16T05:00:00.000Z", "endDate": "2025-06-22T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 79.17, "amountSpent": -79.17, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:12.483Z"}, {"_id": "681f7f1b7510a13374fd1bc7", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841935993, "date": "2025-06-30T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 415.03, "amountSpent": -415.03, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-24T02:48:35.237Z"}, {"_id": "681f7f1b7510a13374fd1baf", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-06-30T05:00:00.000Z", "startDate": "2025-06-30T05:00:00.000Z", "endDate": "2025-07-06T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 22.03, "amountSpent": -22.03, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:21.544Z"}, {"_id": "681f7f1b7510a13374fd1bc0", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836565858, "date": "2025-06-21T05:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 43.25, "amountAssigned": 32.42, "amountSpent": -32.42, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:26.494Z"}, {"_id": "681f7f1b7510a13374fd1bca", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752127638, "date": "2025-06-30T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 114.66, "amountAssigned": 229.32, "amountSpent": -229.32, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-24T02:48:34.104Z"}, {"_id": "681f7f1b7510a13374fd1bc9", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308966101, "date": "2025-06-30T05:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 0, "amountAssigned": 32.99, "amountSpent": -32.99, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-24T02:48:33.227Z"}, {"_id": "681f7f1b7510a13374fd1bc3", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836589455, "date": "2025-06-27T05:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 107.28, "amountAssigned": 340.44, "amountSpent": -340.44, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-24T02:48:27.564Z"}, {"_id": "681f7f1b7510a13374fd1b9e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-06-16T05:00:00.000Z", "startDate": "2025-06-16T05:00:00.000Z", "endDate": "2025-06-22T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 464.33, "amountSpent": -464.33, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.578Z", "updatedAt": "2025-07-24T02:48:07.705Z"}, {"_id": "681f7f1b7510a13374fd1ba5", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-06-30T05:00:00.000Z", "startDate": "2025-06-30T05:00:00.000Z", "endDate": "2025-07-06T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 89.8, "amountSpent": -89.8, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-23T15:20:25.492Z"}, {"_id": "681f7f1b7510a13374fd1bbe", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836541484, "date": "2025-06-25T17:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 381.6, "amountAssigned": 381.6, "amountSpent": -381.6, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-23T15:25:14.874Z"}, {"_id": "681f7f1b7510a13374fd1bc6", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841926686, "date": "2025-06-30T05:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 315.47, "amountSpent": -315.47, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-24T02:48:32.224Z"}, {"_id": "681f7f1b7510a13374fd1b9f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-06-23T05:00:00.000Z", "startDate": "2025-06-23T05:00:00.000Z", "endDate": "2025-06-29T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 464.57, "amountSpent": -464.57, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T01:24:41.939Z"}, {"_id": "681f7f1b7510a13374fd1bab", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-06-02T05:00:00.000Z", "startDate": "2025-06-02T05:00:00.000Z", "endDate": "2025-06-08T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 214.06, "amountSpent": -214.06, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:16.404Z"}, {"_id": "681f7f1b7510a13374fd1bad", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-06-16T05:00:00.000Z", "startDate": "2025-06-16T05:00:00.000Z", "endDate": "2025-06-22T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 121.92, "amountSpent": -121.92, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:18.242Z"}, {"_id": "681f7f1b7510a13374fd1bc4", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836730046, "date": "2025-06-29T05:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 61.99, "amountAssigned": 61.99, "amountSpent": -61.99, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-23T15:25:21.281Z"}, {"_id": "681f7f1b7510a13374fd1ba4", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-06-23T05:00:00.000Z", "startDate": "2025-06-23T05:00:00.000Z", "endDate": "2025-06-29T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 68.26, "amountSpent": -68.26, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:13.378Z"}, {"_id": "681f7f1b7510a13374fd1bc8", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735016086168, "date": "2025-06-30T05:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 0, "amountAssigned": 28.04, "amountSpent": -28.04, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.580Z", "updatedAt": "2025-07-24T02:48:31.004Z"}, {"_id": "681f7f1b7510a13374fd1bae", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-06-23T05:00:00.000Z", "startDate": "2025-06-23T05:00:00.000Z", "endDate": "2025-06-29T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 270.57, "amountSpent": -270.57, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:30:19.579Z", "updatedAt": "2025-07-24T02:48:19.394Z"}, {"_id": "681f81f96e20188f9b57cda2", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-07-21T05:00:00.000Z", "startDate": "2025-07-21T05:00:00.000Z", "endDate": "2025-07-27T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 397.92, "amountSpent": -397.92, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-24T13:18:38.785Z"}, {"_id": "681f81f96e20188f9b57cdb2", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836565858, "date": "2025-07-21T05:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 43.25, "amountAssigned": 43.25, "amountSpent": -10.83, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-23T15:06:58.743Z"}, {"_id": "681f81f96e20188f9b57cdac", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1741784849608, "date": "2025-07-11T05:00:00.000Z", "description": "Medication", "frequency": "monthly", "amountDue": 22, "amountAssigned": 112.21, "amountSpent": -112.21, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-24T01:24:04.912Z"}, {"_id": "681f81f96e20188f9b57cdab", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836554715, "date": "2025-07-11T05:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 27.05, "amountAssigned": 19.47, "amountSpent": -19.47, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-23T15:16:45.919Z"}, {"_id": "681f81f96e20188f9b57cdb8", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841926686, "date": "2025-07-31T05:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 13.92, "amountSpent": -13.92, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-24T02:48:45.833Z"}, {"_id": "681f81f96e20188f9b57cdbb", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308966101, "date": "2025-07-31T05:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 58.86, "amountAssigned": 58.86, "amountSpent": -58.86, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-23T15:23:08.477Z"}, {"_id": "681f81f96e20188f9b57cd95", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-07-14T05:00:00.000Z", "startDate": "2025-07-14T05:00:00.000Z", "endDate": "2025-07-20T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 162.93, "amountSpent": -162.93, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-24T02:56:48.864Z"}, {"_id": "681f81f96e20188f9b57cd96", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-07-21T05:00:00.000Z", "startDate": "2025-07-21T05:00:00.000Z", "endDate": "2025-07-27T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 210, "amountSpent": -38.75, "status": "funded", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-24T13:18:44.271Z"}, {"_id": "681f81f96e20188f9b57cda0", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-07-07T05:00:00.000Z", "startDate": "2025-07-07T05:00:00.000Z", "endDate": "2025-07-13T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 157.76, "amountSpent": -157.76, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-24T02:56:57.818Z"}, {"_id": "681f81f96e20188f9b57cdb5", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836589455, "date": "2025-07-27T05:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 107.28, "amountAssigned": 107.28, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-22T03:26:03.447Z"}, {"_id": "681f81f96e20188f9b57cdb6", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836730046, "date": "2025-07-29T05:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 132.32, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-21T19:38:42.184Z"}, {"_id": "681f81f96e20188f9b57cdaf", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752141968, "date": "2025-07-15T05:00:00.000Z", "description": "Dental Insurance", "frequency": "monthly", "amountDue": 47.96, "amountAssigned": 47.96, "amountSpent": -47.96, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-23T15:07:12.519Z"}, {"_id": "681f81f96e20188f9b57cdb1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-20T05:00:00.000Z", "description": "Sofi - Payment", "frequency": "monthly", "amountDue": 0, "amountAssigned": 250, "amountSpent": -250, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b54", "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-23T15:06:37.873Z"}, {"_id": "681f81f96e20188f9b57cda1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-07-14T05:00:00.000Z", "startDate": "2025-07-14T05:00:00.000Z", "endDate": "2025-07-20T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 188.93, "amountSpent": -188.93, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-24T02:56:54.036Z"}, {"_id": "681f81f96e20188f9b57cd94", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-07-07T05:00:00.000Z", "startDate": "2025-07-07T05:00:00.000Z", "endDate": "2025-07-13T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 106.71, "amountSpent": -106.71, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-24T02:56:45.027Z"}, {"_id": "681f81f96e20188f9b57cda5", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836603983, "date": "2025-07-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 240, "amountSpent": -240, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-07-23T15:19:29.996Z"}, {"_id": "681f81f96e20188f9b57cdb9", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841935993, "date": "2025-07-31T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 30, "amountSpent": -30, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.544Z", "updatedAt": "2025-07-24T13:35:02.451Z"}, {"_id": "681f81f96e20188f9b57cd97", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-07-28T05:00:00.000Z", "startDate": "2025-07-28T05:00:00.000Z", "endDate": "2025-08-03T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-05-10T16:42:33.543Z", "updatedAt": "2025-05-10T16:42:33.543Z"}, {"_id": "681f95f7644d10ae9f7dd81a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-11-01T05:00:00.000Z", "description": "Christmas", "frequency": "oneoff", "amountDue": 2000, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "createdAt": "2025-05-10T18:07:51.332Z", "updatedAt": "2025-05-10T18:07:51.332Z", "__v": 0}, {"_id": "682347a3661652fb1a6f0090", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-13T05:00:00.000Z", "description": "Mint Phone", "frequency": "oneoff", "amountDue": 129.39, "amountAssigned": 129.39, "amountSpent": -129.39, "status": "paid", "isDebtPayment": false, "createdAt": "2025-05-13T13:22:43.794Z", "updatedAt": "2025-07-24T02:36:28.453Z", "__v": 0}, {"_id": "68285d4eeb26562d9e524d7e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1747475768916, "date": "2025-06-30T05:00:00.000Z", "description": "Misc Home", "frequency": "monthly", "amountDue": 0, "amountAssigned": 278.45, "amountSpent": -278.45, "status": "paid", "isDebtPayment": false, "createdAt": "2025-05-17T09:56:30.360Z", "updatedAt": "2025-07-24T02:48:29.981Z", "__v": 0}, {"_id": "68285d5986fef25db8d4d437", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1747475768916, "date": "2025-05-30T05:00:00.000Z", "description": "Misc Home", "frequency": "monthly", "amountDue": 126.57, "amountAssigned": 23.98, "amountSpent": -23.98, "status": "underpaid", "isDebtPayment": false, "createdAt": "2025-05-17T09:56:41.111Z", "updatedAt": "2025-07-24T02:47:52.144Z", "__v": 0}, {"_id": "682f1dd64dbc5e75c9d6f62b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-05-22T05:00:00.000Z", "description": "Comp Leotard", "frequency": "oneoff", "amountDue": 125, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-05-22T12:51:34.623Z", "updatedAt": "2025-07-24T02:46:57.894Z", "__v": 0}, {"_id": "68388309f61cf7ee0b96c4d7", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735309223518, "date": "2025-06-21T05:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 10.65, "amountAssigned": 21.31, "amountSpent": -21.31, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-05-29T15:53:45.952Z", "updatedAt": "2025-07-23T15:28:06.035Z", "__v": 0}, {"_id": "683b8b748533ed354bbde112", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-10T05:00:00.000Z", "description": "Amazon Card - Payment", "frequency": "monthly", "amountDue": 30, "amountAssigned": 157.6, "amountSpent": -157.6, "status": "overpaid", "isDebtPayment": true, "debtId": "683b84ab2095d7a9497970d2", "__v": 0, "createdAt": "2025-05-31T23:06:28.644Z", "updatedAt": "2025-07-24T02:48:23.570Z"}, {"_id": "683b8b748533ed354bbde115", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-20T05:00:00.000Z", "description": "Sofi - Payment", "frequency": "monthly", "amountDue": 855.99, "amountAssigned": 855.99, "amountSpent": -855.99, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b54", "__v": 0, "createdAt": "2025-05-31T23:06:28.644Z", "updatedAt": "2025-07-23T15:26:46.677Z"}, {"_id": "683b8b748533ed354bbde117", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-30T05:00:00.000Z", "description": "Apple Card - Payment", "frequency": "monthly", "amountDue": 100, "amountAssigned": 254.53, "amountSpent": -254.53, "status": "paid", "isDebtPayment": true, "debtId": "683b83e72095d7a9497970cf", "__v": 0, "createdAt": "2025-05-31T23:06:28.644Z", "updatedAt": "2025-07-24T02:48:29.043Z"}, {"_id": "683b8b748533ed354bbde116", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-25T05:00:00.000Z", "description": "Tesla - Payment", "frequency": "monthly", "amountDue": 168.48, "amountAssigned": 4638.74, "amountSpent": -4638.74, "status": "overpaid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b58", "__v": 0, "createdAt": "2025-05-31T23:06:28.644Z", "updatedAt": "2025-07-23T15:27:10.973Z"}, {"_id": "683b8b748533ed354bbde113", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-11T05:00:00.000Z", "description": "Polygon Digital - Payment", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "isDebtPayment": true, "debtId": "67fd482c9b30c5db9898a3de", "__v": 0, "createdAt": "2025-05-31T23:06:28.644Z", "updatedAt": "2025-06-03T20:17:45.353Z"}, {"_id": "68445be6762910737fb406a9", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-06-09T05:00:00.000Z", "startDate": "2025-06-09T05:00:00.000Z", "endDate": "2025-06-15T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 234.81, "amountSpent": -234.81, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-06-07T15:33:58.842Z", "updatedAt": "2025-07-24T02:48:17.297Z"}, {"_id": "68445be6762910737fb406ab", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-06-09T05:00:00.000Z", "startDate": "2025-06-09T05:00:00.000Z", "endDate": "2025-06-15T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 100, "amountAssigned": 92.95, "amountSpent": -92.95, "status": "underpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-06-07T15:33:58.855Z", "updatedAt": "2025-07-24T02:48:11.469Z"}, {"_id": "68445be6762910737fb406af", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836554715, "date": "2025-06-11T05:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 27.05, "amountAssigned": 27.05, "amountSpent": -27.05, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-06-07T15:33:58.855Z", "updatedAt": "2025-07-24T02:40:16.432Z"}, {"_id": "68445be6762910737fb406b1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-11T05:00:00.000Z", "description": "Polygon Digital - Payment", "frequency": "monthly", "amountDue": 214.58, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "67fd482c9b30c5db9898a3de", "__v": 0, "createdAt": "2025-06-07T15:33:58.855Z", "updatedAt": "2025-06-07T15:33:58.855Z"}, {"_id": "68445be6762910737fb406b3", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836530312, "date": "2025-06-15T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 105.16, "amountAssigned": 110.91, "amountSpent": -110.91, "status": "overpaid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-06-07T15:33:58.855Z", "updatedAt": "2025-07-23T15:29:23.305Z"}, {"_id": "68445f62e5551f532edc443f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-12T05:00:00.000Z", "description": "Galveston Trip", "frequency": "oneoff", "amountDue": 500, "amountAssigned": 329.27, "amountSpent": -329.27, "status": "underpaid", "isDebtPayment": false, "createdAt": "2025-06-07T15:48:50.418Z", "updatedAt": "2025-07-24T02:48:03.169Z", "__v": 0}, {"_id": "6844c527604450c22d8bef7a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-01T05:00:00.000Z", "description": "Guild Mortgage - Payment", "frequency": "monthly", "amountDue": 2824.81, "amountAssigned": 2824.81, "amountSpent": -2824.81, "status": "paid", "isDebtPayment": true, "debtId": "6844c4ee317f216c79330f0c", "createdAt": "2025-06-07T23:03:03.816Z", "updatedAt": "2025-07-23T15:19:33.766Z", "__v": 0}, {"_id": "684d7d5d101de261ab90caba", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-01T05:00:00.000Z", "description": "Discover - Payment", "frequency": "monthly", "amountDue": 57, "amountAssigned": 57, "amountSpent": -57, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b52", "createdAt": "2025-06-14T13:47:09.634Z", "updatedAt": "2025-07-23T15:19:13.684Z", "__v": 0}, {"_id": "684d884d08b5573ada01ae33", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-06-09T05:00:00.000Z", "startDate": "2025-06-09T05:00:00.000Z", "endDate": "2025-06-15T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 339.52, "amountSpent": -339.52, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-06-14T14:33:49.249Z", "updatedAt": "2025-07-24T02:48:06.563Z", "__v": 0}, {"_id": "684d8a2d08b5573ada01b6a0", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-06-02T05:00:00.000Z", "startDate": "2025-06-02T05:00:00.000Z", "endDate": "2025-06-08T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 713.4, "amountSpent": -713.4, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-06-14T14:41:49.524Z", "updatedAt": "2025-07-24T01:24:40.368Z", "__v": 0}, {"_id": "684d93434b0332f1d4bce91a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1741784849608, "date": "2025-06-11T05:00:00.000Z", "description": "Medication", "frequency": "monthly", "amountDue": 22, "amountAssigned": 20.64, "amountSpent": -20.64, "status": "underpaid", "isDebtPayment": false, "createdAt": "2025-06-14T15:20:35.998Z", "updatedAt": "2025-07-24T02:48:24.463Z", "__v": 0}, {"_id": "6852c2efe51a2fdd249e83d8", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752141968, "date": "2025-06-15T05:00:00.000Z", "description": "Dental Insurance", "frequency": "monthly", "amountDue": 47.96, "amountAssigned": 47.96, "amountSpent": -47.96, "status": "paid", "isDebtPayment": false, "createdAt": "2025-06-18T13:45:19.432Z", "updatedAt": "2025-07-23T15:29:08.367Z", "__v": 0}, {"_id": "6852c8d3a79cc68e0dfe8afe", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-06-13T05:00:00.000Z", "description": "Flex Card - Payment", "frequency": "monthly", "amountDue": 91.53, "amountAssigned": 91.53, "amountSpent": -91.53, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b4c", "createdAt": "2025-06-18T14:10:27.283Z", "updatedAt": "2025-07-24T02:40:04.153Z", "__v": 0}, {"_id": "6852f72ca79cc68e0dfeb75e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-13T05:00:00.000Z", "description": "Flex Card - Payment", "frequency": "monthly", "amountDue": 91.53, "amountAssigned": 91.53, "amountSpent": -91.53, "status": "paid", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b4c", "createdAt": "2025-06-18T17:28:12.842Z", "updatedAt": "2025-07-23T15:17:20.303Z", "__v": 0}, {"_id": "685b05cebc3430743cfce9a4", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735309223518, "date": "2025-07-21T05:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 10.65, "amountAssigned": 21.31, "amountSpent": -21.31, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-06-24T20:08:46.909Z", "updatedAt": "2025-07-23T15:05:58.915Z", "__v": 0}, {"_id": "685c10921d83b1930e3b5909", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1750863147684, "date": "2025-06-27T05:00:00.000Z", "description": "Amazon Prime", "frequency": "annually", "amountDue": 150.47, "amountAssigned": 188.35, "amountSpent": -188.35, "status": "paid", "isDebtPayment": false, "createdAt": "2025-06-25T15:06:58.346Z", "updatedAt": "2025-07-24T02:48:36.855Z", "__v": 0}, {"_id": "686433a0ddd7f65f86a3da18", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-10T05:00:00.000Z", "description": "Paypal - Payment", "frequency": "monthly", "amountDue": 30, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "683b8b29e93c81d0cb9615e6", "createdAt": "2025-07-01T19:14:40.009Z", "updatedAt": "2025-07-24T02:56:59.031Z", "__v": 0}, {"_id": "6864351e797e817e87efca7f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836603983, "date": "2025-08-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 240, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "createdAt": "2025-07-01T19:21:02.810Z", "updatedAt": "2025-07-01T19:21:02.810Z", "__v": 0}, {"_id": "68643521797e817e87efcc0d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-08-01T05:00:00.000Z", "description": "Guild Mortgage - Payment", "frequency": "monthly", "amountDue": 2824.81, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "6844c4ee317f216c79330f0c", "createdAt": "2025-07-01T19:21:05.693Z", "updatedAt": "2025-07-24T17:21:36.588Z", "__v": 0}, {"_id": "686833fab311a9700777e50e", "userId": "6868175f3a3bc56fcccb723c", "recurringExpenseId": 1, "date": "2025-07-01T05:00:00.000Z", "description": "Rent", "frequency": "monthly", "amountDue": 1000, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-07-04T20:05:14.079Z", "updatedAt": "2025-07-04T20:05:14.079Z", "__v": 0}, {"_id": "686bef92c813d182752d5e3e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-07-07T05:00:00.000Z", "startDate": "2025-07-07T05:00:00.000Z", "endDate": "2025-07-13T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 70, "amountAssigned": 85.99, "amountSpent": -85.99, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-07-07T16:02:26.785Z", "updatedAt": "2025-07-23T15:17:08.755Z", "__v": 0}, {"_id": "686bef95c813d182752d5f65", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-07-14T05:00:00.000Z", "startDate": "2025-07-14T05:00:00.000Z", "endDate": "2025-07-20T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 70, "amountAssigned": 109.75, "amountSpent": -109.75, "status": "overpaid", "isDebtPayment": false, "createdAt": "2025-07-07T16:02:29.096Z", "updatedAt": "2025-07-23T15:07:44.108Z", "__v": 0}, {"_id": "686bef96c813d182752d5fe4", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-07-21T05:00:00.000Z", "startDate": "2025-07-21T05:00:00.000Z", "endDate": "2025-07-27T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 70, "amountAssigned": 70, "amountSpent": -47.09, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-07T16:02:30.808Z", "updatedAt": "2025-07-24T05:51:52.699Z", "__v": 0}, {"_id": "686c529b9f25330265b753e4", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836492078, "date": "2025-07-06T05:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 18.39, "amountSpent": -18.39, "status": "paid", "isDebtPayment": false, "createdAt": "2025-07-07T23:04:59.063Z", "updatedAt": "2025-07-23T15:18:49.960Z", "__v": 0}, {"_id": "686c54689f25330265b79e8e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836541484, "date": "2025-07-25T05:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 410.7, "amountAssigned": 410.7, "amountSpent": -410.7, "status": "paid", "isDebtPayment": false, "createdAt": "2025-07-07T23:12:40.687Z", "updatedAt": "2025-07-24T13:18:41.973Z", "__v": 0}, {"_id": "686d249cd35e9e695fac485d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-08T05:00:00.000Z", "description": "<PERSON>", "frequency": "oneoff", "amountDue": 106.5, "amountAssigned": 109.5, "amountSpent": -109.5, "status": "paid", "isDebtPayment": false, "createdAt": "2025-07-08T14:01:00.937Z", "updatedAt": "2025-07-24T01:23:45.616Z", "__v": 0}, {"_id": "686d4d44d5e54213f25c5aa1", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-06T05:00:00.000Z", "description": "Nintendo", "frequency": "monthly", "amountDue": 4.24, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-07-08T16:54:28.344Z", "updatedAt": "2025-07-24T02:57:13.015Z", "__v": 0}, {"_id": "686d4e73d5e54213f25c871d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-23T17:00:00.000Z", "description": "Haircut", "frequency": "oneoff", "amountDue": 200, "amountAssigned": 255, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-07-08T16:59:31.913Z", "updatedAt": "2025-07-24T14:02:26.047Z", "__v": 0}, {"_id": "686d4e89d5e54213f25c88ed", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-26T05:00:00.000Z", "description": "Pool Party", "frequency": "oneoff", "amountDue": 120, "amountAssigned": 120, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-08T16:59:53.032Z", "updatedAt": "2025-07-22T03:25:48.402Z", "__v": 0}, {"_id": "687281dd9158cf5ed933163b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662534886, "date": "2025-07-12T17:00:00.000Z", "description": "Water", "frequency": "monthly", "amountDue": 188.54, "amountAssigned": 360.54, "amountSpent": -360.54, "status": "paid", "isDebtPayment": false, "createdAt": "2025-07-12T15:40:13.434Z", "updatedAt": "2025-07-24T02:48:43.640Z", "__v": 0}, {"_id": "6872a74a8d933d2fce22ba12", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-08-04T05:00:00.000Z", "startDate": "2025-08-04T05:00:00.000Z", "endDate": "2025-08-10T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba14", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-08-18T05:00:00.000Z", "startDate": "2025-08-18T05:00:00.000Z", "endDate": "2025-08-24T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba23", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-08-20T05:00:00.000Z", "description": "Sofi - Payment", "frequency": "monthly", "amountDue": 855.99, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b54", "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba2c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841935993, "date": "2025-08-31T05:00:00.000Z", "description": "<PERSON><PERSON><PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba0e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-08-04T05:00:00.000Z", "startDate": "2025-08-04T05:00:00.000Z", "endDate": "2025-08-10T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 70, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba1a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836492078, "date": "2025-08-06T05:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba1e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1741784849608, "date": "2025-08-11T05:00:00.000Z", "description": "Medication", "frequency": "monthly", "amountDue": 22, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba0f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-08-11T05:00:00.000Z", "startDate": "2025-08-11T05:00:00.000Z", "endDate": "2025-08-17T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 70, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba2e", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735308966101, "date": "2025-08-31T05:00:00.000Z", "description": "Pets", "frequency": "monthly", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba24", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836565858, "date": "2025-08-21T05:00:00.000Z", "description": "Gym", "frequency": "monthly", "amountDue": 43.25, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba1c", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-16T17:00:00.000Z", "description": "Paypal - Payment", "frequency": "monthly", "amountDue": 379.16, "amountAssigned": 379.16, "amountSpent": -379.16, "status": "paid", "isDebtPayment": true, "debtId": "683b8b29e93c81d0cb9615e6", "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-23T15:07:23.536Z"}, {"_id": "6872a74a8d933d2fce22ba18", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-08-18T05:00:00.000Z", "startDate": "2025-08-18T05:00:00.000Z", "endDate": "2025-08-24T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba27", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836589455, "date": "2025-08-27T05:00:00.000Z", "description": "Car Insurance", "frequency": "monthly", "amountDue": 107.28, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba15", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-08-25T05:00:00.000Z", "startDate": "2025-08-25T05:00:00.000Z", "endDate": "2025-08-31T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba1d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836554715, "date": "2025-08-11T05:00:00.000Z", "description": "Netflix", "frequency": "monthly", "amountDue": 27.05, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba25", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735309223518, "date": "2025-08-21T05:00:00.000Z", "description": "Google Drive", "frequency": "monthly", "amountDue": 21.31, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba28", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836730046, "date": "2025-08-29T05:00:00.000Z", "description": "Natural Gas", "frequency": "monthly", "amountDue": 132.32, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba10", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734842711824, "date": "2025-08-18T05:00:00.000Z", "startDate": "2025-08-18T05:00:00.000Z", "endDate": "2025-08-24T05:00:00.000Z", "description": "Gas", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 70, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba17", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-08-11T05:00:00.000Z", "startDate": "2025-08-11T05:00:00.000Z", "endDate": "2025-08-17T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba1b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1751993668153, "date": "2025-08-06T05:00:00.000Z", "description": "Nintendo", "frequency": "monthly", "amountDue": 4.24, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba16", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-08-04T05:00:00.000Z", "startDate": "2025-08-04T05:00:00.000Z", "endDate": "2025-08-10T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba20", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-08-13T05:00:00.000Z", "description": "Flex Card - Payment", "frequency": "monthly", "amountDue": 91.53, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b4c", "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba29", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1747475768916, "date": "2025-08-30T05:00:00.000Z", "description": "Misc Home", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba19", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662544797, "date": "2025-08-25T05:00:00.000Z", "startDate": "2025-08-25T05:00:00.000Z", "endDate": "2025-08-31T05:00:00.000Z", "description": "Eating Out", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 50, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba1f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-08-11T05:00:00.000Z", "description": "Polygon Digital - Payment", "frequency": "monthly", "amountDue": 214.58, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "67fd482c9b30c5db9898a3de", "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba2b", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734841926686, "date": "2025-08-31T05:00:00.000Z", "description": "<PERSON>", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba22", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752141968, "date": "2025-08-15T05:00:00.000Z", "description": "Dental Insurance", "frequency": "monthly", "amountDue": 47.96, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba26", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836541484, "date": "2025-08-25T05:00:00.000Z", "description": "Electric", "frequency": "monthly", "amountDue": 280, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba2d", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735016086168, "date": "2025-08-31T05:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 100, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba2f", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752127638, "date": "2025-08-31T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 114.66, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6872a74a8d933d2fce22ba13", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836840323, "date": "2025-08-11T05:00:00.000Z", "startDate": "2025-08-11T05:00:00.000Z", "endDate": "2025-08-17T05:00:00.000Z", "description": "Groceries", "frequency": "weekly", "weeklyChargeType": "spread", "amountDue": 250, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-12T18:19:54.649Z", "updatedAt": "2025-07-12T18:19:54.649Z"}, {"_id": "6876d2857eed17c046700f65", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734836530312, "date": "2025-08-05T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 105.16, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "createdAt": "2025-07-15T22:13:25.120Z", "updatedAt": "2025-07-15T22:13:25.120Z", "__v": 0}, {"_id": "6876fd650517e7fd4ee08b0a", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-07-31T05:00:00.000Z", "description": "Apple Card - Payment", "frequency": "monthly", "amountDue": 63, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "683b83e72095d7a9497970cf", "createdAt": "2025-07-16T01:16:21.124Z", "updatedAt": "2025-07-16T01:16:21.124Z", "__v": 0}, {"_id": "6876fdcc891604a503854b64", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735016086168, "date": "2025-07-31T05:00:00.000Z", "description": "Kids Misc", "frequency": "monthly", "amountDue": 0, "amountAssigned": 28.04, "amountSpent": -28.04, "status": "paid", "isDebtPayment": false, "createdAt": "2025-07-16T01:18:04.886Z", "updatedAt": "2025-07-23T15:05:38.888Z", "__v": 0}, {"_id": "6876fde8cb1ac6a5005337b9", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-08-31T05:00:00.000Z", "description": "Apple Card - Payment", "frequency": "monthly", "amountDue": 63, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "683b83e72095d7a9497970cf", "createdAt": "2025-07-16T01:18:32.506Z", "updatedAt": "2025-07-16T01:18:32.506Z", "__v": 0}, {"_id": "68790aed01bf8e3cdab166b2", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1734662534886, "date": "2025-07-27T05:00:00.000Z", "description": "Water", "frequency": "monthly", "amountDue": 146, "amountAssigned": 146, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-17T14:38:37.594Z", "updatedAt": "2025-07-22T03:26:09.715Z", "__v": 0}, {"_id": "687ab19c46ae19fefc071301", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": null, "date": "2025-08-01T05:00:00.000Z", "description": "Discover - Payment", "frequency": "monthly", "amountDue": 57, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "67fd46d3232952bf8d499b52", "createdAt": "2025-07-18T20:42:04.984Z", "updatedAt": "2025-07-18T20:42:04.984Z", "__v": 0}, {"_id": "687b298fbd6b1cedc9eae2ee", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 2, "date": "2025-07-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 104, "amountAssigned": 104, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T05:13:51.363Z", "updatedAt": "2025-07-19T06:01:39.523Z"}, {"_id": "687b298fbd6b1cedc9eae2ef", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 3, "date": "2025-07-01T05:00:00.000Z", "description": "Dragonfly Martial Arts", "frequency": "monthly", "amountDue": 111.04, "amountAssigned": 111.04, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T05:13:51.363Z", "updatedAt": "2025-07-19T06:02:32.767Z"}, {"_id": "687b2e68ed52f4c5df720b0a", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-15T05:00:00.000Z", "description": "Spotify", "frequency": "monthly", "amountDue": 18.39, "amountAssigned": 18.39, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:34:32.789Z", "updatedAt": "2025-07-19T06:01:47.937Z", "__v": 0}, {"_id": "687b2ef72fc2c97276d6a9a8", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-17T05:36:54.975Z", "startDate": "2025-07-17T05:36:54.975Z", "description": "Scoop Soldiers", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 23.8, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "createdAt": "2025-07-19T05:36:55.141Z", "updatedAt": "2025-07-19T05:36:55.482Z", "__v": 0}, {"_id": "687b2fad536b1910ebef6dea", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-15T05:39:56.973Z", "startDate": "2025-07-15T05:39:56.973Z", "description": "Daycare", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 245, "amountAssigned": 245, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:39:57.085Z", "updatedAt": "2025-07-19T06:10:40.440Z", "__v": 0}, {"_id": "687b2fd1b660f8e9aecc81df", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-14T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 101.31, "amountAssigned": 101.31, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:40:33.010Z", "updatedAt": "2025-07-19T06:01:46.816Z", "__v": 0}, {"_id": "687b301aed52f4c5df720c2f", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-13T05:00:00.000Z", "description": "Sling TV", "frequency": "monthly", "amountDue": 85.51, "amountAssigned": 85.51, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:41:46.546Z", "updatedAt": "2025-07-19T06:01:45.511Z", "__v": 0}, {"_id": "687b3107ed52f4c5df720d08", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-01T05:00:00.000Z", "description": "Hagerty Car Insurance", "frequency": "monthly", "amountDue": 103.79, "amountAssigned": 103.79, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:45:43.373Z", "updatedAt": "2025-07-19T06:01:38.235Z", "__v": 0}, {"_id": "687b311c16c9faeadcbd1d7b", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-09T05:00:00.000Z", "description": "Water Bill", "frequency": "monthly", "amountDue": 90.41, "amountAssigned": 90.41, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:46:04.122Z", "updatedAt": "2025-07-19T06:01:43.627Z", "__v": 0}, {"_id": "687b3157ed52f4c5df720d6e", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-05T00:00:00.000Z", "description": "Drift Event", "frequency": "oneoff", "amountDue": 300, "amountAssigned": 300, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:47:03.738Z", "updatedAt": "2025-07-19T06:01:25.916Z", "__v": 0}, {"_id": "687b31f9ed52f4c5df720f95", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-07T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 431.85, "amountAssigned": 462.85, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:49:45.414Z", "updatedAt": "2025-07-19T06:01:42.455Z", "__v": 0}, {"_id": "687b326716c9faeadcbd201c", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-01T05:00:00.000Z", "description": "Gym Station", "frequency": "monthly", "amountDue": 63, "amountAssigned": 126, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:51:35.811Z", "updatedAt": "2025-07-19T06:01:51.524Z", "__v": 0}, {"_id": "687b32f32fc2c97276d6ad7b", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-03T05:00:00.000Z", "description": "Electricity Bill", "frequency": "monthly", "amountDue": 173.03, "amountAssigned": 173.03, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:53:55.690Z", "updatedAt": "2025-07-19T06:01:41.491Z", "__v": 0}, {"_id": "687b330d2fc2c97276d6adef", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-01T05:00:00.000Z", "description": "Dakboard", "frequency": "monthly", "amountDue": 10, "amountAssigned": 10, "amountSpent": 0, "status": "funded", "isDebtPayment": false, "createdAt": "2025-07-19T05:54:21.576Z", "updatedAt": "2025-07-19T06:01:34.320Z", "__v": 0}, {"_id": "687b36510c84a954fdce600b", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903596942, "date": "2025-07-01T05:00:00.000Z", "startDate": "2025-07-01T05:00:00.000Z", "endDate": null, "description": "Daycare", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 245, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-07-19T06:08:17.323Z", "updatedAt": "2025-07-19T06:08:17.323Z", "__v": 0}, {"_id": "687b365b0c84a954fdce607c", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903414859, "date": "2025-07-03T05:00:00.000Z", "startDate": null, "description": "Scoop Soldiers", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 23.8, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": false, "createdAt": "2025-07-19T06:08:27.184Z", "updatedAt": "2025-07-19T06:08:27.184Z", "__v": 0}, {"_id": "687b38970c84a954fdce6299", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903414859, "date": "2025-08-14T05:00:00.000Z", "description": "Scoop Soldiers", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 23.8, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce62a5", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903632858, "date": "2025-08-14T05:00:00.000Z", "description": "Internet", "frequency": "monthly", "amountDue": 101.31, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.188Z", "updatedAt": "2025-07-19T06:17:59.188Z"}, {"_id": "687b38970c84a954fdce629d", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903943209, "date": "2025-08-01T05:00:00.000Z", "description": "Hagerty Car Insurance", "frequency": "monthly", "amountDue": 103.79, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce6296", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903596942, "date": "2025-08-05T05:00:00.000Z", "description": "Daycare", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 245, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce6297", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903596942, "date": "2025-08-12T05:00:00.000Z", "description": "Daycare", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 245, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce62a0", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752904435401, "date": "2025-08-03T05:00:00.000Z", "description": "Electricity Bill", "frequency": "monthly", "amountDue": 173.03, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.187Z", "updatedAt": "2025-07-19T06:17:59.187Z"}, {"_id": "687b38970c84a954fdce62a3", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-08-09T05:00:00.000Z", "description": "USAA - Payment", "frequency": "monthly", "amountDue": 316, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": true, "debtId": "687b1871a6b7ca864f7ff4b8", "__v": 0, "createdAt": "2025-07-19T06:17:59.188Z", "updatedAt": "2025-07-19T06:17:59.188Z"}, {"_id": "687b38970c84a954fdce629b", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 2, "date": "2025-08-01T05:00:00.000Z", "description": "Gymnastics", "frequency": "monthly", "amountDue": 115, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce629a", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1, "date": "2025-08-01T05:00:00.000Z", "description": "Mortgage", "frequency": "monthly", "amountDue": 1936, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:19:09.733Z"}, {"_id": "687b38970c84a954fdce62a4", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903706381, "date": "2025-08-13T05:00:00.000Z", "description": "Sling TV", "frequency": "monthly", "amountDue": 85.51, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.188Z", "updatedAt": "2025-07-19T06:17:59.188Z"}, {"_id": "687b38970c84a954fdce629c", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 3, "date": "2025-08-01T05:00:00.000Z", "description": "Dragonfly Martial Arts", "frequency": "monthly", "amountDue": 105, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce629f", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752904461281, "date": "2025-08-01T05:00:00.000Z", "description": "Dakboard", "frequency": "monthly", "amountDue": 10, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.187Z", "updatedAt": "2025-07-19T06:17:59.187Z"}, {"_id": "687b38970c84a954fdce62a1", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752904185269, "date": "2025-08-07T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 431.85, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.187Z", "updatedAt": "2025-07-19T06:17:59.187Z"}, {"_id": "687b38970c84a954fdce6298", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903414859, "date": "2025-08-07T05:00:00.000Z", "description": "Scoop Soldiers", "frequency": "weekly", "weeklyChargeType": "one-time", "amountDue": 23.8, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.186Z", "updatedAt": "2025-07-19T06:17:59.186Z"}, {"_id": "687b38970c84a954fdce62a2", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": 1752903963970, "date": "2025-08-09T05:00:00.000Z", "description": "Water Bill", "frequency": "monthly", "amountDue": 90.41, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-19T06:17:59.187Z", "updatedAt": "2025-07-19T06:17:59.187Z"}, {"_id": "687b3e5cfce915752bd795c2", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-01T05:00:00.000Z", "description": "Loancare - Mortgage - Payment", "frequency": "monthly", "amountDue": 1936.58, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3d61fce915752bd790cd", "createdAt": "2025-07-19T06:42:36.435Z", "updatedAt": "2025-07-19T06:42:36.435Z", "__v": 0}, {"_id": "687b3e5ffce915752bd79631", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-09T05:00:00.000Z", "description": "USAA - Platinum CC - Payment", "frequency": "monthly", "amountDue": 316, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b1871a6b7ca864f7ff4b8", "createdAt": "2025-07-19T06:42:39.005Z", "updatedAt": "2025-07-19T06:42:39.005Z", "__v": 0}, {"_id": "687b3e60fce915752bd796a0", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-12T05:00:00.000Z", "description": "PNC Bank - HELOC - Payment", "frequency": "monthly", "amountDue": 603.66, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3c7cfce915752bd79073", "createdAt": "2025-07-19T06:42:40.677Z", "updatedAt": "2025-07-19T06:42:40.677Z", "__v": 0}, {"_id": "687b3e61fce915752bd7970f", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-13T05:00:00.000Z", "description": "Sunnova - Solar Loan - Payment", "frequency": "monthly", "amountDue": 387.73, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3c40fce915752bd7905d", "createdAt": "2025-07-19T06:42:41.396Z", "updatedAt": "2025-07-19T06:42:41.396Z", "__v": 0}, {"_id": "687b3e62fce915752bd7977e", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-15T05:00:00.000Z", "description": "USAA - Subaru - Payment", "frequency": "monthly", "amountDue": 500.6, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3beafce915752bd79047", "createdAt": "2025-07-19T06:42:42.627Z", "updatedAt": "2025-07-19T06:42:42.627Z", "__v": 0}, {"_id": "687b3e64fce915752bd797ed", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-16T05:00:00.000Z", "description": "Citi Bank - Simplicity CC - Payment", "frequency": "monthly", "amountDue": 523.27, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3a803b36401e0fc09fcd", "createdAt": "2025-07-19T06:42:44.867Z", "updatedAt": "2025-07-19T06:42:44.867Z", "__v": 0}, {"_id": "687b3e66fce915752bd7985c", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-16T05:00:00.000Z", "description": "Best Buy - Citi Bank CC - Payment", "frequency": "monthly", "amountDue": 67, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3b6dfce915752bd79031", "createdAt": "2025-07-19T06:42:46.131Z", "updatedAt": "2025-07-19T06:42:46.131Z", "__v": 0}, {"_id": "687b3e66fce915752bd798cb", "userId": "687b08d1111b41ebb6c81ce7", "recurringExpenseId": null, "date": "2025-07-17T05:00:00.000Z", "description": "NFM - CC - Payment", "frequency": "monthly", "amountDue": 105, "amountAssigned": 0, "amountSpent": 0, "status": "late", "isDebtPayment": true, "debtId": "687b3ceffce915752bd790b7", "createdAt": "2025-07-19T06:42:46.823Z", "updatedAt": "2025-07-19T06:42:46.823Z", "__v": 0}, {"_id": "687d6af223ee4a9201bae279", "userId": "687d0df81aac89c8c167f77f", "recurringExpenseId": 1, "date": "2025-07-01T05:00:00.000Z", "description": "Rent", "frequency": "monthly", "amountDue": 1200, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "debtId": null, "createdAt": "2025-07-20T22:17:22.497Z", "updatedAt": "2025-07-20T22:17:22.497Z", "__v": 0}, {"_id": "687ed523f61e294d115ac673", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1735752127638, "date": "2025-07-31T05:00:00.000Z", "description": "Health Insurance", "frequency": "monthly", "amountDue": 114.66, "amountAssigned": 0, "amountSpent": 0, "status": "scheduled", "isDebtPayment": false, "__v": 0, "createdAt": "2025-07-22T00:02:43.185Z", "updatedAt": "2025-07-22T00:02:43.185Z"}, {"_id": "68826d3df304725fc9537dc3", "userId": "6764aeeccfd30a63a4a45566", "recurringExpenseId": 1747475768916, "date": "2025-07-30T05:00:00.000Z", "description": "Misc Home", "frequency": "monthly", "amountDue": 0, "amountAssigned": 0, "amountSpent": 0, "status": "paid", "isDebtPayment": false, "createdAt": "2025-07-24T17:28:29.052Z", "updatedAt": "2025-07-24T17:28:29.052Z", "__v": 0}]
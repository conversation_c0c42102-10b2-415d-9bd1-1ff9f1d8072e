{"name": "alto-budget", "version": "0.4.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "update-frequencies": "node scripts/update-expense-frequencies.mjs", "update-webhooks": "node scripts/update-plaid-webhooks.js", "migrate:api": "node scripts/migrate-api-endpoints.js"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "bcryptjs": "^2.4.3", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "framer-motion": "^12.4.2", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "mongodb": "^6.12.0", "mongoose": "^8.9.2", "next": "^15.3.5", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "plaid": "^30.1.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-plaid-link": "^3.6.1", "react-toastify": "^11.0.5", "recharts": "^2.15.0", "stripe": "^18.3.0", "svix": "^1.69.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/react": "19.0.8", "eslint": "^9", "eslint-config-next": "15.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1"}}
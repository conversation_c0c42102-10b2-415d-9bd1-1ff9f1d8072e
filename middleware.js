import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

// Define the admin API key - middleware can access env variables directly if they're exposed in next.config.js
const ADMIN_API_KEY = process.env.ADMIN_API_KEY;

// Create the protected middleware
export default withAuth(
   function middleware(req) {
      const { token } = req.nextauth;

      // Allow static assets
      if (
         req.nextUrl.pathname.startsWith("/_next/static") ||
         req.nextUrl.pathname.startsWith("/_next/image") ||
         req.nextUrl.pathname === "/favicon.ico" ||
         req.nextUrl.pathname.startsWith("/images/")
      ) {
         return NextResponse.next();
      }

      // Define public routes that don't require authentication
      const publicRoutes = [
         "/",
         "/api/plaid/webhook",
         "/api/stripe/webhook",
         "/api/auth/register",
      ];

      // Check if it's a public route
      if (publicRoutes.includes(req.nextUrl.pathname)) {
         return NextResponse.next();
      }

      // Handle auth pages specially
      if (req.nextUrl.pathname.startsWith("/auth/")) {
         // If user is authenticated, redirect them away from auth pages
         if (token) {
            // Skip onboarding check if ADMIN_API_KEY is not available
            if (!ADMIN_API_KEY) {
               return NextResponse.redirect(new URL("/budget", req.url));
            }

            // Check if user has completed onboarding
            if (token.onboardingComplete) {
               return NextResponse.redirect(new URL("/budget", req.url));
            } else {
               return NextResponse.redirect(
                  new URL(`/subscription/${token.id}`, req.url)
               );
            }
         }

         // Unauthenticated users can access auth pages
         return NextResponse.next();
      }

      // For admin routes, check if user is admin
      if (req.nextUrl.pathname.startsWith("/admin/")) {
         if (!token?.isAdmin) {
            return NextResponse.redirect(new URL("/budget", req.url));
         }
      }

      // Check onboarding completion for authenticated users on protected routes
      const isOnboardingPage = req.nextUrl.pathname.startsWith("/onboarding");
      const isSubscriptionPage =
         req.nextUrl.pathname.startsWith("/subscription");
      const isApiRoute = req.nextUrl.pathname.startsWith("/api/");

      // Skip onboarding check for onboarding pages, subscription pages, and API routes
      if (token && !isOnboardingPage && !isSubscriptionPage && !isApiRoute) {
         if (!token.onboardingComplete) {
            return NextResponse.redirect(
               new URL(`/subscription/${token.id}`, req.url)
            );
         }
      }

      return NextResponse.next();
   },
   {
      callbacks: {
         authorized: ({ token, req }) => {
            // Allow all routes during middleware processing
            // We'll handle route protection in the middleware function above
            return true;
         },
      },
   }
);

export const config = {
   matcher: [
      /*
       * Match all request paths except for the ones starting with:
       * - api/auth (NextAuth routes)
       * - _next/static (static files)
       * - _next/image (image optimization files)
       * - favicon.ico (favicon file)
       */
      "/((?!api/auth|_next/static|_next/image|favicon.ico).*)",
   ],
};

import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../auth/[...nextauth]/route";
import dbConnect from "../../../lib/mongodb/dbConnect";
import Expense from "../../../lib/mongodb/models/Expense";
import Income from "../../../lib/mongodb/models/Income";
import Debt from "../../../lib/mongodb/models/Debt";
import User from "../../../lib/mongodb/models/User";
import { parseISO, eachDayOfInterval, format, addDays } from "date-fns";

/**
 * GET /api/reports/budget - Fetches all budget data for reporting
 * Query parameters:
 * - start: Start date (YYYY-MM-DD)
 * - end: End date (YYYY-MM-DD)
 */
export async function GET(request) {
   try {
      const session = await getServerSession(authOptions);

      if (!session || !session.user) {
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const startParam = searchParams.get("start");
      const endParam = searchParams.get("end");

      if (!startParam || !endParam) {
         return NextResponse.json(
            { error: "Missing required date parameters" },
            { status: 400 }
         );
      }

      const startDate = parseISO(startParam);
      const endDate = parseISO(endParam);

      // Set the end date to the end of day to include all items on the last day
      endDate.setHours(23, 59, 59, 999);

      await dbConnect();

      // Fetch user data (including accounts)
      const user = await User.findById(userId).lean();

      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Extract accounts from user document
      const accounts = user.accounts || [];

      // Log what we're finding to help debug
      console.log(`Found ${accounts.length} accounts for user ${userId}`);

      // Get the next period start date (for future expenses)
      const nextPeriodStartDate = new Date(endDate);
      nextPeriodStartDate.setDate(nextPeriodStartDate.getDate() + 1);
      nextPeriodStartDate.setHours(0, 0, 0, 0);

      // Fetch transaction data in parallel
      const [expenses, incomes, debts, futureExpenses] = await Promise.all([
         // Fetch all expenses within date range
         Expense.find({
            userId,
            $or: [
               { date: { $gte: startDate, $lte: endDate } }, // Match by date field
               { dueDate: { $gte: startDate, $lte: endDate } }, // Match by dueDate field
               { recurringId: { $exists: true, $ne: null } }, // Include recurring expenses
            ],
         }).lean(),

         // Fetch all incomes within date range
         Income.find({
            userId,
            $or: [
               { date: { $gte: startDate, $lte: endDate } },
               { recurringId: { $exists: true, $ne: null } }, // Include recurring incomes
            ],
         }).lean(),

         // Fetch all debts
         Debt.find({ userId }).lean(),

         // Fetch future expenses (one-time expenses with dates after the end date)
         Expense.find({
            userId,
            // Only get expenses beyond the end date
            date: { $gt: endDate },
            // Only get one-off expenses (no frequency or oneoff frequency)
            $or: [
               { frequency: { $exists: false } },
               { frequency: "oneoff" },
               { frequency: "" },
            ],
            // Only include expenses with assigned amount > 0
            amountAssigned: { $gt: 0 },
            // Exclude expenses with start/end dates (they are not one-off)
            $and: [
               {
                  $or: [{ startDate: { $exists: false } }, { startDate: null }],
               },
               {
                  $or: [{ endDate: { $exists: false } }, { endDate: null }],
               },
            ],
         }).lean(),
      ]);

      // Combine regular expenses and future expenses
      const allExpenses = [...expenses, ...futureExpenses];

      console.log(
         `Found: ${expenses.length} expenses, ${futureExpenses.length} future expenses, ${incomes.length} incomes, ${debts.length} debts`
      );

      // Format dates for JSON serialization
      const formattedExpenses = allExpenses.map((expense) => {
         // Format dates to MM/DD/YYYY format
         const formatDateString = (dateObj) => {
            if (!dateObj) return null;
            const date = new Date(dateObj);
            return date.toLocaleDateString("en-US", {
               month: "2-digit",
               day: "2-digit",
               year: "numeric",
            });
         };

         return {
            ...expense,
            _id: expense._id.toString(),
            userId: expense.userId.toString(),
            createdAt: expense.createdAt
               ? expense.createdAt.toISOString()
               : null,
            updatedAt: expense.updatedAt
               ? expense.updatedAt.toISOString()
               : null,
            date: formatDateString(expense.date),
            dueDate: formatDateString(expense.dueDate),
            startDate: expense.startDate
               ? formatDateString(expense.startDate)
               : null,
            endDate: expense.endDate ? formatDateString(expense.endDate) : null,
            recurringId: expense.recurringId
               ? expense.recurringId.toString()
               : null,
            // Ensure category is included (even if null)
            category: expense.category || "Uncategorized",
            // Include derived amountAvailable field if not present
            amountAvailable:
               expense.amountAvailable ||
               (expense.amountAssigned || 0) + (expense.amountSpent || 0),
            // Add a flag for future expenses (those with dates after the end date)
            isFutureExpense: expense.date && new Date(expense.date) > endDate,
         };
      });

      const formattedIncomes = incomes.map((income) => {
         // Format date to MM/DD/YYYY format
         const formatDateString = (dateObj) => {
            if (!dateObj) return null;
            const date = new Date(dateObj);
            return date.toLocaleDateString("en-US", {
               month: "2-digit",
               day: "2-digit",
               year: "numeric",
            });
         };

         return {
            ...income,
            _id: income._id.toString(),
            userId: income.userId.toString(),
            createdAt: income.createdAt ? income.createdAt.toISOString() : null,
            updatedAt: income.updatedAt ? income.updatedAt.toISOString() : null,
            date: formatDateString(income.date),
            recurringId: income.recurringId
               ? income.recurringId.toString()
               : null,
         };
      });

      const formattedDebts = debts.map((debt) => ({
         ...debt,
         _id: debt._id.toString(),
         userId: debt.userId.toString(),
         createdAt: debt.createdAt ? debt.createdAt.toISOString() : null,
         updatedAt: debt.updatedAt ? debt.updatedAt.toISOString() : null,
      }));

      // Format accounts from user document - ensure each account has a string id and required fields
      const formattedAccounts = accounts.map((account) => {
         // Make sure account has all required fields
         return {
            ...account,
            _id: account._id ? account._id.toString() : "unknown",
            name: account.name || "Unnamed Account",
            balance: typeof account.balance === "number" ? account.balance : 0,
            accountType: account.accountType || "unknown",
         };
      });

      // Generate projected incomes from recurring incomes
      const projectedIncomes = generateProjectedIncomes(
         user.recurringIncomes || [],
         startDate,
         endDate
      );

      // Combine actual incomes with projected incomes
      const allIncomes = [...formattedIncomes, ...projectedIncomes];

      return NextResponse.json({
         expenses: formattedExpenses,
         incomes: allIncomes,
         debts: formattedDebts,
         accounts: formattedAccounts,
         success: true,
      });
   } catch (error) {
      console.error("Error fetching budget report data:", error);
      return NextResponse.json(
         {
            error: "Failed to fetch budget report data",
            details: error.message,
         },
         { status: 500 }
      );
   }
}

// Helper function to generate projected incomes from recurring incomes
function generateProjectedIncomes(recurringIncomes, startDate, endDate) {
   const projectedIncomes = [];

   // Generate all days in the range
   const days = eachDayOfInterval({ start: startDate, end: endDate });

   for (const day of days) {
      // Check for recurring income on this day
      for (const income of recurringIncomes) {
         if (!income.enabled) continue;

         if (
            shouldProcessRecurringItem(
               income,
               day,
               income.payPeriod,
               income.payDay,
               income.payWeekDay,
               income.lastPaymentDate
            )
         ) {
            projectedIncomes.push({
               _id: `projected-${income._id || "unknown"}-${format(
                  day,
                  "yyyy-MM-dd"
               )}`,
               userId: income.userId,
               date: day.toLocaleDateString("en-US", {
                  month: "2-digit",
                  day: "2-digit",
                  year: "numeric",
               }),
               description: income.description,
               expectedAmount: income.payAmount,
               receivedAmount: 0,
               category: "Salary",
               status: "projected",
               notes: `Projected ${income.payPeriod} income`,
               recurringId: income._id || null,
               createdAt: new Date().toISOString(),
               updatedAt: new Date().toISOString(),
            });
         }
      }
   }

   return projectedIncomes;
}

// Helper function to determine if a recurring item should be processed on a given day
function shouldProcessRecurringItem(
   item,
   targetDate,
   frequency,
   dueDay,
   dayOfWeek = null,
   lastPaymentDate = null
) {
   const day = targetDate.getDate();
   const dayOfWeekNum = targetDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
   const month = targetDate.getMonth();

   switch (frequency) {
      case "monthly":
         // Handle end-of-month edge cases
         const targetDay = parseInt(dueDay);
         const lastDayOfMonth = new Date(
            targetDate.getFullYear(),
            month + 1,
            0
         ).getDate();
         const effectiveDay = Math.min(targetDay, lastDayOfMonth);
         return day === effectiveDay;

      case "weekly":
         // For weekly items, use dayOfWeek if provided (for incomes), otherwise use dueDay
         if (dayOfWeek) {
            const dayMap = {
               Sunday: 0,
               Monday: 1,
               Tuesday: 2,
               Wednesday: 3,
               Thursday: 4,
               Friday: 5,
               Saturday: 6,
            };
            return dayOfWeekNum === dayMap[dayOfWeek];
         } else {
            // For expenses, dueDay might be a number (1-7) or day name
            if (isNaN(parseInt(dueDay))) {
               const dayMap = {
                  Sunday: 0,
                  Monday: 1,
                  Tuesday: 2,
                  Wednesday: 3,
                  Thursday: 4,
                  Friday: 5,
                  Saturday: 6,
               };
               return dayOfWeekNum === dayMap[dueDay];
            } else {
               // dueDay is a number (1-7, where 1 = Monday, 7 = Sunday)
               const adjustedDay =
                  parseInt(dueDay) === 7 ? 0 : parseInt(dueDay);
               return dayOfWeekNum === adjustedDay;
            }
         }

      case "biweekly":
         if (!lastPaymentDate) return false;
         const lastPayment = new Date(lastPaymentDate);
         const daysSinceLastPayment = Math.floor(
            (targetDate - lastPayment) / (1000 * 60 * 60 * 24)
         );

         // Check if it's the right day of the week and exactly 14 days (or multiples)
         if (dayOfWeek) {
            const dayMap = {
               Sunday: 0,
               Monday: 1,
               Tuesday: 2,
               Wednesday: 3,
               Thursday: 4,
               Friday: 5,
               Saturday: 6,
            };
            const expectedDayOfWeek = dayMap[dayOfWeek];
            return (
               dayOfWeekNum === expectedDayOfWeek &&
               daysSinceLastPayment > 0 &&
               daysSinceLastPayment % 14 === 0
            );
         }
         return daysSinceLastPayment > 0 && daysSinceLastPayment % 14 === 0;

      case "semimonthly":
         // Typically 1st and 15th of the month
         return day === 1 || day === 15;

      case "quarterly":
         // First day of quarter months (Jan, Apr, Jul, Oct) on the specified day
         const isQuarterMonth = month % 3 === 0;
         const targetQuarterDay = parseInt(dueDay);
         const lastDayOfQuarter = new Date(
            targetDate.getFullYear(),
            month + 1,
            0
         ).getDate();
         const effectiveQuarterDay = Math.min(
            targetQuarterDay,
            lastDayOfQuarter
         );
         return isQuarterMonth && day === effectiveQuarterDay;

      case "annually":
         // January on the specified day
         const targetAnnualDay = parseInt(dueDay);
         const lastDayOfJanuary = new Date(
            targetDate.getFullYear(),
            1,
            0
         ).getDate();
         const effectiveAnnualDay = Math.min(targetAnnualDay, lastDayOfJanuary);
         return month === 0 && day === effectiveAnnualDay;

      default:
         return false;
   }
}

#!/usr/bin/env node

/**
 * Comprehensive Financial Audit Script
 *
 * This script performs a complete financial audit for a user, validating all
 * financial data integrity, balances, and relationships across:
 * - Transactions and Account Balances
 * - Income and Expense Budget Items
 * - Debt Tracking
 * - Plaid Synchronization
 * - Data Consistency
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class FinancialAuditor {
   constructor(db) {
      this.db = db;
      this.users = db.collection("users");
      this.transactions = db.collection("transactions");
      this.expenses = db.collection("expenses");
      this.incomes = db.collection("incomes");
      this.debts = db.collection("debts");

      this.auditResults = {
         userId: null,
         timestamp: new Date(),
         summary: {
            totalIssues: 0,
            criticalIssues: 0,
            warningIssues: 0,
            infoIssues: 0,
         },
         sections: {},
      };
   }

   // Helper function to round to 2 decimal places
   roundToTwo(num) {
      return Number(Math.round(num + "e+2") + "e-2");
   }

   // Helper function to add an issue to the audit results
   addIssue(section, level, title, description, data = null) {
      if (!this.auditResults.sections[section]) {
         this.auditResults.sections[section] = {
            issues: [],
            summary: { critical: 0, warning: 0, info: 0 },
         };
      }

      const issue = {
         level,
         title,
         description,
         data,
         timestamp: new Date(),
      };

      this.auditResults.sections[section].issues.push(issue);
      this.auditResults.sections[section].summary[level]++;
      this.auditResults.summary.totalIssues++;
      this.auditResults.summary[level + "Issues"]++;
   }

   // Helper function to format currency
   formatCurrency(amount) {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
         minimumFractionDigits: 2,
         maximumFractionDigits: 2,
      }).format(amount || 0);
   }

   /**
    * Main audit function for a specific user
    */
   async auditUser(userId) {
      console.log(
         `🔍 Starting comprehensive financial audit for user: ${userId}`
      );
      this.auditResults.userId = userId;

      try {
         // Load all user data
         const userData = await this.loadUserData(userId);

         if (!userData.user) {
            throw new Error(`User not found: ${userId}`);
         }

         console.log(
            `👤 Auditing user: ${userData.user.name} (${userData.user.email})`
         );

         // Perform all audit checks
         await this.auditAccountBalances(userData);
         await this.auditTransactionIntegrity(userData);
         await this.auditExpenseBudgets(userData);
         await this.auditIncomeBudgets(userData);
         await this.auditReadyToAssign(userData);
         await this.auditDebtTracking(userData);
         await this.auditPlaidSynchronization(userData);
         await this.auditDataConsistency(userData);
         await this.auditRecurringItems(userData);
         await this.auditAssignedToLinkages(userData);

         return this.auditResults;
      } catch (error) {
         this.addIssue(
            "system",
            "critical",
            "Audit System Error",
            `Failed to complete audit: ${error.message}`,
            { error: error.stack }
         );
         throw error;
      }
   }

   /**
    * Load all user data for auditing
    */
   async loadUserData(userId) {
      console.log("📊 Loading user data...");

      const [user, transactions, expenses, incomes, debts] = await Promise.all([
         this.users.findOne({ _id: new ObjectId(userId) }),
         this.transactions.find({ userId: new ObjectId(userId) }).toArray(),
         this.expenses.find({ userId: userId }).toArray(),
         this.incomes.find({ userId: userId }).toArray(),
         this.debts.find({ userId: new ObjectId(userId) }).toArray(),
      ]);

      console.log(
         `📈 Loaded: ${transactions.length} transactions, ${expenses.length} expenses, ${incomes.length} incomes, ${debts.length} debts`
      );

      return { user, transactions, expenses, incomes, debts };
   }

   /**
    * Audit 1: Account Balances vs Transactions
    */
   async auditAccountBalances(userData) {
      console.log("🏦 Auditing account balances...");
      const { user, transactions } = userData;

      for (const account of user.accounts || []) {
         // Calculate expected balance from transactions
         const accountTransactions = transactions.filter(
            (t) =>
               t.accountId === account._id ||
               (t.type === "Transfer" &&
                  (t.fromAccountId === account._id ||
                     t.toAccountId === account._id))
         );

         let calculatedBalance = 0;

         for (const transaction of accountTransactions) {
            if (transaction.type === "Transfer") {
               if (transaction.fromAccountId === account._id) {
                  calculatedBalance -= Math.abs(transaction.amount);
               } else if (transaction.toAccountId === account._id) {
                  calculatedBalance += Math.abs(transaction.amount);
               }
            } else {
               calculatedBalance += transaction.amount;
            }
         }

         calculatedBalance = this.roundToTwo(calculatedBalance);
         const storedBalance = this.roundToTwo(account.balance);

         if (calculatedBalance !== storedBalance) {
            this.addIssue(
               "accountBalances",
               "critical",
               `Account Balance Mismatch: ${account.name}`,
               `Stored balance: ${this.formatCurrency(
                  storedBalance
               )}, Calculated balance: ${this.formatCurrency(
                  calculatedBalance
               )}`,
               {
                  accountId: account._id,
                  accountName: account.name,
                  storedBalance,
                  calculatedBalance,
                  difference: this.roundToTwo(
                     storedBalance - calculatedBalance
                  ),
                  transactionCount: accountTransactions.length,
               }
            );
         }
      }
   }

   /**
    * Audit 2: Transaction Data Integrity
    */
   async auditTransactionIntegrity(userData) {
      console.log("💳 Auditing transaction integrity...");
      const { transactions, user } = userData;

      for (const transaction of transactions) {
         // Check for required fields
         if (
            !transaction.type ||
            !["Income", "Expense", "Transfer"].includes(transaction.type)
         ) {
            this.addIssue(
               "transactionIntegrity",
               "critical",
               "Invalid Transaction Type",
               `Transaction ${transaction._id} has invalid type: ${transaction.type}`,
               { transactionId: transaction._id, transaction }
            );
         }

         // Check amount validity
         if (
            typeof transaction.amount !== "number" ||
            isNaN(transaction.amount)
         ) {
            this.addIssue(
               "transactionIntegrity",
               "critical",
               "Invalid Transaction Amount",
               `Transaction ${transaction._id} has invalid amount: ${transaction.amount}`,
               { transactionId: transaction._id, transaction }
            );
         }

         // Check assignedTo/assignedToType consistency
         if (transaction.assignedTo && !transaction.assignedToType) {
            this.addIssue(
               "transactionIntegrity",
               "warning",
               "Missing AssignedTo Type",
               `Transaction ${transaction._id} has assignedTo but no assignedToType`,
               { transactionId: transaction._id, transaction }
            );
         }

         if (transaction.assignedToType && !transaction.assignedTo) {
            this.addIssue(
               "transactionIntegrity",
               "warning",
               "Missing AssignedTo Reference",
               `Transaction ${transaction._id} has assignedToType but no assignedTo`,
               { transactionId: transaction._id, transaction }
            );
         }

         // Check account existence
         if (transaction.accountId) {
            const accountExists = user.accounts?.some(
               (a) => a._id === transaction.accountId
            );
            if (!accountExists) {
               this.addIssue(
                  "transactionIntegrity",
                  "critical",
                  "Invalid Account Reference",
                  `Transaction ${transaction._id} references non-existent account: ${transaction.accountId}`,
                  { transactionId: transaction._id, transaction }
               );
            }
         }

         // Check transfer-specific fields
         if (transaction.type === "Transfer") {
            if (!transaction.fromAccountId || !transaction.toAccountId) {
               this.addIssue(
                  "transactionIntegrity",
                  "critical",
                  "Incomplete Transfer Transaction",
                  `Transfer transaction ${transaction._id} missing fromAccountId or toAccountId`,
                  { transactionId: transaction._id, transaction }
               );
            }
         }
      }
   }

   /**
    * Audit 3: Expense Budget Calculations
    */
   async auditExpenseBudgets(userData) {
      console.log("💰 Auditing expense budgets...");
      const { expenses, transactions } = userData;

      for (const expense of expenses) {
         // Note: amountAvailable is a virtual field calculated as amountAssigned + amountSpent
         // We'll only flag this as an issue if there's a stored value that differs significantly
         const calculatedAvailable = this.roundToTwo(
            expense.amountAssigned + expense.amountSpent
         );

         // Only compare if amountAvailable is explicitly stored and differs from calculation
         if (
            expense.amountAvailable !== undefined &&
            Math.abs(
               calculatedAvailable - this.roundToTwo(expense.amountAvailable)
            ) > 0.01
         ) {
            this.addIssue(
               "expenseBudgets",
               "info",
               `Expense Amount Available Note: ${expense.description}`,
               `Virtual field: ${this.formatCurrency(
                  calculatedAvailable
               )}, Stored field: ${this.formatCurrency(
                  expense.amountAvailable
               )}`,
               {
                  expenseId: expense._id,
                  description: expense.description,
                  amountAssigned: expense.amountAssigned,
                  amountSpent: expense.amountSpent,
                  calculatedAvailable,
                  storedAvailable: expense.amountAvailable,
               }
            );
         }

         // Verify amountSpent matches linked transactions
         const expenseTransactions = transactions.filter(
            (t) =>
               t.category &&
               t.categoryType === "Expense" &&
               t.category.toString() === expense._id.toString()
         );

         let calculatedSpent = 0;
         for (const transaction of expenseTransactions) {
            if (transaction.type === "Expense") {
               calculatedSpent += transaction.amount; // Already negative
            } else if (transaction.type === "Income") {
               calculatedSpent += transaction.amount; // Positive for refunds
            }
         }

         calculatedSpent = this.roundToTwo(calculatedSpent);
         const storedSpent = this.roundToTwo(expense.amountSpent);

         if (Math.abs(calculatedSpent - storedSpent) > 0.01) {
            this.addIssue(
               "expenseBudgets",
               "critical",
               `Expense Amount Spent Mismatch: ${expense.description}`,
               `Calculated: ${this.formatCurrency(
                  calculatedSpent
               )}, Stored: ${this.formatCurrency(storedSpent)}`,
               {
                  expenseId: expense._id,
                  description: expense.description,
                  calculatedSpent,
                  storedSpent,
                  linkedTransactions: expenseTransactions.length,
               }
            );
         }

         // Note: Negative amountAssigned is normal when there are positive transactions
         // (like refunds) assigned to this expense to balance the amountAvailable calculation
      }
   }

   /**
    * Audit 4: Income Budget Calculations
    */
   async auditIncomeBudgets(userData) {
      console.log("💵 Auditing income budgets...");
      const { incomes, transactions } = userData;

      for (const income of incomes) {
         // Verify receivedAmount matches linked transactions
         const incomeTransactions = transactions.filter(
            (t) =>
               t.assignedTo &&
               t.assignedToType === "Income" &&
               t.assignedTo.toString() === income._id.toString()
         );

         let calculatedReceived = 0;
         for (const transaction of incomeTransactions) {
            if (transaction.type === "Income") {
               calculatedReceived += transaction.amount;
            }
         }

         calculatedReceived = this.roundToTwo(calculatedReceived);
         const storedReceived = this.roundToTwo(income.receivedAmount || 0);

         if (Math.abs(calculatedReceived - storedReceived) > 0.01) {
            this.addIssue(
               "incomeBudgets",
               "critical",
               `Income Received Amount Mismatch: ${income.description}`,
               `Calculated: ${this.formatCurrency(
                  calculatedReceived
               )}, Stored: ${this.formatCurrency(storedReceived)}`,
               {
                  incomeId: income._id,
                  description: income.description,
                  calculatedReceived,
                  storedReceived,
                  linkedTransactions: incomeTransactions.length,
               }
            );
         }

         // Check income status consistency
         if (
            income.receivedAmount >= income.expectedAmount &&
            income.status !== "received"
         ) {
            this.addIssue(
               "incomeBudgets",
               "info",
               `Income Status Inconsistency: ${income.description}`,
               `Received amount (${this.formatCurrency(
                  income.receivedAmount
               )}) >= expected (${this.formatCurrency(
                  income.expectedAmount
               )}) but status is ${income.status}`,
               { incomeId: income._id, income }
            );
         }
      }
   }

   /**
    * Audit 5: Ready to Assign Calculation
    */
   async auditReadyToAssign(userData) {
      console.log("🎯 Auditing ready to assign calculation...");
      const { user, incomes, expenses } = userData;

      // Calculate total received income
      const totalReceived = incomes.reduce(
         (sum, income) => sum + this.roundToTwo(income.receivedAmount || 0),
         0
      );

      // Calculate total assigned to expenses
      const totalAssigned = expenses.reduce(
         (sum, expense) => sum + this.roundToTwo(expense.amountAssigned || 0),
         0
      );

      const calculatedReadyToAssign = this.roundToTwo(
         totalReceived - totalAssigned
      );

      // Note: Ready to assign is calculated dynamically, not stored in user
      // This validates the calculation logic
      this.addIssue(
         "readyToAssign",
         "info",
         "Ready to Assign Summary",
         `Total Received: ${this.formatCurrency(
            totalReceived
         )}, Total Assigned: ${this.formatCurrency(
            totalAssigned
         )}, Ready to Assign: ${this.formatCurrency(calculatedReadyToAssign)}`,
         {
            totalReceived,
            totalAssigned,
            readyToAssign: calculatedReadyToAssign,
            incomeCount: incomes.length,
            expenseCount: expenses.length,
         }
      );

      // Check for over-assignment (negative ready to assign)
      if (calculatedReadyToAssign < 0) {
         this.addIssue(
            "readyToAssign",
            "warning",
            "Over-Assignment Detected",
            `Ready to assign is negative: ${this.formatCurrency(
               calculatedReadyToAssign
            )}. More money assigned than received.`,
            { readyToAssign: calculatedReadyToAssign }
         );
      }
   }

   /**
    * Audit 6: Debt Tracking
    */
   async auditDebtTracking(userData) {
      console.log("🏦 Auditing debt tracking...");
      const { debts, expenses, transactions } = userData;

      for (const debt of debts) {
         // Find related debt payment expenses
         const debtExpenses = expenses.filter(
            (e) => e.isDebtPayment && e.debtId === debt._id.toString()
         );

         // Check debt payment transactions
         const debtTransactions = [];
         for (const expense of debtExpenses) {
            const relatedTransactions = transactions.filter(
               (t) =>
                  t.category &&
                  t.categoryType === "Expense" &&
                  t.category.toString() === expense._id.toString()
            );
            debtTransactions.push(...relatedTransactions);
         }

         // Validate minimum payment tracking
         if (debt.minimumPayment <= 0) {
            this.addIssue(
               "debtTracking",
               "warning",
               `Invalid Minimum Payment: ${debt.lender}`,
               `Minimum payment should be positive: ${this.formatCurrency(
                  debt.minimumPayment
               )}`,
               { debtId: debt._id, debt }
            );
         }

         // Check for debt balance consistency
         if (debt.balance < 0) {
            this.addIssue(
               "debtTracking",
               "warning",
               `Negative Debt Balance: ${debt.lender}`,
               `Debt balance is negative: ${this.formatCurrency(debt.balance)}`,
               { debtId: debt._id, debt }
            );
         }

         // Check APR reasonableness
         if (debt.apr > 100) {
            this.addIssue(
               "debtTracking",
               "warning",
               `Unusually High APR: ${debt.lender}`,
               `APR seems unusually high: ${debt.apr}%`,
               { debtId: debt._id, debt }
            );
         }
      }
   }

   /**
    * Audit 7: Plaid Synchronization
    */
   async auditPlaidSynchronization(userData) {
      console.log("🔄 Auditing Plaid synchronization...");
      const { transactions, user } = userData;

      const plaidTransactions = transactions.filter(
         (t) => t.isPlaidTransaction
      );
      const plaidAccounts = user.accounts?.filter((a) => a.plaidItemId) || [];

      // Check for duplicate Plaid transactions
      const plaidTxnIds = new Map();
      for (const transaction of plaidTransactions) {
         if (transaction.plaidTransactionId) {
            if (plaidTxnIds.has(transaction.plaidTransactionId)) {
               this.addIssue(
                  "plaidSync",
                  "critical",
                  "Duplicate Plaid Transaction",
                  `Plaid transaction ID ${transaction.plaidTransactionId} appears multiple times`,
                  {
                     plaidTransactionId: transaction.plaidTransactionId,
                     duplicateTransactions: [
                        plaidTxnIds.get(transaction.plaidTransactionId),
                        transaction._id,
                     ],
                  }
               );
            } else {
               plaidTxnIds.set(transaction.plaidTransactionId, transaction._id);
            }
         }
      }

      // Check for orphaned pending transactions
      const pendingTransactions = transactions.filter(
         (t) => t.pendingPlaidTransactionId && !t.plaidTransactionId
      );

      if (pendingTransactions.length > 0) {
         this.addIssue(
            "plaidSync",
            "info",
            "Orphaned Pending Transactions",
            `Found ${pendingTransactions.length} transactions with pending Plaid IDs but no final Plaid ID`,
            {
               count: pendingTransactions.length,
               transactions: pendingTransactions.map((t) => t._id),
            }
         );
      }

      // Summary of Plaid integration
      this.addIssue(
         "plaidSync",
         "info",
         "Plaid Integration Summary",
         `${plaidAccounts.length} Plaid accounts, ${plaidTransactions.length} Plaid transactions`,
         {
            plaidAccountCount: plaidAccounts.length,
            plaidTransactionCount: plaidTransactions.length,
            totalTransactionCount: transactions.length,
            plaidPercentage:
               transactions.length > 0
                  ? this.roundToTwo(
                       (plaidTransactions.length / transactions.length) * 100
                    )
                  : 0,
         }
      );
   }

   /**
    * Audit 8: Data Consistency Checks
    */
   async auditDataConsistency(userData) {
      console.log("🔍 Auditing data consistency...");
      const { user, transactions, expenses, incomes } = userData;

      // Check for orphaned transactions (references to non-existent categories)
      for (const transaction of transactions) {
         if (transaction.category) {
            let categoryExists = false;

            if (transaction.categoryType === "Income") {
               categoryExists = incomes.some(
                  (i) => i._id.toString() === transaction.category.toString()
               );
            } else if (transaction.categoryType === "Expense") {
               categoryExists = expenses.some(
                  (e) => e._id.toString() === transaction.category.toString()
               );
            }

            if (!categoryExists) {
               this.addIssue(
                  "dataConsistency",
                  "critical",
                  "Orphaned Transaction Category",
                  `Transaction ${transaction._id} references non-existent ${transaction.categoryType} category: ${transaction.category}`,
                  { transactionId: transaction._id, transaction }
               );
            }
         }
      }

      // Check for monetary value precision issues
      const checkPrecision = (value, name, id) => {
         if (typeof value === "number" && !isNaN(value)) {
            const rounded = this.roundToTwo(value);
            if (Math.abs(value - rounded) > 0.001) {
               this.addIssue(
                  "dataConsistency",
                  "warning",
                  "Precision Issue",
                  `${name} has precision beyond 2 decimal places: ${value}`,
                  { id, name, value, rounded }
               );
            }
         }
      };

      // Check all monetary values for precision
      for (const expense of expenses) {
         checkPrecision(
            expense.amountDue,
            `Expense ${expense.description} amountDue`,
            expense._id
         );
         checkPrecision(
            expense.amountAssigned,
            `Expense ${expense.description} amountAssigned`,
            expense._id
         );
         checkPrecision(
            expense.amountSpent,
            `Expense ${expense.description} amountSpent`,
            expense._id
         );
      }

      for (const income of incomes) {
         checkPrecision(
            income.expectedAmount,
            `Income ${income.description} expectedAmount`,
            income._id
         );
         checkPrecision(
            income.receivedAmount,
            `Income ${income.description} receivedAmount`,
            income._id
         );
      }

      for (const transaction of transactions) {
         checkPrecision(
            transaction.amount,
            `Transaction ${transaction._id} amount`,
            transaction._id
         );
      }
   }

   /**
    * Audit 9: Recurring Items Validation
    */
   async auditRecurringItems(userData) {
      console.log("🔄 Auditing recurring items...");
      const { user, expenses, incomes } = userData;

      // Check user's recurring expenses configuration
      for (const recurringExpense of user.recurringExpenses || []) {
         // Find generated expenses for this recurring expense
         const generatedExpenses = expenses.filter(
            (e) => e.recurringExpenseId === recurringExpense.id
         );

         if (generatedExpenses.length === 0 && recurringExpense.enabled) {
            this.addIssue(
               "recurringItems",
               "info",
               `No Generated Expenses for Recurring Item`,
               `Recurring expense "${recurringExpense.name}" has no generated expense items`,
               { recurringExpenseId: recurringExpense.id, recurringExpense }
            );
         }

         // Validate recurring expense data - only flag enabled expenses with zero amounts
         if (recurringExpense.amount <= 0 && recurringExpense.enabled) {
            this.addIssue(
               "recurringItems",
               "warning",
               `Invalid Recurring Expense Amount`,
               `Enabled recurring expense "${
                  recurringExpense.name
               }" has invalid amount: ${this.formatCurrency(
                  recurringExpense.amount
               )}`,
               { recurringExpenseId: recurringExpense.id, recurringExpense }
            );
         }
      }

      // Check user's recurring incomes configuration
      for (const recurringIncome of user.recurringIncomes || []) {
         // Find generated incomes for this recurring income
         const generatedIncomes = incomes.filter(
            (i) =>
               i.category === recurringIncome.description ||
               i.description.includes(recurringIncome.description)
         );

         if (generatedIncomes.length === 0 && recurringIncome.enabled) {
            this.addIssue(
               "recurringItems",
               "info",
               `No Generated Incomes for Recurring Item`,
               `Recurring income "${recurringIncome.description}" has no generated income items`,
               { recurringIncome }
            );
         }

         // Validate recurring income data
         if (recurringIncome.payAmount <= 0) {
            this.addIssue(
               "recurringItems",
               "warning",
               `Invalid Recurring Income Amount`,
               `Recurring income "${
                  recurringIncome.description
               }" has invalid amount: ${this.formatCurrency(
                  recurringIncome.payAmount
               )}`,
               { recurringIncome }
            );
         }
      }
   }

   /**
    * Audit 10: AssignedTo Linkages
    */
   async auditAssignedToLinkages(userData) {
      console.log("🔗 Auditing assignedTo linkages...");
      const { transactions, expenses, incomes } = userData;

      // Check for transactions with mismatched category types
      for (const transaction of transactions) {
         if (transaction.category && transaction.categoryType) {
            let categoryFound = false;
            let correctType = null;

            // Check if category exists in incomes
            if (
               incomes.some(
                  (i) => i._id.toString() === transaction.category.toString()
               )
            ) {
               categoryFound = true;
               correctType = "Income";
            }

            // Check if category exists in expenses
            if (
               expenses.some(
                  (e) => e._id.toString() === transaction.category.toString()
               )
            ) {
               if (categoryFound) {
                  this.addIssue(
                     "categoryLinkages",
                     "critical",
                     "Duplicate Category ID",
                     `Category ID ${transaction.category} exists in both incomes and expenses`,
                     {
                        transactionId: transaction._id,
                        categoryId: transaction.category,
                     }
                  );
               }
               categoryFound = true;
               correctType = "Expense";
            }

            if (categoryFound && transaction.categoryType !== correctType) {
               this.addIssue(
                  "categoryLinkages",
                  "critical",
                  "Category Type Mismatch",
                  `Transaction ${transaction._id} has categoryType "${transaction.categoryType}" but category exists as "${correctType}"`,
                  {
                     transactionId: transaction._id,
                     categoryId: transaction.category,
                     storedType: transaction.categoryType,
                     correctType,
                  }
               );
            }
         }
      }

      // Summary of category usage
      const incomeTransactions = transactions.filter(
         (t) => t.assignedToType === "Income"
      ).length;
      const expenseTransactions = transactions.filter(
         (t) => t.assignedToType === "Expense"
      ).length;
      const uncategorizedTransactions = transactions.filter(
         (t) => !t.assignedTo
      ).length;

      this.addIssue(
         "categoryLinkages",
         "info",
         "Category Usage Summary",
         `${incomeTransactions} income transactions, ${expenseTransactions} expense transactions, ${uncategorizedTransactions} uncategorized`,
         {
            incomeTransactions,
            expenseTransactions,
            uncategorizedTransactions,
            totalTransactions: transactions.length,
            categorizedPercentage:
               transactions.length > 0
                  ? this.roundToTwo(
                       ((incomeTransactions + expenseTransactions) /
                          transactions.length) *
                          100
                    )
                  : 0,
         }
      );
   }

   /**
    * Generate and display audit report
    */
   generateReport() {
      console.log("\n" + "=".repeat(80));
      console.log("📋 COMPREHENSIVE FINANCIAL AUDIT REPORT");
      console.log("=".repeat(80));
      console.log(`User ID: ${this.auditResults.userId}`);
      console.log(
         `Audit Time: ${this.auditResults.timestamp.toLocaleString()}`
      );
      console.log(`Total Issues: ${this.auditResults.summary.totalIssues}`);
      console.log(
         `Critical: ${this.auditResults.summary.criticalIssues} | Warning: ${this.auditResults.summary.warningIssues} | Info: ${this.auditResults.summary.infoIssues}`
      );
      console.log("=".repeat(80));

      for (const [sectionName, section] of Object.entries(
         this.auditResults.sections
      )) {
         console.log(`\n🔍 ${sectionName.toUpperCase()}`);
         console.log("-".repeat(40));
         console.log(
            `Issues: ${section.issues.length} (Critical: ${section.summary.critical}, Warning: ${section.summary.warning}, Info: ${section.summary.info})`
         );

         for (const issue of section.issues) {
            const icon =
               issue.level === "critical"
                  ? "❌"
                  : issue.level === "warning"
                  ? "⚠️"
                  : "ℹ️";
            console.log(`\n${icon} ${issue.title}`);
            console.log(`   ${issue.description}`);

            if (issue.data && Object.keys(issue.data).length > 0) {
               console.log(`   Data: ${JSON.stringify(issue.data, null, 2)}`);
            }
         }
      }

      console.log("\n" + "=".repeat(80));
      console.log("📊 AUDIT SUMMARY");
      console.log("=".repeat(80));

      if (this.auditResults.summary.criticalIssues > 0) {
         console.log(
            `❌ ${this.auditResults.summary.criticalIssues} CRITICAL issues found - immediate attention required`
         );
      }

      if (this.auditResults.summary.warningIssues > 0) {
         console.log(
            `⚠️  ${this.auditResults.summary.warningIssues} WARNING issues found - should be addressed`
         );
      }

      if (this.auditResults.summary.infoIssues > 0) {
         console.log(
            `ℹ️  ${this.auditResults.summary.infoIssues} INFO items noted - for reference`
         );
      }

      if (this.auditResults.summary.totalIssues === 0) {
         console.log(
            "✅ No issues found - financial data appears to be in perfect balance!"
         );
      }

      console.log("=".repeat(80));

      return this.auditResults;
   }
}

/**
 * Main execution function
 */
async function runFinancialAudit() {
   const userId = process.argv[2];

   if (!userId) {
      console.error(`
❌ Usage: node financial-audit.js <user_id>

Examples:
  node financial-audit.js 507f1f77bcf86cd799439011
  node financial-audit.js all                    # Audit all users
      `);
      process.exit(1);
   }

   const client = new MongoClient(MONGODB_URI);

   try {
      console.log("🔍 Connecting to database...");
      await client.connect();
      const db = client.db(DB_NAME);

      const auditor = new FinancialAuditor(db);

      if (userId.toLowerCase() === "all") {
         console.log("🔍 Auditing all users...");
         const users = await db
            .collection("users")
            .find({}, { projection: { _id: 1, name: 1, email: 1 } })
            .toArray();

         console.log(`Found ${users.length} users to audit\n`);

         for (const user of users) {
            console.log(`\n${"*".repeat(100)}`);
            console.log(`AUDITING USER: ${user.name} (${user.email})`);
            console.log(`${"*".repeat(100)}`);

            try {
               const results = await auditor.auditUser(user._id.toString());
               auditor.generateReport();

               // Reset for next user
               auditor.auditResults = {
                  userId: null,
                  timestamp: new Date(),
                  summary: {
                     totalIssues: 0,
                     criticalIssues: 0,
                     warningIssues: 0,
                     infoIssues: 0,
                  },
                  sections: {},
               };
            } catch (error) {
               console.error(
                  `❌ Failed to audit user ${user.name}: ${error.message}`
               );
            }
         }
      } else {
         const results = await auditor.auditUser(userId);
         auditor.generateReport();
      }
   } catch (error) {
      console.error("❌ Audit failed:", error);
   } finally {
      await client.close();
      console.log("\n🔐 Database connection closed");
   }
}

// Run the audit
if (require.main === module) {
   runFinancialAudit().catch(console.error);
}

module.exports = { FinancialAuditor };

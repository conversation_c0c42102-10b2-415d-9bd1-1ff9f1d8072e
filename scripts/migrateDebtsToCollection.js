/**
 * Migration script to move debt data from the User document to the standalone Debt collection
 *
 * Run with: node scripts/migrateDebtsToCollection.js [--remove]
 * Add --remove flag to remove debts from user documents after migration
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import User from "../app/lib/mongodb/models/User.js";
import Debt from "../app/lib/mongodb/models/Debt.js";

// Load environment variables
dotenv.config();

// Check if removal flag is present
const shouldRemove = process.argv.includes("--remove");

async function connectToDatabase() {
   try {
      await mongoose.connect(process.env.MONGODB_URI);
      console.log("Connected to MongoDB");
   } catch (error) {
      console.error("Error connecting to MongoDB:", error);
      process.exit(1);
   }
}

async function migrateDebts() {
   try {
      // Find all users who have debts
      const users = await User.find({ "debts.0": { $exists: true } });
      console.log(`Found ${users.length} users with debts`);

      let totalMigratedDebts = 0;
      let successfulUsers = 0;
      let errorUsers = 0;
      let removedDebtsCount = 0;

      for (const user of users) {
         try {
            let userDebts = [];
            console.log(
               `Processing user ${user._id} with ${user.debts.length} debts`
            );

            // Process each debt from user document
            for (const debt of user.debts) {
               // Check if this debt already exists in the collection
               const existingDebt = await Debt.findOne({
                  userId: user._id,
                  lender: debt.lender,
                  debtType: debt.debtType,
                  balance: debt.balance,
               });

               if (existingDebt) {
                  console.log(
                     `Debt for ${debt.lender} already exists in collection, skipping`
                  );
                  userDebts.push(existingDebt);
                  continue;
               }

               // Create a new Debt document for each debt in the user
               const newDebt = new Debt({
                  userId: user._id,
                  debtType: debt.debtType,
                  lender: debt.lender,
                  balance: debt.balance,
                  apr: debt.apr,
                  minimumPayment: debt.minimumPayment,
                  dueDate: debt.dueDate,
                  active: debt.active,
                  history: debt.history || [],
                  createdAt: debt.createdAt || new Date(),
                  updatedAt: debt.updatedAt || new Date(),
               });

               // Save the debt
               await newDebt.save();
               userDebts.push(newDebt);
               totalMigratedDebts++;
            }

            // Clear debts from user document if flag is set
            if (shouldRemove) {
               console.log(
                  `Removing ${user.debts.length} debts from user ${user._id}`
               );
               removedDebtsCount += user.debts.length;
               user.debts = [];
               await user.save();
            }

            console.log(
               `Successfully migrated ${userDebts.length} debts for user ${user._id}`
            );
            successfulUsers++;
         } catch (error) {
            console.error(`Error migrating debts for user ${user._id}:`, error);
            errorUsers++;
         }
      }

      const removeMessage = shouldRemove
         ? `Removed ${removedDebtsCount} debts from ${successfulUsers} users`
         : "Debt removal was not requested (use --remove flag to remove debts from users)";

      console.log(`
Migration Summary:
-----------------
Total users processed: ${users.length}
Successful users: ${successfulUsers}
Users with errors: ${errorUsers}
Total debts migrated: ${totalMigratedDebts}
${removeMessage}
    `);
   } catch (error) {
      console.error("Error during migration:", error);
   } finally {
      // Close the database connection
      await mongoose.connection.close();
      console.log("Database connection closed");
   }
}

// Run the migration
connectToDatabase()
   .then(migrateDebts)
   .then(() => {
      console.log("Migration script completed");
      process.exit(0);
   })
   .catch((error) => {
      console.error("Migration failed:", error);
      process.exit(1);
   });

#!/usr/bin/env node

/**
 * Recalculate Income Received Script
 *
 * This script recalculates the receivedAmount field for all incomes based on
 * transactions assigned to them. This is needed after the migration from
 * category/categoryType to assignedTo/assignedToType where the receivedAmount
 * fields weren't properly updated.
 *
 * The script will:
 * - Find all incomes in the database
 * - For each income, calculate the total received from assigned transactions
 * - Update the income's receivedAmount field with the correct value
 * - Update the income status based on the new amounts
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class IncomeReceivedRecalculator {
   constructor(db) {
      this.db = db;
      this.incomes = db.collection("incomes");
      this.transactions = db.collection("transactions");

      this.stats = {
         totalIncomes: 0,
         updatedIncomes: 0,
         unchangedIncomes: 0,
         errors: 0,
      };
   }

   // Helper function to round to 2 decimal places
   roundToTwo(num) {
      return Number(Math.round(num + "e+2") + "e-2");
   }

   // Helper function to format currency
   formatCurrency(amount) {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
      }).format(amount);
   }

   // Helper function to determine income status based on amounts
   determineIncomeStatus(expectedAmount, receivedAmount) {
      const expected = Number(expectedAmount || 0);
      const received = Number(receivedAmount || 0);

      // If received amount meets or exceeds expected amount, it's received
      if (received >= expected) {
         return "received";
      }

      // Otherwise, it's scheduled
      return "scheduled";
   }

   async recalculateIncomeReceived(dryRun = true) {
      console.log(
         `💵 ${
            dryRun ? "DRY RUN:" : ""
         } Recalculating income received amounts...`
      );

      // Get all incomes
      const incomes = await this.incomes.find({}).toArray();
      this.stats.totalIncomes = incomes.length;

      console.log(`Found ${incomes.length} incomes to process`);

      for (const income of incomes) {
         try {
            // Find all transactions assigned to this income
            const incomeTransactions = await this.transactions
               .find({
                  assignedTo: income._id,
                  assignedToType: "Income",
               })
               .toArray();

            // Calculate total received from transactions
            let calculatedReceived = 0;
            for (const transaction of incomeTransactions) {
               if (transaction.type === "Income") {
                  calculatedReceived += transaction.amount; // Positive for income
               }
            }

            calculatedReceived = this.roundToTwo(calculatedReceived);
            const storedReceived = this.roundToTwo(income.receivedAmount || 0);

            // Check if there's a difference
            if (Math.abs(calculatedReceived - storedReceived) > 0.01) {
               console.log(
                  `🔧 Income "${income.description}" (${
                     income._id
                  }) - Received: ${this.formatCurrency(
                     storedReceived
                  )} → ${this.formatCurrency(calculatedReceived)} (${
                     incomeTransactions.length
                  } transactions)`
               );

               if (!dryRun) {
                  // Update the income with the correct receivedAmount
                  await this.incomes.updateOne(
                     { _id: income._id },
                     { $set: { receivedAmount: calculatedReceived } }
                  );

                  // Determine and update the status if needed
                  const newStatus = this.determineIncomeStatus(
                     income.expectedAmount,
                     calculatedReceived
                  );

                  if (income.status !== newStatus) {
                     console.log(`   Status: ${income.status} → ${newStatus}`);
                     await this.incomes.updateOne(
                        { _id: income._id },
                        { $set: { status: newStatus } }
                     );
                  }
               }

               this.stats.updatedIncomes++;
            } else {
               this.stats.unchangedIncomes++;
            }
         } catch (error) {
            console.error(
               `❌ Error processing income ${income._id}:`,
               error.message
            );
            this.stats.errors++;
         }
      }
   }

   async run(dryRun = true) {
      console.log("🚀 Starting Income Received Recalculation");
      console.log(`Mode: ${dryRun ? "DRY RUN" : "LIVE UPDATE"}`);
      console.log("=".repeat(50));

      await this.recalculateIncomeReceived(dryRun);

      console.log("\n📊 SUMMARY:");
      console.log(`Total Incomes: ${this.stats.totalIncomes}`);
      console.log(`Updated Incomes: ${this.stats.updatedIncomes}`);
      console.log(`Unchanged Incomes: ${this.stats.unchangedIncomes}`);
      console.log(`Errors: ${this.stats.errors}`);

      if (dryRun) {
         console.log("\n⚠️  This was a DRY RUN. No changes were made.");
         console.log(
            "Run with --live flag to apply changes: node recalculate-income-received.js --live"
         );
      } else {
         console.log("\n✅ Live update completed!");
      }
   }
}

async function main() {
   let client;

   try {
      console.log("🔗 Connecting to MongoDB...");
      client = new MongoClient(MONGODB_URI);
      await client.connect();

      const db = client.db(DB_NAME);
      console.log(`✅ Connected to database: ${DB_NAME}`);

      const recalculator = new IncomeReceivedRecalculator(db);

      // Check if --live flag is provided
      const isLiveRun = process.argv.includes("--live");

      await recalculator.run(!isLiveRun);
   } catch (error) {
      console.error("💥 Fatal error:", error);
      process.exit(1);
   } finally {
      if (client) {
         await client.close();
         console.log("🔌 Database connection closed");
      }
   }
}

// Run the script
if (require.main === module) {
   main();
}

module.exports = { IncomeReceivedRecalculator };

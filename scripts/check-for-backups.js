#!/usr/bin/env node

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

const MONGODB_URI = process.env.MONGODB_URI;

async function checkForBackups() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");

      const db = mongoose.connection.db;

      // List all collections
      console.log("\n📊 Checking all collections in database...");
      const collections = await db.listCollections().toArray();
      console.log("Available collections:");
      collections.forEach((col) => console.log(`  - ${col.name}`));

      // Check if there are any backup collections
      const backupCollections = collections.filter(
         (col) =>
            col.name.includes("backup") ||
            col.name.includes("migration") ||
            col.name.includes("old") ||
            col.name.includes("transactions_") ||
            col.name.endsWith("_backup")
      );

      if (backupCollections.length > 0) {
         console.log("\n🔍 Found potential backup collections:");
         backupCollections.forEach((col) => console.log(`  - ${col.name}`));

         // Check the first backup collection
         const backupData = await db
            .collection(backupCollections[0].name)
            .findOne({});
         if (backupData) {
            console.log("\nSample backup data:");
            console.log(`  _id: ${backupData._id}`);
            console.log(`  payee: ${backupData.payee}`);
            console.log(`  category: ${backupData.category || "MISSING"}`);
            console.log(
               `  categoryType: ${backupData.categoryType || "MISSING"}`
            );
         }
      } else {
         console.log("\n❌ No backup collections found");
      }

      // Check expenses and incomes to see if we can recover category mappings
      console.log(
         "\n📊 Checking expenses and incomes for category recovery..."
      );

      const expensesCollection = db.collection("expenses");
      const incomesCollection = db.collection("incomes");

      const expenseCount = await expensesCollection.countDocuments({});
      const incomeCount = await incomesCollection.countDocuments({});

      console.log(`Expenses: ${expenseCount}`);
      console.log(`Incomes: ${incomeCount}`);

      if (expenseCount > 0) {
         const sampleExpense = await expensesCollection.findOne({});
         console.log("\nSample expense:");
         console.log(`  _id: ${sampleExpense._id}`);
         console.log(`  description: ${sampleExpense.description}`);
         console.log(`  amountSpent: ${sampleExpense.amountSpent || 0}`);
      }

      if (incomeCount > 0) {
         const sampleIncome = await incomesCollection.findOne({});
         console.log("\nSample income:");
         console.log(`  _id: ${sampleIncome._id}`);
         console.log(`  description: ${sampleIncome.description}`);
         console.log(`  receivedAmount: ${sampleIncome.receivedAmount || 0}`);
      }
   } catch (error) {
      console.error("❌ Error:", error);
   } finally {
      await mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
   }
}

checkForBackups();

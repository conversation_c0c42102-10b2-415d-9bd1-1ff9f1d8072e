#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
const fetch = require("node-fetch");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

const BASE_URL = process.env.NEXT_PUBLIC_URL || "http://localhost:3000";

async function testAPIEndpoints() {
   console.log("🔍 Testing API Endpoints and Authentication Flow...\n");

   try {
      // Test 1: Authentication endpoints
      console.log("1. Testing authentication endpoints...");

      const authEndpoints = [
         "/api/auth/clerk-webhook",
         "/auth/login",
         "/auth/register",
      ];

      for (const endpoint of authEndpoints) {
         try {
            const response = await fetch(`${BASE_URL}${endpoint}`, {
               method: endpoint.includes("webhook") ? "POST" : "GET",
               headers: { "Content-Type": "application/json" },
            });

            console.log(
               `   ${endpoint}: ${response.status} ${response.statusText}`
            );
         } catch (error) {
            console.log(`   ${endpoint}: ❌ ${error.message}`);
         }
      }

      // Test 2: Get a test user from Clerk
      console.log("\n2. Testing user data retrieval...");
      const users = await clerk.users.getUserList({ limit: 1 });

      if (users.length > 0) {
         const testUser = users[0];
         console.log(`   Found test user: ${testUser.id}`);

         // Test user-specific endpoints
         const userEndpoints = [
            `/api/user/${testUser.id}/onboarding`,
            `/api/user/${testUser.id}`,
            `/api/user/balance`,
            `/api/user/accounts`,
            `/api/user/debts`,
         ];

         console.log("\n3. Testing user-specific endpoints...");
         for (const endpoint of userEndpoints) {
            try {
               const response = await fetch(`${BASE_URL}${endpoint}`, {
                  method: "GET",
                  headers: {
                     "Content-Type": "application/json",
                     Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
                  },
               });

               console.log(
                  `   ${endpoint}: ${response.status} ${response.statusText}`
               );

               if (response.ok) {
                  const data = await response.json();
                  console.log(
                     `     Data keys: ${Object.keys(data).join(", ")}`
                  );
               }
            } catch (error) {
               console.log(`   ${endpoint}: ❌ ${error.message}`);
            }
         }
      } else {
         console.log("   No users found in Clerk to test with");
      }

      // Test 3: Protected API routes
      console.log("\n4. Testing protected API routes...");
      const protectedEndpoints = [
         "/api/expenses",
         "/api/incomes",
         "/api/transactions",
         "/api/reports/budget",
      ];

      for (const endpoint of protectedEndpoints) {
         try {
            const response = await fetch(`${BASE_URL}${endpoint}`, {
               method: "GET",
               headers: { "Content-Type": "application/json" },
            });

            console.log(
               `   ${endpoint}: ${response.status} ${response.statusText}`
            );

            if (response.status === 401) {
               console.log("     ✅ Properly protected (401 Unauthorized)");
            } else if (response.ok) {
               console.log("     ⚠️  Accessible without authentication");
            }
         } catch (error) {
            console.log(`   ${endpoint}: ❌ ${error.message}`);
         }
      }

      // Test 4: Webhook simulation
      console.log("\n5. Testing webhook simulation...");
      const webhookEvents = [
         {
            type: "user.created",
            data: {
               id: "test-user-" + Date.now(),
               email_addresses: [{ email_address: "<EMAIL>" }],
               first_name: "Test",
               last_name: "User",
               created_at: Date.now(),
            },
         },
         {
            type: "user.updated",
            data: {
               id: "test-user-" + Date.now(),
               email_addresses: [{ email_address: "<EMAIL>" }],
               first_name: "Updated",
               last_name: "User",
               updated_at: Date.now(),
            },
         },
      ];

      for (const event of webhookEvents) {
         try {
            const response = await fetch(`${BASE_URL}/api/auth/clerk-webhook`, {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
                  "svix-id": "test-id-" + Date.now(),
                  "svix-timestamp": Date.now().toString(),
                  "svix-signature": "test-signature",
               },
               body: JSON.stringify(event),
            });

            console.log(
               `   ${event.type}: ${response.status} ${response.statusText}`
            );

            if (response.ok) {
               const result = await response.json();
               console.log(`     Response: ${JSON.stringify(result)}`);
            } else {
               const error = await response.text();
               console.log(`     Error: ${error}`);
            }
         } catch (error) {
            console.log(`   ${event.type}: ❌ ${error.message}`);
         }
      }

      console.log("\n🎉 API endpoint testing completed!");
   } catch (error) {
      console.error("❌ API endpoint testing failed:", error);
   }
}

// Test session validation
async function testSessionValidation() {
   console.log("\n🔐 Testing Session Validation...\n");

   try {
      // Get active sessions
      const sessions = await clerk.sessions.getSessionList({ limit: 5 });
      console.log(`Found ${sessions.length} active sessions`);

      if (sessions.length > 0) {
         for (const session of sessions) {
            console.log(`\nSession ${session.id}:`);
            console.log(`  User: ${session.userId}`);
            console.log(`  Status: ${session.status}`);
            console.log(
               `  Last Active: ${new Date(
                  session.lastActiveAt
               ).toLocaleString()}`
            );
            console.log(
               `  Expires: ${new Date(session.expireAt).toLocaleString()}`
            );

            // Try to verify the session
            try {
               const sessionToken = await clerk.sessions.getToken(session.id);
               console.log(`  ✅ Session token generated successfully`);
            } catch (error) {
               console.log(
                  `  ❌ Failed to generate session token: ${error.message}`
               );
            }
         }
      } else {
         console.log("No active sessions found");
      }
   } catch (error) {
      console.error("❌ Session validation failed:", error);
   }
}

// Command line options
const args = process.argv.slice(2);
const options = {
   endpoints: args.includes("--endpoints") || args.length === 0,
   sessions: args.includes("--sessions") || args.length === 0,
   all: args.includes("--all") || args.length === 0,
};

async function runTests() {
   if (options.endpoints || options.all) {
      await testAPIEndpoints();
   }

   if (options.sessions || options.all) {
      await testSessionValidation();
   }
}

runTests();

#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

async function testAccountsEndpoint() {
   console.log("🔍 Testing Accounts Endpoint...\n");

   try {
      // Get a test user from Clerk
      const users = await clerk.users.getUserList({ limit: 1 });
      if (users.length === 0) {
         console.log("❌ No users found in Clerk");
         return;
      }

      const testUser = users[0];
      console.log(`✅ Found test user: ${testUser.id}`);

      // Test if we can make authenticated requests
      console.log("\n📊 Testing API authentication pattern...");

      // Simulate what happens in the API
      const { auth } = require("@clerk/nextjs/server");

      // Test our auth pattern
      console.log("✅ Auth function imported successfully");

      // Test MongoDB connection
      const { MongoClient } = require("mongodb");
      const mongoClient = new MongoClient(process.env.MONGODB_URI);

      await mongoClient.connect();
      const db = mongoClient.db();
      const users_collection = db.collection("users");

      // Check if user exists in MongoDB
      const mongoUser = await users_collection.findOne({
         clerkId: testUser.id,
      });
      if (mongoUser) {
         console.log("✅ User found in MongoDB:", mongoUser._id);
         console.log(
            "✅ User has accounts:",
            mongoUser.accounts ? mongoUser.accounts.length : 0
         );
      } else {
         console.log("❌ User not found in MongoDB");
      }

      await mongoClient.close();
      console.log("\n🎉 Accounts endpoint test completed!");
   } catch (error) {
      console.error("❌ Test failed:", error.message);
      console.error("Stack:", error.stack);
   }
}

// Run the test
testAccountsEndpoint().catch(console.error);

#!/usr/bin/env node

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

const MONGODB_URI = process.env.MONGODB_URI;

async function checkDatabaseState() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");

      const db = mongoose.connection.db;
      const transactionsCollection = db.collection("transactions");

      // Check a few sample transactions
      console.log("\n📊 Checking sample transactions...");
      const sampleTransactions = await transactionsCollection
         .find({})
         .limit(5)
         .toArray();

      console.log("\nSample transactions:");
      sampleTransactions.forEach((tx, index) => {
         console.log(`\nTransaction ${index + 1}:`);
         console.log(`  _id: ${tx._id}`);
         console.log(`  payee: ${tx.payee}`);
         console.log(`  amount: ${tx.amount}`);
         console.log(`  category: ${tx.category || "MISSING"}`);
         console.log(`  categoryType: ${tx.categoryType || "MISSING"}`);
         console.log(`  assignedTo: ${tx.assignedTo || "MISSING"}`);
         console.log(`  assignedToType: ${tx.assignedToType || "MISSING"}`);
      });

      // Count transactions with different property combinations
      const withCategory = await transactionsCollection.countDocuments({
         category: { $exists: true },
      });
      const withCategoryType = await transactionsCollection.countDocuments({
         categoryType: { $exists: true },
      });
      const withAssignedTo = await transactionsCollection.countDocuments({
         assignedTo: { $exists: true },
      });
      const withAssignedToType = await transactionsCollection.countDocuments({
         assignedToType: { $exists: true },
      });
      const withAssignedToNotNull = await transactionsCollection.countDocuments(
         { assignedTo: { $ne: null } }
      );
      const withAssignedToTypeNotNull =
         await transactionsCollection.countDocuments({
            assignedToType: { $ne: null },
         });

      console.log("\n📊 Property counts:");
      console.log(`Transactions with 'category' property: ${withCategory}`);
      console.log(
         `Transactions with 'categoryType' property: ${withCategoryType}`
      );
      console.log(`Transactions with 'assignedTo' property: ${withAssignedTo}`);
      console.log(
         `Transactions with 'assignedToType' property: ${withAssignedToType}`
      );
      console.log(
         `Transactions with non-null 'assignedTo': ${withAssignedToNotNull}`
      );
      console.log(
         `Transactions with non-null 'assignedToType': ${withAssignedToTypeNotNull}`
      );

      // Check if we have any backup data in other collections or if there are any transactions with the old properties
      const hasOldData = withCategory > 0 || withCategoryType > 0;
      console.log(
         `\n${
            hasOldData
               ? "✅ Old data still exists!"
               : "❌ Old data has been lost"
         }`
      );
   } catch (error) {
      console.error("❌ Error:", error);
   } finally {
      await mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
   }
}

checkDatabaseState();

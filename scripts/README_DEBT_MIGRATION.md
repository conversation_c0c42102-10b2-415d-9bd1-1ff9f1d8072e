# Debt Model Migration

This document explains the process of migrating debts from being embedded in the User document to being stored in their own collection.

## Background

Previously, all debt information was stored within the User document in an array called `debts`. This approach has limitations in terms of scalability, query performance, and flexibility. To address these issues, we've created a standalone Debt model that maintains a reference to the User.

## Migration Process

### 1. Create the Debt Model

We've created a new `Debt.js` model in `app/lib/mongodb/models/` that includes:

-  A reference to the user who owns the debt
-  All the same fields that were previously in the User's debt schema
-  Proper validation and formatting

### 2. Update the API Routes

The debt API routes have been updated to use the new Debt model:

-  `GET /api/user/debts` now queries the Debt collection instead of the User document
-  `POST /api/user/debts` creates new Debt documents
-  `PUT /api/user/debts` updates existing Debt documents
-  `DELETE /api/user/debts` marks Debt documents as inactive (soft delete)

### 3. Run the Migration Script

To migrate existing debt data from User documents to the new Debt collection, run:

```bash
node scripts/migrateDebtsToCollection.js
```

The script:

-  Finds all users with debt data
-  Creates new Debt documents for each debt
-  Reports on the migration progress
-  Keeps the original data in the User document (until you verify the migration)

### 4. Verify and Clean Up

After running the migration and verifying that all debts are correctly stored in the new collection, you can:

1. Uncomment the cleanup code in the migration script and run it again, or
2. Run a separate cleanup script to remove the debts array from User documents.

## Required Changes

The following files were modified or created:

1. **Created new model**:

   -  `app/lib/mongodb/models/Debt.js`

2. **Updated API routes**:

   -  `app/api/user/debts/route.js`

3. **Created migration tools**:

   -  `app/api/admin/migrate-debts-to-collection/route.js`
   -  `scripts/migrateDebtsToCollection.js`

4. **Updated frontend code**:
   -  `app/budget/page.js` - Updated to fetch debts from the standalone API

## Benefits

This architecture change provides:

-  Better query performance for debt-related operations
-  Improved scalability for users with many debts
-  Cleaner data model
-  Reduced document size for User records
-  Better separation of concerns

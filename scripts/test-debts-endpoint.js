#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

async function testDebtsEndpoint() {
   console.log("🔍 Testing Debts Endpoint...\n");

   try {
      // Get a test user from Clerk
      const users = await clerk.users.getUserList({ limit: 1 });
      if (users.length === 0) {
         console.log("❌ No users found in Clerk");
         return;
      }

      const testUser = users[0];
      console.log(`✅ Found test user: ${testUser.id}`);

      // Test MongoDB connection and user lookup
      const { MongoClient } = require("mongodb");
      const mongoClient = new MongoClient(process.env.MONGODB_URI);

      await mongoClient.connect();
      const db = mongoClient.db();
      const users_collection = db.collection("users");
      const debts_collection = db.collection("debts");

      // Check if user exists in MongoDB
      const mongoUser = await users_collection.findOne({
         clerkId: testUser.id,
      });
      if (mongoUser) {
         console.log("✅ User found in MongoDB:", mongoUser._id);

         // Check debts for this user
         const debts = await debts_collection
            .find({ userId: mongoUser._id })
            .toArray();
         console.log("✅ User has debts:", debts.length);

         // Test auth pattern import
         try {
            const { auth } = require("@clerk/nextjs/server");
            console.log("✅ Clerk auth function imported successfully");
         } catch (error) {
            console.log("❌ Error importing auth function:", error.message);
         }
      } else {
         console.log("❌ User not found in MongoDB");
      }

      await mongoClient.close();
      console.log("\n🎉 Debts endpoint test completed!");
   } catch (error) {
      console.error("❌ Test failed:", error.message);
   }
}

// Run the test
testDebtsEndpoint().catch(console.error);

#!/usr/bin/env node

/**
 * Cash Flow Audit Script
 *
 * This script performs a detailed cash flow analysis to identify discrepancies between:
 * - Account balances
 * - Ready to assign amounts
 * - Money assigned but not spent
 * - Transaction flows
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class CashFlowAuditor {
   constructor(db) {
      this.db = db;
      this.users = db.collection("users");
      this.transactions = db.collection("transactions");
      this.expenses = db.collection("expenses");
      this.incomes = db.collection("incomes");
   }

   // Helper function to round to 2 decimal places
   roundToTwo(num) {
      return Number(Math.round(num + "e+2") + "e-2");
   }

   // Helper function to format currency
   formatCurrency(amount) {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
         minimumFractionDigits: 2,
         maximumFractionDigits: 2,
      }).format(amount || 0);
   }

   /**
    * Main cash flow audit function
    */
   async auditCashFlow(userId) {
      console.log(`💰 Starting cash flow audit for user: ${userId}`);

      try {
         // Load all user data
         const userData = await this.loadUserData(userId);

         if (!userData.user) {
            throw new Error(`User not found: ${userId}`);
         }

         console.log(
            `👤 Analyzing cash flow for: ${userData.user.name} (${userData.user.email})`
         );
         console.log("=".repeat(80));

         // Perform detailed cash flow analysis
         const analysis = await this.performCashFlowAnalysis(userData);

         // Display results
         this.displayCashFlowReport(analysis);

         return analysis;
      } catch (error) {
         console.error(`❌ Cash flow audit failed: ${error.message}`);
         throw error;
      }
   }

   /**
    * Load all user data
    */
   async loadUserData(userId) {
      console.log("📊 Loading user data...");

      const [user, transactions, expenses, incomes] = await Promise.all([
         this.users.findOne({ _id: new ObjectId(userId) }),
         this.transactions.find({ userId: new ObjectId(userId) }).toArray(),
         this.expenses.find({ userId: userId }).toArray(),
         this.incomes.find({ userId: userId }).toArray(),
      ]);

      console.log(
         `📈 Loaded: ${transactions.length} transactions, ${expenses.length} expenses, ${incomes.length} incomes`
      );

      return { user, transactions, expenses, incomes };
   }

   /**
    * Perform comprehensive cash flow analysis
    */
   async performCashFlowAnalysis(userData) {
      const { user, transactions, expenses, incomes } = userData;

      console.log("\n🔍 CASH FLOW ANALYSIS");
      console.log("=".repeat(50));

      // 1. Account Balances
      console.log("\n💳 ACCOUNT BALANCES:");
      let totalAccountBalance = 0;

      if (user.accounts && user.accounts.length > 0) {
         for (const account of user.accounts) {
            console.log(
               `  ${account.name}: ${this.formatCurrency(account.balance)}`
            );
            totalAccountBalance += account.balance;
         }
      }
      console.log(
         `  📊 TOTAL ACCOUNT BALANCE: ${this.formatCurrency(
            totalAccountBalance
         )}`
      );

      // 2. Income Analysis
      console.log("\n💵 INCOME ANALYSIS:");
      const totalExpectedIncome = incomes.reduce(
         (sum, income) => sum + (income.expectedAmount || 0),
         0
      );
      const totalReceivedIncome = incomes.reduce(
         (sum, income) => sum + (income.receivedAmount || 0),
         0
      );

      console.log(
         `  Expected Income: ${this.formatCurrency(totalExpectedIncome)}`
      );
      console.log(
         `  Received Income: ${this.formatCurrency(totalReceivedIncome)}`
      );
      console.log(
         `  Income Gap: ${this.formatCurrency(
            totalExpectedIncome - totalReceivedIncome
         )}`
      );

      // 3. Expense Analysis
      console.log("\n💰 EXPENSE ANALYSIS:");
      const totalAmountDue = expenses.reduce(
         (sum, expense) => sum + (expense.amountDue || 0),
         0
      );
      const totalAmountAssigned = expenses.reduce(
         (sum, expense) => sum + (expense.amountAssigned || 0),
         0
      );
      const totalAmountSpent = expenses.reduce(
         (sum, expense) => sum + (expense.amountSpent || 0),
         0
      );
      const totalAmountAvailable = expenses.reduce(
         (sum, expense) =>
            sum +
            this.roundToTwo(
               (expense.amountAssigned || 0) + (expense.amountSpent || 0)
            ),
         0
      );

      console.log(`  Total Amount Due: ${this.formatCurrency(totalAmountDue)}`);
      console.log(
         `  Total Amount Assigned: ${this.formatCurrency(totalAmountAssigned)}`
      );
      console.log(
         `  Total Amount Spent: ${this.formatCurrency(totalAmountSpent)}`
      );
      console.log(
         `  Total Amount Available: ${this.formatCurrency(
            totalAmountAvailable
         )}`
      );

      // 4. Ready to Assign Calculation
      console.log("\n🎯 READY TO ASSIGN:");
      const readyToAssign = this.roundToTwo(
         totalReceivedIncome - totalAmountAssigned
      );
      console.log(
         `  Total Received: ${this.formatCurrency(totalReceivedIncome)}`
      );
      console.log(
         `  Total Assigned: ${this.formatCurrency(totalAmountAssigned)}`
      );
      console.log(
         `  📊 READY TO ASSIGN: ${this.formatCurrency(readyToAssign)}`
      );

      // 5. Transaction Flow Analysis
      console.log("\n🔄 TRANSACTION FLOW ANALYSIS:");

      // Categorize transactions
      const incomeTransactions = transactions.filter(
         (t) => t.type === "Income"
      );
      const expenseTransactions = transactions.filter(
         (t) => t.type === "Expense"
      );
      const transferTransactions = transactions.filter(
         (t) => t.type === "Transfer"
      );
      const uncategorizedTransactions = transactions.filter(
         (t) => !t.assignedTo
      );

      const totalIncomeFromTransactions = incomeTransactions.reduce(
         (sum, t) => sum + t.amount,
         0
      );
      const totalExpenseFromTransactions = expenseTransactions.reduce(
         (sum, t) => sum + t.amount,
         0
      ); // This will be negative

      console.log(
         `  Income Transactions: ${
            incomeTransactions.length
         } totaling ${this.formatCurrency(totalIncomeFromTransactions)}`
      );
      console.log(
         `  Expense Transactions: ${
            expenseTransactions.length
         } totaling ${this.formatCurrency(totalExpenseFromTransactions)}`
      );
      console.log(`  Transfer Transactions: ${transferTransactions.length}`);
      console.log(
         `  Uncategorized Transactions: ${uncategorizedTransactions.length}`
      );

      // 6. Expected vs Actual Account Balance
      console.log("\n⚖️  BALANCE RECONCILIATION:");

      // Calculate what account balance should be based on transactions
      let calculatedAccountBalance = 0;
      for (const transaction of transactions) {
         if (transaction.type === "Transfer") {
            // For transfers, we need to check which account this affects
            // This is simplified assuming one account
            continue;
         } else {
            calculatedAccountBalance += transaction.amount;
         }
      }
      calculatedAccountBalance = this.roundToTwo(calculatedAccountBalance);

      console.log(
         `  Stored Account Balance: ${this.formatCurrency(totalAccountBalance)}`
      );
      console.log(
         `  Calculated from Transactions: ${this.formatCurrency(
            calculatedAccountBalance
         )}`
      );
      console.log(
         `  Account Balance Difference: ${this.formatCurrency(
            totalAccountBalance - calculatedAccountBalance
         )}`
      );

      // 7. Cash Flow Theory vs Reality
      console.log("\n🧮 CASH FLOW THEORY:");
      const theoreticalCash = this.roundToTwo(
         readyToAssign + totalAmountAvailable
      );
      console.log(`  Ready to Assign: ${this.formatCurrency(readyToAssign)}`);
      console.log(
         `  + Amount Available in Expenses: ${this.formatCurrency(
            totalAmountAvailable
         )}`
      );
      console.log(
         `  = Theoretical Total Cash: ${this.formatCurrency(theoreticalCash)}`
      );
      console.log(
         `  Actual Account Balance: ${this.formatCurrency(totalAccountBalance)}`
      );
      console.log(
         `  📊 DISCREPANCY: ${this.formatCurrency(
            totalAccountBalance - theoreticalCash
         )}`
      );

      // 8. Detailed Transaction Analysis
      console.log("\n🔍 DETAILED TRANSACTION BREAKDOWN:");

      // Check for transactions that might not be properly categorized
      const incomeTransactionsLinked = transactions.filter(
         (t) => t.type === "Income" && t.assignedToType === "Income"
      );
      const incomeTransactionsToExpenses = transactions.filter(
         (t) => t.type === "Income" && t.assignedToType === "Expense"
      );
      const expenseTransactionsLinked = transactions.filter(
         (t) => t.type === "Expense" && t.assignedToType === "Expense"
      );

      console.log(
         `  Income → Income Categories: ${
            incomeTransactionsLinked.length
         } (${this.formatCurrency(
            incomeTransactionsLinked.reduce((s, t) => s + t.amount, 0)
         )})`
      );
      console.log(
         `  Income → Expense Categories (refunds): ${
            incomeTransactionsToExpenses.length
         } (${this.formatCurrency(
            incomeTransactionsToExpenses.reduce((s, t) => s + t.amount, 0)
         )})`
      );
      console.log(
         `  Expense → Expense Categories: ${
            expenseTransactionsLinked.length
         } (${this.formatCurrency(
            expenseTransactionsLinked.reduce((s, t) => s + t.amount, 0)
         )})`
      );

      return {
         totalAccountBalance,
         totalReceivedIncome,
         totalAmountAssigned,
         totalAmountAvailable,
         readyToAssign,
         theoreticalCash,
         discrepancy: totalAccountBalance - theoreticalCash,
         calculatedAccountBalance,
         accountBalanceDiff: totalAccountBalance - calculatedAccountBalance,
         transactions: {
            income: incomeTransactions.length,
            expense: expenseTransactions.length,
            transfer: transferTransactions.length,
            uncategorized: uncategorizedTransactions.length,
         },
      };
   }

   /**
    * Display cash flow report
    */
   displayCashFlowReport(analysis) {
      console.log("\n" + "=".repeat(80));
      console.log("📋 CASH FLOW AUDIT SUMMARY");
      console.log("=".repeat(80));

      console.log(
         `💳 Account Balance: ${this.formatCurrency(
            analysis.totalAccountBalance
         )}`
      );
      console.log(
         `🎯 Ready to Assign: ${this.formatCurrency(analysis.readyToAssign)}`
      );
      console.log(
         `💰 Available in Expenses: ${this.formatCurrency(
            analysis.totalAmountAvailable
         )}`
      );
      console.log(
         `🧮 Theoretical Total: ${this.formatCurrency(
            analysis.theoreticalCash
         )}`
      );

      console.log("\n" + "-".repeat(50));

      if (Math.abs(analysis.discrepancy) > 0.01) {
         console.log(
            `❌ DISCREPANCY FOUND: ${this.formatCurrency(analysis.discrepancy)}`
         );

         if (Math.abs(analysis.accountBalanceDiff) > 0.01) {
            console.log(
               `   Account balance calculation error: ${this.formatCurrency(
                  analysis.accountBalanceDiff
               )}`
            );
         }

         console.log("\n🔍 POTENTIAL CAUSES:");
         console.log(
            "   • Transactions not properly linked to income/expense categories"
         );
         console.log("   • Account balance not matching transaction history");
         console.log("   • Manual adjustments or data entry errors");
         console.log(
            "   • Transfer transactions affecting balance calculation"
         );
      } else {
         console.log(
            "✅ CASH FLOW IS BALANCED - No significant discrepancies found!"
         );
      }

      console.log("=".repeat(80));
   }
}

/**
 * Main execution function
 */
async function runCashFlowAudit() {
   const userId = process.argv[2];

   if (!userId) {
      console.error(`
❌ Usage: node cash-flow-audit.js <user_id>

Example:
  node cash-flow-audit.js 6764aeeccfd30a63a4a45566
      `);
      process.exit(1);
   }

   const client = new MongoClient(MONGODB_URI);

   try {
      console.log("🔍 Connecting to database...");
      await client.connect();
      const db = client.db(DB_NAME);

      const auditor = new CashFlowAuditor(db);
      const results = await auditor.auditCashFlow(userId);
   } catch (error) {
      console.error("❌ Cash flow audit failed:", error);
   } finally {
      await client.close();
      console.log("\n🔐 Database connection closed");
   }
}

// Run the audit
if (require.main === module) {
   runCashFlowAudit().catch(console.error);
}

module.exports = { CashFlowAuditor };

#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up Free Tier Price in Stripe
 *
 * This script provides instructions for setting up the free tier price in Stripe.
 * You'll need to create a $0 price in your Stripe dashboard and add the price ID
 * to your environment variables.
 */

console.log(`
🔧 Setting up Free Tier in Stripe

To complete the free tier setup, you need to:

1. Log into your Stripe Dashboard (https://dashboard.stripe.com)

2. Navigate to Products > Add Product

3. Create a new product:
   - Name: "Free Plan"
   - Description: "Free tier subscription with basic features"

4. Add a price to the product:
   - Price: $0.00
   - Billing period: Monthly
   - Currency: USD

5. Copy the Price ID (starts with "price_")

6. Add the Price ID to your .env file:
   STRIPE_FREE_PRICE_ID=price_your_price_id_here

7. Restart your development server

Alternative: The application will work without STRIPE_FREE_PRICE_ID by creating 
dynamic prices, but having a fixed price ID is recommended for better tracking.

✅ Once completed, new users will automatically get a free tier subscription 
   in Stripe, and existing users can downgrade to free.
`);

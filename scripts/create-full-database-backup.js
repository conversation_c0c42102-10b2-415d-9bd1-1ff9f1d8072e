#!/usr/bin/env node

/**
 * Database Backup Script
 *
 * This script creates a complete backup of all collections in the MongoDB database
 * and saves them as JSON files in a timestamped backup directory.
 *
 * Usage: node scripts/create-full-database-backup.js
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import fs from "fs/promises";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI environment variable is not set");
   process.exit(1);
}

// Create backup directory with timestamp
const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
const backupDir = path.join(__dirname, `../database-backup-${timestamp}`);

// Connect to MongoDB
async function connectToMongoDB() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");
   } catch (error) {
      console.error("❌ Failed to connect to MongoDB:", error);
      process.exit(1);
   }
}

// Create backup directory
async function createBackupDirectory() {
   try {
      await fs.mkdir(backupDir, { recursive: true });
      console.log(`📁 Created backup directory: ${backupDir}`);
   } catch (error) {
      console.error("❌ Failed to create backup directory:", error);
      process.exit(1);
   }
}

// Backup a single collection
async function backupCollection(db, collectionName) {
   try {
      console.log(`📥 Backing up collection: ${collectionName}`);

      const collection = db.collection(collectionName);
      const documents = await collection.find({}).toArray();

      const filePath = path.join(backupDir, `${collectionName}.json`);
      await fs.writeFile(filePath, JSON.stringify(documents, null, 2));

      console.log(
         `✅ Backed up ${documents.length} documents from ${collectionName}`
      );
      return documents.length;
   } catch (error) {
      console.error(`❌ Failed to backup collection ${collectionName}:`, error);
      throw error;
   }
}

// Main backup function
async function createFullBackup() {
   try {
      console.log("🚀 Starting full database backup...");

      await connectToMongoDB();
      await createBackupDirectory();

      const db = mongoose.connection.db;

      // Get all collections
      const collections = await db.listCollections().toArray();
      console.log(`📊 Found ${collections.length} collections to backup`);

      let totalDocuments = 0;
      const backupSummary = {};

      // Backup each collection
      for (const collectionInfo of collections) {
         const collectionName = collectionInfo.name;
         const documentCount = await backupCollection(db, collectionName);
         backupSummary[collectionName] = documentCount;
         totalDocuments += documentCount;
      }

      // Create backup metadata
      const metadata = {
         timestamp: new Date().toISOString(),
         totalCollections: collections.length,
         totalDocuments,
         collections: backupSummary,
         mongoUri: MONGODB_URI.replace(/\/\/.*@/, "//[CREDENTIALS]@"), // Hide credentials in metadata
      };

      const metadataPath = path.join(backupDir, "backup-metadata.json");
      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));

      console.log("\n🎉 Backup completed successfully!");
      console.log(`📁 Backup location: ${backupDir}`);
      console.log(`📊 Total collections: ${collections.length}`);
      console.log(`📊 Total documents: ${totalDocuments}`);
      console.log("\n📋 Collection summary:");

      for (const [collectionName, count] of Object.entries(backupSummary)) {
         console.log(`  ${collectionName}: ${count} documents`);
      }
   } catch (error) {
      console.error("❌ Backup failed:", error);
      process.exit(1);
   } finally {
      await mongoose.connection.close();
      console.log("🔌 Disconnected from MongoDB");
   }
}

// Run the backup
createFullBackup();

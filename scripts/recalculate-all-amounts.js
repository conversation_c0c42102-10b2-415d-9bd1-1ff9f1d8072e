#!/usr/bin/env node

/**
 * Recalculate All Amounts Script
 *
 * This script combines both the expense spent amount recalculation and the
 * income received amount recalculation. It's a convenience script to run
 * both fixes after the migration from category/categoryType to assignedTo/assignedToType.
 *
 * The script will:
 * - Recalculate expense amountSpent fields from assigned transactions
 * - Recalculate income receivedAmount fields from assigned transactions
 * - Update status fields for both expenses and incomes
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

// Import the individual recalculators
const { ExpenseSpentRecalculator } = require("./recalculate-expense-spent");
const { IncomeReceivedRecalculator } = require("./recalculate-income-received");

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class AllAmountsRecalculator {
   constructor(db) {
      this.db = db;
      this.expenseRecalculator = new ExpenseSpentRecalculator(db);
      this.incomeRecalculator = new IncomeReceivedRecalculator(db);
   }

   async run(dryRun = true) {
      console.log("🚀 Starting Complete Amount Recalculation");
      console.log(`Mode: ${dryRun ? "DRY RUN" : "LIVE UPDATE"}`);
      console.log("=".repeat(60));

      // Run expense recalculation
      console.log("\n📝 STEP 1: RECALCULATING EXPENSE AMOUNTS");
      console.log("-".repeat(50));
      await this.expenseRecalculator.recalculateExpenseSpent(dryRun);

      // Run income recalculation
      console.log("\n📝 STEP 2: RECALCULATING INCOME AMOUNTS");
      console.log("-".repeat(50));
      await this.incomeRecalculator.recalculateIncomeReceived(dryRun);

      // Combined summary
      console.log("\n📊 COMBINED SUMMARY:");
      console.log("=".repeat(40));
      console.log(
         `Total Expenses: ${this.expenseRecalculator.stats.totalExpenses}`
      );
      console.log(
         `Updated Expenses: ${this.expenseRecalculator.stats.updatedExpenses}`
      );
      console.log(
         `Unchanged Expenses: ${this.expenseRecalculator.stats.unchangedExpenses}`
      );
      console.log(`Expense Errors: ${this.expenseRecalculator.stats.errors}`);
      console.log("");
      console.log(
         `Total Incomes: ${this.incomeRecalculator.stats.totalIncomes}`
      );
      console.log(
         `Updated Incomes: ${this.incomeRecalculator.stats.updatedIncomes}`
      );
      console.log(
         `Unchanged Incomes: ${this.incomeRecalculator.stats.unchangedIncomes}`
      );
      console.log(`Income Errors: ${this.incomeRecalculator.stats.errors}`);
      console.log("");
      console.log(
         `Total Items Updated: ${
            this.expenseRecalculator.stats.updatedExpenses +
            this.incomeRecalculator.stats.updatedIncomes
         }`
      );
      console.log(
         `Total Errors: ${
            this.expenseRecalculator.stats.errors +
            this.incomeRecalculator.stats.errors
         }`
      );

      if (dryRun) {
         console.log("\n⚠️  This was a DRY RUN. No changes were made.");
         console.log(
            "Run with --live flag to apply changes: node recalculate-all-amounts.js --live"
         );
      } else {
         console.log("\n✅ Complete recalculation finished!");
         console.log(
            "Both expense spent amounts and income received amounts have been updated."
         );
      }
   }
}

async function main() {
   let client;

   try {
      console.log("🔗 Connecting to MongoDB...");
      client = new MongoClient(MONGODB_URI);
      await client.connect();

      const db = client.db(DB_NAME);
      console.log(`✅ Connected to database: ${DB_NAME}`);

      const recalculator = new AllAmountsRecalculator(db);

      // Check if --live flag is provided
      const isLiveRun = process.argv.includes("--live");

      await recalculator.run(!isLiveRun);
   } catch (error) {
      console.error("💥 Fatal error:", error);
      process.exit(1);
   } finally {
      if (client) {
         await client.close();
         console.log("🔌 Database connection closed");
      }
   }
}

// Run the script
if (require.main === module) {
   main();
}

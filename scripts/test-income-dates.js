#!/usr/bin/env node

/**
 * Test Script: Verify Income Date String Format
 *
 * This script tests that income API endpoints work correctly with YYYY-MM-DD string dates
 *
 * Usage: node scripts/test-income-dates.js
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI environment variable is not set");
   process.exit(1);
}

// Connect to MongoDB
async function connectToMongoDB() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");
   } catch (error) {
      console.error("❌ Failed to connect to MongoDB:", error);
      process.exit(1);
   }
}

// Test function
async function testIncomeDates() {
   try {
      console.log("🧪 Testing income date string format...");

      // Get the incomes collection directly
      const db = mongoose.connection.db;
      const incomesCollection = db.collection("incomes");

      // Check that all income dates are strings
      const totalIncomes = await incomesCollection.countDocuments();
      const stringDateIncomes = await incomesCollection.countDocuments({
         date: { $type: "string" },
      });
      const dateObjectIncomes = await incomesCollection.countDocuments({
         date: { $type: "date" },
      });

      console.log(`📊 Total incomes: ${totalIncomes}`);
      console.log(`✅ String date incomes: ${stringDateIncomes}`);
      console.log(`❌ Date object incomes: ${dateObjectIncomes}`);

      if (dateObjectIncomes === 0) {
         console.log("✅ All income dates are in string format!");
      } else {
         console.log(
            `⚠️  ${dateObjectIncomes} incomes still have Date objects`
         );
      }

      // Test date format validation
      const sampleIncomes = await incomesCollection.find().limit(5).toArray();

      console.log("\n📝 Sample income dates:");
      sampleIncomes.forEach((income, index) => {
         const isValidFormat = /^\d{4}-\d{2}-\d{2}$/.test(income.date);
         console.log(
            `  ${index + 1}. ${income.description}: ${
               income.date
            } (${typeof income.date}) ${isValidFormat ? "✅" : "❌"}`
         );
      });

      // Test date range query
      console.log("\n🔍 Testing date range queries...");
      const recentIncomes = await incomesCollection
         .find({
            date: { $gte: "2024-12-01", $lte: "2024-12-31" },
         })
         .toArray();

      console.log(`📅 Found ${recentIncomes.length} incomes in December 2024`);

      return true;
   } catch (error) {
      console.error("❌ Test failed:", error);
      return false;
   }
}

// Main execution
async function main() {
   try {
      await connectToMongoDB();
      const success = await testIncomeDates();

      if (success) {
         console.log(
            "\n🎉 All tests passed! Income date migration is successful."
         );
      } else {
         console.log("\n💥 Some tests failed. Please check the output above.");
         process.exit(1);
      }
   } catch (error) {
      console.error("❌ Script failed:", error);
      process.exit(1);
   } finally {
      await mongoose.connection.close();
      console.log("🔌 Disconnected from MongoDB");
   }
}

// Run the test
main();

#!/usr/bin/env node

/**
 * Duplicate Expenses Scanner
 *
 * Scans the database for expenses that have the same date and description
 * Useful for identifying potential duplicate entries
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

async function scanDuplicateExpenses() {
   const client = new MongoClient(MONGODB_URI);

   try {
      console.log("🔍 Connecting to database...");
      await client.connect();
      const db = client.db(DB_NAME);
      const expensesCollection = db.collection("expenses");

      console.log("📊 Scanning for duplicate expenses...\n");

      // Get all expenses to analyze comprehensively
      console.log("🔍 Loading all expenses for comprehensive analysis...");
      const allExpenses = await expensesCollection.find({}).toArray();

      console.log(`📋 Found ${allExpenses.length} total expenses to analyze\n`);

      // Group expenses by user, description for initial filtering
      const expenseGroups = new Map();

      allExpenses.forEach((expense) => {
         const key = `${expense.userId}_${expense.description}`;
         if (!expenseGroups.has(key)) {
            expenseGroups.set(key, []);
         }
         expenseGroups.get(key).push(expense);
      });

      console.log(
         `📊 Grouped into ${expenseGroups.size} unique user-description combinations\n`
      );

      const duplicateGroups = [];

      // Analyze each group for potential duplicates
      for (const [key, expenses] of expenseGroups) {
         if (expenses.length < 2) continue; // Skip if only one expense

         const [userId, description] = key.split("_");
         console.log(
            `🔍 Analyzing "${description}" for user ${userId} (${expenses.length} expenses)...`
         );

         // Find different types of duplicates within this group
         const duplicates = findDuplicatesInGroup(expenses);

         if (duplicates.length > 0) {
            duplicateGroups.push(...duplicates);
         }
      }

      if (duplicateGroups.length === 0) {
         console.log("✅ No duplicate expenses found!");
         return;
      }

      console.log(
         `⚠️  Found ${duplicateGroups.length} groups of duplicate expenses:\n`
      );

      // Display results
      displayDuplicateResults(duplicateGroups);
   } catch (error) {
      console.error("❌ Error scanning for duplicates:", error);
   } finally {
      await client.close();
      console.log("🔐 Database connection closed");
   }
}

function findDuplicatesInGroup(expenses) {
   const duplicateGroups = [];
   const processedExpenses = new Set();

   for (let i = 0; i < expenses.length; i++) {
      if (processedExpenses.has(i)) continue;

      const expense1 = expenses[i];
      const duplicates = [expense1];
      processedExpenses.add(i);

      for (let j = i + 1; j < expenses.length; j++) {
         if (processedExpenses.has(j)) continue;

         const expense2 = expenses[j];

         if (areExpensesDuplicate(expense1, expense2)) {
            duplicates.push(expense2);
            processedExpenses.add(j);
         }
      }

      // If we found duplicates, add them to the results
      if (duplicates.length > 1) {
         const duplicateType = getDuplicateType(duplicates);
         duplicateGroups.push({
            _id: {
               description: expense1.description,
               userId: expense1.userId,
               type: duplicateType,
            },
            count: duplicates.length,
            expenses: duplicates,
         });
      }
   }

   return duplicateGroups;
}

function areExpensesDuplicate(expense1, expense2) {
   // Both expenses must have the same description and userId (already grouped by this)

   // Case 1: Both are regular expenses (non-weekly) - check if same date
   if (expense1.frequency !== "weekly" && expense2.frequency !== "weekly") {
      return isSameDate(expense1.date, expense2.date);
   }

   // Case 2: Both are weekly one-time expenses - check if same date
   if (
      expense1.frequency === "weekly" &&
      expense1.weeklyChargeType === "one-time" &&
      expense2.frequency === "weekly" &&
      expense2.weeklyChargeType === "one-time"
   ) {
      return isSameDate(expense1.date, expense2.date);
   }

   // Case 3: Both are weekly spread expenses - check for overlapping or identical ranges
   if (
      expense1.frequency === "weekly" &&
      expense1.weeklyChargeType === "spread" &&
      expense2.frequency === "weekly" &&
      expense2.weeklyChargeType === "spread"
   ) {
      // Check if both have valid date ranges
      if (
         expense1.startDate &&
         expense1.endDate &&
         expense2.startDate &&
         expense2.endDate
      ) {
         // Check for identical date ranges (exact duplicates)
         if (
            isSameDate(expense1.startDate, expense2.startDate) &&
            isSameDate(expense1.endDate, expense2.endDate)
         ) {
            return true;
         }

         // Check for overlapping date ranges (potential duplicates)
         // Since dates are now strings in YYYY-MM-DD format, we can use string comparison
         // or convert to Date objects for proper comparison
         const start1 = new Date(expense1.startDate);
         const end1 = new Date(expense1.endDate);
         const start2 = new Date(expense2.startDate);
         const end2 = new Date(expense2.endDate);

         return start1 <= end2 && start2 <= end1;
      }
   }

   // Case 4: Mixed types - check if they represent the same time period
   // For example, a weekly spread expense might overlap with a regular expense

   // Get the effective dates for each expense
   const dates1 = getEffectiveDates(expense1);
   const dates2 = getEffectiveDates(expense2);

   // Check if there's any overlap in their effective date ranges
   return dates1.some((date1) =>
      dates2.some((date2) => isSameDate(date1, date2))
   );
}

function getEffectiveDates(expense) {
   const dates = [];

   // Always include the main date if it exists
   if (expense.date) {
      dates.push(expense.date);
   }

   // For weekly spread expenses, include the date range
   if (
      expense.frequency === "weekly" &&
      expense.weeklyChargeType === "spread" &&
      expense.startDate &&
      expense.endDate
   ) {
      const start = new Date(expense.startDate);
      const end = new Date(expense.endDate);

      // Add each day in the range
      const current = new Date(start);
      while (current <= end) {
         dates.push(new Date(current));
         current.setDate(current.getDate() + 1);
      }
   }

   return dates;
}

function isSameDate(date1, date2) {
   if (!date1 || !date2) return false;

   const d1 = new Date(date1);
   const d2 = new Date(date2);

   return (
      d1.getFullYear() === d2.getFullYear() &&
      d1.getMonth() === d2.getMonth() &&
      d1.getDate() === d2.getDate()
   );
}

function getDuplicateType(expenses) {
   const types = expenses.map((exp) => {
      if (exp.frequency !== "weekly") return "regular";
      if (exp.weeklyChargeType === "one-time") return "weekly-one-time";
      if (exp.weeklyChargeType === "spread") return "weekly-spread";
      return "weekly-other";
   });

   const uniqueTypes = [...new Set(types)];

   if (uniqueTypes.length === 1) {
      return uniqueTypes[0];
   } else {
      return "mixed-types";
   }
}

function displayDuplicateResults(duplicateGroups) {
   let totalDuplicateExpenses = 0;
   const duplicatesByUser = new Map();

   duplicateGroups.forEach((group, index) => {
      const { count, expenses } = group;
      const groupType = group._id.type;

      totalDuplicateExpenses += count;

      // Extract userId
      const userId = group._id.userId;

      // Track duplicates by user
      if (!duplicatesByUser.has(userId.toString())) {
         duplicatesByUser.set(userId.toString(), []);
      }
      duplicatesByUser.get(userId.toString()).push(group);

      console.log(`📋 Group ${index + 1} (${groupType}):`);
      console.log(`   Description: "${group._id.description}"`);
      console.log(`   User ID: ${userId}`);
      console.log(`   Count: ${count} expenses`);
      console.log("   Expenses:");

      expenses.forEach((expense, expenseIndex) => {
         console.log(`     ${expenseIndex + 1}. ID: ${expense._id}`);
         console.log(
            `        Amount Due: $${(expense.amountDue || 0).toFixed(2)}`
         );
         console.log(
            `        Amount Assigned: $${(expense.amountAssigned || 0).toFixed(
               2
            )}`
         );
         console.log(
            `        Amount Spent: $${(expense.amountSpent || 0).toFixed(2)}`
         );
         console.log(`        Status: ${expense.status || "N/A"}`);
         console.log(`        Frequency: ${expense.frequency || "N/A"}`);
         console.log(
            `        Weekly Charge Type: ${expense.weeklyChargeType || "N/A"}`
         );

         if (expense.startDate && expense.endDate) {
            console.log(
               `        Date Range: ${new Date(
                  expense.startDate
               ).toLocaleDateString()} - ${new Date(
                  expense.endDate
               ).toLocaleDateString()}`
            );
         }
         if (expense.date) {
            console.log(
               `        Date: ${new Date(expense.date).toLocaleDateString()}`
            );
         }

         console.log(
            `        Created: ${
               expense.createdAt
                  ? new Date(expense.createdAt).toLocaleString()
                  : "N/A"
            }`
         );
      });
      console.log("");
   });

   // Summary statistics
   console.log("📈 Summary:");
   console.log(`   Total duplicate groups: ${duplicateGroups.length}`);
   console.log(`   Total duplicate expenses: ${totalDuplicateExpenses}`);
   console.log(`   Users affected: ${duplicatesByUser.size}`);
   console.log("");

   // Per-user breakdown
   console.log("👥 Per-user breakdown:");
   for (const [userId, userDuplicates] of duplicatesByUser) {
      const userTotalDuplicates = userDuplicates.reduce(
         (sum, group) => sum + group.count,
         0
      );
      console.log(
         `   User ${userId}: ${userDuplicates.length} groups, ${userTotalDuplicates} total duplicates`
      );
   }
   console.log("");

   // Enhanced recommendations
   console.log("💡 Recommendations:");
   console.log(
      "   1. Review each group to determine which expenses are legitimate"
   );
   console.log(
      "   2. For regular duplicates: Check creation dates to identify the original"
   );
   console.log(
      "   3. For weekly one-time duplicates: Verify if they should be on different dates"
   );
   console.log(
      "   4. For weekly spread overlaps: Check if date ranges should be adjacent, not overlapping"
   );
   console.log(
      "   5. For identical weekly spreads: Likely true duplicates - keep the one with transactions"
   );
   console.log(
      "   6. For mixed-type duplicates: Check if they represent the same expense in different formats"
   );
   console.log(
      "   7. Consider if duplicates have different assigned amounts or spending patterns"
   );
   console.log(
      "   8. Use the expense IDs above to manually review and delete duplicates"
   );
}

// Additional function to get detailed expense information
async function getExpenseDetails(expenseIds) {
   if (!Array.isArray(expenseIds) || expenseIds.length === 0) {
      console.log("Please provide an array of expense IDs");
      return;
   }

   const client = new MongoClient(MONGODB_URI);

   try {
      await client.connect();
      const db = client.db(DB_NAME);
      const expensesCollection = db.collection("expenses");

      const expenses = await expensesCollection
         .find({
            _id: {
               $in: expenseIds.map((id) =>
                  typeof id === "string" ? new ObjectId(id) : id
               ),
            },
         })
         .toArray();

      console.log("📋 Detailed Expense Information:");
      expenses.forEach((expense, index) => {
         console.log(`\n${index + 1}. Expense ID: ${expense._id}`);
         console.log(`   Description: ${expense.description}`);
         console.log(`   Date: ${new Date(expense.date).toLocaleDateString()}`);
         console.log(`   Amount Due: $${(expense.amountDue || 0).toFixed(2)}`);
         console.log(
            `   Amount Assigned: $${(expense.amountAssigned || 0).toFixed(2)}`
         );
         console.log(
            `   Amount Spent: $${(expense.amountSpent || 0).toFixed(2)}`
         );
         console.log(`   Status: ${expense.status || "N/A"}`);
         console.log(`   Frequency: ${expense.frequency || "N/A"}`);
         console.log(
            `   Created: ${
               expense.createdAt
                  ? new Date(expense.createdAt).toLocaleString()
                  : "N/A"
            }`
         );
         console.log(
            `   Updated: ${
               expense.updatedAt
                  ? new Date(expense.updatedAt).toLocaleString()
                  : "N/A"
            }`
         );
      });
   } catch (error) {
      console.error("❌ Error getting expense details:", error);
   } finally {
      await client.close();
   }
}

// Command line usage
if (require.main === module) {
   const args = process.argv.slice(2);

   if (args.length > 0 && args[0] === "--help") {
      console.log(`
Usage: node scripts/scan-duplicate-expenses.js [options]

Options:
  --help     Show this help message
  
Examples:
  node scripts/scan-duplicate-expenses.js
      `);
      process.exit(0);
   }

   scanDuplicateExpenses().catch(console.error);
}

module.exports = {
   scanDuplicateExpenses,
   getExpenseDetails,
};

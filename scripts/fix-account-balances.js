const mongoose = require("mongoose");
require("dotenv").config();

// Connect to MongoDB
async function connectDB() {
   try {
      await mongoose.connect(process.env.MONGODB_URI);
      console.log("Connected to MongoDB");
   } catch (error) {
      console.error("Failed to connect to MongoDB:", error);
      process.exit(1);
   }
}

// Load the User model
const User = mongoose.model(
   "User",
   new mongoose.Schema(
      {
         accounts: [
            {
               _id: Number,
               name: String,
               bank: String,
               accountType: String,
               balance: Number,
               active: Boolean,
               plaidItemId: String,
               dueDate: String,
               minimumPayment: Number,
               interestRate: Number,
            },
         ],
         email: String,
         name: String,
      },
      { collection: "users" }
   )
);

// Main function to fix account balances
async function fixAccountBalances() {
   try {
      await connectDB();

      // Find all users with accounts
      const users = await User.find({ "accounts.0": { $exists: true } });
      console.log(`Found ${users.length} users with accounts to process`);

      let fixedCount = 0;

      // Process each user
      for (const user of users) {
         const originalAccounts = [...user.accounts];
         let needsUpdate = false;

         // Check and fix each account balance
         for (let i = 0; i < user.accounts.length; i++) {
            const account = user.accounts[i];
            const originalBalance = account.balance;
            const roundedBalance = Number(Number(originalBalance).toFixed(2));

            // If the balance changed when rounded, update it
            if (originalBalance !== roundedBalance) {
               console.log(
                  `User ${user.email || "Unknown"}: Account "${
                     account.name
                  }" balance being fixed`
               );
               console.log(`  Original: ${originalBalance}`);
               console.log(`  Rounded:  ${roundedBalance}`);

               user.accounts[i].balance = roundedBalance;
               needsUpdate = true;
               fixedCount++;
            }
         }

         // Save the user if any accounts were updated
         if (needsUpdate) {
            await user.save();
            console.log(`Updated user ${user.email || user.name || "Unknown"}`);
         }
      }

      console.log(
         `Fixed ${fixedCount} account balances across ${users.length} users`
      );
   } catch (error) {
      console.error("Error fixing account balances:", error);
   } finally {
      await mongoose.disconnect();
      console.log("Disconnected from MongoDB");
   }
}

// Run the script
fixAccountBalances().catch(console.error);

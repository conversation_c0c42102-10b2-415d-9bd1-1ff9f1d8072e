#!/usr/bin/env node

/**
 * Migration Script: Convert Expense Dates to String Format
 *
 * This script converts all expense date fields from Date objects to YYYY-MM-DD string format:
 * - 'date' field (Date → String YYYY-MM-DD)
 * - 'startDate' field (Date → String YYYY-MM-DD)
 * - 'endDate' field (Date → String YYYY-MM-DD)
 *
 * Usage: node scripts/migrate-expense-dates-to-strings.js
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI environment variable is not set");
   process.exit(1);
}

// Connect to MongoDB
async function connectToMongoDB() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");
   } catch (error) {
      console.error("❌ Failed to connect to MongoDB:", error);
      process.exit(1);
   }
}

// Helper function to convert Date to YYYY-MM-DD string
function dateToString(date) {
   if (!date) return null;

   // If it's already a string, check if it's in the correct format
   if (typeof date === "string") {
      // If it's already in YYYY-MM-DD format, return as is
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
         return date;
      }
      // If it's an ISO string, convert to Date first
      date = new Date(date);
   }

   // If it's a Date object, convert to YYYY-MM-DD
   if (date instanceof Date && !isNaN(date)) {
      return date.toISOString().split("T")[0];
   }

   return null;
}

// Migration function
async function migrateExpenseDates() {
   try {
      console.log(
         "🚀 Starting migration: Converting expense dates to string format"
      );

      // Get the expenses collection directly
      const db = mongoose.connection.db;
      const expensesCollection = db.collection("expenses");

      // First, let's see what we're working with
      const totalExpenses = await expensesCollection.countDocuments();
      console.log(`📊 Found ${totalExpenses} total expenses in database`);

      // Find expenses with Date objects in date fields
      const expensesWithDateObjects = await expensesCollection
         .find({
            $or: [
               { date: { $type: "date" } },
               { startDate: { $type: "date" } },
               { endDate: { $type: "date" } },
            ],
         })
         .toArray();

      console.log(
         `🔄 Found ${expensesWithDateObjects.length} expenses with Date objects that need conversion`
      );

      if (expensesWithDateObjects.length === 0) {
         console.log(
            "✅ No expenses need date conversion. All dates are already in string format!"
         );
         return;
      }

      // Show some examples of what we'll be converting
      console.log("\n📝 Sample of conversions to be made:");
      expensesWithDateObjects.slice(0, 3).forEach((expense, index) => {
         console.log(`  Example ${index + 1}:`);
         console.log(`    Description: ${expense.description}`);
         if (expense.date) {
            console.log(
               `    date: ${expense.date} → ${dateToString(expense.date)}`
            );
         }
         if (expense.startDate) {
            console.log(
               `    startDate: ${expense.startDate} → ${dateToString(
                  expense.startDate
               )}`
            );
         }
         if (expense.endDate) {
            console.log(
               `    endDate: ${expense.endDate} → ${dateToString(
                  expense.endDate
               )}`
            );
         }
      });

      // Ask for confirmation
      console.log(
         `\n⚠️  This will update ${expensesWithDateObjects.length} expense documents.`
      );
      console.log("🔄 Starting migration in 3 seconds... (Ctrl+C to cancel)");
      await new Promise((resolve) => setTimeout(resolve, 3000));

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each expense
      for (const expense of expensesWithDateObjects) {
         try {
            const updateFields = {};

            // Convert date field if it's a Date object
            if (expense.date && expense.date instanceof Date) {
               updateFields.date = dateToString(expense.date);
            }

            // Convert startDate field if it's a Date object
            if (expense.startDate && expense.startDate instanceof Date) {
               updateFields.startDate = dateToString(expense.startDate);
            }

            // Convert endDate field if it's a Date object
            if (expense.endDate && expense.endDate instanceof Date) {
               updateFields.endDate = dateToString(expense.endDate);
            }

            // Only update if we have fields to update
            if (Object.keys(updateFields).length > 0) {
               await expensesCollection.updateOne(
                  { _id: expense._id },
                  { $set: updateFields }
               );

               successCount++;

               // Log progress every 100 updates
               if (successCount % 100 === 0) {
                  console.log(
                     `✅ Processed ${successCount}/${expensesWithDateObjects.length} expenses...`
                  );
               }
            }
         } catch (error) {
            errorCount++;
            errors.push({
               expenseId: expense._id,
               error: error.message,
            });
            console.error(
               `❌ Failed to update expense ${expense._id}:`,
               error.message
            );
         }
      }

      // Report results
      console.log("\n🎉 Migration completed!");
      console.log(`✅ Successfully updated: ${successCount} expenses`);
      console.log(`❌ Failed to update: ${errorCount} expenses`);

      if (errors.length > 0) {
         console.log("\n❌ Errors encountered:");
         errors.forEach(({ expenseId, error }) => {
            console.log(`  ${expenseId}: ${error}`);
         });
      }

      // Verify the migration
      console.log("\n🔍 Verifying migration...");
      const remainingDateObjects = await expensesCollection.countDocuments({
         $or: [
            { date: { $type: "date" } },
            { startDate: { $type: "date" } },
            { endDate: { $type: "date" } },
         ],
      });

      if (remainingDateObjects === 0) {
         console.log(
            "✅ Migration verification successful! All expense dates are now strings."
         );
      } else {
         console.log(
            `⚠️  Migration verification found ${remainingDateObjects} expenses still with Date objects.`
         );
      }

      // Show sample of converted data
      console.log("\n📝 Sample of converted expenses:");
      const convertedSamples = await expensesCollection
         .find({
            $or: [
               { date: { $type: "string" } },
               { startDate: { $type: "string" } },
               { endDate: { $type: "string" } },
            ],
         })
         .limit(3)
         .toArray();

      convertedSamples.forEach((expense, index) => {
         console.log(`  Sample ${index + 1}:`);
         console.log(`    Description: ${expense.description}`);
         console.log(`    date: ${expense.date} (${typeof expense.date})`);
         if (expense.startDate) {
            console.log(
               `    startDate: ${
                  expense.startDate
               } (${typeof expense.startDate})`
            );
         }
         if (expense.endDate) {
            console.log(
               `    endDate: ${expense.endDate} (${typeof expense.endDate})`
            );
         }
      });
   } catch (error) {
      console.error("❌ Migration failed:", error);
      throw error;
   }
}

// Main execution
async function main() {
   try {
      await connectToMongoDB();
      await migrateExpenseDates();
   } catch (error) {
      console.error("❌ Script failed:", error);
      process.exit(1);
   } finally {
      await mongoose.connection.close();
      console.log("🔌 Disconnected from MongoDB");
   }
}

// Run the migration
main();

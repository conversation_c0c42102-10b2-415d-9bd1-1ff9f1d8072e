# Amount Recalculation Scripts

This document describes the scripts created to fix the budget amounts after the migration from `category`/`categoryType` to `assignedTo`/`assignedToType` in the transaction model.

## Problem Statement

After migrating from the old `category` and `categoryType` fields to the new `assignedTo` and `assignedToType` fields in the Transaction model, the `amountSpent` fields in expenses and `receivedAmount` fields in incomes were no longer accurate. The migration removed the old category assignments but didn't properly populate the new `assignedTo` fields, leaving most expenses and incomes with their historical amounts but no currently assigned transactions.

## Scripts Overview

### 1. `recalculate-expense-spent.js`

**Purpose**: Recalculates the `amountSpent` field for all expenses based on transactions currently assigned to them.

**What it does**:

-  Finds all expenses in the database
-  For each expense, calculates the total spent from transactions with `assignedTo: expenseId` and `assignedToType: "Expense"`
-  Updates the expense's `amountSpent` field
-  Updates the expense status based on the new amounts (scheduled/funded/paid/overpaid)

**Usage**:

```bash
# Dry run (shows what would be changed)
node scripts/recalculate-expense-spent.js

# Live run (actually makes changes)
node scripts/recalculate-expense-spent.js --live
```

### 2. `recalculate-income-received.js`

**Purpose**: Recalculates the `receivedAmount` field for all incomes based on transactions currently assigned to them.

**What it does**:

-  Finds all incomes in the database
-  For each income, calculates the total received from transactions with `assignedTo: incomeId` and `assignedToType: "Income"`
-  Updates the income's `receivedAmount` field
-  Updates the income status based on the new amounts (scheduled/received)

**Usage**:

```bash
# Dry run (shows what would be changed)
node scripts/recalculate-income-received.js

# Live run (actually makes changes)
node scripts/recalculate-income-received.js --live
```

### 3. `recalculate-all-amounts.js`

**Purpose**: Convenience script that runs both expense and income recalculations in sequence.

**What it does**:

-  Runs the expense recalculation
-  Runs the income recalculation
-  Provides a combined summary of all changes

**Usage**:

```bash
# Dry run (shows what would be changed)
node scripts/recalculate-all-amounts.js

# Live run (actually makes changes)
node scripts/recalculate-all-amounts.js --live
```

## Results Summary

### Expense Recalculation Results

-  **Total Expenses Processed**: 486
-  **Expenses Updated**: 359
-  **Expenses Unchanged**: 127
-  **Errors**: 0

### Income Recalculation Results

-  **Total Incomes Processed**: 66
-  **Updated Incomes**: 58
-  **Unchanged Incomes**: 8
-  **Errors**: 0

### Combined Results

-  **Total Items Updated**: 417
-  **Total Items Processed**: 552

## What Was Fixed

1. **Expense Amounts**: Most expenses went from having negative spent amounts to $0.00 (since no transactions are currently assigned due to the migration issue)
2. **Income Amounts**: Most incomes went from having positive received amounts to $0.00 (same reason)
3. **Status Updates**: Expense and income statuses were automatically updated based on the new amounts

## Current State

After running these scripts:

-  All `amountSpent` fields accurately reflect transactions currently assigned to expenses
-  All `receivedAmount` fields accurately reflect transactions currently assigned to incomes
-  Status fields are consistent with the calculated amounts

However, since the migration didn't properly populate the `assignedTo` field on transactions, most expenses and incomes now show $0 spent/received.

## Next Steps

You'll likely want to run a separate migration script to properly assign transactions back to their corresponding expenses and incomes using matching logic (e.g., matching transaction amounts, dates, and descriptions to expense/income details). Once that's done, you can run these recalculation scripts again to ensure the amounts remain accurate.

## Safety Features

-  All scripts support dry-run mode by default (use `--live` flag to actually make changes)
-  Scripts include error handling and detailed logging
-  Database connections are properly managed and closed
-  Scripts can be run multiple times safely (idempotent)

## File Locations

-  `scripts/recalculate-expense-spent.js` - Expense recalculation script
-  `scripts/recalculate-income-received.js` - Income recalculation script
-  `scripts/recalculate-all-amounts.js` - Combined script
-  `scripts/README-AMOUNT-RECALCULATION.md` - This documentation

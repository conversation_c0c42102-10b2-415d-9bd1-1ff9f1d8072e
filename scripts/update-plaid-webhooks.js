#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update webhook URLs for all Plaid Items
 *
 * Usage:
 * 1. Make sure the PLAID_WEBHOOK_URL environment variable is set
 * 2. Run: node scripts/update-plaid-webhooks.js --apiKey=YOUR_ADMIN_API_KEY
 */

// Load environment variables
require("dotenv").config();

const fetch = require("node-fetch");
const args = process.argv.slice(2);

// Parse command line arguments
const getArg = (name) => {
   const arg = args.find((arg) => arg.startsWith(`--${name}=`));
   return arg ? arg.split("=")[1] : null;
};

async function main() {
   console.log("Plaid Webhook Migration Tool");
   console.log("--------------------------");

   // Validate environment variables
   if (!process.env.PLAID_WEBHOOK_URL) {
      console.error("Error: PLAID_WEBHOOK_URL environment variable is not set");
      process.exit(1);
   }

   console.log(`Webhook URL: ${process.env.PLAID_WEBHOOK_URL}`);

   // Get API key
   const apiKey = getArg("apiKey") || process.env.ADMIN_API_KEY;
   if (!apiKey) {
      console.error(
         "Error: Admin API key is required. Use --apiKey=YOUR_API_KEY or set ADMIN_API_KEY environment variable"
      );
      process.exit(1);
   }

   console.log("Starting webhook migration...");

   try {
      // Make API request to update webhooks
      const response = await fetch(
         `${
            process.env.NEXTAUTH_URL || "http://localhost:3000"
         }/api/admin/update-plaid-webhooks`,
         {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
            },
            body: JSON.stringify({ apiKey }),
         }
      );

      if (!response.ok) {
         const errorData = await response.json();
         throw new Error(
            errorData.error ||
               errorData.details ||
               `HTTP error ${response.status}`
         );
      }

      const results = await response.json();

      console.log("\nMigration Results:");
      console.log(`- Users processed: ${results.usersProcessed}`);
      console.log(`- Items processed: ${results.itemsProcessed}`);
      console.log(`- Successful updates: ${results.succeeded}`);
      console.log(`- Failed updates: ${results.failed}`);

      if (results.errors && results.errors.length > 0) {
         console.log("\nErrors:");
         results.errors.forEach((err) => {
            console.log(`- Item ${err.itemId}: ${err.error}`);
         });
      }

      console.log(
         "\nLog file with full details saved at: logs/plaid-webhook-migration.json"
      );
   } catch (error) {
      console.error(`\nError: ${error.message}`);
      process.exit(1);
   }
}

main();

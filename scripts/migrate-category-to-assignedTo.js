#!/usr/bin/env node

/**
 * Migration Script: Rename Transaction Properties
 *
 * This script renames the following properties in all Transaction documents:
 * - 'category' → 'assignedTo'
 * - 'categoryType' → 'assignedToType'
 *
 * Usage: node scripts/migrate-category-to-assignedTo.js
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI environment variable is not set");
   process.exit(1);
}

// Connect to MongoDB
async function connectToMongoDB() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");
   } catch (error) {
      console.error("❌ Failed to connect to MongoDB:", error);
      process.exit(1);
   }
}

// Migration function
async function migrateTransactionProperties() {
   try {
      console.log(
         "🚀 Starting migration: category → assignedTo, categoryType → assignedToType"
      );

      // Get the transactions collection directly
      const db = mongoose.connection.db;
      const transactionsCollection = db.collection("transactions");

      // First, let's see what we're working with
      const totalCount = await transactionsCollection.countDocuments();
      console.log(`📊 Total transactions in database: ${totalCount}`);

      // Count documents that have the old properties
      const withCategory = await transactionsCollection.countDocuments({
         category: { $exists: true },
      });
      const withCategoryType = await transactionsCollection.countDocuments({
         categoryType: { $exists: true },
      });

      console.log(`📊 Transactions with 'category' property: ${withCategory}`);
      console.log(
         `📊 Transactions with 'categoryType' property: ${withCategoryType}`
      );

      // Count documents that already have the new properties (in case this script was run before)
      const withAssignedTo = await transactionsCollection.countDocuments({
         assignedTo: { $exists: true },
      });
      const withAssignedToType = await transactionsCollection.countDocuments({
         assignedToType: { $exists: true },
      });

      console.log(
         `📊 Transactions with 'assignedTo' property: ${withAssignedTo}`
      );
      console.log(
         `📊 Transactions with 'assignedToType' property: ${withAssignedToType}`
      );

      if (withCategory === 0 && withCategoryType === 0) {
         console.log(
            "✅ No transactions found with old property names. Migration may have already been completed."
         );
         return;
      }

      if (withAssignedTo > 0 || withAssignedToType > 0) {
         console.log(
            "⚠️  Some transactions already have new property names. This script will handle both cases."
         );
      }

      // Confirm before proceeding
      console.log(
         "\n⚠️  This migration will rename properties in your database."
      );
      console.log(
         "Make sure you have a backup of your database before proceeding."
      );

      if (process.env.NODE_ENV === "production") {
         console.log(
            "❌ Migration cannot run in production environment for safety."
         );
         console.log(
            "Please run this in development or staging environment first."
         );
         return;
      }

      // Perform the migration using aggregation pipeline
      console.log("🔄 Starting property renaming...");

      let updatedCount = 0;

      // Use aggregation pipeline to rename fields
      const pipeline = [
         {
            $addFields: {
               assignedTo: {
                  $cond: {
                     if: { $ne: ["$assignedTo", undefined] },
                     then: "$assignedTo", // Keep existing assignedTo if it exists
                     else: "$category", // Otherwise use category value
                  },
               },
               assignedToType: {
                  $cond: {
                     if: { $ne: ["$assignedToType", undefined] },
                     then: "$assignedToType", // Keep existing assignedToType if it exists
                     else: "$categoryType", // Otherwise use categoryType value
                  },
               },
            },
         },
         {
            $unset: ["category", "categoryType"], // Remove old fields
         },
         {
            $merge: {
               into: "transactions",
               whenMatched: "replace",
            },
         },
      ];

      await transactionsCollection.aggregate(pipeline).toArray();

      // Count the results
      const finalWithAssignedTo = await transactionsCollection.countDocuments({
         assignedTo: { $exists: true },
      });
      const finalWithAssignedToType =
         await transactionsCollection.countDocuments({
            assignedToType: { $exists: true },
         });
      const remainingWithCategory = await transactionsCollection.countDocuments(
         { category: { $exists: true } }
      );
      const remainingWithCategoryType =
         await transactionsCollection.countDocuments({
            categoryType: { $exists: true },
         });

      console.log("\n📊 Migration Results:");
      console.log(`✅ Transactions with 'assignedTo': ${finalWithAssignedTo}`);
      console.log(
         `✅ Transactions with 'assignedToType': ${finalWithAssignedToType}`
      );
      console.log(`📉 Remaining with 'category': ${remainingWithCategory}`);
      console.log(
         `📉 Remaining with 'categoryType': ${remainingWithCategoryType}`
      );

      if (remainingWithCategory === 0 && remainingWithCategoryType === 0) {
         console.log("🎉 Migration completed successfully!");
      } else {
         console.log(
            "⚠️  Some documents still have old property names. Please check manually."
         );
      }
   } catch (error) {
      console.error("❌ Migration failed:", error);
      throw error;
   }
}

// Main execution
async function main() {
   try {
      await connectToMongoDB();
      await migrateTransactionProperties();
   } catch (error) {
      console.error("❌ Script failed:", error);
      process.exit(1);
   } finally {
      await mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
   }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
   console.log("\n⚠️  Received SIGINT. Cleaning up...");
   await mongoose.disconnect();
   process.exit(0);
});

process.on("SIGTERM", async () => {
   console.log("\n⚠️  Received SIGTERM. Cleaning up...");
   await mongoose.disconnect();
   process.exit(0);
});

// Run the migration
main();

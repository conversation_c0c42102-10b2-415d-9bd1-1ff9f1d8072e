/**
 * Cleanup script to check for and fix duplicate IDs in the Debt collection
 *
 * Run with: node scripts/cleanupDuplicateDebtIds.js
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import Debt from "../app/lib/mongodb/models/Debt.js";

// Load environment variables
dotenv.config();

async function connectToDatabase() {
   try {
      await mongoose.connect(process.env.MONGODB_URI);
      console.log("Connected to MongoDB");
   } catch (error) {
      console.error("Error connecting to MongoDB:", error);
      process.exit(1);
   }
}

async function cleanupDuplicateDebts() {
   try {
      // Group debts by userId and check for duplicates
      const debts = await Debt.find({}).lean();

      // Map to track potential duplicates
      const debtMap = new Map();
      const duplicatesFound = [];

      // Find potential duplicates based on userId, lender and similar data
      for (const debt of debts) {
         const key = `${debt.userId}_${debt.lender}_${debt.debtType}`;

         if (!debtMap.has(key)) {
            debtMap.set(key, []);
         }

         debtMap.get(key).push(debt);
      }

      // Check for duplicates
      for (const [key, debtList] of debtMap.entries()) {
         if (debtList.length > 1) {
            console.log(
               `Potential duplicates found for ${key}: ${debtList.length} records`
            );
            duplicatesFound.push(...debtList);
         }
      }

      if (duplicatesFound.length === 0) {
         console.log("No potential duplicates found.");
      } else {
         console.log(
            `${duplicatesFound.length} potential duplicate records found.`
         );

         // Display duplicates
         for (const dup of duplicatesFound) {
            console.log(
               `ID: ${dup._id}, Lender: ${dup.lender}, Balance: ${dup.balance}, User: ${dup.userId}`
            );
         }

         // You can uncomment this to remove all but the newest duplicate
         /*
      for (const [key, debtList] of debtMap.entries()) {
        if (debtList.length > 1) {
          // Sort by createdAt (newest first)
          debtList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          
          // Keep the newest one, delete the rest
          for (let i = 1; i < debtList.length; i++) {
            console.log(`Removing duplicate: ${debtList[i]._id}, Lender: ${debtList[i].lender}`);
            await Debt.deleteOne({ _id: debtList[i]._id });
          }
        }
      }
      */
      }

      console.log("Cleanup process completed.");
   } catch (error) {
      console.error("Error during cleanup:", error);
   } finally {
      await mongoose.connection.close();
      console.log("Database connection closed");
   }
}

// Run the cleanup
connectToDatabase()
   .then(cleanupDuplicateDebts)
   .then(() => {
      process.exit(0);
   })
   .catch((error) => {
      console.error("Cleanup failed:", error);
      process.exit(1);
   });

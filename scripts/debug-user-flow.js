#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
const { MongoClient } = require("mongodb");
const readline = require("readline");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

// MongoDB connection
const mongoClient = new MongoClient(process.env.MONGODB_URI);

// Create readline interface
const rl = readline.createInterface({
   input: process.stdin,
   output: process.stdout,
});

function question(query) {
   return new Promise((resolve) => rl.question(query, resolve));
}

async function debugUserFlow() {
   console.log("🔍 Clerk User Flow Debugger\n");

   try {
      await mongoClient.connect();
      const db = mongoClient.db();

      while (true) {
         console.log("\n=== DEBUGGING MENU ===");
         console.log("1. Test user by email");
         console.log("2. Test user by Clerk ID");
         console.log("3. Create test user");
         console.log("4. Check onboarding status");
         console.log("5. Fix sync issues");
         console.log("6. View user sessions");
         console.log("7. Test webhook manually");
         console.log("8. Check user data integrity");
         console.log("9. Exit");

         const choice = await question("\nEnter your choice (1-9): ");

         switch (choice) {
            case "1":
               await testUserByEmail(db);
               break;
            case "2":
               await testUserByClerkId(db);
               break;
            case "3":
               await createTestUser(db);
               break;
            case "4":
               await checkOnboardingStatus(db);
               break;
            case "5":
               await fixSyncIssues(db);
               break;
            case "6":
               await viewUserSessions();
               break;
            case "7":
               await testWebhookManually();
               break;
            case "8":
               await checkUserDataIntegrity(db);
               break;
            case "9":
               console.log("Goodbye!");
               return;
            default:
               console.log("Invalid choice. Please try again.");
         }
      }
   } catch (error) {
      console.error("❌ Error:", error);
   } finally {
      await mongoClient.close();
      rl.close();
   }
}

async function testUserByEmail(db) {
   const email = await question("Enter user email: ");

   console.log(`\n🔍 Searching for user with email: ${email}`);

   // Check Clerk
   try {
      const clerkUsers = await clerk.users.getUserList({
         emailAddress: [email],
      });
      if (clerkUsers.length > 0) {
         const clerkUser = clerkUsers[0];
         console.log("✅ Found in Clerk:");
         console.log(`   ID: ${clerkUser.id}`);
         console.log(`   Name: ${clerkUser.firstName} ${clerkUser.lastName}`);
         console.log(
            `   Created: ${new Date(clerkUser.createdAt).toLocaleString()}`
         );
         console.log(
            `   Last Sign In: ${
               clerkUser.lastSignInAt
                  ? new Date(clerkUser.lastSignInAt).toLocaleString()
                  : "Never"
            }`
         );

         // Check MongoDB
         const mongoUser = await db
            .collection("users")
            .findOne({ clerkId: clerkUser.id });
         if (mongoUser) {
            console.log("✅ Found in MongoDB:");
            console.log(`   MongoDB ID: ${mongoUser._id}`);
            console.log(`   Email: ${mongoUser.email}`);
            console.log(
               `   Onboarding Complete: ${
                  mongoUser.onboardingComplete || false
               }`
            );
            console.log(
               `   Created: ${new Date(mongoUser.createdAt).toLocaleString()}`
            );
         } else {
            console.log("❌ NOT found in MongoDB - sync issue detected!");
         }
      } else {
         console.log("❌ NOT found in Clerk");

         // Check if exists in MongoDB only
         const mongoUser = await db.collection("users").findOne({ email });
         if (mongoUser) {
            console.log("⚠️  Found in MongoDB but not in Clerk:");
            console.log(`   MongoDB ID: ${mongoUser._id}`);
            console.log(`   Clerk ID: ${mongoUser.clerkId || "Missing"}`);
         }
      }
   } catch (error) {
      console.error("❌ Error searching for user:", error.message);
   }
}

async function testUserByClerkId(db) {
   const clerkId = await question("Enter Clerk ID: ");

   console.log(`\n🔍 Searching for user with Clerk ID: ${clerkId}`);

   try {
      // Check Clerk
      const clerkUser = await clerk.users.getUser(clerkId);
      console.log("✅ Found in Clerk:");
      console.log(`   Email: ${clerkUser.emailAddresses[0]?.emailAddress}`);
      console.log(`   Name: ${clerkUser.firstName} ${clerkUser.lastName}`);
      console.log(
         `   Created: ${new Date(clerkUser.createdAt).toLocaleString()}`
      );

      // Check MongoDB
      const mongoUser = await db.collection("users").findOne({ clerkId });
      if (mongoUser) {
         console.log("✅ Found in MongoDB:");
         console.log(`   MongoDB ID: ${mongoUser._id}`);
         console.log(`   Email: ${mongoUser.email}`);
         console.log(
            `   Onboarding Complete: ${mongoUser.onboardingComplete || false}`
         );
      } else {
         console.log("❌ NOT found in MongoDB - sync issue detected!");
      }
   } catch (error) {
      console.error("❌ Error:", error.message);
   }
}

async function createTestUser(db) {
   const email = await question("Enter test user email: ");
   const firstName = await question("Enter first name: ");
   const lastName = await question("Enter last name: ");

   console.log(`\n🔨 Creating test user: ${email}`);

   try {
      // Create user in Clerk
      const clerkUser = await clerk.users.createUser({
         emailAddress: [email],
         firstName,
         lastName,
      });

      console.log("✅ Created in Clerk:", clerkUser.id);

      // Create user in MongoDB
      const mongoUser = await db.collection("users").insertOne({
         clerkId: clerkUser.id,
         email,
         firstName,
         lastName,
         onboardingComplete: false,
         createdAt: new Date(),
         updatedAt: new Date(),
      });

      console.log("✅ Created in MongoDB:", mongoUser.insertedId);
      console.log("🎉 Test user created successfully!");
   } catch (error) {
      console.error("❌ Error creating test user:", error.message);
   }
}

async function checkOnboardingStatus(db) {
   const email = await question("Enter user email: ");

   try {
      const mongoUser = await db.collection("users").findOne({ email });
      if (mongoUser) {
         console.log(`\n📋 Onboarding Status for ${email}:`);
         console.log(
            `   Onboarding Complete: ${mongoUser.onboardingComplete || false}`
         );
         console.log(
            `   Created: ${new Date(mongoUser.createdAt).toLocaleString()}`
         );

         if (!mongoUser.onboardingComplete) {
            const fix = await question("Mark onboarding as complete? (y/n): ");
            if (fix.toLowerCase() === "y") {
               await db.collection("users").updateOne(
                  { _id: mongoUser._id },
                  {
                     $set: {
                        onboardingComplete: true,
                        updatedAt: new Date(),
                     },
                  }
               );
               console.log("✅ Onboarding marked as complete");
            }
         }
      } else {
         console.log("❌ User not found in MongoDB");
      }
   } catch (error) {
      console.error("❌ Error:", error.message);
   }
}

async function fixSyncIssues(db) {
   console.log("\n🔧 Scanning for sync issues...");

   try {
      const clerkUsers = await clerk.users.getUserList({ limit: 50 });
      const mongoUsers = await db.collection("users").find({}).toArray();

      let fixed = 0;

      for (const clerkUser of clerkUsers) {
         const mongoUser = mongoUsers.find((u) => u.clerkId === clerkUser.id);

         if (!mongoUser) {
            console.log(
               `Found missing MongoDB user for Clerk ID: ${clerkUser.id}`
            );
            const create = await question("Create MongoDB user? (y/n): ");

            if (create.toLowerCase() === "y") {
               await db.collection("users").insertOne({
                  clerkId: clerkUser.id,
                  email: clerkUser.emailAddresses[0]?.emailAddress,
                  firstName: clerkUser.firstName,
                  lastName: clerkUser.lastName,
                  onboardingComplete: false,
                  createdAt: new Date(),
                  updatedAt: new Date(),
               });
               console.log("✅ Created MongoDB user");
               fixed++;
            }
         }
      }

      console.log(`\n🎉 Fixed ${fixed} sync issues`);
   } catch (error) {
      console.error("❌ Error:", error.message);
   }
}

async function viewUserSessions() {
   const email = await question("Enter user email: ");

   try {
      const clerkUsers = await clerk.users.getUserList({
         emailAddress: [email],
      });
      if (clerkUsers.length > 0) {
         const clerkUser = clerkUsers[0];
         const sessions = await clerk.sessions.getSessionList({
            userId: clerkUser.id,
         });

         console.log(`\n📱 Sessions for ${email}:`);
         if (sessions.length > 0) {
            sessions.forEach((session, index) => {
               console.log(`   ${index + 1}. ${session.id}`);
               console.log(`      Status: ${session.status}`);
               console.log(
                  `      Last Active: ${new Date(
                     session.lastActiveAt
                  ).toLocaleString()}`
               );
               console.log(
                  `      Expires: ${new Date(
                     session.expireAt
                  ).toLocaleString()}`
               );
            });
         } else {
            console.log("   No active sessions");
         }
      } else {
         console.log("❌ User not found in Clerk");
      }
   } catch (error) {
      console.error("❌ Error:", error.message);
   }
}

async function testWebhookManually() {
   console.log("\n🔗 Testing webhook manually...");

   const eventType = await question(
      "Enter event type (user.created, user.updated, user.deleted): "
   );
   const userId = await question("Enter user ID: ");
   const email = await question("Enter email: ");

   try {
      const webhookPayload = {
         type: eventType,
         data: {
            id: userId,
            email_addresses: [{ email_address: email }],
            first_name: "Test",
            last_name: "User",
            created_at: Date.now(),
            updated_at: Date.now(),
         },
      };

      const response = await fetch(
         `${
            process.env.NEXT_PUBLIC_URL || "http://localhost:3000"
         }/api/auth/clerk-webhook`,
         {
            method: "POST",
            headers: {
               "Content-Type": "application/json",
               "svix-id": "test-" + Date.now(),
               "svix-timestamp": Date.now().toString(),
               "svix-signature": "test-signature",
            },
            body: JSON.stringify(webhookPayload),
         }
      );

      console.log(`Response: ${response.status} ${response.statusText}`);

      if (response.ok) {
         const result = await response.json();
         console.log("✅ Webhook processed successfully:", result);
      } else {
         const error = await response.text();
         console.log("❌ Webhook error:", error);
      }
   } catch (error) {
      console.error("❌ Error testing webhook:", error.message);
   }
}

async function checkUserDataIntegrity(db) {
   console.log("\n🔍 Checking user data integrity...");

   try {
      const issues = [];

      // Check for users without email
      const usersWithoutEmail = await db
         .collection("users")
         .find({ email: { $exists: false } })
         .toArray();
      if (usersWithoutEmail.length > 0) {
         issues.push(`${usersWithoutEmail.length} users without email`);
      }

      // Check for users without clerkId
      const usersWithoutClerkId = await db
         .collection("users")
         .find({ clerkId: { $exists: false } })
         .toArray();
      if (usersWithoutClerkId.length > 0) {
         issues.push(`${usersWithoutClerkId.length} users without clerkId`);
      }

      // Check for duplicate emails
      const duplicateEmails = await db
         .collection("users")
         .aggregate([
            {
               $group: {
                  _id: "$email",
                  count: { $sum: 1 },
                  docs: { $push: "$$ROOT" },
               },
            },
            { $match: { count: { $gt: 1 } } },
         ])
         .toArray();

      if (duplicateEmails.length > 0) {
         issues.push(`${duplicateEmails.length} duplicate email addresses`);
      }

      // Check for invalid dates
      const invalidDates = await db
         .collection("users")
         .find({
            $or: [
               { createdAt: { $type: "string" } },
               { updatedAt: { $type: "string" } },
            ],
         })
         .toArray();

      if (invalidDates.length > 0) {
         issues.push(`${invalidDates.length} users with invalid date formats`);
      }

      if (issues.length === 0) {
         console.log("✅ All user data looks good!");
      } else {
         console.log("⚠️  Data integrity issues found:");
         issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
         });
      }
   } catch (error) {
      console.error("❌ Error checking data integrity:", error.message);
   }
}

// Run the debugger
debugUserFlow();

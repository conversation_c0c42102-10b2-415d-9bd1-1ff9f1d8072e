#!/usr/bin/env node

/**
 * Balance Fixer Script
 *
 * This script corrects common financial balance issues found by the audit script:
 * - Recalculates and fixes account balances
 * - Corrects expense amountSpent from linked transactions
 * - Fixes income receivedAmount from linked transactions
 * - Updates expense status based on current amounts
 * - Validates and corrects data precision issues
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class BalanceFixer {
   constructor(db) {
      this.db = db;
      this.users = db.collection("users");
      this.transactions = db.collection("transactions");
      this.expenses = db.collection("expenses");
      this.incomes = db.collection("incomes");
      this.debts = db.collection("debts");

      this.fixes = {
         accountBalances: 0,
         expenseAmounts: 0,
         incomeAmounts: 0,
         precisionIssues: 0,
         statusUpdates: 0,
      };
   }

   // Helper function to round to 2 decimal places
   roundToTwo(num) {
      return Number(Math.round(num + "e+2") + "e-2");
   }

   // Helper function to format currency
   formatCurrency(amount) {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
         minimumFractionDigits: 2,
         maximumFractionDigits: 2,
      }).format(amount || 0);
   }

   /**
    * Main function to fix balances for a user
    */
   async fixUserBalances(userId, dryRun = true) {
      console.log(
         `🔧 ${dryRun ? "DRY RUN - " : ""}Fixing balances for user: ${userId}`
      );

      try {
         // Load all user data
         const userData = await this.loadUserData(userId);

         if (!userData.user) {
            throw new Error(`User not found: ${userId}`);
         }

         console.log(
            `👤 Processing user: ${userData.user.name} (${userData.user.email})`
         );

         // Perform all fixes
         await this.fixAccountBalances(userData, dryRun);
         await this.fixExpenseAmounts(userData, dryRun);
         await this.fixIncomeAmounts(userData, dryRun);
         await this.fixPrecisionIssues(userData, dryRun);
         await this.updateStatuses(userData, dryRun);

         return this.fixes;
      } catch (error) {
         console.error(`❌ Failed to fix balances: ${error.message}`);
         throw error;
      }
   }

   /**
    * Load all user data
    */
   async loadUserData(userId) {
      console.log("📊 Loading user data...");

      const [user, transactions, expenses, incomes, debts] = await Promise.all([
         this.users.findOne({ _id: new ObjectId(userId) }),
         this.transactions.find({ userId: new ObjectId(userId) }).toArray(),
         this.expenses.find({ userId: userId }).toArray(),
         this.incomes.find({ userId: userId }).toArray(),
         this.debts.find({ userId: new ObjectId(userId) }).toArray(),
      ]);

      console.log(
         `📈 Loaded: ${transactions.length} transactions, ${expenses.length} expenses, ${incomes.length} incomes, ${debts.length} debts`
      );

      return { user, transactions, expenses, incomes, debts };
   }

   /**
    * Fix 1: Account Balances
    */
   async fixAccountBalances(userData, dryRun) {
      console.log("🏦 Fixing account balances...");
      const { user, transactions } = userData;

      for (const account of user.accounts || []) {
         // Calculate expected balance from transactions
         const accountTransactions = transactions.filter(
            (t) =>
               t.accountId === account._id ||
               (t.type === "Transfer" &&
                  (t.fromAccountId === account._id ||
                     t.toAccountId === account._id))
         );

         let calculatedBalance = 0;

         for (const transaction of accountTransactions) {
            if (transaction.type === "Transfer") {
               if (transaction.fromAccountId === account._id) {
                  calculatedBalance -= Math.abs(transaction.amount);
               } else if (transaction.toAccountId === account._id) {
                  calculatedBalance += Math.abs(transaction.amount);
               }
            } else {
               calculatedBalance += transaction.amount;
            }
         }

         calculatedBalance = this.roundToTwo(calculatedBalance);
         const storedBalance = this.roundToTwo(account.balance);

         if (Math.abs(calculatedBalance - storedBalance) > 0.01) {
            console.log(
               `🔧 Account "${account.name}": ${this.formatCurrency(
                  storedBalance
               )} → ${this.formatCurrency(calculatedBalance)}`
            );

            if (!dryRun) {
               await this.users.updateOne(
                  { _id: user._id, "accounts._id": account._id },
                  { $set: { "accounts.$.balance": calculatedBalance } }
               );
            }

            this.fixes.accountBalances++;
         }
      }
   }

   /**
    * Fix 2: Expense Amounts
    */
   async fixExpenseAmounts(userData, dryRun) {
      console.log("💰 Fixing expense amounts...");
      const { expenses, transactions } = userData;

      for (const expense of expenses) {
         // Verify amountSpent matches linked transactions
         const expenseTransactions = transactions.filter(
            (t) =>
               t.category &&
               t.categoryType === "Expense" &&
               t.category.toString() === expense._id.toString()
         );

         let calculatedSpent = 0;
         for (const transaction of expenseTransactions) {
            if (transaction.type === "Expense") {
               calculatedSpent += transaction.amount; // Already negative
            } else if (transaction.type === "Income") {
               calculatedSpent += transaction.amount; // Positive for refunds
            }
         }

         calculatedSpent = this.roundToTwo(calculatedSpent);
         const storedSpent = this.roundToTwo(expense.amountSpent);

         if (Math.abs(calculatedSpent - storedSpent) > 0.01) {
            console.log(
               `🔧 Expense "${
                  expense.description
               }" amountSpent: ${this.formatCurrency(
                  storedSpent
               )} → ${this.formatCurrency(calculatedSpent)}`
            );

            if (!dryRun) {
               await this.expenses.updateOne(
                  { _id: expense._id },
                  { $set: { amountSpent: calculatedSpent } }
               );
            }

            this.fixes.expenseAmounts++;
         }

         // Check and fix precision for other expense amounts
         const fixedAmountDue = this.roundToTwo(expense.amountDue);
         const fixedAmountAssigned = this.roundToTwo(expense.amountAssigned);

         if (
            Math.abs(expense.amountDue - fixedAmountDue) > 0.001 ||
            Math.abs(expense.amountAssigned - fixedAmountAssigned) > 0.001
         ) {
            if (!dryRun) {
               await this.expenses.updateOne(
                  { _id: expense._id },
                  {
                     $set: {
                        amountDue: fixedAmountDue,
                        amountAssigned: fixedAmountAssigned,
                     },
                  }
               );
            }

            this.fixes.precisionIssues++;
         }
      }
   }

   /**
    * Fix 3: Income Amounts
    */
   async fixIncomeAmounts(userData, dryRun) {
      console.log("💵 Fixing income amounts...");
      const { incomes, transactions } = userData;

      for (const income of incomes) {
         // Verify receivedAmount matches linked transactions
         const incomeTransactions = transactions.filter(
            (t) =>
               t.category &&
               t.categoryType === "Income" &&
               t.category.toString() === income._id.toString()
         );

         let calculatedReceived = 0;
         for (const transaction of incomeTransactions) {
            if (transaction.type === "Income") {
               calculatedReceived += transaction.amount;
            }
         }

         calculatedReceived = this.roundToTwo(calculatedReceived);
         const storedReceived = this.roundToTwo(income.receivedAmount || 0);

         if (Math.abs(calculatedReceived - storedReceived) > 0.01) {
            console.log(
               `🔧 Income "${
                  income.description
               }" receivedAmount: ${this.formatCurrency(
                  storedReceived
               )} → ${this.formatCurrency(calculatedReceived)}`
            );

            if (!dryRun) {
               await this.incomes.updateOne(
                  { _id: income._id },
                  { $set: { receivedAmount: calculatedReceived } }
               );
            }

            this.fixes.incomeAmounts++;
         }

         // Check and fix precision for expected amount
         const fixedExpectedAmount = this.roundToTwo(income.expectedAmount);

         if (Math.abs(income.expectedAmount - fixedExpectedAmount) > 0.001) {
            if (!dryRun) {
               await this.incomes.updateOne(
                  { _id: income._id },
                  { $set: { expectedAmount: fixedExpectedAmount } }
               );
            }

            this.fixes.precisionIssues++;
         }
      }
   }

   /**
    * Fix 4: Precision Issues
    */
   async fixPrecisionIssues(userData, dryRun) {
      console.log("🔢 Fixing precision issues...");
      const { transactions } = userData;

      for (const transaction of transactions) {
         const fixedAmount = this.roundToTwo(transaction.amount);

         if (Math.abs(transaction.amount - fixedAmount) > 0.001) {
            console.log(
               `🔧 Transaction ${transaction._id} amount: ${transaction.amount} → ${fixedAmount}`
            );

            if (!dryRun) {
               await this.transactions.updateOne(
                  { _id: transaction._id },
                  { $set: { amount: fixedAmount } }
               );
            }

            this.fixes.precisionIssues++;
         }
      }
   }

   /**
    * Fix 5: Update Statuses
    */
   async updateStatuses(userData, dryRun) {
      console.log("📊 Updating statuses...");
      const { incomes, expenses } = userData;

      // Update income statuses
      for (const income of incomes) {
         let newStatus = income.status;
         const receivedAmount = this.roundToTwo(income.receivedAmount || 0);
         const expectedAmount = this.roundToTwo(income.expectedAmount || 0);

         if (receivedAmount >= expectedAmount && income.status !== "received") {
            newStatus = "received";
         } else if (
            receivedAmount > 0 &&
            receivedAmount < expectedAmount &&
            income.status === "received"
         ) {
            newStatus = "scheduled";
         }

         if (newStatus !== income.status) {
            console.log(
               `🔧 Income "${income.description}" status: ${income.status} → ${newStatus}`
            );

            if (!dryRun) {
               await this.incomes.updateOne(
                  { _id: income._id },
                  { $set: { status: newStatus } }
               );
            }

            this.fixes.statusUpdates++;
         }
      }

      // Update expense statuses based on amounts
      for (const expense of expenses) {
         const amountAssigned = this.roundToTwo(expense.amountAssigned || 0);
         const amountSpent = this.roundToTwo(expense.amountSpent || 0);
         const amountAvailable = this.roundToTwo(amountAssigned + amountSpent);
         const amountDue = this.roundToTwo(expense.amountDue || 0);

         let newStatus = expense.status;

         if (Math.abs(amountSpent) >= amountDue && amountDue > 0) {
            if (Math.abs(amountSpent) > amountDue) {
               newStatus = "overpaid";
            } else {
               newStatus = "paid";
            }
         } else if (amountAssigned >= amountDue && amountDue > 0) {
            newStatus = "funded";
         } else if (amountAssigned > 0) {
            newStatus = "scheduled";
         }

         if (newStatus !== expense.status) {
            console.log(
               `🔧 Expense "${expense.description}" status: ${expense.status} → ${newStatus}`
            );

            if (!dryRun) {
               await this.expenses.updateOne(
                  { _id: expense._id },
                  { $set: { status: newStatus } }
               );
            }

            this.fixes.statusUpdates++;
         }
      }
   }

   /**
    * Generate summary report
    */
   generateSummary(dryRun) {
      console.log("\n" + "=".repeat(60));
      console.log(`📋 BALANCE FIXER ${dryRun ? "DRY RUN " : ""}SUMMARY`);
      console.log("=".repeat(60));
      console.log(`Account Balances Fixed: ${this.fixes.accountBalances}`);
      console.log(`Expense Amounts Fixed: ${this.fixes.expenseAmounts}`);
      console.log(`Income Amounts Fixed: ${this.fixes.incomeAmounts}`);
      console.log(`Precision Issues Fixed: ${this.fixes.precisionIssues}`);
      console.log(`Status Updates: ${this.fixes.statusUpdates}`);
      console.log("=".repeat(60));

      const totalFixes = Object.values(this.fixes).reduce(
         (sum, count) => sum + count,
         0
      );

      if (totalFixes === 0) {
         console.log("✅ No fixes needed - all balances are correct!");
      } else if (dryRun) {
         console.log(
            `⚠️  ${totalFixes} issues found that would be fixed in actual run`
         );
         console.log("Run with --fix flag to apply these changes");
      } else {
         console.log(`✅ ${totalFixes} issues have been fixed!`);
      }

      console.log("=".repeat(60));
   }
}

/**
 * Main execution function
 */
async function runBalanceFixer() {
   const args = process.argv.slice(2);
   const userId = args.find((arg) => !arg.startsWith("--"));
   const dryRun = !args.includes("--fix");
   const allUsers = args.includes("--all");

   if (!userId && !allUsers) {
      console.error(`
❌ Usage: node balance-fixer.js <user_id> [options]

Options:
  --fix       Actually apply the fixes (default is dry-run)
  --all       Process all users
  
Examples:
  node balance-fixer.js 507f1f77bcf86cd799439011           # Dry run for user
  node balance-fixer.js 507f1f77bcf86cd799439011 --fix     # Fix user balances
  node balance-fixer.js --all                              # Dry run for all users
  node balance-fixer.js --all --fix                        # Fix all users
      `);
      process.exit(1);
   }

   const client = new MongoClient(MONGODB_URI);

   try {
      console.log("🔍 Connecting to database...");
      await client.connect();
      const db = client.db(DB_NAME);

      const fixer = new BalanceFixer(db);

      if (allUsers) {
         console.log("🔧 Processing all users...");
         const users = await db
            .collection("users")
            .find({}, { projection: { _id: 1, name: 1, email: 1 } })
            .toArray();

         console.log(`Found ${users.length} users to process\n`);

         let totalFixes = {
            accountBalances: 0,
            expenseAmounts: 0,
            incomeAmounts: 0,
            precisionIssues: 0,
            statusUpdates: 0,
         };

         for (const user of users) {
            console.log(`\n${"*".repeat(80)}`);
            console.log(`PROCESSING USER: ${user.name} (${user.email})`);
            console.log(`${"*".repeat(80)}`);

            try {
               // Reset fixes counter for each user
               fixer.fixes = {
                  accountBalances: 0,
                  expenseAmounts: 0,
                  incomeAmounts: 0,
                  precisionIssues: 0,
                  statusUpdates: 0,
               };

               await fixer.fixUserBalances(user._id.toString(), dryRun);
               fixer.generateSummary(dryRun);

               // Accumulate total fixes
               Object.keys(totalFixes).forEach((key) => {
                  totalFixes[key] += fixer.fixes[key];
               });
            } catch (error) {
               console.error(
                  `❌ Failed to process user ${user.name}: ${error.message}`
               );
            }
         }

         // Overall summary
         console.log(`\n${"=".repeat(80)}`);
         console.log(
            `📋 OVERALL ${dryRun ? "DRY RUN " : ""}SUMMARY FOR ALL USERS`
         );
         console.log(`${"=".repeat(80)}`);
         console.log(
            `Total Account Balances Fixed: ${totalFixes.accountBalances}`
         );
         console.log(
            `Total Expense Amounts Fixed: ${totalFixes.expenseAmounts}`
         );
         console.log(`Total Income Amounts Fixed: ${totalFixes.incomeAmounts}`);
         console.log(
            `Total Precision Issues Fixed: ${totalFixes.precisionIssues}`
         );
         console.log(`Total Status Updates: ${totalFixes.statusUpdates}`);
         console.log(`${"=".repeat(80)}`);
      } else {
         await fixer.fixUserBalances(userId, dryRun);
         fixer.generateSummary(dryRun);
      }
   } catch (error) {
      console.error("❌ Balance fixer failed:", error);
   } finally {
      await client.close();
      console.log("\n🔐 Database connection closed");
   }
}

// Run the fixer
if (require.main === module) {
   runBalanceFixer().catch(console.error);
}

module.exports = { BalanceFixer };

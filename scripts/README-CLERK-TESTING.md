# Clerk Authentication Testing & Debugging Scripts

This directory contains scripts to help test, monitor, and debug the Clerk authentication integration with MongoDB.

## Quick Start

Make sure your environment variables are set up in `.env`:

```bash
CLERK_SECRET_KEY=your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
MONGODB_URI=your_mongodb_connection_string
NEXT_PUBLIC_URL=http://localhost:3000  # or your deployed URL
```

## Available Scripts

### 1. Integration Testing

**Test Clerk and MongoDB Integration:**

```bash
npm run test:clerk
```

This script:

-  Tests Clerk connection
-  Tests MongoDB connection
-  Checks user synchronization between Clerk and MongoDB
-  Tests webhook endpoint accessibility
-  Reports any sync issues

**Watch Mode (runs every 30 seconds):**

```bash
npm run test:clerk:watch
```

### 2. API Endpoint Testing

**Test all API endpoints:**

```bash
npm run test:api
```

**Test only endpoints:**

```bash
npm run test:api:endpoints
```

**Test only sessions:**

```bash
npm run test:api:sessions
```

This script:

-  Tests authentication endpoints
-  Tests user-specific endpoints
-  Tests protected API routes
-  Simulates webhook events
-  Validates session tokens

### 3. Authentication Monitoring

**One-time monitor run:**

```bash
npm run monitor:auth
```

**Continuous monitoring with health checks:**

```bash
npm run monitor:auth:watch
```

Features:

-  Continuous monitoring of auth status
-  Health checks every 5 minutes
-  Auto-recovery attempts every hour
-  Logs to `scripts/auth-monitor.log`
-  Creates `scripts/health-status.json`

Monitor options:

```bash
# Custom interval (default: 60 seconds)
node scripts/monitor-auth.js --interval=30

# One-time run with health check
node scripts/monitor-auth.js --once --health-check

# Full monitoring with auto-recovery
node scripts/monitor-auth.js --health-check --auto-recovery
```

### 4. Interactive User Debugging

**Launch interactive debugger:**

```bash
npm run debug:user
```

Interactive menu options:

1. **Test user by email** - Check if user exists in both Clerk and MongoDB
2. **Test user by Clerk ID** - Verify user data consistency
3. **Create test user** - Create a test user in both systems
4. **Check onboarding status** - View and fix onboarding completion
5. **Fix sync issues** - Automatically create missing MongoDB users
6. **View user sessions** - Check active sessions for a user
7. **Test webhook manually** - Simulate webhook events
8. **Check user data integrity** - Scan for data consistency issues

## Common Issues & Solutions

### Issue: Users exist in Clerk but not in MongoDB

**Symptoms:**

-  Users can authenticate but have no app data
-  Onboarding flow breaks
-  API calls fail with user not found

**Solution:**

```bash
npm run debug:user
# Choose option 5: Fix sync issues
```

Or manually create MongoDB user:

```bash
npm run debug:user
# Choose option 3: Create test user
```

### Issue: Webhook not receiving events

**Symptoms:**

-  New users not appearing in MongoDB
-  User updates not syncing

**Debugging:**

```bash
npm run test:api
# Check webhook endpoint status

npm run debug:user
# Choose option 7: Test webhook manually
```

**Solution:**

1. Check webhook URL in Clerk Dashboard
2. Verify `CLERK_WEBHOOK_SECRET` environment variable
3. Ensure webhook endpoint is accessible

### Issue: Authentication redirects not working

**Symptoms:**

-  Infinite redirect loops
-  Users stuck on login page
-  Middleware errors

**Debugging:**

```bash
npm run test:api:sessions
# Check session validity

npm run debug:user
# Choose option 6: View user sessions
```

### Issue: Onboarding flow problems

**Symptoms:**

-  Users redirected to onboarding repeatedly
-  Onboarding never completes

**Debugging:**

```bash
npm run debug:user
# Choose option 4: Check onboarding status
```

## Monitoring & Logs

### Log Files

-  `scripts/auth-monitor.log` - Continuous monitoring logs
-  `scripts/health-status.json` - Latest health check results

### Real-time Monitoring

```bash
# Watch logs in real-time
tail -f scripts/auth-monitor.log

# Monitor health status
watch -n 5 'cat scripts/health-status.json | jq .'
```

### Health Check Data

The health status includes:

-  User count statistics
-  Sync issue counts
-  System uptime
-  Memory usage
-  Last check timestamp

## Environment Setup

### Required Environment Variables

```bash
# Clerk Configuration
CLERK_SECRET_KEY=sk_live_...
CLERK_PUBLISHABLE_KEY=pk_live_...
CLERK_WEBHOOK_SECRET=whsec_...

# Database
MONGODB_URI=mongodb://localhost:27017/your-db

# Application
NEXT_PUBLIC_URL=http://localhost:3000
ADMIN_API_KEY=your_admin_key  # For internal API calls
```

### Optional Environment Variables

```bash
# Clerk customization
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/login
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/register
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/budget
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/onboarding
```

## Troubleshooting

### Script Errors

**Error: "Cannot connect to MongoDB"**

-  Check `MONGODB_URI` in `.env`
-  Ensure MongoDB is running
-  Verify network connectivity

**Error: "Clerk API key invalid"**

-  Check `CLERK_SECRET_KEY` in `.env`
-  Verify key is not expired
-  Ensure key has correct permissions

**Error: "Fetch is not defined"**

-  Run `npm install node-fetch`
-  Scripts should automatically handle this

### Performance Issues

**Scripts running slowly:**

-  Reduce user limit in scripts (default: 100 users)
-  Increase monitoring intervals
-  Check network latency to services

**Memory issues:**

```bash
# Check memory usage
node --max-old-space-size=4096 scripts/monitor-auth.js
```

## Best Practices

### Development

1. Run integration test after any auth changes
2. Use watch mode during active development
3. Check logs regularly for early issue detection

### Production

1. Set up continuous monitoring
2. Configure health check alerts
3. Regular data integrity checks
4. Monitor webhook delivery

### Testing

1. Create isolated test users
2. Use different email domains for testing
3. Clean up test data regularly
4. Test with different user states (new, existing, onboarded)

## Script Customization

### Adding Custom Checks

Edit the scripts to add application-specific checks:

```javascript
// In test-clerk-integration.js
async function customAppChecks(db) {
   // Check for required user fields
   const usersWithoutBalance = await db
      .collection("users")
      .find({
         initialBalance: { $exists: false },
      })
      .toArray();

   if (usersWithoutBalance.length > 0) {
      console.log(
         `⚠️  ${usersWithoutBalance.length} users missing initial balance`
      );
   }
}
```

### Environment-specific Configuration

Create different config files for different environments:

```javascript
// config/test.js
module.exports = {
   clerkConfig: {
      userLimit: 5,
      testMode: true,
   },
   mongoConfig: {
      connectTimeout: 5000,
   },
};
```

## Support

If you encounter issues:

1. Check the logs first: `tail -f scripts/auth-monitor.log`
2. Run the integration test: `npm run test:clerk`
3. Use the interactive debugger: `npm run debug:user`
4. Check Clerk Dashboard for webhook delivery status
5. Verify environment variables are set correctly

For persistent issues, the interactive debugger (`npm run debug:user`) is the most powerful tool for diagnosing and fixing problems.

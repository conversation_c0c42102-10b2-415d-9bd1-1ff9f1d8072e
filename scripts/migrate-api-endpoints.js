#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Get all API files that need migration
function getApiFilesToMigrate() {
   try {
      const result = execSync(
         'grep -r "getServerSession\\|authOptions" app/api/ --include="*.js" -l',
         { encoding: "utf8" }
      );
      return result
         .trim()
         .split("\n")
         .filter(
            (file) =>
               file &&
               !file.includes("auth/[...nextauth]") && // Skip NextAuth config file
               !file.includes("auth/clerk-webhook") // Skip Clerk webhook
         );
   } catch (error) {
      console.log("No files found or error:", error.message);
      return [];
   }
}

// Migration patterns
const migrations = [
   {
      // Replace imports
      pattern: /import { getServerSession } from ["']next-auth.*?["'];?\n/g,
      replacement: "",
   },
   {
      pattern:
         /import { authOptions } from ["'][^"']*auth\/\[\.\.\.nextauth\]\/route["'];?\n/g,
      replacement: "",
   },
   {
      // Add Clerk import if not present
      pattern:
         /^(import.*?from.*?["'];?\n)(?![\s\S]*import.*?auth.*?from.*?@clerk)/m,
      replacement: '$1import { auth } from "@clerk/nextjs/server";\n',
   },
   {
      // Replace session calls
      pattern: /const session = await getServerSession\(authOptions\);/g,
      replacement: "const { userId } = await auth();",
   },
   {
      // Replace session checks
      pattern: /if \(!session\) {/g,
      replacement: "if (!userId) {",
   },
   {
      // Replace user lookups with session.user.id
      pattern: /User\.findById\(session\.user\.id\)/g,
      replacement: "User.findOne({ clerkId: userId })",
   },
   {
      // Replace transactions queries with session.user.id
      pattern: /userId: session\.user\.id/g,
      replacement: "userId: user._id",
   },
   {
      // Replace other session.user.id references
      pattern: /session\.user\.id/g,
      replacement: "user._id",
   },
];

// Special handling for files that need user lookup first
const userLookupPattern =
   /const { userId } = await auth\(\);\s*if \(!userId\) {\s*return NextResponse\.json\({ error: ["']Unauthorized["'] }, { status: 401 }\);\s*}\s*await dbConnect\(\);/g;

function migrateFile(filePath) {
   console.log(`Migrating: ${filePath}`);

   let content = fs.readFileSync(filePath, "utf8");
   const originalContent = content;

   // Apply basic migrations
   migrations.forEach((migration) => {
      content = content.replace(migration.pattern, migration.replacement);
   });

   // Special handling: Add user lookup after auth check
   const needsUserLookup = content.includes(
      "User.findOne({ clerkId: userId })"
   );
   if (needsUserLookup) {
      content = content.replace(
         userLookupPattern,
         `const { userId } = await auth();
      if (!userId) {
         return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      await dbConnect();

      // Get user from MongoDB
      const user = await User.findOne({ clerkId: userId });
      if (!user) {
         return NextResponse.json({ error: "User not found" }, { status: 404 });
      }`
      );
   }

   // Clean up duplicate imports
   content = content.replace(
      /import { auth } from "@clerk\/nextjs\/server";\s*import { auth } from "@clerk\/nextjs\/server";/g,
      'import { auth } from "@clerk/nextjs/server";'
   );

   // Fix spacing issues
   content = content.replace(/\n\n\n+/g, "\n\n");

   if (content !== originalContent) {
      fs.writeFileSync(filePath, content, "utf8");
      console.log(`✅ Successfully migrated: ${filePath}`);
      return true;
   } else {
      console.log(`⚠️  No changes needed: ${filePath}`);
      return false;
   }
}

// Backup function
function createBackup(files) {
   const backupDir = path.join(__dirname, "api-migration-backup");
   if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
   }

   files.forEach((file) => {
      const backupPath = path.join(backupDir, path.basename(file) + ".backup");
      fs.copyFileSync(file, backupPath);
   });

   console.log(`📁 Backup created in: ${backupDir}`);
}

// Main function
function main() {
   console.log("🔍 Finding API files to migrate...");

   const filesToMigrate = getApiFilesToMigrate();

   if (filesToMigrate.length === 0) {
      console.log("✅ No files need migration!");
      return;
   }

   console.log(`Found ${filesToMigrate.length} files to migrate:`);
   filesToMigrate.forEach((file) => console.log(`  - ${file}`));

   // Create backup
   console.log("\n📁 Creating backup...");
   createBackup(filesToMigrate);

   // Migrate files
   console.log("\n🔧 Starting migration...");
   let migratedCount = 0;

   filesToMigrate.forEach((file) => {
      try {
         const wasMigrated = migrateFile(file);
         if (wasMigrated) migratedCount++;
      } catch (error) {
         console.error(`❌ Error migrating ${file}:`, error.message);
      }
   });

   console.log(`\n🎉 Migration complete! Updated ${migratedCount} files.`);

   // Check for remaining issues
   console.log("\n🔍 Checking for remaining NextAuth references...");
   try {
      const result = execSync(
         'grep -r "getServerSession\\|authOptions\\|session\\.user" app/api/ --include="*.js" | grep -v "auth/\\[...nextauth\\]" | head -10',
         { encoding: "utf8" }
      );
      if (result.trim()) {
         console.log("⚠️  Some files may need manual review:");
         console.log(result);
      } else {
         console.log("✅ No obvious NextAuth references found!");
      }
   } catch (error) {
      console.log("✅ No remaining NextAuth references found!");
   }

   console.log("\n📝 Next steps:");
   console.log("1. Test the application to ensure everything works");
   console.log("2. Run: npm run test:api");
   console.log("3. Check the backup folder if you need to revert any changes");
   console.log("4. Remove NextAuth dependencies if no longer needed");
}

// Run if called directly
if (require.main === module) {
   main();
}

module.exports = { migrateFile, getApiFilesToMigrate };

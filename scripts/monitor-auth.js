#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
const { MongoClient } = require("mongodb");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

// MongoDB connection
const mongoClient = new MongoClient(process.env.MONGODB_URI);

// Log file path
const logFile = path.join(__dirname, "auth-monitor.log");

// Statistics tracking
let stats = {
   totalUsers: 0,
   syncedUsers: 0,
   orphanedUsers: 0,
   failedSyncs: 0,
   lastCheck: null,
   uptime: Date.now(),
};

function log(message, level = "INFO") {
   const timestamp = new Date().toISOString();
   const logMessage = `[${timestamp}] [${level}] ${message}\n`;

   console.log(logMessage.trim());
   fs.appendFileSync(logFile, logMessage);
}

function formatUptime(ms) {
   const seconds = Math.floor(ms / 1000);
   const minutes = Math.floor(seconds / 60);
   const hours = Math.floor(minutes / 60);
   const days = Math.floor(hours / 24);

   if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
   if (hours > 0) return `${hours}h ${minutes % 60}m`;
   if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
   return `${seconds}s`;
}

async function monitorAuth() {
   try {
      log("Starting authentication monitoring cycle...");

      // Connect to MongoDB
      await mongoClient.connect();
      const db = mongoClient.db();

      // Get all users from Clerk
      const clerkUsers = await clerk.users.getUserList({ limit: 100 });
      const mongoUsers = await db.collection("users").find({}).toArray();

      stats.totalUsers = clerkUsers.length;
      stats.syncedUsers = 0;
      stats.orphanedUsers = 0;
      stats.failedSyncs = 0;
      stats.lastCheck = new Date();

      // Check for sync issues
      const issues = [];

      for (const clerkUser of clerkUsers) {
         const mongoUser = mongoUsers.find((u) => u.clerkId === clerkUser.id);

         if (!mongoUser) {
            issues.push({
               type: "missing_mongo_user",
               clerkId: clerkUser.id,
               email: clerkUser.emailAddresses[0]?.emailAddress,
            });
            stats.orphanedUsers++;
         } else {
            stats.syncedUsers++;

            // Check for data inconsistencies
            const clerkEmail = clerkUser.emailAddresses[0]?.emailAddress;
            if (mongoUser.email !== clerkEmail) {
               issues.push({
                  type: "email_mismatch",
                  clerkId: clerkUser.id,
                  clerkEmail,
                  mongoEmail: mongoUser.email,
               });
            }
         }
      }

      // Check for orphaned MongoDB users
      for (const mongoUser of mongoUsers) {
         if (
            mongoUser.clerkId &&
            !clerkUsers.find((u) => u.id === mongoUser.clerkId)
         ) {
            issues.push({
               type: "orphaned_mongo_user",
               clerkId: mongoUser.clerkId,
               email: mongoUser.email,
            });
            stats.orphanedUsers++;
         }
      }

      // Report issues
      if (issues.length > 0) {
         log(`Found ${issues.length} sync issues:`, "WARN");
         issues.forEach((issue, index) => {
            log(
               `  ${index + 1}. ${issue.type}: ${issue.clerkId} (${
                  issue.email || issue.clerkEmail || issue.mongoEmail
               })`,
               "WARN"
            );
         });
         stats.failedSyncs = issues.length;
      } else {
         log("✅ All users are synchronized properly");
      }

      // Test webhook endpoint health
      try {
         const webhookResponse = await fetch(
            `${
               process.env.NEXT_PUBLIC_URL || "http://localhost:3000"
            }/api/auth/clerk-webhook`,
            {
               method: "GET",
               timeout: 5000,
            }
         );

         if (webhookResponse.ok) {
            log("✅ Webhook endpoint is healthy");
         } else {
            log(
               `⚠️  Webhook endpoint returned ${webhookResponse.status}`,
               "WARN"
            );
         }
      } catch (error) {
         log(
            `❌ Webhook endpoint health check failed: ${error.message}`,
            "ERROR"
         );
      }

      // Check for recent user activity
      const recentUsers = await db
         .collection("users")
         .find({
            lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
         })
         .toArray();

      log(`Recent activity: ${recentUsers.length} users active in last 24h`);

      // Check for failed onboarding
      const incompleteOnboarding = await db
         .collection("users")
         .find({
            onboardingComplete: false,
            createdAt: { $lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
         })
         .toArray();

      if (incompleteOnboarding.length > 0) {
         log(
            `⚠️  ${incompleteOnboarding.length} users have incomplete onboarding for >7 days`,
            "WARN"
         );
      }

      // Log summary
      log(
         `Monitor Summary - Total: ${stats.totalUsers}, Synced: ${stats.syncedUsers}, Issues: ${stats.failedSyncs}`
      );
   } catch (error) {
      log(`❌ Monitor cycle failed: ${error.message}`, "ERROR");
   } finally {
      await mongoClient.close();
   }
}

// Health check endpoint simulation
async function performHealthCheck() {
   const healthData = {
      timestamp: new Date().toISOString(),
      uptime: formatUptime(Date.now() - stats.uptime),
      stats: stats,
      environment: {
         nodeVersion: process.version,
         platform: process.platform,
         memory: process.memoryUsage(),
      },
   };

   log(
      `Health Check - Uptime: ${healthData.uptime}, Users: ${stats.totalUsers}, Issues: ${stats.failedSyncs}`
   );

   // Save health data to file
   const healthFile = path.join(__dirname, "health-status.json");
   fs.writeFileSync(healthFile, JSON.stringify(healthData, null, 2));
}

// Auto-recovery for common issues
async function autoRecovery() {
   log("Running auto-recovery checks...");

   try {
      await mongoClient.connect();
      const db = mongoClient.db();

      // Check for users with missing clerkId
      const usersWithoutClerkId = await db
         .collection("users")
         .find({
            clerkId: { $exists: false },
         })
         .toArray();

      if (usersWithoutClerkId.length > 0) {
         log(
            `Found ${usersWithoutClerkId.length} users without clerkId - manual intervention required`,
            "WARN"
         );
      }

      // Check for duplicate emails
      const duplicateEmails = await db
         .collection("users")
         .aggregate([
            { $group: { _id: "$email", count: { $sum: 1 } } },
            { $match: { count: { $gt: 1 } } },
         ])
         .toArray();

      if (duplicateEmails.length > 0) {
         log(
            `Found ${duplicateEmails.length} duplicate email addresses - manual intervention required`,
            "WARN"
         );
      }

      log("Auto-recovery checks completed");
   } catch (error) {
      log(`❌ Auto-recovery failed: ${error.message}`, "ERROR");
   } finally {
      await mongoClient.close();
   }
}

// Signal handlers for graceful shutdown
process.on("SIGINT", () => {
   log("Received SIGINT, shutting down gracefully...");
   process.exit(0);
});

process.on("SIGTERM", () => {
   log("Received SIGTERM, shutting down gracefully...");
   process.exit(0);
});

// Command line options
const args = process.argv.slice(2);
const options = {
   interval:
      parseInt(
         args.find((arg) => arg.startsWith("--interval="))?.split("=")[1]
      ) || 60,
   healthCheck: args.includes("--health-check"),
   autoRecovery: args.includes("--auto-recovery"),
   once: args.includes("--once"),
};

async function runMonitor() {
   log("🚀 Starting Clerk Authentication Monitor...");
   log(
      `Configuration: interval=${options.interval}s, healthCheck=${options.healthCheck}, autoRecovery=${options.autoRecovery}`
   );

   // Create log file if it doesn't exist
   if (!fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, "");
   }

   if (options.once) {
      await monitorAuth();
      if (options.healthCheck) await performHealthCheck();
      if (options.autoRecovery) await autoRecovery();
      return;
   }

   // Run initial check
   await monitorAuth();

   // Set up intervals
   const monitorInterval = setInterval(monitorAuth, options.interval * 1000);

   if (options.healthCheck) {
      const healthInterval = setInterval(performHealthCheck, 300000); // Every 5 minutes

      process.on("exit", () => {
         clearInterval(healthInterval);
      });
   }

   if (options.autoRecovery) {
      const recoveryInterval = setInterval(autoRecovery, 3600000); // Every hour

      process.on("exit", () => {
         clearInterval(recoveryInterval);
      });
   }

   process.on("exit", () => {
      clearInterval(monitorInterval);
      log("Monitor stopped");
   });
}

runMonitor();

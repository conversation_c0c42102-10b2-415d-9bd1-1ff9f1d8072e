#!/usr/bin/env node

/**
 * Negative Balance Expenses <PERSON>ript
 *
 * This script finds and lists all expenses with negative balances
 * (where amountAvailable = amountAssigned + amountSpent is negative)
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class NegativeBalanceAnalyzer {
   constructor(db) {
      this.db = db;
      this.expenses = db.collection("expenses");
   }

   // Helper function to round to 2 decimal places
   roundToTwo(num) {
      return Number(Math.round(num + "e+2") + "e-2");
   }

   // Helper function to format currency
   formatCurrency(amount) {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
         minimumFractionDigits: 2,
         maximumFractionDigits: 2,
      }).format(amount || 0);
   }

   /**
    * Find all expenses with negative balances
    */
   async findNegativeBalanceExpenses(userId) {
      console.log(
         `🔍 Finding expenses with negative balances for user: ${userId}`
      );

      try {
         // Load all expenses for the user
         const expenses = await this.expenses
            .find({ userId: userId })
            .toArray();

         console.log(`📊 Analyzing ${expenses.length} expenses...`);

         // Calculate amountAvailable for each expense and filter negatives
         const negativeBalanceExpenses = [];
         let totalNegativeAmount = 0;

         for (const expense of expenses) {
            const amountAssigned = expense.amountAssigned || 0;
            const amountSpent = expense.amountSpent || 0;
            const amountAvailable = this.roundToTwo(
               amountAssigned + amountSpent
            );

            if (amountAvailable < 0) {
               negativeBalanceExpenses.push({
                  ...expense,
                  amountAvailable,
               });
               totalNegativeAmount += Math.abs(amountAvailable);
            }
         }

         // Display results
         this.displayResults(negativeBalanceExpenses, totalNegativeAmount);

         return negativeBalanceExpenses;
      } catch (error) {
         console.error(`❌ Analysis failed: ${error.message}`);
         throw error;
      }
   }

   /**
    * Display the results
    */
   displayResults(negativeExpenses, totalNegativeAmount) {
      console.log("\n" + "=".repeat(80));
      console.log("📋 EXPENSES WITH NEGATIVE BALANCES");
      console.log("=".repeat(80));

      if (negativeExpenses.length === 0) {
         console.log("✅ No expenses with negative balances found!");
         return;
      }

      console.log(
         `Found ${negativeExpenses.length} expenses with negative balances:`
      );
      console.log(
         `Total overspent amount: ${this.formatCurrency(totalNegativeAmount)}`
      );
      console.log("\n" + "-".repeat(80));

      // Sort by most negative first
      negativeExpenses.sort((a, b) => a.amountAvailable - b.amountAvailable);

      for (const expense of negativeExpenses) {
         console.log(`\n📌 ${expense.name}`);
         console.log(`   ID: ${expense._id}`);
         console.log(`   Category: ${expense.category || "Uncategorized"}`);
         console.log(
            `   Amount Assigned: ${this.formatCurrency(
               expense.amountAssigned || 0
            )}`
         );
         console.log(
            `   Amount Spent: ${this.formatCurrency(expense.amountSpent || 0)}`
         );
         console.log(
            `   ❌ Amount Available: ${this.formatCurrency(
               expense.amountAvailable
            )}`
         );
         console.log(
            `   Overspent by: ${this.formatCurrency(
               Math.abs(expense.amountAvailable)
            )}`
         );

         if (expense.dueDate) {
            console.log(
               `   Due Date: ${new Date(expense.dueDate).toLocaleDateString()}`
            );
         }

         if (expense.isRecurring) {
            console.log(
               `   🔄 Recurring: ${expense.frequency || "Unknown frequency"}`
            );
         }
      }

      console.log("\n" + "=".repeat(80));
      console.log("💡 SUMMARY:");
      console.log(`   • ${negativeExpenses.length} expenses are overspent`);
      console.log(
         `   • Total overspent amount: ${this.formatCurrency(
            totalNegativeAmount
         )}`
      );
      console.log(`   • Consider assigning more money to these categories`);
      console.log("=".repeat(80));
   }
}

/**
 * Main execution function
 */
async function runNegativeBalanceAnalysis() {
   const userId = process.argv[2];

   if (!userId) {
      console.error(`
❌ Usage: node negative-balance-expenses.js <user_id>

Example:
  node negative-balance-expenses.js 6764aeeccfd30a63a4a45566
      `);
      process.exit(1);
   }

   const client = new MongoClient(MONGODB_URI);

   try {
      console.log("🔍 Connecting to database...");
      await client.connect();
      const db = client.db(DB_NAME);

      const analyzer = new NegativeBalanceAnalyzer(db);
      const results = await analyzer.findNegativeBalanceExpenses(userId);
   } catch (error) {
      console.error("❌ Analysis failed:", error);
   } finally {
      await client.close();
      console.log("\n🔐 Database connection closed");
   }
}

// Run the analysis
if (require.main === module) {
   runNegativeBalanceAnalysis().catch(console.error);
}

module.exports = { NegativeBalanceAnalyzer };

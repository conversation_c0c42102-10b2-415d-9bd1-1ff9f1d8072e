# Financial Audit & Balance Correction Scripts

This directory contains powerful scripts for auditing and correcting financial data in your budget application. These scripts provide comprehensive validation and automatic correction of financial balances, data integrity, and relationship consistency.

## Scripts Overview

### 1. `financial-audit.js` - Comprehensive Financial Auditor

A sophisticated auditing script that performs deep analysis of all financial data for a user, validating:

-  **Account Balance Consistency**: Verifies stored account balances match calculated balances from transactions
-  **Expense Budget Calculations**: Validates `amountAvailable = amountAssigned + amountSpent` for all expenses
-  **Income Budget Calculations**: Verifies `receivedAmount` matches linked transaction totals
-  **Ready to Assign Logic**: Validates `totalReceived - totalAssigned` calculation
-  **Transaction Data Integrity**: Checks for invalid types, amounts, and missing required fields
-  **Category Linkages**: Validates transaction-to-category relationships and types
-  **Debt Tracking**: Audits debt payment tracking and validation
-  **Plaid Synchronization**: Checks for duplicate Plaid transactions and orphaned pending transactions
-  **Data Consistency**: Validates monetary precision and checks for orphaned references
-  **Recurring Items**: Validates recurring expense and income configurations

### 2. `balance-fixer.js` - Automated Balance Correction

A companion script that automatically fixes common balance issues discovered by the audit:

-  **Account Balance Correction**: Recalculates and fixes account balances based on transactions
-  **Expense Amount Correction**: Fixes `amountSpent` based on linked transactions
-  **Income Amount Correction**: Fixes `receivedAmount` based on linked transactions
-  **Precision Correction**: Rounds all monetary values to 2 decimal places
-  **Status Updates**: Updates expense and income statuses based on current amounts

## Usage Examples

### Running Financial Audit

```bash
# Audit a specific user
./scripts/financial-audit.js 507f1f77bcf86cd799439011

# Audit all users (comprehensive system audit)
./scripts/financial-audit.js all
```

### Running Balance Fixer

```bash
# Dry run (see what would be fixed without making changes)
./scripts/balance-fixer.js 507f1f77bcf86cd799439011

# Actually fix the balances
./scripts/balance-fixer.js 507f1f77bcf86cd799439011 --fix

# Dry run for all users
./scripts/balance-fixer.js --all

# Fix balances for all users (use with caution)
./scripts/balance-fixer.js --all --fix
```

## Audit Report Sections

The financial audit generates a comprehensive report with the following sections:

### 1. Account Balances

-  Compares stored vs calculated account balances
-  Identifies discrepancies requiring correction
-  **Critical Issues**: Balance mismatches that affect financial accuracy

### 2. Transaction Integrity

-  Validates transaction types, amounts, and required fields
-  Checks account references and transfer completeness
-  **Critical Issues**: Invalid transactions that could corrupt data

### 3. Expense Budgets

-  Validates expense calculations: `amountAvailable = amountAssigned + amountSpent`
-  Verifies `amountSpent` matches linked transaction totals
-  **Critical Issues**: Calculation mismatches affecting budget accuracy

### 4. Income Budgets

-  Verifies `receivedAmount` matches linked transaction totals
-  Checks income status consistency
-  **Critical Issues**: Income tracking discrepancies

### 5. Ready to Assign

-  Validates the core budget formula: `totalReceived - totalAssigned`
-  Identifies over-assignment situations
-  **Warning Issues**: Negative ready-to-assign amounts

### 6. Debt Tracking

-  Validates debt payment tracking and minimum payments
-  Checks for reasonable APR values and positive balances
-  **Warning Issues**: Invalid debt configurations

### 7. Plaid Synchronization

-  Identifies duplicate Plaid transactions
-  Finds orphaned pending transactions
-  **Critical Issues**: Duplicate transaction imports

### 8. Data Consistency

-  Validates transaction-to-category references
-  Checks monetary value precision
-  **Critical Issues**: Orphaned transaction references

### 9. Recurring Items

-  Validates recurring expense/income configurations
-  Checks for generated items from recurring templates
-  **Info Issues**: Missing generated items

### 10. Category Linkages

-  Validates transaction category type consistency
-  Provides categorization statistics
-  **Critical Issues**: Category type mismatches

## Issue Severity Levels

-  **🔴 Critical**: Issues that affect data integrity or financial accuracy - require immediate attention
-  **🟡 Warning**: Issues that should be addressed but don't compromise core functionality
-  **🔵 Info**: Informational items for reference and system health monitoring

## Key Financial Validations

Based on your budget system rules, these scripts validate the core financial relationships:

### Expense Calculations

```
amountAvailable = amountAssigned + amountSpent
```

Where `amountSpent` is updated by:

-  Expense transactions: Add negative amount
-  Income transactions (refunds): Add positive amount

### Income Tracking

```
receivedAmount = sum of linked Income transactions
```

### Ready to Assign

```
readyToAssign = totalReceivedIncome - totalAssignedExpenses
```

### Account Balances

```
accountBalance = sum of all account transactions
```

Including transfers, incomes, and expenses.

## Safety Features

### Dry Run by Default

The balance fixer runs in dry-run mode by default, showing what changes would be made without actually applying them. Use the `--fix` flag to apply changes.

### Transaction Integrity

Both scripts maintain transaction history and only fix calculated fields, never modifying the core transaction data that represents your financial history.

### Comprehensive Logging

All operations are logged with detailed information about what was found and what actions were taken.

## Best Practices

### 1. Regular Auditing

Run the financial audit regularly (weekly/monthly) to catch issues early:

```bash
./scripts/financial-audit.js all > audit-$(date +%Y%m%d).log
```

### 2. Fix Issues Promptly

Address critical issues immediately to maintain data integrity:

```bash
# First audit to identify issues
./scripts/financial-audit.js <user_id>

# Then fix if needed
./scripts/balance-fixer.js <user_id> --fix
```

### 3. Backup Before Mass Fixes

Before running fixes on all users, ensure you have a database backup:

```bash
# Backup your database first
mongodump --uri="$MONGODB_URI" --out=backup-$(date +%Y%m%d)

# Then run mass fixes
./scripts/balance-fixer.js --all --fix
```

### 4. Monitor Plaid Integration

If using Plaid, regularly check for synchronization issues:

```bash
./scripts/financial-audit.js all | grep -A 10 "PLAID"
```

## Troubleshooting

### Common Issues

**Permission Denied**

```bash
chmod +x scripts/financial-audit.js scripts/balance-fixer.js
```

**Database Connection Errors**

-  Verify `.env` file contains correct `MONGODB_URI`
-  Ensure database is accessible from your network

**Memory Issues with Large Datasets**

-  Process users individually instead of using `--all`
-  Consider running during low-usage periods

### Getting Help

If you encounter issues or need to understand specific audit results:

1. **Review the audit output** - each issue includes detailed context and data
2. **Check the balance fixer dry run** - see what fixes are recommended
3. **Examine specific user data** - focus on users with the most critical issues

## Integration with Budget System

These scripts are designed to work with your budget application's data models:

-  **Users Collection**: Account balances, recurring items
-  **Transactions Collection**: Core financial transactions
-  **Expenses Collection**: Budget expense items with assigned/spent amounts
-  **Incomes Collection**: Budget income items with expected/received amounts
-  **Debts Collection**: Debt tracking and payment monitoring

The scripts enforce the budget rules defined in your system and ensure all financial relationships remain consistent and accurate.

#!/usr/bin/env node

/**
 * Migration Script: Convert Income Dates to String Format
 *
 * This script converts all income date fields from Date objects to YYYY-MM-DD string format:
 * - 'date' field (Date → String YYYY-MM-DD)
 *
 * Usage: node scripts/migrate-income-dates-to-strings.js
 */

import mongoose from "mongoose";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, "../.env") });

// MongoDB connection URI
const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI environment variable is not set");
   process.exit(1);
}

// Connect to MongoDB
async function connectToMongoDB() {
   try {
      await mongoose.connect(MONGODB_URI);
      console.log("✅ Connected to MongoDB");
   } catch (error) {
      console.error("❌ Failed to connect to MongoDB:", error);
      process.exit(1);
   }
}

// Helper function to convert Date to YYYY-MM-DD string
function dateToString(date) {
   if (!date) return null;

   // If it's already a string, check if it's in the correct format
   if (typeof date === "string") {
      // If it's already in YYYY-MM-DD format, return as is
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
         return date;
      }
      // If it's an ISO string, convert to Date first
      date = new Date(date);
   }

   // If it's a Date object, convert to YYYY-MM-DD
   if (date instanceof Date && !isNaN(date)) {
      return date.toISOString().split("T")[0];
   }

   return null;
}

// Migration function
async function migrateIncomeDates() {
   try {
      console.log(
         "🚀 Starting migration: Converting income dates to string format"
      );

      // Get the incomes collection directly
      const db = mongoose.connection.db;
      const incomesCollection = db.collection("incomes");

      // First, let's see what we're working with
      const totalIncomes = await incomesCollection.countDocuments();
      console.log(`📊 Found ${totalIncomes} total incomes in database`);

      // Find incomes with Date objects in date fields
      const incomesWithDateObjects = await incomesCollection
         .find({
            date: { $type: "date" },
         })
         .toArray();

      console.log(
         `🔄 Found ${incomesWithDateObjects.length} incomes with Date objects that need conversion`
      );

      if (incomesWithDateObjects.length === 0) {
         console.log(
            "✅ No incomes need date conversion. All dates are already in string format!"
         );
         return;
      }

      // Show some examples of what we'll be converting
      console.log("\n📝 Sample of conversions to be made:");
      incomesWithDateObjects.slice(0, 3).forEach((income, index) => {
         console.log(`  Example ${index + 1}:`);
         console.log(`    Description: ${income.description}`);
         if (income.date) {
            console.log(
               `    date: ${income.date} → ${dateToString(income.date)}`
            );
         }
      });

      // Ask for confirmation
      console.log(
         `\n⚠️  This will update ${incomesWithDateObjects.length} income documents.`
      );
      console.log("🔄 Starting migration in 3 seconds... (Ctrl+C to cancel)");
      await new Promise((resolve) => setTimeout(resolve, 3000));

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each income
      for (const income of incomesWithDateObjects) {
         try {
            const updateFields = {};

            // Convert date field if it's a Date object
            if (income.date && income.date instanceof Date) {
               updateFields.date = dateToString(income.date);
            }

            // Only update if we have fields to update
            if (Object.keys(updateFields).length > 0) {
               await incomesCollection.updateOne(
                  { _id: income._id },
                  { $set: updateFields }
               );

               successCount++;

               // Log progress every 50 updates
               if (successCount % 50 === 0) {
                  console.log(
                     `✅ Processed ${successCount}/${incomesWithDateObjects.length} incomes...`
                  );
               }
            }
         } catch (error) {
            errorCount++;
            errors.push({
               incomeId: income._id,
               error: error.message,
            });
            console.error(
               `❌ Failed to update income ${income._id}:`,
               error.message
            );
         }
      }

      // Report results
      console.log("\n🎉 Migration completed!");
      console.log(`✅ Successfully updated: ${successCount} incomes`);
      console.log(`❌ Failed to update: ${errorCount} incomes`);

      if (errors.length > 0) {
         console.log("\n❌ Errors encountered:");
         errors.forEach(({ incomeId, error }) => {
            console.log(`  ${incomeId}: ${error}`);
         });
      }

      // Verify the migration
      console.log("\n🔍 Verifying migration...");
      const remainingDateObjects = await incomesCollection.countDocuments({
         date: { $type: "date" },
      });

      if (remainingDateObjects === 0) {
         console.log(
            "✅ Migration verification successful! All income dates are now strings."
         );
      } else {
         console.log(
            `⚠️  Migration verification found ${remainingDateObjects} incomes still with Date objects.`
         );
      }

      // Show sample of converted data
      console.log("\n📝 Sample of converted incomes:");
      const convertedSamples = await incomesCollection
         .find({
            date: { $type: "string" },
         })
         .limit(3)
         .toArray();

      convertedSamples.forEach((income, index) => {
         console.log(`  Sample ${index + 1}:`);
         console.log(`    Description: ${income.description}`);
         console.log(`    date: ${income.date} (${typeof income.date})`);
      });
   } catch (error) {
      console.error("❌ Migration failed:", error);
      throw error;
   }
}

// Main execution
async function main() {
   try {
      await connectToMongoDB();
      await migrateIncomeDates();
   } catch (error) {
      console.error("❌ Script failed:", error);
      process.exit(1);
   } finally {
      await mongoose.connection.close();
      console.log("🔌 Disconnected from MongoDB");
   }
}

// Run the migration
main();

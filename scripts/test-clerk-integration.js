#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
const { MongoClient } = require("mongodb");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

// MongoDB connection
const mongoClient = new MongoClient(process.env.MONGODB_URI);

async function testClerkIntegration() {
   console.log("🔍 Testing Clerk and MongoDB Integration...\n");

   try {
      // Test Clerk connection
      console.log("1. Testing Clerk connection...");
      const clerkUsers = await clerk.users.getUserList({ limit: 5 });
      console.log(
         `✅ Clerk connected successfully. Found ${clerkUsers.length} users.`
      );

      // Test MongoDB connection
      console.log("\n2. Testing MongoDB connection...");
      await mongoClient.connect();
      const db = mongoClient.db();
      const mongoUsers = await db
         .collection("users")
         .find({})
         .limit(5)
         .toArray();
      console.log(
         `✅ MongoDB connected successfully. Found ${mongoUsers.length} users.`
      );

      // Test user sync
      console.log("\n3. Testing user sync between Clerk and MongoDB...");
      const syncIssues = [];

      for (const clerkUser of clerkUsers) {
         const mongoUser = await db
            .collection("users")
            .findOne({ clerkId: clerkUser.id });

         if (!mongoUser) {
            syncIssues.push({
               type: "missing_in_mongo",
               clerkId: clerkUser.id,
               email: clerkUser.emailAddresses[0]?.emailAddress || "No email",
               firstName: clerkUser.firstName,
               lastName: clerkUser.lastName,
            });
         } else {
            // Check if data is in sync
            const clerkEmail = clerkUser.emailAddresses[0]?.emailAddress;
            if (mongoUser.email !== clerkEmail) {
               syncIssues.push({
                  type: "email_mismatch",
                  clerkId: clerkUser.id,
                  clerkEmail,
                  mongoEmail: mongoUser.email,
               });
            }

            if (
               mongoUser.firstName !== clerkUser.firstName ||
               mongoUser.lastName !== clerkUser.lastName
            ) {
               syncIssues.push({
                  type: "name_mismatch",
                  clerkId: clerkUser.id,
                  clerkName: `${clerkUser.firstName} ${clerkUser.lastName}`,
                  mongoName: `${mongoUser.firstName} ${mongoUser.lastName}`,
               });
            }
         }
      }

      // Check for MongoDB users not in Clerk
      const mongoUserIds = mongoUsers.map((u) => u.clerkId).filter(Boolean);
      const clerkUserIds = clerkUsers.map((u) => u.id);

      for (const mongoUser of mongoUsers) {
         if (mongoUser.clerkId && !clerkUserIds.includes(mongoUser.clerkId)) {
            syncIssues.push({
               type: "missing_in_clerk",
               clerkId: mongoUser.clerkId,
               email: mongoUser.email,
               mongoUserId: mongoUser._id.toString(),
            });
         }
      }

      if (syncIssues.length === 0) {
         console.log("✅ All users are in sync between Clerk and MongoDB!");
      } else {
         console.log(`⚠️  Found ${syncIssues.length} sync issues:`);
         syncIssues.forEach((issue, index) => {
            console.log(`\n${index + 1}. ${issue.type.toUpperCase()}:`);
            console.log(`   Clerk ID: ${issue.clerkId}`);
            console.log(
               `   Email: ${
                  issue.email || issue.clerkEmail || issue.mongoEmail
               }`
            );
            if (issue.type === "name_mismatch") {
               console.log(`   Clerk Name: ${issue.clerkName}`);
               console.log(`   MongoDB Name: ${issue.mongoName}`);
            }
            if (issue.type === "email_mismatch") {
               console.log(`   Clerk Email: ${issue.clerkEmail}`);
               console.log(`   MongoDB Email: ${issue.mongoEmail}`);
            }
         });
      }

      // Test webhook endpoint
      console.log("\n4. Testing webhook endpoint...");
      try {
         const response = await fetch(
            `${
               process.env.NEXT_PUBLIC_URL || "http://localhost:3000"
            }/api/auth/clerk-webhook`,
            {
               method: "POST",
               headers: {
                  "Content-Type": "application/json",
                  "svix-id": "test-id",
                  "svix-timestamp": Date.now().toString(),
                  "svix-signature": "test-signature",
               },
               body: JSON.stringify({
                  type: "user.created",
                  data: {
                     id: "test-user-id",
                     email_addresses: [{ email_address: "<EMAIL>" }],
                     first_name: "Test",
                     last_name: "User",
                  },
               }),
            }
         );

         if (response.status === 200) {
            console.log("✅ Webhook endpoint is accessible");
         } else {
            console.log(
               `⚠️  Webhook endpoint returned status: ${response.status}`
            );
         }
      } catch (error) {
         console.log(`❌ Webhook endpoint test failed: ${error.message}`);
      }

      console.log("\n🎉 Integration test completed!");
   } catch (error) {
      console.error("❌ Integration test failed:", error);
   } finally {
      await mongoClient.close();
   }
}

// Command line options
const args = process.argv.slice(2);
const options = {
   watch: args.includes("--watch"),
   verbose: args.includes("--verbose"),
};

if (options.watch) {
   console.log("👀 Running in watch mode (every 30 seconds)...");
   testClerkIntegration();
   setInterval(testClerkIntegration, 30000);
} else {
   testClerkIntegration();
}

#!/usr/bin/env node

/**
 * Recalculate Expense Spent Script
 *
 * This script recalculates the amountSpent field for all expenses based on
 * transactions assigned to them. This is needed after the migration from
 * category/categoryType to assignedTo/assignedToType where the amountSpent
 * fields weren't properly updated.
 *
 * The script will:
 * - Find all expenses in the database
 * - For each expense, calculate the total spent from assigned transactions
 * - Update the expense's amountSpent field with the correct value
 * - Update the expense status based on the new amounts
 */

const { MongoClient, ObjectId } = require("mongodb");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, "..", ".env") });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.DB_NAME || "budget_app";

if (!MONGODB_URI) {
   console.error("❌ MONGODB_URI not found in environment variables");
   process.exit(1);
}

class ExpenseSpentRecalculator {
   constructor(db) {
      this.db = db;
      this.expenses = db.collection("expenses");
      this.transactions = db.collection("transactions");

      this.stats = {
         totalExpenses: 0,
         updatedExpenses: 0,
         unchangedExpenses: 0,
         errors: 0,
      };
   }

   // Helper function to round to 2 decimal places
   roundToTwo(num) {
      return Number(Math.round(num + "e+2") + "e-2");
   }

   // Helper function to format currency
   formatCurrency(amount) {
      return new Intl.NumberFormat("en-US", {
         style: "currency",
         currency: "USD",
      }).format(amount);
   }

   // Helper function to determine expense status based on amounts
   determineExpenseStatus(amountDue, amountAssigned, amountSpent) {
      // Convert to absolute values and ensure proper number handling
      const due = Math.abs(Number(amountDue || 0));
      const assigned = Number(amountAssigned || 0);
      const spent = Math.abs(Number(amountSpent || 0));

      // If spent exceeds due, it's overpaid (regardless of due date)
      if (spent > due) {
         return "overpaid";
      }

      // If spent exactly matches due, it's paid
      if (spent === due) {
         return "paid";
      }

      // Check if expense is funded
      if (assigned >= due) {
         return "funded";
      }

      // Default to scheduled
      return "scheduled";
   }

   async recalculateExpenseSpent(dryRun = true) {
      console.log(
         `💰 ${dryRun ? "DRY RUN:" : ""} Recalculating expense spent amounts...`
      );

      // Get all expenses
      const expenses = await this.expenses.find({}).toArray();
      this.stats.totalExpenses = expenses.length;

      console.log(`Found ${expenses.length} expenses to process`);

      for (const expense of expenses) {
         try {
            // Find all transactions assigned to this expense
            const expenseTransactions = await this.transactions
               .find({
                  assignedTo: expense._id,
                  assignedToType: "Expense",
               })
               .toArray();

            // Calculate total spent from transactions
            let calculatedSpent = 0;
            for (const transaction of expenseTransactions) {
               if (transaction.type === "Expense") {
                  calculatedSpent += transaction.amount; // Already negative
               } else if (transaction.type === "Income") {
                  calculatedSpent += transaction.amount; // Positive for refunds
               }
            }

            calculatedSpent = this.roundToTwo(calculatedSpent);
            const storedSpent = this.roundToTwo(expense.amountSpent || 0);

            // Check if there's a difference
            if (Math.abs(calculatedSpent - storedSpent) > 0.01) {
               console.log(
                  `🔧 Expense "${expense.description}" (${
                     expense._id
                  }) - Spent: ${this.formatCurrency(
                     storedSpent
                  )} → ${this.formatCurrency(calculatedSpent)} (${
                     expenseTransactions.length
                  } transactions)`
               );

               if (!dryRun) {
                  // Update the expense with the correct amountSpent
                  await this.expenses.updateOne(
                     { _id: expense._id },
                     { $set: { amountSpent: calculatedSpent } }
                  );

                  // Determine and update the status if needed
                  const newStatus = this.determineExpenseStatus(
                     expense.amountDue,
                     expense.amountAssigned,
                     calculatedSpent
                  );

                  if (expense.status !== newStatus) {
                     console.log(`   Status: ${expense.status} → ${newStatus}`);
                     await this.expenses.updateOne(
                        { _id: expense._id },
                        { $set: { status: newStatus } }
                     );
                  }
               }

               this.stats.updatedExpenses++;
            } else {
               this.stats.unchangedExpenses++;
            }
         } catch (error) {
            console.error(
               `❌ Error processing expense ${expense._id}:`,
               error.message
            );
            this.stats.errors++;
         }
      }
   }

   async run(dryRun = true) {
      console.log("🚀 Starting Expense Spent Recalculation");
      console.log(`Mode: ${dryRun ? "DRY RUN" : "LIVE UPDATE"}`);
      console.log("=".repeat(50));

      await this.recalculateExpenseSpent(dryRun);

      console.log("\n📊 SUMMARY:");
      console.log(`Total Expenses: ${this.stats.totalExpenses}`);
      console.log(`Updated Expenses: ${this.stats.updatedExpenses}`);
      console.log(`Unchanged Expenses: ${this.stats.unchangedExpenses}`);
      console.log(`Errors: ${this.stats.errors}`);

      if (dryRun) {
         console.log("\n⚠️  This was a DRY RUN. No changes were made.");
         console.log(
            "Run with --live flag to apply changes: node recalculate-expense-spent.js --live"
         );
      } else {
         console.log("\n✅ Live update completed!");
      }
   }
}

async function main() {
   let client;

   try {
      console.log("🔗 Connecting to MongoDB...");
      client = new MongoClient(MONGODB_URI);
      await client.connect();

      const db = client.db(DB_NAME);
      console.log(`✅ Connected to database: ${DB_NAME}`);

      const recalculator = new ExpenseSpentRecalculator(db);

      // Check if --live flag is provided
      const isLiveRun = process.argv.includes("--live");

      await recalculator.run(!isLiveRun);
   } catch (error) {
      console.error("💥 Fatal error:", error);
      process.exit(1);
   } finally {
      if (client) {
         await client.close();
         console.log("🔌 Database connection closed");
      }
   }
}

// Run the script
if (require.main === module) {
   main();
}

module.exports = { ExpenseSpentRecalculator };

#!/usr/bin/env node

const { createClerkClient } = require("@clerk/clerk-sdk-node");
require("dotenv").config();

// Initialize Clerk client
const clerk = createClerkClient({
   secretKey: process.env.CLERK_SECRET_KEY,
});

async function testAllAPIFixes() {
   console.log("🔍 Testing All API Fixes...\n");

   try {
      // Get a test user from Clerk
      const users = await clerk.users.getUserList({ limit: 1 });
      if (users.length === 0) {
         console.log("❌ No users found in Clerk");
         return;
      }

      const testUser = users[0];
      console.log(`✅ Found test user: ${testUser.id}`);

      // Test MongoDB connection and user lookup
      const { MongoClient } = require("mongodb");
      const mongoClient = new MongoClient(process.env.MONGODB_URI);

      await mongoClient.connect();
      const db = mongoClient.db();
      const users_collection = db.collection("users");

      // Check if user exists in MongoDB
      const mongoUser = await users_collection.findOne({
         clerkId: testUser.id,
      });
      if (!mongoUser) {
         console.log("❌ User not found in MongoDB");
         return;
      }

      console.log("✅ User found in MongoDB:", mongoUser._id);

      // Test each collection
      const collections = [
         { name: "accounts", field: "accounts" },
         { name: "debts", field: "debts" },
         { name: "transactions", field: "transactions" },
         { name: "incomes", field: "incomes" },
         { name: "expenses", field: "expenses" },
      ];

      for (const collection of collections) {
         const data = await db
            .collection(collection.name)
            .find({ userId: mongoUser._id })
            .toArray();
         console.log(`✅ User has ${collection.name}:`, data.length);
      }

      console.log("\n🧪 Testing Component Imports...");

      // Test Clerk imports
      try {
         const { auth } = require("@clerk/nextjs/server");
         const { useUser } = require("@clerk/nextjs");
         console.log("✅ Clerk server and client imports working");
      } catch (error) {
         console.log("❌ Error importing Clerk functions:", error.message);
      }

      await mongoClient.close();
      console.log("\n🎉 All API fixes test completed!");
   } catch (error) {
      console.error("❌ Test failed:", error.message);
   }
}

// Run the test
testAllAPIFixes().catch(console.error);

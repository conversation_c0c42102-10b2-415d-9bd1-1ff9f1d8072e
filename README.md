# Alto Budget

A comprehensive personal finance management application built with Next.js that helps users track their income, expenses, and maintain multiple accounts while providing detailed insights into their financial health.

## ✨ New: Shadcn/ui Integration

This project now uses [Shadcn/ui](https://ui.shadcn.com/) as its core component library, providing:

-  🎨 **Beautiful, consistent design system**
-  ♿ **Built-in accessibility features**
-  🌙 **Automatic dark/light mode support**
-  🎯 **TypeScript-ready components**
-  🔧 **Easy customization with CSS variables**

### Quick Start with Shadcn

-  Visit `/settings` to see shadcn/ui components in action
-  Check `SHADCN_MIGRATION_GUIDE.md` for migration instructions
-  New components are in `components/ui/` directory

## Features

### 1. Account Management

-  Multiple account types support (cash, credit, loan, investment)
-  Track balances across different accounts
-  Monitor credit cards and loans with interest rates and minimum payments
-  Active/inactive account status tracking

### 2. Income Tracking

-  Multiple income sources
-  Recurring income management with flexible payment schedules:
   -  Weekly
   -  Biweekly
   -  Monthly
   -  Quarterly
   -  Annually
-  Customizable payment dates and frequencies

### 3. Expense Management

-  Recurring expense tracking
-  Auto-generated expense entries
-  Flexible expense categorization
-  Transaction status tracking (pending/cleared)

### 4. Financial Overview

-  Real-time balance tracking
-  Total assigned budget monitoring
-  Spending analysis
-  Income vs. expense comparison
-  Transaction history with detailed filtering

### 5. Plaid Integration

-  Link bank accounts directly using Plaid
-  Automatic transaction syncing
-  Transaction categorization
-  Real-time balance updates via webhooks
-  Historical transaction import

## Technical Stack

### Frontend

-  **Next.js 14**: React framework with App Router
-  **TailwindCSS**: Utility-first CSS framework
-  **ShadcN UI**: Modern UI components
-  **React Hook Form**: Form handling
-  **Date-fns**: Date manipulation

### Backend

-  **Next.js API Routes**: Server-side API endpoints
-  **MongoDB**: NoSQL database
-  **Mongoose**: MongoDB object modeling
-  **NextAuth.js**: Authentication
-  **Plaid API**: Financial data connectivity

## Data Models

### User Model

-  Personal information (name, email)
-  Income settings
-  Account configurations
-  Recurring expenses
-  Financial tracking (currentBalance, totalAssigned, totalSpent, totalIncome)

### Transaction Model

-  Transaction type (Income/Expense)
-  Amount tracking
-  Category management
-  Account association
-  Status tracking (pending/cleared)
-  Date and notes

## How It Works

1. **User Onboarding**

   -  Users create an account
   -  Set up initial income sources
   -  Configure bank accounts

2. **Transaction Flow**

   -  Transactions are created manually or auto-generated
   -  Each transaction updates account balances
   -  Transactions can be marked as pending or cleared

3. **Financial Tracking**

   -  Real-time balance updates across accounts
   -  Automatic recurring transaction generation
   -  Budget allocation tracking
   -  Spending analysis and categorization

4. **Data Synchronization**
   -  All changes update the MongoDB database
   -  Real-time balance calculations
   -  Automatic recurring transaction management
   -  Plaid webhook integration for real-time transaction updates

## Getting Started

1. Clone the repository
2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:

   ```env
   MONGODB_URI=your_mongodb_connection_string
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=http://localhost:3000
   ```

4. Run the development server:

   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) to view the application

## Development Guidelines

-  All monetary values are automatically rounded to 2 decimal places
-  Transactions affect account balances in real-time
-  Recurring transactions are automatically generated based on user settings
-  Account types (cash, credit, loan, investment) have different required fields
-  All dates are stored in ISO format and handled using date-fns

## Security Features

-  Password hashing for user accounts
-  Protected API routes
-  Session-based authentication
-  Secure environment variable handling

## Best Practices

-  Early returns in functions
-  Tailwind classes for styling
-  Descriptive variable naming
-  Accessibility features implementation
-  Type safety where possible
-  DRY (Don't Repeat Yourself) principle adherence

## Setting Up Plaid Integration

1. Create a Plaid account at [plaid.com](https://plaid.com)
2. Obtain your client ID and secret keys
3. Set up your environment variables:

   ```env
   PLAID_CLIENT_ID=your_plaid_client_id
   PLAID_SECRET=your_plaid_secret
   PLAID_ENV=sandbox # or development, production
   PLAID_WEBHOOK_URL=https://your-domain.com/api/plaid/webhook
   ```

4. For local development, you'll need a publicly accessible URL for webhooks:
   -  Use a service like [ngrok](https://ngrok.com) to create a tunnel to your local server
   -  Configure your `PLAID_WEBHOOK_URL` to point to your ngrok URL
   -  Example: `https://your-ngrok-id.ngrok.io/api/plaid/webhook`

### Updating Webhook URLs for Existing Plaid Items

The application includes tools to update webhook URLs for existing Plaid Items without requiring users to reconnect their accounts:

#### Using the Admin UI

1. Navigate to `/settings/webhook-migration` in your browser
2. Verify you're logged in as an admin or have the admin API key
3. Review the current webhook URL configuration
4. Click "Update All Plaid Webhooks" to start the migration

#### Using the Command Line Script

1. Ensure your environment variables are properly set in `.env`
2. Run the migration script:

   ```bash
   # Using the API key from command line
   node scripts/update-plaid-webhooks.js --apiKey=your_admin_api_key

   # Or using ADMIN_API_KEY from environment variables
   node scripts/update-plaid-webhooks.js
   ```

3. View the results in the console and in the log file at `logs/plaid-webhook-migration.json`

This migration uses Plaid's `item/webhook/update` endpoint to update the webhook URL for each Item without requiring users to go through the Link flow again.

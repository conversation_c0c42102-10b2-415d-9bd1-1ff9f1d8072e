/** @type {import('next').NextConfig} */

// Next.js configuration
const nextConfig = {
   env: {
      MONGODB_URI: process.env.MONGODB_URI,
      DB_NAME: process.env.DB_NAME,
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      PLAID_CLIENT_ID: process.env.PLAID_CLIENT_ID,
      PLAID_SECRET: process.env.PLAID_SECRET,
      PLAID_ENV: process.env.PLAID_ENV,
      NEXT_PUBLIC_URL: process.env.NEXT_PUBLIC_URL,
      PLAID_WEBHOOK_URL: process.env.PLAID_WEBHOOK_URL,
      NEXT_PUBLIC_PLAID_WEBHOOK_URL: process.env.NEXT_PUBLIC_PLAID_WEBHOOK_URL,
      ADMIN_API_KEY: process.env.ADMIN_API_KEY,
      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
      STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
      STRIPE_BASIC_PRICE_ID: process.env.STRIPE_BASIC_PRICE_ID,
      STRIPE_PRO_PRICE_ID: process.env.STRIPE_PRO_PRICE_ID,
   },
   // Add ADMIN_API_KEY to the list of environment variables available to middleware
   serverRuntimeConfig: {
      ADMIN_API_KEY: process.env.ADMIN_API_KEY,
   },
   publicRuntimeConfig: {
      NEXT_PUBLIC_URL: process.env.NEXT_PUBLIC_URL,
      NEXT_PUBLIC_PLAID_WEBHOOK_URL: process.env.NEXT_PUBLIC_PLAID_WEBHOOK_URL,
   },
   async headers() {
      return [
         {
            source: "/:path*",
            headers: [
               {
                  key: "Access-Control-Allow-Origin",
                  value: process.env.NEXT_PUBLIC_URL || "*",
               },
               {
                  key: "Access-Control-Allow-Methods",
                  value: "GET, POST, PUT, DELETE, OPTIONS",
               },
               {
                  key: "Access-Control-Allow-Headers",
                  value: "X-Requested-With, Content-Type, Authorization",
               },
            ],
         },
      ];
   },
};

// Export the configuration
export default nextConfig;

---
description: Rules for interacting with different amounts for the income, expenses and transactions and the users accounts.
globs: 
---
### Rules

#### 1. Transactions and Account Balances

-  **Transactions** (`Transaction` collection) affect account balances:
   -  **Income**: Increases `accountId` balance by `amount` (positive).
   -  **Expense**: Decreases `accountId` balance by `amount` (negative).
   -  **Transfer**: Decreases `fromAccountId` balance and increases `toAccountId` balance by `amount`.
-  The `status` field ("pending" or "cleared") is a visual indicator only; all transactions impact balances regardless of status.

#### 2. Linking Transactions to Budget Items

-  The `category` field in a `Transaction` is an `ObjectId` referencing an `Income` or `Expense` document:
   -  `categoryType: "Income"`: Links to an `Income` document.
   -  `categoryType: "Expense"`: Links to an `Expense` document.
   -  If `category` is null, `categoryType` is null (enforced by pre-save middleware).
-  **Income Budget Items**:
   -  Contain `expectedAmount` (planned income) and `receivedAmount` (actual income received).
   -  `receivedAmount` is updated when a transaction with `type: "Income"` and `categoryType: "Income"` links to it.
-  **Expense Budget Items**:
   -  Contain `amountDue` (reference), `amountAssigned` (budgeted amount), and `amountSpent` (actual spending).
   -  `amountSpent` is updated by transactions with `categoryType: "Expense"`:
      -  `type: "Expense"`: Adds a negative `amount` to `amountSpent`.
      -  `type: "Income"`: Adds a positive `amount` (e.g., refund) to `amountSpent`.
   -  Virtual `amountAvailable`: `amountAssigned + amountSpent`.

#### 3. Budget Item Totals

-  **Income**:
   -  `receivedAmount` is stored directly in the `Income` document and incremented by the `amount` of linked "Income" transactions.
-  **Expense**:
   -  `amountSpent` is stored in the `Expense` document and updated by summing linked transaction amounts (negative for expenses, positive for refunds).
   -  `amountAvailable` is a virtual field recalculated on access, reflecting available funds.

#### 4. "Ready to Assign" Metric

-  Calculated as:
   -  Total `receivedAmount` from all `Income` documents minus total `amountAssigned` from all `Expense` documents.
-  Affected by:
   -  Adding an "Income" transaction with `categoryType: "Income"`: Increases `receivedAmount`, thus increasing "ready to assign."
   -  Changing `amountAssigned` in an `Expense`: Adjusts "ready to assign" inversely.

#### 5. Data Flow on Changes

-  **Adding a Transaction**:
   -  Creates a `Transaction` document.
   -  Updates account balance(s).
   -  If `category` is set:
      -  `categoryType: "Income"`, `type: "Income"`: Increments `receivedAmount` in the linked `Income` document.
      -  `categoryType: "Expense"`: Adjusts `amountSpent` in the linked `Expense` document (negative for `type: "Expense"`, positive for `type: "Income"`).
   -  Updates "ready to assign" if income-related.
-  **Editing a Transaction**:
   -  Updates the `Transaction` document.
   -  Adjusts account balances based on `amount` or `type` changes.
   -  Reverses effects on the old `category` document and applies to the new one if `category` changes.
   -  Updates "ready to assign" if income linkage changes.
-  **Deleting a Transaction**:
   -  Removes the `Transaction` document.
   -  Reverses effects on account balances and linked `Income` or `Expense` documents.
-  **Updating Budget Items**:
   -  Changing `expectedAmount` in `Income`: Informational only.
   -  Changing `amountAssigned` in `Expense`: Adjusts `amountAvailable` and "ready to assign."

#### 6. Documents Affected

-  **`Transaction`**: Added, edited, or deleted.
-  **`Income`**: `receivedAmount` updated by transactions.
-  **`Expense`**: `amountSpent` updated by transactions, `amountAssigned` updated manually.

---

### Example Scenario

#### Initial State

-  **Income Document**:
   ```json
   {
      "_id": "income123",
      "userId": "user123",
      "description": "Salary",
      "expectedAmount": 1000,
      "receivedAmount": 0,
      "date": "2025-03-01"
   }
   ```
-  **Expense Document**:
   ```json
   {
      "_id": "expense456",
      "userId": "user123",
      "description": "Groceries",
      "amountDue": 200,
      "amountAssigned": 200,
      "amountSpent": 0,
      "date": "2025-03-01"
   }
   ```
-  **Account**: ID 1, balance: 0.
-  **"Ready to assign"**: 0 (received) - 200 (assigned) = -200.

#### Transaction 1: Income Received

-  **Transaction**:
   ```json
   {
      "userId": "user123",
      "type": "Income",
      "amount": 1000,
      "payee": "Employer",
      "category": "income123",
      "categoryType": "Income",
      "accountId": 1
   }
   ```
-  **Effects**:
   -  Account 1 balance: 0 → 1000.
   -  `Income.income123.receivedAmount`: 0 → 1000.
   -  "Ready to assign": -200 → 800.
-  **Documents Changed**:
   -  New `Transaction` document.
   -  `Income.income123` updated.

#### Transaction 2: Expense Spent

-  **Transaction**:
   ```json
   {
      "userId": "user123",
      "type": "Expense",
      "amount": -50,
      "payee": "Grocery Store",
      "category": "expense456",
      "categoryType": "Expense",
      "accountId": 1
   }
   ```
-  **Effects**:
   -  Account 1 balance: 1000 → 950.
   -  `Expense.expense456.amountSpent`: 0 → -50.
   -  `Expense.expense456.amountAvailable`: 200 → 150.
   -  "Ready to assign": Unchanged at 800.
-  **Documents Changed**:
   -  New `Transaction` document.
   -  `Expense.expense456` updated.

#### Transaction 3: Refund Received

-  **Transaction**:
   ```json
   {
      "userId": "user123",
      "type": "Income",
      "amount": 20,
      "payee": "Grocery Refund",
      "category": "expense456",
      "categoryType": "Expense",
      "accountId": 1
   }
   ```
-  **Effects**:
   -  Account 1 balance: 950 → 970.
   -  `Expense.expense456.amountSpent`: -50 → -30.
   -  `Expense.expense456.amountAvailable`: 150 → 170.
   -  "Ready to assign": Unchanged at 800.
-  **Documents Changed**:
   -  New `Transaction` document.
   -  `Expense.expense456` updated.

#### Update Expense Budget

-  **Change**: Update `Expense.expense456.amountAssigned` from 200 to 250.
-  **Effects**:
   -  `Expense.expense456.amountAssigned`: 200 → 250.
   -  `Expense.expense456.amountAvailable`: 170 → 220.
   -  "Ready to assign": 1000 - 250 = 750.
-  **Documents Changed**:
   -  `Expense.expense456` updated.

#### Edit Transaction 1: Increase Income

-  **Change**: Update Transaction 1 `amount` from 1000 to 1200.
-  **Effects**:
   -  Account 1 balance: 970 → 1170.
   -  `Income.income123.receivedAmount`: 1000 → 1200.
   -  "Ready to assign": 750 → 950.
-  **Documents Changed**:
   -  `Transaction` document updated.
   -  `Income.income123` updated.

#### Delete Transaction 2: Remove Expense

-  **Action**: Delete Transaction 2.
-  **Effects**:
   -  Account 1 balance: 1170 → 1220.
   -  `Expense.expense456.amountSpent`: -30 → 20.
   -  `Expense.expense456.amountAvailable`: 220 → 270.
   -  "Ready to assign": Unchanged at 950.
-  **Documents Changed**:
   -  `Transaction` document deleted.
   -  `Expense.expense456` updated.

---

### Final Summary

-  **Income Documents**: Store `expectedAmount` and `receivedAmount`, with `receivedAmount` updated by linked transactions.
-  **Expense Documents**: Store `amountDue`, `amountAssigned`, and `amountSpent`, with `amountSpent` updated by linked transactions and `amountAvailable` as a virtual field.
-  **Transactions**: Drive updates to account balances and budget item fields (`receivedAmount` or `amountSpent`).
-  **"Ready to assign"**: Reflects total `receivedAmount` minus total `amountAssigned`, adjusting with income transactions or budget changes.

This aligns with your `IncomeSchema` and `ExpenseSchema`, ensuring budget items are distinct entities updated by transactions, providing a clear and accurate data flow for your app. Let me know if you’d like further refinements!
